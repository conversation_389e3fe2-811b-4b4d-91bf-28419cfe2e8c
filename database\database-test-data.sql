-- Test data for GP Mobile Quotation System with Authentication
-- Run this script in carsoft_giaphat database to create test data
-- This script works with your existing database structure
-- Microsoft SQL Server 2014 compatible

USE carsoft_giaphat;
GO

-- Check if we're in the right database
PRINT 'Current database: ' + DB_NAME();
PRINT 'Current user: ' + SYSTEM_USER;
PRINT '';

-- Check existing table structure first
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DM_NguoiDung]') AND type in (N'U'))
BEGIN
    PRINT 'DM_NguoiDung table exists - checking structure...';

    -- Show current columns
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'DM_NguoiDung'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT 'DM_NguoiDung table does not exist!';
END

-- Create test user if not exists (adjust column names based on your actual table structure)
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DM_NguoiDung]') AND type in (N'U'))
AND NOT EXISTS (SELECT 1 FROM DM_NguoiDung WHERE TenDangNhap = 'admin')
BEGIN
    INSERT INTO DM_NguoiDung (
        Khoa, TenDangNhap, MatKhau, TenViet, LoaiNguoiDung,
        DonViDangNhap, Active, NgayTao, NguoiTao
    )
    VALUES (
        '0000000001', 'admin', 'admin123', N'Quản trị viên', 'ADMIN',
        'DONVI001|DONVI002|', 1, '20241201', 'SYSTEM'
    );
    PRINT 'Created admin user';
END
ELSE IF EXISTS (SELECT 1 FROM DM_NguoiDung WHERE TenDangNhap = 'admin')
BEGIN
    PRINT 'Admin user already exists';
END

-- Create test user for mobile
IF NOT EXISTS (SELECT 1 FROM DM_NguoiDung WHERE TenDangNhap = 'mobile')
BEGIN
    INSERT INTO DM_NguoiDung (
        Khoa, TenDangNhap, MatKhau, TenViet, LoaiNguoiDung, 
        DonViDangNhap, Active, NgayTao, NguoiTao
    )
    VALUES (
        '0000000002', 'mobile', 'mobile123', N'Người dùng di động', 'USER',
        'DONVI001|', 1, '20241201', 'SYSTEM'
    );
END

-- Create test branches/units if not exists
IF NOT EXISTS (SELECT 1 FROM DM_DonVi WHERE Ma = 'DONVI001')
BEGIN
    INSERT INTO DM_DonVi (
        Khoa, Ma, TenViet, TenAnh, Prefix, Active, NgayTao, NguoiTao
    )
    VALUES (
        'DONVI001', 'DV001', N'Chi nhánh Hà Nội', 'Hanoi Branch', 'HN', 1, '20241201', 'SYSTEM'
    );
END

IF NOT EXISTS (SELECT 1 FROM DM_DonVi WHERE Ma = 'DONVI002')
BEGIN
    INSERT INTO DM_DonVi (
        Khoa, Ma, TenViet, TenAnh, Prefix, Active, NgayTao, NguoiTao
    )
    VALUES (
        'DONVI002', 'DV002', N'Chi nhánh TP.HCM', 'HCMC Branch', 'HCM', 1, '20241201', 'SYSTEM'
    );
END

-- Create test terms & conditions if not exists
IF NOT EXISTS (SELECT 1 FROM DM_DieuKhoanbaoGia WHERE Ma = 'BG001')
BEGIN
    INSERT INTO DM_DieuKhoanbaoGia (
        Khoa, Ma, TenViet, TenAnh, DienGiai, Active, Loai, STT, NoiDung, 
        NgayTao, NguoiTao, NgayCapNhat, NguoiCapNhat, KhoaDonVi, IsDefault, IsSystem
    )
    VALUES 
    -- Báo giá terms
    ('BG001', 'BG001', N'Điều khoản báo giá chung', 'General Quotation Terms', N'Điều khoản báo giá chung', 1, 'BG', 1, 
     N'1. Báo giá có hiệu lực trong 30 ngày
2. Giá đã bao gồm VAT 10%
3. Thời gian bảo hành: 12 tháng
4. Thanh toán: 50% trước, 50% sau khi hoàn thành', 
     '20241201', 'ADMIN', '20241201', 'ADMIN', 'DONVI001', 1, 0),

    -- Sửa chữa terms  
    ('SC001', 'SC001', N'Điều khoản sửa chữa', 'Repair Terms', N'Điều khoản sửa chữa', 1, 'SC', 1,
     N'1. Xe được bảo quản trong thời gian sửa chữa
2. Khách hàng có trách nhiệm lấy xe đúng hẹn
3. Phụ tùng thay thế được bảo hành theo quy định
4. Garage không chịu trách nhiệm về đồ dùng cá nhân trong xe',
     '20241201', 'ADMIN', '20241201', 'ADMIN', 'DONVI001', 1, 0),

    -- Quyết toán terms
    ('QT001', 'QT001', N'Điều khoản quyết toán', 'Settlement Terms', N'Điều khoản quyết toán', 1, 'QT', 1,
     N'1. Thanh toán đầy đủ trước khi giao xe
2. Hóa đơn VAT được xuất theo yêu cầu
3. Bảo hành có hiệu lực từ ngày giao xe
4. Khiếu nại phải được thông báo trong 24h',
     '20241201', 'ADMIN', '20241201', 'ADMIN', 'DONVI001', 1, 0);
END

-- Create test quotation data if not exists
IF NOT EXISTS (SELECT 1 FROM Temp_BaoGia WHERE SoChungTu = 'BG2024001')
BEGIN
    INSERT INTO Temp_BaoGia (
        Khoa, SoChungTu, SoXe, TenKhachHang, TrangThai, TongTien, 
        NgayTao, NguoiTao, KhoaDonVi, DienGiai
    )
    VALUES 
    ('QUOTE001', 'BG2024001', '29A-12345', N'Nguyễn Văn An', 1, 5500000, 
     '20241201', 'ADMIN', 'DONVI001', N'Sửa chữa định kỳ'),
     
    ('QUOTE002', 'BG2024002', '30B-67890', N'Trần Thị Bình', 0, 3200000, 
     '20241201', 'ADMIN', 'DONVI001', N'Thay nhớt và bảo dưỡng'),
     
    ('QUOTE003', 'BG2024003', '51C-11111', N'Lê Văn Cường', 2, 8900000, 
     '20241201', 'ADMIN', 'DONVI001', N'Sửa chữa tai nạn');
END

-- Create tables for authentication if they don't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LoginAttempts]') AND type in (N'U'))
BEGIN
    CREATE TABLE LoginAttempts (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL,
        AttemptTime datetime NOT NULL,
        Success bit NOT NULL,
        Reason nvarchar(255),
        IpAddress nvarchar(50)
    );
END

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UserActivityLog]') AND type in (N'U'))
BEGIN
    CREATE TABLE UserActivityLog (
        Id int IDENTITY(1,1) PRIMARY KEY,
        UserId nvarchar(50) NOT NULL,
        Activity nvarchar(100) NOT NULL,
        Details nvarchar(500),
        ActivityTime datetime NOT NULL
    );
END

-- Create health check endpoint data
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SystemHealth]') AND type in (N'U'))
BEGIN
    CREATE TABLE SystemHealth (
        Id int IDENTITY(1,1) PRIMARY KEY,
        CheckTime datetime NOT NULL,
        Status nvarchar(20) NOT NULL,
        Details nvarchar(500)
    );
    
    INSERT INTO SystemHealth (CheckTime, Status, Details)
    VALUES (GETDATE(), 'Healthy', 'System initialized successfully');
END

PRINT 'Test data created successfully!';
PRINT '';
PRINT 'Test credentials:';
PRINT 'Username: admin, Password: admin123 (Full access)';
PRINT 'Username: mobile, Password: mobile123 (Limited access)';
PRINT '';
PRINT 'Available branches:';
PRINT 'DONVI001 - Chi nhánh Hà Nội';
PRINT 'DONVI002 - Chi nhánh TP.HCM';
PRINT '';
PRINT 'Test quotations created: 3 quotations';
PRINT 'Terms & conditions created: BG, SC, QT types';
GO
