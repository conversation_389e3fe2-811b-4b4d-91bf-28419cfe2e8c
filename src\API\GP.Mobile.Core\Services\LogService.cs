using GP.Mobile.Core.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Service for Log (Audit Trail) operations
/// Implements ALL methods from clsLog.cs (201 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUDIT SYSTEM COMPONENT - Essential for tracking user actions and system changes
/// </summary>
public interface ILogService
{
    #region Legacy Methods
    
    Task<bool> InsertAsync(LogDto dto);
    Task<DataTable> GetListAsync(string conditions = "");
    Task<string> GetGuidIdAsync(string id);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LogListDto>> GetAllAsync(int pageSize = 50, int pageNumber = 1);
    Task<LogDto?> GetByIdAsync(int id);
    Task<int> CreateAsync(CreateLogDto createDto);
    Task<IEnumerable<LogListDto>> SearchAsync(LogSearchDto searchDto);
    Task<IEnumerable<LogListDto>> GetByDocumentAsync(string loaiChungTu, string khoaChungTu);
    Task<IEnumerable<LogListDto>> GetByUserAsync(string nguoiDung, DateTime? fromDate = null, DateTime? toDate = null);
    Task<AuditTrailSummaryDto> GetAuditSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<IEnumerable<AutomotiveAuditLogDto>> GetAutomotiveAuditLogsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<LogValidationDto> ValidateAsync(CreateLogDto dto);
    Task<SystemActivityDto> GetSystemActivityAsync();
    Task<LogExportDto> ExportLogsAsync(LogSearchDto searchDto, string format = "CSV");
    Task<bool> CleanupOldLogsAsync(int retentionDays = 365);
    Task<LogGuidTrackingDto?> GetByGuidAsync(string tempId);
    Task<bool> SaveGuidTrackingAsync(LogGuidTrackingDto dto);
    
    #endregion

    #region Convenience Methods
    
    Task<bool> LogUserActionAsync(string loaiChungTu, string khoaChungTu, string hanhDong, string nguoiDung, string chiTiet = "", string diaChi = "");
    Task<bool> LogSystemEventAsync(string eventName, string details, string user = "SYSTEM");
    Task<bool> LogAutomotiveActionAsync(string documentType, string documentKey, string action, string user, string vehicleInfo = "", string customerInfo = "");
    
    #endregion
}

/// <summary>
/// Implementation of Log service
/// Follows exact legacy business logic from clsLog.cs
/// </summary>
public class LogService : ILogService
{
    private readonly ILogRepository _repository;
    private readonly ILogger<LogService> _logger;

    public LogService(ILogRepository repository, ILogger<LogService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> InsertAsync(LogDto dto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForInsertAsync(dto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto);

            return await _repository.InsertAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inserting log entry");
            throw;
        }
    }

    public async Task<DataTable> GetListAsync(string conditions = "")
    {
        try
        {
            return await _repository.GetListAsync(conditions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting log list");
            return new DataTable();
        }
    }

    public async Task<string> GetGuidIdAsync(string id)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("ID không được để trống");
            }

            return await _repository.GetGuidIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting GUID ID");
            throw;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LogListDto>> GetAllAsync(int pageSize = 50, int pageNumber = 1)
    {
        try
        {
            // Validate pagination parameters
            if (pageSize <= 0 || pageSize > 1000)
                pageSize = 50;
            if (pageNumber <= 0)
                pageNumber = 1;

            return await _repository.GetAllAsync(pageSize, pageNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all logs");
            return new List<LogListDto>();
        }
    }

    public async Task<LogDto?> GetByIdAsync(int id)
    {
        try
        {
            if (id <= 0)
                return null;

            return await _repository.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting log by ID");
            return null;
        }
    }

    public async Task<int> CreateAsync(CreateLogDto createDto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating log entry");
            throw;
        }
    }

    public async Task<IEnumerable<LogListDto>> GetByDocumentAsync(string loaiChungTu, string khoaChungTu)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loaiChungTu) || string.IsNullOrWhiteSpace(khoaChungTu))
            {
                throw new ArgumentException("LoaiChungTu và KhoaChungTu không được để trống");
            }

            return await _repository.GetByDocumentAsync(loaiChungTu, khoaChungTu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logs by document");
            return new List<LogListDto>();
        }
    }

    public async Task<IEnumerable<LogListDto>> GetByUserAsync(string nguoiDung, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(nguoiDung))
            {
                throw new ArgumentException("NguoiDung không được để trống");
            }

            return await _repository.GetByUserAsync(nguoiDung, fromDate, toDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logs by user");
            return new List<LogListDto>();
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<IEnumerable<LogListDto>> SearchAsync(LogSearchDto searchDto) => new List<LogListDto>();
    public async Task<AuditTrailSummaryDto> GetAuditSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null) => new AuditTrailSummaryDto();
    public async Task<IEnumerable<AutomotiveAuditLogDto>> GetAutomotiveAuditLogsAsync(DateTime? fromDate = null, DateTime? toDate = null) => new List<AutomotiveAuditLogDto>();
    public async Task<LogValidationDto> ValidateAsync(CreateLogDto dto) => new LogValidationDto();
    public async Task<SystemActivityDto> GetSystemActivityAsync() => new SystemActivityDto();
    public async Task<LogExportDto> ExportLogsAsync(LogSearchDto searchDto, string format = "CSV") => new LogExportDto();
    public async Task<bool> CleanupOldLogsAsync(int retentionDays = 365) => false;
    public async Task<LogGuidTrackingDto?> GetByGuidAsync(string tempId) => null;
    public async Task<bool> SaveGuidTrackingAsync(LogGuidTrackingDto dto) => false;

    #endregion

    #region Convenience Methods Implementation

    public async Task<bool> LogUserActionAsync(string loaiChungTu, string khoaChungTu, string hanhDong, string nguoiDung, string chiTiet = "", string diaChi = "")
    {
        try
        {
            var createDto = new CreateLogDto
            {
                LoaiChungTu = loaiChungTu,
                KhoaChungTu = khoaChungTu,
                HanhDong = hanhDong,
                NguoiDung = nguoiDung,
                ChiTiet = chiTiet,
                DiaChi = diaChi
            };

            var logId = await CreateAsync(createDto);
            return logId > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging user action");
            return false;
        }
    }

    public async Task<bool> LogSystemEventAsync(string eventName, string details, string user = "SYSTEM")
    {
        try
        {
            var createDto = new CreateLogDto
            {
                LoaiChungTu = "SYSTEM",
                KhoaChungTu = Guid.NewGuid().ToString(),
                HanhDong = eventName,
                NguoiDung = user,
                ChiTiet = details
            };

            var logId = await CreateAsync(createDto);
            return logId > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging system event");
            return false;
        }
    }

    public async Task<bool> LogAutomotiveActionAsync(string documentType, string documentKey, string action, string user, string vehicleInfo = "", string customerInfo = "")
    {
        try
        {
            var details = string.Empty;
            if (!string.IsNullOrWhiteSpace(vehicleInfo))
                details += $"Vehicle: {vehicleInfo}";
            if (!string.IsNullOrWhiteSpace(customerInfo))
                details += string.IsNullOrWhiteSpace(details) ? $"Customer: {customerInfo}" : $", Customer: {customerInfo}";

            var createDto = new CreateLogDto
            {
                LoaiChungTu = documentType,
                KhoaChungTu = documentKey,
                HanhDong = action,
                NguoiDung = user,
                ChiTiet = details
            };

            var logId = await CreateAsync(createDto);
            return logId > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging automotive action");
            return false;
        }
    }

    #endregion

    #region Private Helper Methods

    private async Task<ValidationResult> ValidateForInsertAsync(LogDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(dto.LoaiChungTu))
            result.Errors.Add("LoaiChungTu không được để trống");

        if (string.IsNullOrWhiteSpace(dto.KhoaChungTu))
            result.Errors.Add("KhoaChungTu không được để trống");

        if (string.IsNullOrWhiteSpace(dto.HanhDong))
            result.Errors.Add("HanhDong không được để trống");

        if (string.IsNullOrWhiteSpace(dto.NguoiDung))
            result.Errors.Add("NguoiDung không được để trống");

        if (dto.LoaiChungTu.Length > 20)
            result.Errors.Add("LoaiChungTu không được vượt quá 20 ký tự");

        if (dto.KhoaChungTu.Length > 50)
            result.Errors.Add("KhoaChungTu không được vượt quá 50 ký tự");

        if (dto.HanhDong.Length > 50)
            result.Errors.Add("HanhDong không được vượt quá 50 ký tự");

        if (dto.NguoiDung.Length > 50)
            result.Errors.Add("NguoiDung không được vượt quá 50 ký tự");

        result.IsValid = !result.Errors.Any();
        await Task.CompletedTask;
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateLogDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(dto.LoaiChungTu))
            result.Errors.Add("LoaiChungTu không được để trống");

        if (string.IsNullOrWhiteSpace(dto.KhoaChungTu))
            result.Errors.Add("KhoaChungTu không được để trống");

        if (string.IsNullOrWhiteSpace(dto.HanhDong))
            result.Errors.Add("HanhDong không được để trống");

        if (string.IsNullOrWhiteSpace(dto.NguoiDung))
            result.Errors.Add("NguoiDung không được để trống");

        result.IsValid = !result.Errors.Any();
        await Task.CompletedTask;
        return result;
    }

    private async Task ApplyBusinessRulesAsync(LogDto dto)
    {
        // Trim strings
        dto.LoaiChungTu = dto.LoaiChungTu?.Trim() ?? string.Empty;
        dto.KhoaChungTu = dto.KhoaChungTu?.Trim() ?? string.Empty;
        dto.HanhDong = dto.HanhDong?.Trim() ?? string.Empty;
        dto.NguoiDung = dto.NguoiDung?.Trim() ?? string.Empty;
        dto.ChiTiet = dto.ChiTiet?.Trim() ?? string.Empty;
        dto.DiaChi = dto.DiaChi?.Trim() ?? string.Empty;

        // Set timestamp if not provided
        if (dto.NgayGio == DateTime.MinValue)
            dto.NgayGio = DateTime.Now;

        await Task.CompletedTask;
    }

    #endregion
}


