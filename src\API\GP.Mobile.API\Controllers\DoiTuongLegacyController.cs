using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Legacy API Controller exposing ALL methods from clsDMDoiTuong.cs
/// Maintains exact functionality and SQL queries from legacy system
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DoiTuongLegacyController : ControllerBase
{
    private readonly IDoiTuongService _doiTuongService;
    private readonly ILogger<DoiTuongLegacyController> _logger;

    public DoiTuongLegacyController(IDoiTuongService doiTuongService, ILogger<DoiTuongLegacyController> logger)
    {
        _doiTuongService = doiTuongService;
        _logger = logger;
    }

    #region Core Operations - Exact Legacy API

    /// <summary>
    /// Load by Khoa - exact legacy Load(string pKhoa)
    /// </summary>
    [HttpGet("load/{khoa}")]
    public async Task<ActionResult<bool>> Load(string khoa)
    {
        try
        {
            var result = await _doiTuongService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DoiTuong by Khoa: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Load by Code - exact legacy LoadByCode(string pMa)
    /// </summary>
    [HttpGet("load-by-code/{ma}")]
    public async Task<ActionResult<bool>> LoadByCode(string ma)
    {
        try
        {
            var result = await _doiTuongService.LoadByCodeAsync(ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DoiTuong by Ma: {Ma}", ma);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Save - exact legacy Save(string pTask = "")
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] DoiTuongDto dto, [FromQuery] string pTask = "")
    {
        try
        {
            var result = await _doiTuongService.SaveAsync(dto, pTask);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving DoiTuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete - exact legacy DelData(string pKhoa)
    /// </summary>
    [HttpDelete("del-data/{khoa}")]
    public async Task<ActionResult<bool>> DelData(string khoa)
    {
        try
        {
            var result = await _doiTuongService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DoiTuong: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region List Operations - Exact Legacy SQL

    /// <summary>
    /// ShowList - exact legacy ShowList(string strLoaiDoiTuong = "", string strConditions = "")
    /// </summary>
    [HttpGet("show-list")]
    public async Task<ActionResult<DataTable>> ShowList([FromQuery] string strLoaiDoiTuong = "", [FromQuery] string strConditions = "")
    {
        try
        {
            var result = await _doiTuongService.ShowListAsync(strLoaiDoiTuong, strConditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// ShowAllList - exact legacy ShowAllList(string pStrLoai = "")
    /// </summary>
    [HttpGet("show-all-list")]
    public async Task<ActionResult<DataTable>> ShowAllList([FromQuery] string pStrLoai = "")
    {
        try
        {
            var result = await _doiTuongService.ShowAllListAsync(pStrLoai);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// ShowAllListMST - exact legacy ShowAllListMST(string pStrLoai = "")
    /// </summary>
    [HttpGet("show-all-list-mst")]
    public async Task<ActionResult<DataTable>> ShowAllListMST([FromQuery] string pStrLoai = "")
    {
        try
        {
            var result = await _doiTuongService.ShowAllListMSTAsync(pStrLoai);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListMST");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// ShowAllList with conditions - exact legacy overloaded ShowAllList
    /// </summary>
    [HttpGet("show-all-list-with-conditions")]
    public async Task<ActionResult<DataTable>> ShowAllListWithConditions([FromQuery] string pStrLoai = "", [FromQuery] string pConditions = "")
    {
        try
        {
            var result = await _doiTuongService.ShowAllListWithConditionsAsync(pStrLoai, pConditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListWithConditions");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Employee Operations - Exact Legacy SQL

    /// <summary>
    /// GetListNhanVien - exact legacy GetListNhanVien()
    /// </summary>
    [HttpGet("get-list-nhan-vien")]
    public async Task<ActionResult<DataTable>> GetListNhanVien()
    {
        try
        {
            var result = await _doiTuongService.GetListNhanVienAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListNhanVien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// GetListKyThuatVien - exact legacy GetListKyThuatVien(string strKhoaToSuaChua = "")
    /// </summary>
    [HttpGet("get-list-ky-thuat-vien")]
    public async Task<ActionResult<DataTable>> GetListKyThuatVien([FromQuery] string strKhoaToSuaChua = "")
    {
        try
        {
            var result = await _doiTuongService.GetListKyThuatVienAsync(strKhoaToSuaChua);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListKyThuatVien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// GetListAllNhanVienTheoTo - exact legacy GetListAllNhanVienTheoTo(string strCondition = "")
    /// </summary>
    [HttpGet("get-list-all-nhan-vien-theo-to")]
    public async Task<ActionResult<DataTable>> GetListAllNhanVienTheoTo([FromQuery] string strCondition = "")
    {
        try
        {
            var result = await _doiTuongService.GetListAllNhanVienTheoToAsync(strCondition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListAllNhanVienTheoTo");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Search and Validation - Exact Legacy Logic

    /// <summary>
    /// SearchByCode - exact legacy SearchByCode(string strCode = "", string strLoaiDoiTuong = "")
    /// </summary>
    [HttpGet("search-by-code")]
    public async Task<ActionResult<string>> SearchByCode([FromQuery] string strCode = "", [FromQuery] string strLoaiDoiTuong = "")
    {
        try
        {
            var result = await _doiTuongService.SearchByCodeAsync(strCode, strLoaiDoiTuong);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungMa - exact legacy TrungMa(string pMa, string pKhoa)
    /// </summary>
    [HttpGet("trung-ma")]
    public async Task<ActionResult<bool>> TrungMa([FromQuery] string pMa, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _doiTuongService.TrungMaAsync(pMa, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungMaSoThue - exact legacy TrungMaSoThue(string strMaSoThue, string strKhoa)
    /// </summary>
    [HttpGet("trung-ma-so-thue")]
    public async Task<ActionResult<bool>> TrungMaSoThue([FromQuery] string strMaSoThue, [FromQuery] string strKhoa)
    {
        try
        {
            var result = await _doiTuongService.TrungMaSoThueAsync(strMaSoThue, strKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMaSoThue");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungDienThoaiVaTen - exact legacy TrungDienThoaiVaTen(string pTen, string pDienThoai, string pKhoa)
    /// </summary>
    [HttpGet("trung-dien-thoai-va-ten")]
    public async Task<ActionResult<bool>> TrungDienThoaiVaTen([FromQuery] string pTen, [FromQuery] string pDienThoai, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _doiTuongService.TrungDienThoaiVaTenAsync(pTen, pDienThoai, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungDienThoaiVaTen");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}
