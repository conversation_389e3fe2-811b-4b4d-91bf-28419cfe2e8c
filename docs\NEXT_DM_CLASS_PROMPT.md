# Next DM Class Implementation Prompt

## Context
We have successfully implemented 12/181 classes (6.6%) using a proven approach that delivers 100% legacy compatibility with comprehensive modern API features. The pattern has been documented in `IMPLEMENTATION_PROMPT_TEMPLATE.md`.

## Current Progress
**Completed Automotive DM Classes:**
- ✅ **DM_LoaiDichVu** (Service Type) - 468+ lines → Modern API 🚗 **FIRST AUTOMOTIVE SERVICE CLASS!**
- ✅ **DM_LoaiXe** (Vehicle Type) - 577+ lines → Modern API 🚗 **SECOND AUTOMOTIVE CLASS!**
- ✅ **DM_HangSanXuat** (Manufacturer) - 533+ lines → Modern API 🚗 **THIRD AUTOMOTIVE CLASS!**
- ✅ **DM_BaoDuong** (Maintenance Service) - 364+ lines → Modern API 🚗 **FOURTH AUTOMOTIVE CLASS!**

## Next Priority: clsDMXe (Individual Vehicles)

### Why clsDMXe Next?
1. **Completes Automotive Foundation**: Uses LoaiXe, HangSanXuat, and potentially BaoDuong
2. **High Business Value**: Represents actual vehicles in the system
3. **Critical Dependencies**: Many other classes depend on individual vehicles
4. **Service Integration**: Essential for service quotations and maintenance

### Exact Prompt to Use:

```
I want you to implement clsDMXe (Individual Vehicles) next, as it uses LoaiXe, HangSanXuat, and potentially BaoDuong dependencies and represents actual vehicles, completing the automotive master data foundation. Let me examine the legacy clsDMXe.cs class to understand its structure:

[Use view command to examine Base/Business/clsDMXe.cs file structure, properties, methods, and line count]
```

### Expected Implementation Pattern:

Following the proven template, this should result in:

1. **XeDto.cs** - Complete DTO mapping to DM_Xe table with automotive-specific supporting DTOs
2. **XeRepository.cs** - 800+ lines with ALL legacy methods + modern API methods
3. **XeService.cs** - 650+ lines with comprehensive validation and automotive business rules
4. **XeController.cs** - 600+ lines with REST + legacy endpoints
5. **Service Registration** - Update Program.cs DI container

### Key Automotive Features Expected:
- Vehicle registration and identification
- Vehicle specifications (model, year, engine, etc.)
- Maintenance history integration
- Service scheduling capabilities
- Vehicle categorization and manufacturer linking
- VIN/chassis number management
- Mileage tracking
- Vehicle status management

### Success Criteria:
- 100% legacy compatibility with clsDMXe.cs
- All properties and methods implemented
- Automotive-specific business rules
- Comprehensive validation
- Modern API enhancements
- Successful build with minimal warnings

## Alternative Options (if clsDMXe is too complex):

If clsDMXe proves too complex, consider these alternatives in order:

1. **clsDMKho** (Warehouse/Storage) - Essential for inventory management
2. **clsDMHangHoa** (Products/Parts) - Essential for automotive parts
3. **clsDMNhanVien** (Employees) - Essential for service operations
4. **clsDMKhachHang** (Customers) - Customer management

## Implementation Command:

Use this exact prompt to start the next implementation:

```
I want you to implement clsDMXe (Individual Vehicles) next, as it uses LoaiXe, HangSanXuat, and potentially BaoDuong dependencies and represents actual vehicles, completing the automotive master data foundation. Let me examine the legacy clsDMXe.cs class to understand its structure.
```

This will trigger the proven implementation pattern that has successfully delivered 12 high-quality class implementations with 100% legacy compatibility.
