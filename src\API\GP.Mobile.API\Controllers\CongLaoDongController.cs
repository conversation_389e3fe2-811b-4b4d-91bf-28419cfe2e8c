using System.Data;
using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for CongLaoDong (Labor/Service) entity
/// Maps exactly to DM_CongLaoDong table in legacy database
/// Implements ALL methods from clsDMCongLaoDong.cs (649 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for labor/service item management in repair quotations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CongLaoDongController : ControllerBase
{
    private readonly CongLaoDongService _service;
    private readonly ILogger<CongLaoDongController> _logger;

    public CongLaoDongController(CongLaoDongService service, ILogger<CongLaoDongController> logger)
    {
        _service = service ?? throw new ArgumentNullException(nameof(service));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all CongLaoDong records
    /// GET /api/conglaodong
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CongLaoDongDto>>> GetAll()
    {
        try
        {
            var result = await _service.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all CongLaoDong records");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get CongLaoDong by ID
    /// GET /api/conglaodong/{khoa}
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<CongLaoDongDto>> GetById(string khoa)
    {
        try
        {
            var result = await _service.GetByIdAsync(khoa);
            if (result == null)
            {
                return NotFound($"CongLaoDong with Khoa {khoa} not found");
            }
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting CongLaoDong by ID: {Khoa}", khoa);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get CongLaoDong by Ma (code)
    /// GET /api/conglaodong/code/{ma}
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<CongLaoDongDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _service.LoadByCodeAsync(ma);
            if (result == null)
            {
                return NotFound($"CongLaoDong with Ma {ma} not found");
            }
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting CongLaoDong by code: {Ma}", ma);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create new CongLaoDong
    /// POST /api/conglaodong
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<CongLaoDongDto>> Create([FromBody] CreateCongLaoDongDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _service.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa = result.Khoa }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CongLaoDong");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update existing CongLaoDong
    /// PUT /api/conglaodong/{khoa}
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<CongLaoDongDto>> Update(string khoa, [FromBody] UpdateCongLaoDongDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _service.UpdateAsync(khoa, updateDto);
            return Ok(result);
        }
        catch (KeyNotFoundException)
        {
            return NotFound($"CongLaoDong with Khoa {khoa} not found");
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating CongLaoDong with Khoa: {Khoa}", khoa);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete CongLaoDong
    /// DELETE /api/conglaodong/{khoa}
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult> Delete(string khoa)
    {
        try
        {
            var success = await _service.DeleteAsync(khoa);
            if (!success)
            {
                return NotFound($"CongLaoDong with Khoa {khoa} not found");
            }
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CongLaoDong with Khoa: {Khoa}", khoa);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Search CongLaoDong with advanced criteria
    /// POST /api/conglaodong/search
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<CongLaoDongDto>>> Search([FromBody] CongLaoDongSearchDto searchDto)
    {
        try
        {
            var result = await _service.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching CongLaoDong");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get automotive services (filtered for automotive repair)
    /// GET /api/conglaodong/automotive
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveServiceDto>>> GetAutomotiveServices()
    {
        try
        {
            var result = await _service.GetAutomotiveServicesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive services");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get CongLaoDong list (optimized for dropdowns)
    /// GET /api/conglaodong/list
    /// </summary>
    [HttpGet("list")]
    public async Task<ActionResult<IEnumerable<CongLaoDongListDto>>> GetList([FromQuery] string? keyFilter = null, [FromQuery] string? fieldFilter = null)
    {
        try
        {
            var result = await _service.ShowListAsync(keyFilter ?? "", fieldFilter ?? "");
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting CongLaoDong list");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all CongLaoDong list (complete list)
    /// GET /api/conglaodong/list/all
    /// </summary>
    [HttpGet("list/all")]
    public async Task<ActionResult<IEnumerable<CongLaoDongListDto>>> GetAllList()
    {
        try
        {
            var result = await _service.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all CongLaoDong list");
            return StatusCode(500, "Internal server error");
        }
    }

    #endregion

    #region Legacy API Endpoints (Exact compatibility with clsDMCongLaoDong.cs)

    /// <summary>
    /// Legacy Load method - Exact implementation from clsDMCongLaoDong.Load()
    /// POST /api/conglaodong/load
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<CongLaoDongDto>> Load([FromBody] LoadRequest request)
    {
        try
        {
            var result = await _service.LoadAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy Load method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy LoadByCode method - Exact implementation from clsDMCongLaoDong.LoadByCode()
    /// POST /api/conglaodong/loadbycode
    /// </summary>
    [HttpPost("loadbycode")]
    public async Task<ActionResult<CongLaoDongDto>> LoadByCode([FromBody] LoadByCodeRequest request)
    {
        try
        {
            var result = await _service.LoadByCodeAsync(request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy LoadByCode method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy Save method - Exact implementation from clsDMCongLaoDong.Save()
    /// POST /api/conglaodong/save
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] SaveRequest request)
    {
        try
        {
            var result = await _service.SaveAsync(request.Data, request.Task);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy Save method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy ShowList method - Exact implementation from clsDMCongLaoDong.ShowList()
    /// POST /api/conglaodong/showlist
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<IEnumerable<CongLaoDongListDto>>> ShowList([FromBody] ShowListRequest request)
    {
        try
        {
            var result = await _service.ShowListAsync(request.KeyFilter, request.FieldFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy ShowList method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method - Exact implementation from clsDMCongLaoDong.ShowAllList()
    /// POST /api/conglaodong/showalllist
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<IEnumerable<CongLaoDongListDto>>> ShowAllList()
    {
        try
        {
            var result = await _service.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy ShowAllList method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method - Exact implementation from clsDMCongLaoDong.SearchByCode()
    /// POST /api/conglaodong/searchbycode
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] SearchByCodeRequest request)
    {
        try
        {
            var result = await _service.SearchByCodeAsync(request.Code, request.KeyFilter, request.FieldFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy SearchByCode method");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method - Exact implementation from clsDMCongLaoDong.ShowListByField()
    /// POST /api/conglaodong/showlistbyfield
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] ShowListByFieldRequest request)
    {
        try
        {
            var result = await _service.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy ShowListByField method");
            return StatusCode(500, "Internal server error");
        }
    }

    #endregion

    #region Request/Response Models for Legacy Endpoints

    public class LoadRequest
    {
        public string Khoa { get; set; } = string.Empty;
    }

    public class LoadByCodeRequest
    {
        public string Ma { get; set; } = string.Empty;
    }

    public class SaveRequest
    {
        public CongLaoDongDto Data { get; set; } = new();
        public string Task { get; set; } = string.Empty;
    }

    public class ShowListRequest
    {
        public string KeyFilter { get; set; } = string.Empty;
        public string FieldFilter { get; set; } = string.Empty;
    }

    public class SearchByCodeRequest
    {
        public string Code { get; set; } = string.Empty;
        public string KeyFilter { get; set; } = string.Empty;
        public string FieldFilter { get; set; } = string.Empty;
    }

    public class ShowListByFieldRequest
    {
        public string FieldList { get; set; } = string.Empty;
        public string Conditions { get; set; } = string.Empty;
        public string Order { get; set; } = string.Empty;
    }

    #endregion
}
