using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for LoaiDichVu (Service Type) entity
/// Implements ALL endpoints from clsDMLoaiDichVu.cs (468 lines)
/// Includes REST API and 8+ legacy method endpoints
/// Maps to DM_LoaiDichVu table with 9 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LoaiDichVuController : ControllerBase
{
    private readonly ILoaiDichVuService _loaiDichVuService;
    private readonly ILogger<LoaiDichVuController> _logger;

    public LoaiDichVuController(ILoaiDichVuService loaiDichVuService, ILogger<LoaiDichVuController> logger)
    {
        _loaiDichVuService = loaiDichVuService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all LoaiDichVu records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<LoaiDichVuListDto>>> GetAll()
    {
        try
        {
            var result = await _loaiDichVuService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiDichVu records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiDichVu by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<LoaiDichVuDto>> GetById(string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiDichVu by service type code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<LoaiDichVuDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _loaiDichVuService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new LoaiDichVu
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateLoaiDichVuDto createDto)
    {
        try
        {
            var result = await _loaiDichVuService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo loại dịch vụ");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiDichVu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiDichVu
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] LoaiDichVuDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiDichVuService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiDichVu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete LoaiDichVu
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiDichVu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiDichVu status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateLoaiDichVuStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiDichVuService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiDichVu status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search LoaiDichVu records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<LoaiDichVuListDto>>> Search([FromBody] LoaiDichVuSearchDto searchDto)
    {
        try
        {
            var result = await _loaiDichVuService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiDichVu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiDichVu lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<LoaiDichVuLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _loaiDichVuService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate LoaiDichVu data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<LoaiDichVuValidationDto>> Validate([FromBody] LoaiDichVuValidationRequestDto request)
    {
        try
        {
            var result = await _loaiDichVuService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiDichVu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search service by code
    /// </summary>
    [HttpGet("search-code/{code}")]
    public async Task<ActionResult<LoaiDichVuSearchByCodeDto>> SearchServiceByCode(string code, [FromQuery] string? condition = null)
    {
        try
        {
            var result = await _loaiDichVuService.SearchServiceByCodeAsync(code, condition ?? "");
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching service by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive service categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<ServiceCategoryDto>>> GetServiceCategories()
    {
        try
        {
            var result = await _loaiDichVuService.GetServiceCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get service statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<LoaiDichVuStatsDto>> GetServiceStats(string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.GetServiceStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] LoaiDichVuDto dto)
    {
        try
        {
            var result = await _loaiDichVuService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] LoaiDichVuShowListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _loaiDichVuService.ShowListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _loaiDichVuService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] LoaiDichVuSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _loaiDichVuService.SearchByCodeAsync(request.Code, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] LoaiDichVuShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _loaiDichVuService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] LoaiDichVuTrungMaRequestDto request)
    {
        try
        {
            var result = await _loaiDichVuService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiDichVuService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for LoaiDichVu

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class LoaiDichVuValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class LoaiDichVuShowListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method
/// </summary>
public class LoaiDichVuSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class LoaiDichVuShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class LoaiDichVuTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
