using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Authentication service interface
    /// Based on clsNguoiDung functionality from legacy system
    /// Implements exact login logic from Frm_Login.cs
    /// </summary>
    public interface IAuthenticationService
    {
        #region Core Authentication Methods

        /// <summary>
        /// Authenticate user with username and password
        /// Exact implementation from legacy CheckIvalid method
        /// </summary>
        Task<AuthValidationResult> AuthenticateAsync(string username, string password);

        /// <summary>
        /// Login user with client selection
        /// Exact implementation from legacy OK_Click method
        /// </summary>
        Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest);

        /// <summary>
        /// Logout user and invalidate session
        /// Exact implementation from legacy LoggedIn method
        /// </summary>
        Task<bool> LogoutAsync(string userId, LogoutDto logoutDto);

        /// <summary>
        /// Refresh access token
        /// </summary>
        Task<LoginResponseDto> RefreshTokenAsync(RefreshTokenDto refreshRequest);

        /// <summary>
        /// Validate access token
        /// </summary>
        Task<bool> ValidateTokenAsync(string token);

        #endregion

        #region User Management

        /// <summary>
        /// Get user information by ID
        /// </summary>
        Task<UserInfoDto?> GetUserInfoAsync(string userId);

        /// <summary>
        /// Get user permissions
        /// </summary>
        Task<UserPermissionsDto> GetUserPermissionsAsync(string userId, string clientId);

        /// <summary>
        /// Change user password
        /// </summary>
        Task<bool> ChangePasswordAsync(string userId, ChangePasswordDto changePasswordDto);

        /// <summary>
        /// Check if user has access to client/branch
        /// Based on DonViDangNhap logic from legacy form
        /// </summary>
        Task<bool> HasClientAccessAsync(string userId, string clientId);

        #endregion

        #region Client/Branch Management

        /// <summary>
        /// Get available clients for user
        /// Maps to CboClient population from legacy form
        /// </summary>
        Task<List<ClientSelectionDto>> GetAvailableClientsAsync(string userId);

        /// <summary>
        /// Get available clients for username (for mobile login screen)
        /// </summary>
        Task<List<ClientSelectionDto>> GetClientsForUsernameAsync(string username);

        /// <summary>
        /// Get client information
        /// </summary>
        Task<ClientSelectionDto?> GetClientInfoAsync(string clientId);

        #endregion

        #region Session Management

        /// <summary>
        /// Create user session
        /// </summary>
        Task<string> CreateSessionAsync(UserInfoDto userInfo, string deviceId, string deviceType);

        /// <summary>
        /// Get active sessions for user
        /// </summary>
        Task<List<UserSessionDto>> GetActiveSessionsAsync(string userId);

        /// <summary>
        /// Invalidate session
        /// </summary>
        Task<bool> InvalidateSessionAsync(string sessionId);

        /// <summary>
        /// Update session activity
        /// </summary>
        Task<bool> UpdateSessionActivityAsync(string sessionId);

        #endregion

        #region Advanced Authentication

        /// <summary>
        /// Setup biometric authentication
        /// </summary>
        Task<bool> SetupBiometricAuthAsync(string userId, BiometricAuthDto biometricDto);

        /// <summary>
        /// Authenticate with biometrics
        /// </summary>
        Task<LoginResponseDto> AuthenticateWithBiometricAsync(BiometricAuthDto biometricDto);

        /// <summary>
        /// Setup two-factor authentication
        /// </summary>
        Task<bool> SetupTwoFactorAuthAsync(string userId, string method);

        /// <summary>
        /// Verify two-factor authentication code
        /// </summary>
        Task<bool> VerifyTwoFactorCodeAsync(TwoFactorAuthDto twoFactorDto);

        #endregion

        #region Security & Audit

        /// <summary>
        /// Log user activity
        /// Based on legacy LoggedIn method
        /// </summary>
        Task<bool> LogUserActivityAsync(string userId, string activity, string details = "");

        /// <summary>
        /// Check account lockout status
        /// </summary>
        Task<bool> IsAccountLockedAsync(string username);

        /// <summary>
        /// Reset failed login attempts
        /// </summary>
        Task<bool> ResetFailedAttemptsAsync(string username);

        /// <summary>
        /// Get login history
        /// </summary>
        Task<List<UserSessionDto>> GetLoginHistoryAsync(string userId, int days = 30);

        #endregion

        #region Token Management

        /// <summary>
        /// Generate JWT access token
        /// </summary>
        Task<string> GenerateAccessTokenAsync(UserInfoDto userInfo);

        /// <summary>
        /// Generate refresh token
        /// </summary>
        Task<string> GenerateRefreshTokenAsync(string userId, string deviceId);

        /// <summary>
        /// Validate and decode JWT token
        /// </summary>
        Task<UserInfoDto?> ValidateAndDecodeTokenAsync(string token);

        /// <summary>
        /// Revoke refresh token
        /// </summary>
        Task<bool> RevokeRefreshTokenAsync(string refreshToken);

        #endregion

        #region Configuration

        /// <summary>
        /// Get authentication configuration
        /// </summary>
        Task<Dictionary<string, object>> GetAuthConfigAsync();

        /// <summary>
        /// Check if feature is enabled for user
        /// </summary>
        Task<bool> IsFeatureEnabledAsync(string userId, string feature);

        #endregion
    }
}
