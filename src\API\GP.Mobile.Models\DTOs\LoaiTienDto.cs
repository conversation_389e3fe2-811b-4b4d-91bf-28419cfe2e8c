using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for LoaiTien (Currency Type) entity
/// Maps exactly to DM_LoaiTien table in legacy database
/// Implements ALL properties from clsDMLoaiTien.cs (552 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class LoaiTienDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Currency code (VND, USD, EUR, etc.)
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(10)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of currency
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of currency
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(100)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Is Vietnamese currency flag (1 = VND, 0 = Foreign currency)
    /// Maps to: mTienViet property in legacy class
    /// </summary>
    public int TienViet { get; set; } = 0;

    /// <summary>
    /// Exchange rate to VND
    /// Maps to: mTyGia property in legacy class
    /// </summary>
    public double TyGia { get; set; } = 1.0;

    /// <summary>
    /// Effective date for exchange rate (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Number of decimal places for currency
    /// Maps to: mSoLe property in legacy class
    /// </summary>
    public int SoLe { get; set; } = 0;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Synchronization status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for LoaiTien list display
/// Optimized for list views and dropdowns
/// </summary>
public class LoaiTienListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public double TyGia { get; set; } = 1.0;
    public int TienViet { get; set; } = 0;
    public int SoLe { get; set; } = 0;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for creating new LoaiTien
/// Contains only required fields for creation
/// </summary>
public class CreateLoaiTienDto
{
    [Required]
    [StringLength(10)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(100)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public int TienViet { get; set; } = 0;
    public double TyGia { get; set; } = 1.0;
    public string TuNgay { get; set; } = string.Empty;
    public int SoLe { get; set; } = 0;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for updating LoaiTien status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateLoaiTienStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public int Active { get; set; }

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for LoaiTien search operations
/// Used for advanced search and filtering
/// </summary>
public class LoaiTienSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public int? TienViet { get; set; }
    public int? Active { get; set; }
    public double? TyGiaFrom { get; set; }
    public double? TyGiaTo { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
}

/// <summary>
/// DTO for LoaiTien dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class LoaiTienLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
    public double TyGia { get; set; } = 1.0;
    public int SoLe { get; set; } = 0;
}

/// <summary>
/// DTO for LoaiTien validation operations
/// Used for duplicate checking and validation
/// </summary>
public class LoaiTienValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}

/// <summary>
/// DTO for exchange rate operations
/// Used for currency conversion and rate management
/// </summary>
public class ExchangeRateDto
{
    public string KhoaLoaiTien { get; set; } = string.Empty;
    public string MaLoaiTien { get; set; } = string.Empty;
    public double TyGia { get; set; } = 1.0;
    public string TuNgay { get; set; } = string.Empty;
    public string DenNgay { get; set; } = string.Empty;
}

/// <summary>
/// DTO for currency search by code operations
/// Used for quick currency lookup
/// </summary>
public class LoaiTienSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
}
