using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for KhoanMucChiPhi (Cost Categories) entity
/// Implements ALL endpoints from clsDMKhoanMucChiPhi.cs (425 lines)
/// Includes REST API and 8+ legacy method endpoints
/// Maps to DM_KhoanMucChiPhi table with 8 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service cost management and expense categorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class KhoanMucChiPhiController : ControllerBase
{
    private readonly IKhoanMucChiPhiService _khoanMucChiPhiService;
    private readonly ILogger<KhoanMucChiPhiController> _logger;

    public KhoanMucChiPhiController(IKhoanMucChiPhiService khoanMucChiPhiService, ILogger<KhoanMucChiPhiController> logger)
    {
        _khoanMucChiPhiService = khoanMucChiPhiService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all cost categories
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<KhoanMucChiPhiListDto>>> GetAll()
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all cost categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost category by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<KhoanMucChiPhiDto>> GetById(string khoa)
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost category by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost category by code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<KhoanMucChiPhiDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost category by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost category by name
    /// </summary>
    [HttpGet("name/{tenViet}")]
    public async Task<ActionResult<KhoanMucChiPhiDto>> GetByName(string tenViet)
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetByNameAsync(tenViet);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost category by name");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new cost category
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateKhoanMucChiPhiDto createDto)
    {
        try
        {
            var result = await _khoanMucChiPhiService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo khoản mục chi phí");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating cost category");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update cost category
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] KhoanMucChiPhiDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _khoanMucChiPhiService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cost category");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete cost category
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _khoanMucChiPhiService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting cost category");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update cost category status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateKhoanMucChiPhiStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _khoanMucChiPhiService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cost category status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search cost categories
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<KhoanMucChiPhiListDto>>> Search([FromBody] KhoanMucChiPhiSearchDto searchDto)
    {
        try
        {
            var result = await _khoanMucChiPhiService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching cost categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost category lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<KhoanMucChiPhiLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost category lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate cost category data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<KhoanMucChiPhiValidationDto>> Validate([FromBody] KhoanMucChiPhiValidationRequestDto request)
    {
        try
        {
            var result = await _khoanMucChiPhiService.ValidateAsync(request.Khoa, request.Ma, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating cost category");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search cost category by code (modern)
    /// </summary>
    [HttpPost("search-by-code")]
    public async Task<ActionResult<KhoanMucChiPhiSearchByCodeDto>> SearchByCodeModern([FromBody] KhoanMucChiPhiSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _khoanMucChiPhiService.SearchByCodeModernAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching cost category by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive cost categories
    /// </summary>
    [HttpGet("automotive-categories")]
    public async Task<ActionResult<IEnumerable<AutomotiveCostCategoryDto>>> GetAutomotiveCostCategories()
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetAutomotiveCostCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive cost categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost categories with statistics
    /// </summary>
    [HttpGet("with-stats")]
    public async Task<ActionResult<IEnumerable<KhoanMucChiPhiWithStatsDto>>> GetCostCategoriesWithStats()
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetCostCategoriesWithStatsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost categories with stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get cost category report
    /// </summary>
    [HttpGet("report")]
    public async Task<ActionResult<IEnumerable<CostCategoryReportDto>>> GetCostCategoryReport([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _khoanMucChiPhiService.GetCostCategoryReportAsync(fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost category report");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _khoanMucChiPhiService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] KhoanMucChiPhiDto dto)
    {
        try
        {
            var result = await _khoanMucChiPhiService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _khoanMucChiPhiService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList()
    {
        try
        {
            var result = await _khoanMucChiPhiService.ShowListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _khoanMucChiPhiService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] KhoanMucChiPhiShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _khoanMucChiPhiService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] KhoanMucChiPhiSearchByCodeLegacyRequestDto request)
    {
        try
        {
            var result = await _khoanMucChiPhiService.SearchByCodeAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] KhoanMucChiPhiTrungMaRequestDto request)
    {
        try
        {
            var result = await _khoanMucChiPhiService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _khoanMucChiPhiService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for KhoanMucChiPhi

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class KhoanMucChiPhiValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for search by code (modern) endpoint
/// </summary>
public class KhoanMucChiPhiSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class KhoanMucChiPhiShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method (legacy)
/// </summary>
public class KhoanMucChiPhiSearchByCodeLegacyRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class KhoanMucChiPhiTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
