using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for DonViTinh (Unit of Measure) service
/// Defines business logic operations for DonViTinh entity
/// </summary>
public interface IDonViTinhService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadNameAsync(string tenViet);
    Task<bool> SaveAsync(DonViTinhDto dto, string action);
    Task<bool> DeleteAsync(string khoa);
    Task<DataTable> GetListAsync();
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<bool> IsDuplicateCodeAsync(string khoa, string tenViet);
    Task<bool> WasUsedAsync(string khoa);
    Task<string> SearchByNameAsync(string name);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<DonViTinhListDto>> GetAllAsync();
    Task<DonViTinhDto?> GetByIdAsync(string khoa);
    Task<string> CreateAsync(CreateDonViTinhDto createDto);
    Task<bool> UpdateAsync(DonViTinhDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateDonViTinhStatusDto statusDto);
    Task<IEnumerable<DonViTinhListDto>> SearchAsync(DonViTinhSearchDto searchDto);
    Task<IEnumerable<DonViTinhLookupDto>> GetLookupAsync(string language = "vi");
    Task<DonViTinhValidationDto> ValidateAsync(string khoa, string tenViet);
    
    #endregion
}

/// <summary>
/// Complete Service for DonViTinh entity
/// Implements ALL business logic from clsDMDonViTinh.cs (504 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class DonViTinhService : IDonViTinhService
{
    private readonly IDonViTinhRepository _repository;
    private readonly ILogger<DonViTinhService> _logger;

    public DonViTinhService(IDonViTinhRepository repository, ILogger<DonViTinhService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DonViTinh");
            throw;
        }
    }

    public async Task<bool> LoadNameAsync(string tenViet)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(tenViet))
            {
                throw new ArgumentException("Tên đơn vị tính không được để trống");
            }

            return await _repository.LoadNameAsync(tenViet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DonViTinh by name");
            throw;
        }
    }

    public async Task<bool> SaveAsync(DonViTinhDto dto, string action)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto, action);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto, action);

            return await _repository.SaveAsync(dto, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving DonViTinh");
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa đơn vị tính đã được sử dụng");
            }

            return await _repository.DeleteAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DonViTinh");
            throw;
        }
    }

    public async Task<DataTable> GetListAsync()
    {
        try
        {
            return await _repository.GetListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Apply security filters
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.ShowListAsync(secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing DonViTinh list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fields);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.GetListFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh list by field");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fieldList);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.ShowListByFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing DonViTinh list by field");
            return new DataTable();
        }
    }

    public async Task<bool> IsDuplicateCodeAsync(string khoa, string tenViet)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(tenViet))
            {
                return false;
            }

            return await _repository.IsDuplicateCodeAsync(khoa, tenViet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                return false;
            }

            return await _repository.WasUsedAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if DonViTinh was used");
            return true; // Return true to be safe
        }
    }

    public async Task<string> SearchByNameAsync(string name)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return "";
            }

            return await _repository.SearchByNameAsync(name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonViTinh by name");
            return "";
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<DonViTinhListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all DonViTinh records");
            return new List<DonViTinhListDto>();
        }
    }

    public async Task<DonViTinhDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh by ID");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateDonViTinhDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DonViTinh");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(DonViTinhDto dto)
    {
        return await SaveAsync(dto, "UPDATE");
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DeleteAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateDonViTinhStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonViTinh status");
            throw;
        }
    }

    public async Task<IEnumerable<DonViTinhListDto>> SearchAsync(DonViTinhSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonViTinh");
            return new List<DonViTinhListDto>();
        }
    }

    public async Task<IEnumerable<DonViTinhLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh lookup");
            return new List<DonViTinhLookupDto>();
        }
    }

    public async Task<DonViTinhValidationDto> ValidateAsync(string khoa, string tenViet)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, tenViet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating DonViTinh");
            return new DonViTinhValidationDto { Khoa = khoa, TenViet = tenViet };
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(DonViTinhDto dto, string action)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên đơn vị tính (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate names
        if (!string.IsNullOrEmpty(dto.TenViet))
        {
            var isDuplicate = await _repository.IsDuplicateCodeAsync(dto.Khoa, dto.TenViet);
            if (isDuplicate)
            {
                result.Errors.Add("Tên đơn vị tính đã tồn tại");
            }
        }

        // Length validation
        if (dto.TenViet.Length > 100)
            result.Errors.Add("Tên đơn vị tính (Tiếng Việt) không được vượt quá 100 ký tự");

        if (dto.TenAnh.Length > 100)
            result.Errors.Add("Tên đơn vị tính (Tiếng Anh) không được vượt quá 100 ký tự");

        if (dto.DienGiai.Length > 500)
            result.Errors.Add("Diễn giải không được vượt quá 500 ký tự");

        // Status validation
        if (dto.Active != 0 && dto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateDonViTinhDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên đơn vị tính (Tiếng Việt) không được để trống");

        // Check for duplicate names
        if (!string.IsNullOrEmpty(createDto.TenViet))
        {
            var isDuplicate = await _repository.IsDuplicateCodeAsync("", createDto.TenViet);
            if (isDuplicate)
            {
                result.Errors.Add("Tên đơn vị tính đã tồn tại");
            }
        }

        // Length validation
        if (createDto.TenViet.Length > 100)
            result.Errors.Add("Tên đơn vị tính (Tiếng Việt) không được vượt quá 100 ký tự");

        if (createDto.TenAnh.Length > 100)
            result.Errors.Add("Tên đơn vị tính (Tiếng Anh) không được vượt quá 100 ký tự");

        if (createDto.DienGiai.Length > 500)
            result.Errors.Add("Diễn giải không được vượt quá 500 ký tự");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the unit of measure is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateDonViTinhStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (statusDto.Active != 0 && statusDto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Business rule: Cannot deactivate if being used
        if (statusDto.Active == 0)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động đơn vị tính đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(DonViTinhDto dto, string action)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa) && action.ToUpper() == "INSERT")
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Set update timestamp
        dto.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");

        // Trim whitespace
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        if (action.ToUpper() == "INSERT")
        {
            dto.Send = 0; // Not synchronized yet
        }
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show active records for non-admin users
        // - Filter by user's department/branch

        return conditions;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "TenViet", "TenAnh", "DienGiai", "Active",
            "KhoaNhanVienCapNhat", "NgayCapNhat", "Send"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    #endregion
}
