using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for Xe (Individual Vehicles) repository
/// Defines ALL methods from clsDMXe.cs (1417 lines)
/// Includes both legacy methods and modern API methods
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
public interface IXeRepository
{
    #region Legacy Methods (Exact mapping from clsDMXe.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadBienSoAsync(string soXe);
    Task<bool> SaveAsync(XeDto dto);
    Task<bool> DelDataAsync(string khoa);
    Task ClearTempAsync(string khoaDoiTuong);
    
    // List and data retrieval methods
    Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");
    Task<string> GetCarInfomationFirstAsync(string khoaDoiTuong);
    Task<DataTable> GetCarListAsync(string khoaDoiTuong);
    Task<DataTable> GetInfDetailOfCarAsync(string strFieldList, string strFrom, string strConditions = "", string strOrder = "");
    Task<DataTable> GetPDHanBaoHiemAsync(string pCondition);
    Task<string> GetIDFromSoXeAsync(string pSoXe);
    Task<string> SearchSoXeAsync(string strSoXeFilter);
    Task<DataTable> GetXeBaoHiemAsync(string pCondition);
    Task<DataTable> GetLanSuaXeAsync(string pCondition);
    Task<DataTable> GetXeListToKhachHangAsync(string pKhoaKH);
    Task<DataTable> GetListTheoKhachHangAsync(string pKhoa);
    
    // Update methods
    Task<bool> UpdateNewInfoAsync(string pKhoaHangBH, string pSoBH, string pHanBH, double pSoKm, string pKhoa);
    Task<bool> UpdateKmAsync(double pSoKm, string pKhoa);
    Task<bool> UpdateNgayHetHanBHAsync(string pHanBH, string pKhoa);
    Task<bool> UpdateTinhTrangBaoHiemAsync(string pKhoa, XeDto dto);
    
    // Business logic methods
    Task<bool> CheckValidForDelAsync(string pKhoa);
    Task<bool> GomXeAsync(string pKhoaXeXoa, string pKhoaXeCanGom);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<XeListDto>> GetAllAsync();
    Task<XeDto?> GetByIdAsync(string khoa);
    Task<XeDto?> GetByLicensePlateAsync(string soXe);
    Task<string> CreateAsync(CreateXeDto createDto);
    Task<bool> UpdateAsync(XeDto dto);
    Task<bool> UpdateInfoAsync(UpdateXeInfoDto updateDto);
    Task<IEnumerable<XeListDto>> SearchAsync(XeSearchDto searchDto);
    Task<IEnumerable<XeLookupDto>> GetLookupAsync();
    Task<XeValidationDto> ValidateAsync(string khoa, string soXe, string soSuon, string soMay, string maVin);
    Task<IEnumerable<XeInsuranceDto>> GetInsuranceInfoAsync(string condition = "");
    Task<IEnumerable<XeMaintenanceDto>> GetMaintenanceHistoryAsync(string condition = "");
    Task<IEnumerable<CustomerVehicleDto>> GetCustomerVehiclesAsync(string khoaKhachHang);
    Task<IEnumerable<IndividualVehicleCategoryDto>> GetVehicleCategoriesAsync();
    Task<IEnumerable<XeWithDetailsDto>> GetVehiclesWithDetailsAsync();
    Task<XeStatsDto?> GetVehicleStatsAsync(string khoa);
    Task<bool> MergeVehiclesAsync(XeMergeDto mergeDto);
    Task<IEnumerable<XeRegistrationDto>> GetRegistrationStatusAsync();
    Task<IEnumerable<VehicleSpecificationDto>> GetVehicleSpecificationsAsync();
    
    #endregion
}

/// <summary>
/// Complete Repository for Xe entity
/// Implements ALL methods from clsDMXe.cs (1417 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
public class XeRepository : IXeRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<XeRepository> _logger;

    public XeRepository(IDbConnection connection, ILogger<XeRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 650)
            string commandText = "SELECT * FROM DM_Xe WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<XeDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Xe: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> LoadBienSoAsync(string soXe)
    {
        try
        {
            // Exact SQL from legacy LoadBienSo method (line 729)
            string commandText = "SELECT * FROM DM_Xe WHERE SoXeTimKiem = @SoXe";
            var result = await _connection.QueryFirstOrDefaultAsync<XeDto>(commandText, new { SoXe = soXe });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Xe by license plate: {SoXe}", soXe);
            return false;
        }
    }

    public async Task<bool> SaveAsync(XeDto dto)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 833)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@KhoaDoiTuong", dto.KhoaDoiTuong);
            parameters.Add("@SoXeTimKiem", dto.SoXeTimKiem);
            parameters.Add("@SoXe", dto.SoXe);
            parameters.Add("@KhoaLoaiXe", dto.KhoaLoaiXe);
            parameters.Add("@MaLuc", dto.MaLuc);
            parameters.Add("@DoiXe", dto.DoiXe);
            parameters.Add("@KhoaHangSanXuat", dto.KhoaHangSanXuat);
            parameters.Add("@SoKmHienTai", dto.SoKmHienTai);
            parameters.Add("@MauSon", dto.MauSon);
            parameters.Add("@SoSuon", dto.SoSuon);
            parameters.Add("@SoMay", dto.SoMay);
            parameters.Add("@MaVin", dto.MaVin);
            parameters.Add("@KhoaHangBaoHiem", dto.KhoaHangBaoHiem);
            parameters.Add("@SoBaoHiem", dto.SoBaoHiem);
            parameters.Add("@NgayBatDauBaoHiem", dto.NgayBatDauBaoHiem);
            parameters.Add("@NgayHetHanBaoHiem", dto.NgayHetHanBaoHiem);
            parameters.Add("@TongLanSuaChua", dto.TongLanSuaChua);
            parameters.Add("@Model", dto.Model);
            parameters.Add("@KhoaBaoGiaGanNhat", dto.KhoaBaoGiaGanNhat);
            parameters.Add("@NgayBaoGiaGanNhat", dto.NgayBaoGiaGanNhat);
            parameters.Add("@SoKMTruoc", dto.SoKMTruoc);
            parameters.Add("@TenTaiXe", dto.TenTaiXe);
            parameters.Add("@DienThoaiTaiXe", dto.DienThoaiTaiXe);
            parameters.Add("@NgayGiaoXe", dto.NgayGiaoXe);
            parameters.Add("@SoXeCu", dto.SoXeCu);
            parameters.Add("@GioHoatDong", dto.GioHoatDong);
            parameters.Add("@GioHoatDongTruoc", dto.GioHoatDongTruoc);
            parameters.Add("@MaNoiThat", dto.MaNoiThat);
            parameters.Add("@NoiLamViec", dto.NoiLamViec);
            parameters.Add("@NgayHetHanDangKiem", dto.NgayHetHanDangKiem);
            parameters.Add("@KhoaDonVi", dto.KhoaDonVi);
            parameters.Add("@SoCho", dto.SoCho);
            parameters.Add("@SoNoiBo", dto.SoNoiBo);
            parameters.Add("@KieuDongCo", dto.KieuDongCo);
            parameters.Add("@KhoaNhienLieu", dto.KhoaNhienLieu);
            parameters.Add("@KhoaHopSo", dto.KhoaHopSo);
            parameters.Add("@KichThuocLop", dto.KichThuocLop);
            parameters.Add("@IsXeChuyenDung", dto.IsXeChuyenDung);
            parameters.Add("@BinhDien", dto.BinhDien);

            var rowsAffected = await _connection.ExecuteAsync("sp_DM_Xe", parameters, commandType: CommandType.StoredProcedure);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Xe: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy DelData method (line 866)
            string commandText = "DELETE FROM DM_Xe WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Xe: {Khoa}", khoa);
            return false;
        }
    }

    public async Task ClearTempAsync(string khoaDoiTuong)
    {
        try
        {
            // Exact SQL from legacy ClearTemp method (line 850)
            string commandText = "Delete DM_XeTemp where KhoaDoiTuong=@KhoaDoiTuong";
            await _connection.ExecuteAsync(commandText, new { KhoaDoiTuong = khoaDoiTuong });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing temp data for customer: {KhoaDoiTuong}", khoaDoiTuong);
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        try
        {
            // Exact SQL from legacy ShowListByField method (line 883)
            string whereClause = "";
            string orderClause = "";
            
            strFieldList = strFieldList.Replace("|", ",");
            
            if (!string.IsNullOrWhiteSpace(strConditions))
            {
                whereClause = " WHERE " + strConditions;
            }
            
            if (!string.IsNullOrWhiteSpace(strOrder))
            {
                orderClause = " ORDER BY " + strOrder;
            }
            
            string commandText = $" SELECT {strFieldList}  FROM DM_Xe{whereClause}{orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Xe list by field");
            return new DataTable();
        }
    }

    public async Task<string> GetCarInfomationFirstAsync(string khoaDoiTuong)
    {
        try
        {
            // Exact SQL from legacy GetCarInfomationFirst method (line 929)
            string commandText = "select top 1 Khoa from DM_Xe where KhoaDoiTuong=@KhoaDoiTuong";
            var result = await _connection.QueryFirstOrDefaultAsync<string>(commandText, new { KhoaDoiTuong = khoaDoiTuong });
            return result ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting first car information");
            return "";
        }
    }

    public async Task<DataTable> GetCarListAsync(string khoaDoiTuong)
    {
        try
        {
            // Exact SQL from legacy GetCarList method (line 946)
            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string commandText = @"
                SELECT XE.Khoa,XE.KhoaDoiTuong,XE.KhoaLoaiXe,XE.KhoaHangSanXuat,XE.KhoaHangBaoHiem,XE.SoXe,LX.TenViet as LoaiXe ,SX.TenViet as HangSanXuat,XE.MauSon,XE.SoKmHienTai,XE.SoMay,XE.SoSuon,XE.Model as MaVin,XE.DoiXe, BH.TenViet as BaoHiem ,XE.SoBaoHiem, dbo.char2date(XE.NgayBatDauBaoHiem) as NgayBatDau, dbo.char2date(XE.NgayHetHanBaoHiem) as NgayKetThuc
                FROM DM_Xe XE
                LEFT JOIN DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                LEFT JOIN DM_DoiTuong BH on XE.KhoaHangBaoHiem=BH.Khoa
                LEFT JOIN DM_HangSanXuat SX on XE.KhoaHangSanXuat=SX.Khoa
                where KhoaDoiTuong=@KhoaDoiTuong";

            var result = await _connection.QueryAsync(commandText, new { KhoaDoiTuong = khoaDoiTuong });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting car list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetInfDetailOfCarAsync(string strFieldList, string strFrom, string strConditions = "", string strOrder = "")
    {
        try
        {
            // Exact SQL from legacy GetInfDetailOfCar method (line 969)
            string whereClause = "";
            string orderClause = "";

            strFieldList = strFieldList.Replace("|", ",");

            if (!string.IsNullOrWhiteSpace(strConditions))
            {
                whereClause = " WHERE 1=1 " + strConditions;
            }

            if (!string.IsNullOrWhiteSpace(strOrder))
            {
                orderClause = " ORDER BY " + strOrder;
            }

            string commandText = $" SELECT {strFieldList}  FROM {strFrom}{whereClause}{orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting car detail information");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetPDHanBaoHiemAsync(string pCondition)
    {
        try
        {
            // Exact SQL from legacy GetPDHanBaoHiem method (line 1017)
            string whereClause = " Where 1=1 " + pCondition;
            string commandText = @"
                Select XE.Khoa as KhoaXe,XE.SoXe,LX.TenViet as LoaiXe,DT.TenViet as KhachHang,DT.DiaChi,DT.DienThoai,isnull(XE.KhoaHangBaoHiem,'') as KhoaBaoHiem,isnull(BH.TenViet,'') as HangBaoHiem,dbo.char2date(XE.NgayBatDauBaoHiem) as NgayBD,dbo.char2date(XE.NgayHetHanBaoHiem) as NgayHH
                From DM_Xe XE
                left join DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                left join DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa
                left join DM_DoiTuong BH on XE.KhoaHangBaoHiem=BH.Khoa" + whereClause + " Order by XE.NgayHetHanBaoHiem ";

            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insurance expiry data");
            return new DataTable();
        }
    }

    public async Task<string> GetIDFromSoXeAsync(string pSoXe)
    {
        try
        {
            // Exact SQL from legacy GetIDFromSoXe method (line 1044)
            string commandText = "select Khoa from DM_Xe where rtrim(SoXe)=@SoXe";
            var result = await _connection.QueryFirstOrDefaultAsync<string>(commandText, new { SoXe = pSoXe });
            return result ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ID from license plate");
            return "";
        }
    }

    public async Task<string> SearchSoXeAsync(string strSoXeFilter)
    {
        try
        {
            // Exact SQL from legacy SearchSoXe method (line 1135)
            string commandText = "SELECT Khoa FROM DM_Xe WHERE Rtrim(SoXeTimKiem) = @SoXeFilter";
            var result = await _connection.QueryFirstOrDefaultAsync<string>(commandText, new { SoXeFilter = strSoXeFilter });
            return (result ?? "").Trim();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching license plate");
            return "";
        }
    }

    public async Task<DataTable> GetXeBaoHiemAsync(string pCondition)
    {
        try
        {
            // Exact SQL from legacy GetXeBaoHiem method (line 1146)
            string whereClause = " Where 1=1 " + pCondition;
            string commandText = @"
                Select XE.Khoa,XE.KhoaLoaiXe,isnull(XE.KhoaHangBaoHiem,''),XE.SoXe,LX.TenViet as LoaiXe,DT.TenViet as KhachHang,DT.DienThoai ,isnull(BH.TenViet,'') as HangBaoHiem,isnull(XE.SoBaoHiem,''),dbo.char2date(XE.NgayBatDauBaoHiem),dbo.char2date(XE.NgayHetHanBaoHiem)
                From DM_Xe XE
                left join DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                left join DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa
                left join DM_DoiTuong BH on XE.KhoaHangBaoHiem=BH.Khoa" + whereClause + " Order by XE.NgayHetHanBaoHiem ";

            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle insurance data");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetLanSuaXeAsync(string pCondition)
    {
        try
        {
            // Exact SQL from legacy GetLanSuaXe method (line 1174)
            string whereClause = " Where 1=1 " + pCondition;
            string commandText = @"
                Select XE.Khoa,XE.KhoaLoaiXe,XE.SoXe,LX.TenViet as LoaiXe,XE.SoKmHienTai,DT.TenViet as KhachHang,DT.DienThoai,DT.DiaChi,isnull(XE.TongLanSuaChua,0)
                From DM_Xe XE
                left join DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                left join DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa" + whereClause;

            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle repair history");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetXeListToKhachHangAsync(string pKhoaKH)
    {
        try
        {
            // Exact SQL from legacy GetXeListToKhachHang method (line 1227)
            string commandText = @"
                Select '' as Khoa,XE.Khoa as KhoaXe,0 as Chon,XE.SoXe,LX.TenViet as LoaiXe,XE.SoKMHienTai,0 as TienBaoDuong,0 as LanSuaChua,'' as DienGiai
                From DM_Xe XE
                Left join DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                Where KhoaDoiTuong=@KhoaKH";

            var result = await _connection.QueryAsync(commandText, new { KhoaKH = pKhoaKH });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list for customer");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListTheoKhachHangAsync(string pKhoa)
    {
        try
        {
            // Exact SQL from legacy GetListTheoKhachHang method (line 1247)
            string commandText = @"
                SELECT X.Khoa, X.SoXe, LX.TenViet As LoaiXe, X.DoiXe, X.SoMay, X.SoSuon, X.SoKmHienTai,X.TenTaiXe,X.DienThoaiTaiXe
                FROM DM_Xe X
                LEFT JOIN DM_LoaiXe LX ON X.KhoaLoaiXe = LX.Khoa
                WHERE X.KhoaDoiTuong = @Khoa";

            var result = await _connection.QueryAsync(commandText, new { Khoa = pKhoa });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list by customer");
            return new DataTable();
        }
    }

    public async Task<bool> UpdateNewInfoAsync(string pKhoaHangBH, string pSoBH, string pHanBH, double pSoKm, string pKhoa)
    {
        try
        {
            // Exact SQL from legacy UpdateNewInfo method (line 1060)
            string commandText = @"
                Update DM_Xe set KhoaHangBaoHiem=@KhoaHangBH,SoBaoHiem=@SoBH,NgayHetHanBaoHiem=@HanBH,SoKMHienTai=@SoKm
                Where Rtrim(Khoa)=@Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                KhoaHangBH = pKhoaHangBH,
                SoBH = pSoBH,
                HanBH = pHanBH,
                SoKm = pSoKm,
                Khoa = pKhoa
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle new info");
            return false;
        }
    }

    public async Task<bool> UpdateKmAsync(double pSoKm, string pKhoa)
    {
        try
        {
            // Exact SQL from legacy UpdateKm method (line 1090)
            string commandText = "Update DM_Xe set SoKMHienTai=@SoKm Where Rtrim(Khoa)=@Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { SoKm = pSoKm, Khoa = pKhoa });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle mileage");
            return false;
        }
    }

    public async Task<bool> UpdateNgayHetHanBHAsync(string pHanBH, string pKhoa)
    {
        try
        {
            // Exact SQL from legacy UpdateNgayHetHanBH method (line 1116)
            string commandText = "Update DM_Xe set NgayHetHanBaoHiem=@HanBH Where Rtrim(Khoa)=@Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { HanBH = pHanBH, Khoa = pKhoa });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating insurance expiry date");
            return false;
        }
    }

    public async Task<bool> UpdateTinhTrangBaoHiemAsync(string pKhoa, XeDto dto)
    {
        try
        {
            // Exact SQL from legacy UpdateTinhTrangBaoHiem method (line 1197)
            string commandText = @"
                Update DM_Xe set KhoaHangBaoHiem=@KhoaHangBaoHiem,SoBaoHiem=@SoBaoHiem,NgayBatDauBaoHiem=@NgayBatDauBaoHiem,NgayHetHanBaoHiem=@NgayHetHanBaoHiem
                Where Rtrim(Khoa)=@Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                dto.KhoaHangBaoHiem,
                dto.SoBaoHiem,
                dto.NgayBatDauBaoHiem,
                dto.NgayHetHanBaoHiem,
                Khoa = pKhoa
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating insurance status");
            return false;
        }
    }

    public async Task<bool> CheckValidForDelAsync(string pKhoa)
    {
        try
        {
            // Exact SQL from legacy CheckValidForDel method (line 1264)
            string commandText = "SELECT * FROM SC_BaoGia Where KhoaXe = @Khoa";
            var result = await _connection.QueryAsync(commandText, new { Khoa = pKhoa });
            return result.Any(); // Returns true if vehicle is used in quotations
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if vehicle can be deleted");
            return false;
        }
    }

    public async Task<bool> GomXeAsync(string pKhoaXeXoa, string pKhoaXeCanGom)
    {
        try
        {
            // Exact stored procedure from legacy GomXe method (line 1276)
            var parameters = new DynamicParameters();
            parameters.Add("@KhoaXeXoa", pKhoaXeXoa);
            parameters.Add("@KhoaXeGom", pKhoaXeCanGom);
            parameters.Add("@pError", dbType: DbType.Double, direction: ParameterDirection.Output);

            await _connection.ExecuteAsync("SC_sp_GomXe", parameters, commandType: CommandType.StoredProcedure);

            var errorCode = parameters.Get<double>("@pError");
            return errorCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging vehicles");
            return false;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<XeListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT XE.Khoa,XE.KhoaDoiTuong,XE.KhoaLoaiXe,XE.KhoaHangSanXuat,XE.KhoaHangBaoHiem,XE.SoXe,
                       LX.TenViet as LoaiXe,SX.TenViet as HangSanXuat,XE.MauSon,XE.SoKmHienTai,XE.SoMay,XE.SoSuon,
                       XE.Model,XE.DoiXe,BH.TenViet as BaoHiem,XE.SoBaoHiem,
                       dbo.char2date(XE.NgayBatDauBaoHiem) as NgayBatDau,dbo.char2date(XE.NgayHetHanBaoHiem) as NgayKetThuc,
                       DT.TenViet as KhachHang,DT.DienThoai,DT.DiaChi,isnull(XE.TongLanSuaChua,0) as TongLanSuaChua
                FROM DM_Xe XE
                LEFT JOIN DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                LEFT JOIN DM_DoiTuong BH on XE.KhoaHangBaoHiem=BH.Khoa
                LEFT JOIN DM_HangSanXuat SX on XE.KhoaHangSanXuat=SX.Khoa
                LEFT JOIN DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa
                ORDER BY XE.SoXe";

            return await _connection.QueryAsync<XeListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all vehicles");
            return new List<XeListDto>();
        }
    }

    public async Task<XeDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_Xe WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<XeDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<XeDto?> GetByLicensePlateAsync(string soXe)
    {
        try
        {
            string commandText = "SELECT * FROM DM_Xe WHERE SoXeTimKiem = @SoXe OR SoXe = @SoXe";
            return await _connection.QueryFirstOrDefaultAsync<XeDto>(commandText, new { SoXe = soXe });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by license plate: {SoXe}", soXe);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateXeDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new XeDto
            {
                Khoa = khoa,
                KhoaDoiTuong = createDto.KhoaDoiTuong,
                SoXe = createDto.SoXe,
                SoXeTimKiem = NormalizeLicensePlate(createDto.SoXe),
                KhoaLoaiXe = createDto.KhoaLoaiXe,
                KhoaHangSanXuat = createDto.KhoaHangSanXuat,
                DoiXe = createDto.DoiXe,
                Model = createDto.Model,
                MauSon = createDto.MauSon,
                SoSuon = createDto.SoSuon,
                SoMay = createDto.SoMay,
                MaVin = createDto.MaVin,
                SoKmHienTai = createDto.SoKmHienTai,
                TenTaiXe = createDto.TenTaiXe,
                DienThoaiTaiXe = createDto.DienThoaiTaiXe
            };

            var success = await SaveAsync(dto);
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(XeDto dto)
    {
        try
        {
            // Normalize license plate for search
            dto.SoXeTimKiem = NormalizeLicensePlate(dto.SoXe);
            return await SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateInfoAsync(UpdateXeInfoDto updateDto)
    {
        try
        {
            return await UpdateNewInfoAsync(
                updateDto.KhoaHangBaoHiem,
                updateDto.SoBaoHiem,
                updateDto.NgayHetHanBaoHiem,
                updateDto.SoKmHienTai,
                updateDto.Khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle info: {Khoa}", updateDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<XeListDto>> SearchAsync(XeSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.SoXe))
            {
                conditions.Add("(XE.SoXe LIKE @SoXe OR XE.SoXeTimKiem LIKE @SoXe)");
                parameters.Add("@SoXe", $"%{searchDto.SoXe}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaDoiTuong))
            {
                conditions.Add("XE.KhoaDoiTuong = @KhoaDoiTuong");
                parameters.Add("@KhoaDoiTuong", searchDto.KhoaDoiTuong);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaLoaiXe))
            {
                conditions.Add("XE.KhoaLoaiXe = @KhoaLoaiXe");
                parameters.Add("@KhoaLoaiXe", searchDto.KhoaLoaiXe);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaHangSanXuat))
            {
                conditions.Add("XE.KhoaHangSanXuat = @KhoaHangSanXuat");
                parameters.Add("@KhoaHangSanXuat", searchDto.KhoaHangSanXuat);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DoiXe))
            {
                conditions.Add("XE.DoiXe LIKE @DoiXe");
                parameters.Add("@DoiXe", $"%{searchDto.DoiXe}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.Model))
            {
                conditions.Add("XE.Model LIKE @Model");
                parameters.Add("@Model", $"%{searchDto.Model}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.SoSuon))
            {
                conditions.Add("XE.SoSuon LIKE @SoSuon");
                parameters.Add("@SoSuon", $"%{searchDto.SoSuon}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.SoMay))
            {
                conditions.Add("XE.SoMay LIKE @SoMay");
                parameters.Add("@SoMay", $"%{searchDto.SoMay}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.MaVin))
            {
                conditions.Add("XE.MaVin LIKE @MaVin");
                parameters.Add("@MaVin", $"%{searchDto.MaVin}%");
            }

            if (searchDto.SoKmFrom.HasValue)
            {
                conditions.Add("XE.SoKmHienTai >= @SoKmFrom");
                parameters.Add("@SoKmFrom", searchDto.SoKmFrom.Value);
            }

            if (searchDto.SoKmTo.HasValue)
            {
                conditions.Add("XE.SoKmHienTai <= @SoKmTo");
                parameters.Add("@SoKmTo", searchDto.SoKmTo.Value);
            }

            if (searchDto.IsXeChuyenDung.HasValue)
            {
                conditions.Add("XE.IsXeChuyenDung = @IsXeChuyenDung");
                parameters.Add("@IsXeChuyenDung", searchDto.IsXeChuyenDung.Value);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT XE.Khoa,XE.KhoaDoiTuong,XE.KhoaLoaiXe,XE.KhoaHangSanXuat,XE.KhoaHangBaoHiem,XE.SoXe,
                       LX.TenViet as LoaiXe,SX.TenViet as HangSanXuat,XE.MauSon,XE.SoKmHienTai,XE.SoMay,XE.SoSuon,
                       XE.Model,XE.DoiXe,BH.TenViet as BaoHiem,XE.SoBaoHiem,
                       dbo.char2date(XE.NgayBatDauBaoHiem) as NgayBatDau,dbo.char2date(XE.NgayHetHanBaoHiem) as NgayKetThuc,
                       DT.TenViet as KhachHang,DT.DienThoai,DT.DiaChi,isnull(XE.TongLanSuaChua,0) as TongLanSuaChua
                FROM DM_Xe XE
                LEFT JOIN DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                LEFT JOIN DM_DoiTuong BH on XE.KhoaHangBaoHiem=BH.Khoa
                LEFT JOIN DM_HangSanXuat SX on XE.KhoaHangSanXuat=SX.Khoa
                LEFT JOIN DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa
                {whereClause}
                ORDER BY XE.SoXe";

            return await _connection.QueryAsync<XeListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vehicles");
            return new List<XeListDto>();
        }
    }

    public async Task<IEnumerable<XeLookupDto>> GetLookupAsync()
    {
        try
        {
            string commandText = @"
                SELECT XE.Khoa,XE.SoXe,LX.TenViet as LoaiXe,SX.TenViet as HangSanXuat,XE.Model,XE.DoiXe,XE.SoKmHienTai,DT.TenViet as KhachHang
                FROM DM_Xe XE
                LEFT JOIN DM_LoaiXe LX on XE.KhoaLoaiXe=LX.Khoa
                LEFT JOIN DM_HangSanXuat SX on XE.KhoaHangSanXuat=SX.Khoa
                LEFT JOIN DM_DoiTuong DT on XE.KhoaDoiTuong=DT.Khoa
                ORDER BY XE.SoXe";

            return await _connection.QueryAsync<XeLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle lookup");
            return new List<XeLookupDto>();
        }
    }

    public async Task<XeValidationDto> ValidateAsync(string khoa, string soXe, string soSuon, string soMay, string maVin)
    {
        try
        {
            var result = new XeValidationDto
            {
                Khoa = khoa,
                SoXe = soXe,
                SoSuon = soSuon,
                SoMay = soMay,
                MaVin = maVin
            };

            // Check for duplicate license plate
            if (!string.IsNullOrEmpty(soXe))
            {
                string commandText = "SELECT COUNT(*) FROM DM_Xe WHERE (SoXe = @SoXe OR SoXeTimKiem = @SoXeTimKiem) AND Khoa != @Khoa";
                var count = await _connection.QuerySingleAsync<int>(commandText, new
                {
                    SoXe = soXe,
                    SoXeTimKiem = NormalizeLicensePlate(soXe),
                    Khoa = khoa ?? ""
                });
                result.IsDuplicateSoXe = count > 0;
            }

            // Check for duplicate chassis number
            if (!string.IsNullOrEmpty(soSuon))
            {
                string commandText = "SELECT COUNT(*) FROM DM_Xe WHERE SoSuon = @SoSuon AND Khoa != @Khoa";
                var count = await _connection.QuerySingleAsync<int>(commandText, new { SoSuon = soSuon, Khoa = khoa ?? "" });
                result.IsDuplicateSoSuon = count > 0;
            }

            // Check for duplicate engine number
            if (!string.IsNullOrEmpty(soMay))
            {
                string commandText = "SELECT COUNT(*) FROM DM_Xe WHERE SoMay = @SoMay AND Khoa != @Khoa";
                var count = await _connection.QuerySingleAsync<int>(commandText, new { SoMay = soMay, Khoa = khoa ?? "" });
                result.IsDuplicateSoMay = count > 0;
            }

            // Check for duplicate VIN
            if (!string.IsNullOrEmpty(maVin))
            {
                string commandText = "SELECT COUNT(*) FROM DM_Xe WHERE MaVin = @MaVin AND Khoa != @Khoa";
                var count = await _connection.QuerySingleAsync<int>(commandText, new { MaVin = maVin, Khoa = khoa ?? "" });
                result.IsDuplicateMaVin = count > 0;
            }

            // Check if used in quotations
            if (!string.IsNullOrEmpty(khoa))
            {
                result.IsUsedInQuotations = await CheckValidForDelAsync(khoa);
            }

            result.CanDelete = !result.IsUsedInQuotations;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating vehicle");
            return new XeValidationDto { Khoa = khoa, SoXe = soXe, SoSuon = soSuon, SoMay = soMay, MaVin = maVin };
        }
    }

    // Placeholder implementations for remaining methods (to be implemented based on business needs)
    public async Task<IEnumerable<XeInsuranceDto>> GetInsuranceInfoAsync(string condition = "")
    {
        try
        {
            var dataTable = await GetPDHanBaoHiemAsync(condition);
            var result = new List<XeInsuranceDto>();

            foreach (DataRow row in dataTable.Rows)
            {
                var insurance = new XeInsuranceDto
                {
                    KhoaXe = row["KhoaXe"]?.ToString() ?? "",
                    SoXe = row["SoXe"]?.ToString() ?? "",
                    LoaiXe = row["LoaiXe"]?.ToString() ?? "",
                    KhachHang = row["KhachHang"]?.ToString() ?? "",
                    DiaChi = row["DiaChi"]?.ToString() ?? "",
                    DienThoai = row["DienThoai"]?.ToString() ?? "",
                    KhoaBaoHiem = row["KhoaBaoHiem"]?.ToString() ?? "",
                    HangBaoHiem = row["HangBaoHiem"]?.ToString() ?? "",
                    NgayBD = row["NgayBD"] as DateTime?,
                    NgayHH = row["NgayHH"] as DateTime?
                };

                // Calculate expiry status
                if (insurance.NgayHH.HasValue)
                {
                    var daysToExpiry = (insurance.NgayHH.Value - DateTime.Now).Days;
                    insurance.DaysToExpiry = daysToExpiry;
                    insurance.IsExpired = daysToExpiry < 0;
                    insurance.IsExpiringSoon = daysToExpiry >= 0 && daysToExpiry <= 30;
                }

                result.Add(insurance);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insurance info");
            return new List<XeInsuranceDto>();
        }
    }

    public async Task<IEnumerable<XeMaintenanceDto>> GetMaintenanceHistoryAsync(string condition = "")
    {
        try
        {
            var dataTable = await GetLanSuaXeAsync(condition);
            var result = new List<XeMaintenanceDto>();

            foreach (DataRow row in dataTable.Rows)
            {
                result.Add(new XeMaintenanceDto
                {
                    Khoa = row["Khoa"]?.ToString() ?? "",
                    KhoaLoaiXe = row["KhoaLoaiXe"]?.ToString() ?? "",
                    SoXe = row["SoXe"]?.ToString() ?? "",
                    LoaiXe = row["LoaiXe"]?.ToString() ?? "",
                    SoKmHienTai = Convert.ToDouble(row["SoKmHienTai"] ?? 0),
                    KhachHang = row["KhachHang"]?.ToString() ?? "",
                    DienThoai = row["DienThoai"]?.ToString() ?? "",
                    DiaChi = row["DiaChi"]?.ToString() ?? "",
                    TongLanSuaChua = Convert.ToInt32(row["TongLanSuaChua"] ?? 0)
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance history");
            return new List<XeMaintenanceDto>();
        }
    }

    public async Task<IEnumerable<CustomerVehicleDto>> GetCustomerVehiclesAsync(string khoaKhachHang)
    {
        try
        {
            var dataTable = await GetListTheoKhachHangAsync(khoaKhachHang);
            var result = new List<CustomerVehicleDto>();

            foreach (DataRow row in dataTable.Rows)
            {
                result.Add(new CustomerVehicleDto
                {
                    Khoa = row["Khoa"]?.ToString() ?? "",
                    KhoaXe = row["Khoa"]?.ToString() ?? "",
                    SoXe = row["SoXe"]?.ToString() ?? "",
                    LoaiXe = row["LoaiXe"]?.ToString() ?? "",
                    DoiXe = row["DoiXe"]?.ToString() ?? "",
                    SoMay = row["SoMay"]?.ToString() ?? "",
                    SoSuon = row["SoSuon"]?.ToString() ?? "",
                    SoKMHienTai = Convert.ToDouble(row["SoKmHienTai"] ?? 0),
                    TenTaiXe = row["TenTaiXe"]?.ToString() ?? "",
                    DienThoaiTaiXe = row["DienThoaiTaiXe"]?.ToString() ?? ""
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer vehicles");
            return new List<CustomerVehicleDto>();
        }
    }

    // Placeholder implementations for remaining complex methods
    public async Task<IEnumerable<IndividualVehicleCategoryDto>> GetVehicleCategoriesAsync()
    {
        // TODO: Implement based on business requirements
        return new List<IndividualVehicleCategoryDto>();
    }

    public async Task<IEnumerable<XeWithDetailsDto>> GetVehiclesWithDetailsAsync()
    {
        // TODO: Implement based on business requirements
        return new List<XeWithDetailsDto>();
    }

    public async Task<XeStatsDto?> GetVehicleStatsAsync(string khoa)
    {
        // TODO: Implement based on business requirements
        return null;
    }

    public async Task<bool> MergeVehiclesAsync(XeMergeDto mergeDto)
    {
        try
        {
            return await GomXeAsync(mergeDto.KhoaXeXoa, mergeDto.KhoaXeCanGom);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging vehicles");
            return false;
        }
    }

    public async Task<IEnumerable<XeRegistrationDto>> GetRegistrationStatusAsync()
    {
        // TODO: Implement based on business requirements
        return new List<XeRegistrationDto>();
    }

    public async Task<IEnumerable<VehicleSpecificationDto>> GetVehicleSpecificationsAsync()
    {
        // TODO: Implement based on business requirements
        return new List<VehicleSpecificationDto>();
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    private string NormalizeLicensePlate(string soXe)
    {
        if (string.IsNullOrEmpty(soXe))
            return "";

        // Remove spaces, dashes, and convert to uppercase for search
        return soXe.Replace(" ", "").Replace("-", "").Replace(".", "").ToUpper().Trim();
    }

    #endregion
}
