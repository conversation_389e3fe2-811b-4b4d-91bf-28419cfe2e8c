using System.Data;
using GP.Mobile.Core.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Complete Service for CongLaoDong (Labor/Service) entity
/// Maps exactly to DM_CongLaoDong table in legacy database
/// Implements ALL methods from clsDMCongLaoDong.cs (649 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for labor/service item management in repair quotations
/// </summary>
public class CongLaoDongService
{
    private readonly CongLaoDongRepository _repository;
    private readonly ILogger<CongLaoDongService> _logger;

    public CongLaoDongService(CongLaoDongRepository repository, ILogger<CongLaoDongService> logger)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Legacy Methods (Exact implementation from clsDMCongLaoDong.cs)

    /// <summary>
    /// Legacy Load method - Exact implementation from clsDMCongLaoDong.Load()
    /// </summary>
    public async Task<CongLaoDongDto?> LoadAsync(string pKhoa)
    {
        try
        {
            _logger.LogInformation("Loading CongLaoDong with Khoa: {Khoa}", pKhoa);
            return await _repository.LoadAsync(pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading CongLaoDong with Khoa: {Khoa}", pKhoa);
            throw;
        }
    }

    /// <summary>
    /// Legacy LoadByCode method - Exact implementation from clsDMCongLaoDong.LoadByCode()
    /// </summary>
    public async Task<CongLaoDongDto?> LoadByCodeAsync(string pMa)
    {
        try
        {
            _logger.LogInformation("Loading CongLaoDong with Ma: {Ma}", pMa);
            return await _repository.LoadByCodeAsync(pMa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading CongLaoDong with Ma: {Ma}", pMa);
            throw;
        }
    }

    /// <summary>
    /// Legacy Save method - Exact implementation from clsDMCongLaoDong.Save()
    /// </summary>
    public async Task<bool> SaveAsync(CongLaoDongDto dto, string pTask)
    {
        try
        {
            _logger.LogInformation("Saving CongLaoDong with Khoa: {Khoa}, Task: {Task}", dto.Khoa, pTask);
            
            // Apply business rules before saving
            await ApplyBusinessRulesAsync(dto);
            
            // Validate data
            var validationResult = await ValidateForSaveAsync(dto);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            return await _repository.SaveAsync(dto, pTask);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving CongLaoDong with Khoa: {Khoa}, Task: {Task}", dto.Khoa, pTask);
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowList method - Exact implementation from clsDMCongLaoDong.ShowList()
    /// </summary>
    public async Task<IEnumerable<CongLaoDongListDto>> ShowListAsync(string strKeyFilter = "", string strFiledNameFilter = "")
    {
        try
        {
            _logger.LogInformation("Showing CongLaoDong list with KeyFilter: {KeyFilter}, FieldFilter: {FieldFilter}", strKeyFilter, strFiledNameFilter);
            return await _repository.ShowListAsync(strKeyFilter, strFiledNameFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing CongLaoDong list");
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowAllList method - Exact implementation from clsDMCongLaoDong.ShowAllList()
    /// </summary>
    public async Task<IEnumerable<CongLaoDongListDto>> ShowAllListAsync()
    {
        try
        {
            _logger.LogInformation("Showing all CongLaoDong list");
            return await _repository.ShowAllListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all CongLaoDong list");
            throw;
        }
    }

    /// <summary>
    /// Legacy SearchByCode method - Exact implementation from clsDMCongLaoDong.SearchByCode()
    /// </summary>
    public async Task<string> SearchByCodeAsync(string strCode = "", string strKeyFilter = "", string strFiledNameFilter = "")
    {
        try
        {
            _logger.LogInformation("Searching CongLaoDong by code: {Code}", strCode);
            return await _repository.SearchByCodeAsync(strCode, strKeyFilter, strFiledNameFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching CongLaoDong by code: {Code}", strCode);
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowListByField method - Exact implementation from clsDMCongLaoDong.ShowListByField()
    /// </summary>
    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        try
        {
            _logger.LogInformation("Showing CongLaoDong list by field: {Fields}", strFieldList);
            return await _repository.ShowListByFieldAsync(strFieldList, strConditions, strOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing CongLaoDong list by field");
            throw;
        }
    }

    #endregion

    #region Modern API Methods (Additional functionality for React Native app)

    /// <summary>
    /// Get all CongLaoDong records
    /// Modern API method for React Native app
    /// </summary>
    public async Task<IEnumerable<CongLaoDongDto>> GetAllAsync()
    {
        try
        {
            _logger.LogInformation("Getting all CongLaoDong records");
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all CongLaoDong records");
            throw;
        }
    }

    /// <summary>
    /// Get CongLaoDong by ID
    /// Modern API method for React Native app
    /// </summary>
    public async Task<CongLaoDongDto?> GetByIdAsync(string khoa)
    {
        return await LoadAsync(khoa);
    }

    /// <summary>
    /// Create new CongLaoDong
    /// Modern API method for React Native app
    /// </summary>
    public async Task<CongLaoDongDto> CreateAsync(CreateCongLaoDongDto createDto)
    {
        try
        {
            _logger.LogInformation("Creating new CongLaoDong with Ma: {Ma}", createDto.Ma);
            
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CongLaoDong with Ma: {Ma}", createDto.Ma);
            throw;
        }
    }

    /// <summary>
    /// Update existing CongLaoDong
    /// Modern API method for React Native app
    /// </summary>
    public async Task<CongLaoDongDto> UpdateAsync(string khoa, UpdateCongLaoDongDto updateDto)
    {
        try
        {
            _logger.LogInformation("Updating CongLaoDong with Khoa: {Khoa}", khoa);
            
            // Validate update data
            var validationResult = await ValidateForUpdateAsync(updateDto);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            return await _repository.UpdateAsync(khoa, updateDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating CongLaoDong with Khoa: {Khoa}", khoa);
            throw;
        }
    }

    /// <summary>
    /// Delete CongLaoDong
    /// Modern API method for React Native app
    /// </summary>
    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            _logger.LogInformation("Deleting CongLaoDong with Khoa: {Khoa}", khoa);
            return await _repository.DeleteAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CongLaoDong with Khoa: {Khoa}", khoa);
            throw;
        }
    }

    /// <summary>
    /// Search CongLaoDong with advanced criteria
    /// Modern API method for React Native app
    /// </summary>
    public async Task<IEnumerable<CongLaoDongDto>> SearchAsync(CongLaoDongSearchDto searchDto)
    {
        try
        {
            _logger.LogInformation("Searching CongLaoDong with criteria");
            
            // Build dynamic search conditions
            var conditions = new List<string>();
            var parameters = new Dictionary<string, object>();

            if (!string.IsNullOrWhiteSpace(searchDto.Ma))
            {
                conditions.Add("Ma LIKE @Ma");
                parameters["Ma"] = $"%{searchDto.Ma}%";
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenViet))
            {
                conditions.Add("TenViet LIKE @TenViet");
                parameters["TenViet"] = $"%{searchDto.TenViet}%";
            }

            if (searchDto.DonGiaFrom.HasValue)
            {
                conditions.Add("DonGia >= @DonGiaFrom");
                parameters["DonGiaFrom"] = searchDto.DonGiaFrom.Value;
            }

            if (searchDto.DonGiaTo.HasValue)
            {
                conditions.Add("DonGia <= @DonGiaTo");
                parameters["DonGiaTo"] = searchDto.DonGiaTo.Value;
            }

            if (searchDto.Active.HasValue)
            {
                conditions.Add("Active = @Active");
                parameters["Active"] = searchDto.Active.Value;
            }

            var whereClause = conditions.Any() ? string.Join(" AND ", conditions) : "1=1";
            var fieldList = "*";
            var orderBy = "Ma";

            var dataTable = await _repository.ShowListByFieldAsync(fieldList, whereClause, orderBy);
            
            // Convert DataTable to DTOs (simplified for now)
            var results = new List<CongLaoDongDto>();
            foreach (DataRow row in dataTable.Rows)
            {
                results.Add(new CongLaoDongDto
                {
                    Khoa = row["Khoa"].ToString() ?? "",
                    Ma = row["Ma"].ToString() ?? "",
                    TenViet = row["TenViet"].ToString() ?? "",
                    TenAnh = row["TenAnh"].ToString() ?? "",
                    DienGiai = row["DienGiai"].ToString() ?? "",
                    DonGia = Convert.ToDecimal(row["DonGia"] ?? 0),
                    ThoiGianXuLy = Convert.ToDecimal(row["ThoiGianXuLy"] ?? 0),
                    Active = Convert.ToInt32(row["Active"] ?? 1),
                    KhoaDonViTinh = row["KhoaDonViTinh"].ToString() ?? ""
                });
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching CongLaoDong");
            throw;
        }
    }

    /// <summary>
    /// Get automotive services (filtered for automotive repair)
    /// Modern API method for React Native app
    /// </summary>
    public async Task<IEnumerable<AutomotiveServiceDto>> GetAutomotiveServicesAsync()
    {
        try
        {
            _logger.LogInformation("Getting automotive services");
            
            var allServices = await GetAllAsync();
            var automotiveServices = allServices.Where(s => s.Active == 1).Select(s => new AutomotiveServiceDto
            {
                Khoa = s.Khoa,
                Ma = s.Ma,
                TenViet = s.TenViet,
                DienGiai = s.DienGiai,
                DonGia = s.DonGia,
                ThoiGianXuLy = s.ThoiGianXuLy,
                IsQuickService = s.ThoiGianXuLy < 1, // Less than 1 hour
                ServiceCategory = GetServiceCategory(s.Ma)
            });

            return automotiveServices;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive services");
            throw;
        }
    }

    #endregion

    #region Private Helper Methods

    private string GetServiceCategory(string ma)
    {
        // Categorize services based on Ma prefix (automotive specific)
        if (ma.StartsWith("RX")) return "Rửa xe";
        if (ma.StartsWith("SC")) return "Sửa chữa";
        if (ma.StartsWith("BD")) return "Bảo dưỡng";
        if (ma.StartsWith("KT")) return "Kiểm tra";
        if (ma.StartsWith("TH")) return "Thay thế";
        return "Khác";
    }

    private async Task ApplyBusinessRulesAsync(CongLaoDongDto dto)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa))
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim();
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        if (dto.Active == 0 && dto.Active != 1)
        {
            dto.Active = 1; // Default to active
        }

        await Task.CompletedTask; // Placeholder for future business rules
    }

    private async Task<ValidationResult> ValidateForSaveAsync(CongLaoDongDto dto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã công lao động không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên Việt không được để trống");

        // Business logic validation
        if (dto.DonGia < 0)
            result.Errors.Add("Đơn giá không được âm");

        if (dto.ThoiGianXuLy < 0)
            result.Errors.Add("Thời gian xử lý không được âm");

        // Length validation
        if (dto.Ma.Length > 20)
            result.Errors.Add("Mã không được vượt quá 20 ký tự");

        if (dto.TenViet.Length > 200)
            result.Errors.Add("Tên Việt không được vượt quá 200 ký tự");

        result.IsValid = !result.Errors.Any();
        return await Task.FromResult(result);
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateCongLaoDongDto dto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã công lao động không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên Việt không được để trống");

        // Check for duplicate Ma
        var existing = await LoadByCodeAsync(dto.Ma);
        if (existing != null)
            result.Errors.Add($"Mã '{dto.Ma}' đã tồn tại");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForUpdateAsync(UpdateCongLaoDongDto dto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã công lao động không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên Việt không được để trống");

        result.IsValid = !result.Errors.Any();
        return await Task.FromResult(result);
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    #endregion
}
