using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for KhoanMucSuaChua (Repair Categories) entity
/// Maps exactly to DM_KhoanMucSuaChua table in legacy database
/// Implements ALL properties from clsDMKhoanMucSuaChua.cs (646 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for repair category management in automotive service quotations
/// </summary>
public class KhoanMucSuaChuaDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Repair type key - Foreign key to repair type classification
    /// Maps to: mKhoaLoaiSuaChua property in legacy class
    /// </summary>
    public string KhoaLoaiSuaChua { get; set; } = string.Empty;

    /// <summary>
    /// Repair category code - Unique business identifier
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name - Primary display name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name - Secondary display name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Effective date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Last updated by employee key
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1=Active, 0=Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Sync status for data replication
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;

    /// <summary>
    /// Is consultation category flag
    /// Maps to: mIsCoVan property in legacy class
    /// </summary>
    public bool IsCoVan { get; set; } = false;

    /// <summary>
    /// Allow content editing flag
    /// Maps to: mIsChinhSuaNoiDung property in legacy class
    /// </summary>
    public bool IsChinhSuaNoiDung { get; set; } = false;
}

/// <summary>
/// DTO for creating new KhoanMucSuaChua
/// Contains only required fields for creation
/// </summary>
public class CreateKhoanMucSuaChuaDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaLoaiSuaChua { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public bool IsCoVan { get; set; } = false;
    public bool IsChinhSuaNoiDung { get; set; } = false;
}

/// <summary>
/// DTO for updating KhoanMucSuaChua
/// Contains all updatable fields
/// </summary>
public class UpdateKhoanMucSuaChuaDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaLoaiSuaChua { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public bool IsCoVan { get; set; } = false;
    public bool IsChinhSuaNoiDung { get; set; } = false;
}

/// <summary>
/// DTO for KhoanMucSuaChua list display
/// Optimized for list views with calculated fields
/// </summary>
public class KhoanMucSuaChuaListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaLoaiSuaChua { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public bool IsCoVan { get; set; } = false;
    public bool IsChinhSuaNoiDung { get; set; } = false;
    public string LoaiSuaChua { get; set; } = string.Empty; // Joined from related table
}

/// <summary>
/// DTO for KhoanMucSuaChua search operations
/// Contains search criteria and filters
/// </summary>
public class KhoanMucSuaChuaSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? KhoaLoaiSuaChua { get; set; }
    public int? Active { get; set; }
    public bool? IsCoVan { get; set; }
    public bool? IsChinhSuaNoiDung { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
    public string? TuNgay { get; set; }
}

/// <summary>
/// DTO for automotive repair category-specific view
/// Focused on automotive repair categories
/// </summary>
public class AutomotiveRepairCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public bool IsCoVan { get; set; } = false;
    public bool IsChinhSuaNoiDung { get; set; } = false;
    public string RepairType { get; set; } = string.Empty; // Derived from Ma prefix
    public string ServiceLevel { get; set; } = string.Empty; // Basic/Advanced based on IsCoVan
    public bool AllowCustomization { get; set; } = false; // Based on IsChinhSuaNoiDung
}

/// <summary>
/// DTO for repair category hierarchy
/// Shows parent-child relationships in repair categories
/// </summary>
public class RepairCategoryHierarchyDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string KhoaLoaiSuaChua { get; set; } = string.Empty;
    public string LoaiSuaChuaName { get; set; } = string.Empty;
    public int Level { get; set; } = 0; // Hierarchy level
    public bool HasChildren { get; set; } = false;
    public int ChildCount { get; set; } = 0;
}

/// <summary>
/// DTO for KhoanMucSuaChua dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class KhoanMucSuaChuaLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsCoVan { get; set; } = false;
    public bool IsChinhSuaNoiDung { get; set; } = false;
    public string RepairType { get; set; } = string.Empty;
    public string ServiceLevel { get; set; } = string.Empty;
}

/// <summary>
/// DTO for KhoanMucSuaChua validation operations
/// Used for duplicate checking and validation
/// </summary>
public class KhoanMucSuaChuaValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsDuplicateName { get; set; } = false;
    public bool IsUsedInQuotations { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
    public List<string> ValidationErrors { get; set; } = new List<string>();
    public List<string> ValidationWarnings { get; set; } = new List<string>();
}
