using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for NhapKho (Inventory Receiving) entity
/// Maps exactly to ST_NhapKho table in legacy database
/// Implements ALL 65+ properties from clsNhapKho.cs (2,243 lines)
/// This is a core inventory management class - handle with extreme care
/// Manual implementation based on exact legacy class analysis
/// </summary>
public class NhapKhoDto
{
    #region Core Identification Properties
    
    /// <summary>Primary key - Legacy field: mKhoa</summary>
    public string Khoa { get; set; } = string.Empty;
    
    /// <summary>Document number - Legacy field: mSoChungtu</summary>
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;
    
    /// <summary>Document date - Legacy field: mNgayChungTu</summary>
    public string NgayChungTu { get; set; } = string.Empty;
    
    /// <summary>Import type - Legacy field: mLoaiNhap</summary>
    public int LoaiNhap { get; set; } = 0;
    
    #endregion

    #region Import Type and Category
    
    /// <summary>Import type key - Legacy field: mKhoaLoaiNhap</summary>
    public string KhoaLoaiNhap { get; set; } = string.Empty;
    
    /// <summary>Category key - Legacy field: mKhoaKhoanMuc</summary>
    public string KhoaKhoanMuc { get; set; } = string.Empty;
    
    /// <summary>Import source - Legacy field: mNguonNhap</summary>
    public string NguonNhap { get; set; } = string.Empty;
    
    #endregion

    #region Purchase Order Information
    
    /// <summary>Purchase order key - Legacy field: mKhoaDonHang</summary>
    public string KhoaDonHang { get; set; } = string.Empty;
    
    /// <summary>Purchase order number - Legacy field: mSoDonHang</summary>
    public string SoDonHang { get; set; } = string.Empty;
    
    /// <summary>Purchase order date - Legacy field: mNgayDonHang</summary>
    public string NgayDonHang { get; set; } = string.Empty;
    
    #endregion

    #region Supplier Information
    
    /// <summary>Supplier key - Legacy field: mKhoaDoiTuong</summary>
    public string KhoaDoiTuong { get; set; } = string.Empty;
    
    /// <summary>Delivery person - Legacy field: mNguoiGiao</summary>
    public string NguoiGiao { get; set; } = string.Empty;
    
    /// <summary>Tax code - Legacy field: mMaSoThue</summary>
    public string MaSoThue { get; set; } = string.Empty;
    
    #endregion

    #region Currency and Exchange
    
    /// <summary>Currency type - Legacy field: mLoaiTien</summary>
    public string LoaiTien { get; set; } = string.Empty;
    
    /// <summary>Exchange rate - Legacy field: mTyGia</summary>
    public double TyGia { get; set; } = 0.0;
    
    #endregion

    #region Invoice Information
    
    /// <summary>Invoice type key - Legacy field: mKhoaLoaiHoaDon</summary>
    public string KhoaLoaiHoaDon { get; set; } = string.Empty;
    
    /// <summary>Tax rate - Legacy field: mThueSuat</summary>
    public int ThueSuat { get; set; } = 0;
    
    /// <summary>Invoice number - Legacy field: mSoHoaDon</summary>
    public string SoHoaDon { get; set; } = string.Empty;
    
    /// <summary>Invoice date - Legacy field: mNgayHoaDon</summary>
    public string NgayHoaDon { get; set; } = string.Empty;
    
    /// <summary>Invoice series - Legacy field: mSoSeri</summary>
    public string SoSeri { get; set; } = string.Empty;
    
    /// <summary>Invoice symbol - Legacy field: mKiHieuHoaDon</summary>
    public string KiHieuHoaDon { get; set; } = string.Empty;
    
    #endregion

    #region Import Declaration Information
    
    /// <summary>Declaration number - Legacy field: mSoToKhai</summary>
    public string SoToKhai { get; set; } = string.Empty;
    
    /// <summary>Declaration date - Legacy field: mNgayToKhai</summary>
    public string NgayToKhai { get; set; } = string.Empty;
    
    /// <summary>Import invoice number - Legacy field: mSoHoaDonNK</summary>
    public string SoHoaDonNK { get; set; } = string.Empty;
    
    /// <summary>Import invoice date - Legacy field: mNgayHoaDonNK</summary>
    public string NgayHoaDonNK { get; set; } = string.Empty;
    
    /// <summary>Import tax payment deadline - Legacy field: mHanNopThueNK</summary>
    public int HanNopThueNK { get; set; } = 0;
    
    /// <summary>Import tax payment date - Legacy field: mNgayNopThueNK</summary>
    public string NgayNopThueNK { get; set; } = string.Empty;
    
    /// <summary>Import tax allocation type - Legacy field: mLoaiPhanBoThueNK</summary>
    public string LoaiPhanBoThueNK { get; set; } = string.Empty;
    
    #endregion

    #region Financial Information - Foreign Currency
    
    /// <summary>Goods amount in foreign currency - Legacy field: mTienHangNT</summary>
    public double TienHangNT { get; set; } = 0.0;
    
    /// <summary>Discount amount in foreign currency - Legacy field: mTienChietKhauNT</summary>
    public double TienChietKhauNT { get; set; } = 0.0;
    
    /// <summary>Import fee in foreign currency - Legacy field: mTienPhiNhapKhauNT</summary>
    public double TienPhiNhapKhauNT { get; set; } = 0.0;
    
    /// <summary>Import tax in foreign currency - Legacy field: mTienThueNhapKhauNT</summary>
    public double TienThueNhapKhauNT { get; set; } = 0.0;
    
    /// <summary>VAT amount in foreign currency - Legacy field: mTienThueVATNT</summary>
    public double TienThueVATNT { get; set; } = 0.0;
    
    /// <summary>Other fees in foreign currency - Legacy field: mTienPhiNT</summary>
    public double TienPhiNT { get; set; } = 0.0;
    
    /// <summary>Paid amount in foreign currency - Legacy field: mDaThanhToanNT</summary>
    public double DaThanhToanNT { get; set; } = 0.0;
    
    #endregion

    #region Financial Information - Local Currency
    
    /// <summary>Goods amount in local currency - Legacy field: mTienHang</summary>
    public double TienHang { get; set; } = 0.0;
    
    /// <summary>Discount amount in local currency - Legacy field: mTienChietKhau</summary>
    public double TienChietKhau { get; set; } = 0.0;
    
    /// <summary>Import fee in local currency - Legacy field: mTienPhiNhapKhau</summary>
    public double TienPhiNhapKhau { get; set; } = 0.0;
    
    /// <summary>Import tax in local currency - Legacy field: mTienThueNhapKhau</summary>
    public double TienThueNhapKhau { get; set; } = 0.0;
    
    /// <summary>VAT amount in local currency - Legacy field: mTienThueVAT</summary>
    public double TienThueVAT { get; set; } = 0.0;
    
    /// <summary>Other fees in local currency - Legacy field: mTienPhi</summary>
    public double TienPhi { get; set; } = 0.0;
    
    /// <summary>Paid amount in local currency - Legacy field: mDaThanhToan</summary>
    public double DaThanhToan { get; set; } = 0.0;
    
    /// <summary>Import tax paid - Legacy field: mDaNopThueNK</summary>
    public double DaNopThueNK { get; set; } = 0.0;
    
    /// <summary>Tax paid - Legacy field: mDaNopThue</summary>
    public double DaNopThue { get; set; } = 0.0;
    
    /// <summary>Import fee amount - Legacy field: mTienPhiNhap</summary>
    public double TienPhiNhap { get; set; } = 0.0;
    
    /// <summary>Import fee tax - Legacy field: mTienThuePhiNhap</summary>
    public double TienThuePhiNhap { get; set; } = 0.0;
    
    #endregion

    #region Account Information
    
    /// <summary>Payment account key - Legacy field: mKhoaTKThanhToan</summary>
    public string KhoaTKThanhToan { get; set; } = string.Empty;
    
    /// <summary>Import tax account key - Legacy field: mKhoaTKThueNK</summary>
    public string KhoaTKThueNK { get; set; } = string.Empty;
    
    /// <summary>VAT debit account key - Legacy field: mKhoaTKNoThueVAT</summary>
    public string KhoaTKNoThueVAT { get; set; } = string.Empty;
    
    /// <summary>VAT credit account key - Legacy field: mKhoaTKCoThueVAT</summary>
    public string KhoaTKCoThueVAT { get; set; } = string.Empty;
    
    #endregion

    #region Payment Information
    
    /// <summary>Payment deadline - Legacy field: mHanThanhToan</summary>
    public int HanThanhToan { get; set; } = 0;
    
    /// <summary>Payment date - Legacy field: mNgayThanhToan</summary>
    public string NgayThanhToan { get; set; } = string.Empty;
    
    #endregion

    #region Description and Notes
    
    /// <summary>Description - Legacy field: mDienGiai</summary>
    public string DienGiai { get; set; } = string.Empty;
    
    #endregion

    #region System Information
    
    /// <summary>Creator staff key - Legacy field: mKhoaNhanVienTao</summary>
    public string KhoaNhanVienTao { get; set; } = string.Empty;
    
    /// <summary>Creation date - Legacy field: mNgayTao</summary>
    public string NgayTao { get; set; } = string.Empty;
    
    /// <summary>Updater staff key - Legacy field: mKhoaNhanVienSua</summary>
    public string KhoaNhanVienSua { get; set; } = string.Empty;
    
    /// <summary>Update date - Legacy field: mNgaySua</summary>
    public string NgaySua { get; set; } = string.Empty;
    
    /// <summary>Posted status - Legacy field: mGhiSo</summary>
    public int GhiSo { get; set; } = 0;
    
    /// <summary>Send status - Legacy field: mSend</summary>
    public int Send { get; set; } = 0;
    
    #endregion

    #region Organization Information
    
    /// <summary>Unit key - Legacy field: mKhoaDonVi</summary>
    public string KhoaDonVi { get; set; } = string.Empty;
    
    /// <summary>Department key - Legacy field: mKhoaBoPhan</summary>
    public string KhoaBoPhan { get; set; } = string.Empty;
    
    /// <summary>Product key - Legacy field: mKhoaSanPham</summary>
    public string KhoaSanPham { get; set; } = string.Empty;
    
    #endregion

    #region Contract Information
    
    /// <summary>Contract key - Legacy field: mKhoaHopDong</summary>
    public string KhoaHopDong { get; set; } = string.Empty;
    
    /// <summary>Contract number - Legacy field: mSoHopDong</summary>
    public string SoHopDong { get; set; } = string.Empty;
    
    #endregion

    #region Quotation Reference
    
    /// <summary>Quotation key - Legacy field: mKhoaBaoGia</summary>
    public string KhoaBaoGia { get; set; } = string.Empty;
    
    /// <summary>Quotation number - Legacy field: mSoBaoGia</summary>
    public string SoBaoGia { get; set; } = string.Empty;
    
    #endregion
}

/// <summary>
/// List DTO for NhapKho entity - Used for list operations and search results
/// Contains key properties for display in inventory receiving lists
/// </summary>
public class NhapKhoListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public string NgayChungTu { get; set; } = string.Empty;
    public string SoHoaDon { get; set; } = string.Empty;
    public string NhaCungCap { get; set; } = string.Empty;
    public double SoTienNT { get; set; } = 0.0;
    public double SoTien { get; set; } = 0.0;
    public string DienGiai { get; set; } = string.Empty;
    public int GhiSo { get; set; } = 0;
    public int LoaiNhap { get; set; } = 0;
    public string NguonNhap { get; set; } = string.Empty;
}

/// <summary>
/// Create DTO for NhapKho entity - Used for creating new inventory receiving documents
/// Excludes auto-generated fields like Khoa
/// </summary>
public class CreateNhapKhoDto
{
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;

    public string NgayChungTu { get; set; } = string.Empty;

    public int LoaiNhap { get; set; } = 0;

    public string KhoaLoaiNhap { get; set; } = string.Empty;

    [Required]
    public string KhoaDoiTuong { get; set; } = string.Empty;

    public string NguoiGiao { get; set; } = string.Empty;

    public string SoHoaDon { get; set; } = string.Empty;

    public string NgayHoaDon { get; set; } = string.Empty;

    public string SoSeri { get; set; } = string.Empty;

    public string NguonNhap { get; set; } = string.Empty;

    public string DienGiai { get; set; } = string.Empty;

    public string LoaiTien { get; set; } = "VND";

    public double TyGia { get; set; } = 1.0;

    public int ThueSuat { get; set; } = 10;
}

/// <summary>
/// Update status DTO for NhapKho entity - Used for posting and status changes
/// </summary>
public class UpdateNhapKhoStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int GhiSo { get; set; } = 0;

    public int Send { get; set; } = 0;

    public string NgayThanhToan { get; set; } = string.Empty;

    public double DaThanhToan { get; set; } = 0.0;

    public double DaThanhToanNT { get; set; } = 0.0;
}
