/**
 * Create Test User for GP Mobile Authentication Testing
 * Adds a test user with known credentials to the database
 */

const sql = require('mssql');
const crypto = require('crypto');

// Database configuration
const config = {
    server: 'DESKTOP-J990JBB',
    database: 'carsoft_giaphat',
    user: 'sa',
    password: 'cxM123654',
    options: {
        enableArithAbort: true,
        trustServerCertificate: true,
        encrypt: false
    }
};

// MD5 hash function
function computeMD5Hash(input) {
    return crypto.createHash('md5').update(input).digest('hex').toUpperCase();
}

async function createTestUser() {
    try {
        console.log('🔧 Creating Test User for GP Mobile Authentication');
        console.log('==================================================\n');
        
        const pool = await sql.connect(config);
        
        // Test user details
        const testUser = {
            khoaNhanVien: 'TEST001',
            tenDangNhap: 'testuser',
            password: 'test123',
            donViDangNhap: '0000000000|', // Access to main client
            khoaNhom: '0000000000',
            khoaDonVi: '0000000000'
        };
        
        const passwordHash = computeMD5Hash(testUser.password);
        
        console.log('👤 Test User Details:');
        console.log(`   Username: ${testUser.tenDangNhap}`);
        console.log(`   Password: ${testUser.password}`);
        console.log(`   Password Hash: ${passwordHash}`);
        console.log(`   Employee ID: ${testUser.khoaNhanVien}`);
        console.log(`   Allowed Clients: ${testUser.donViDangNhap}`);
        
        // First, get the actual client ID from DM_DonVi
        const clientResult = await pool.request().query(`
            SELECT TOP 1 Khoa FROM DM_DonVi ORDER BY Khoa
        `);

        let actualClientId = '0000000000';
        if (clientResult.recordset.length > 0) {
            actualClientId = clientResult.recordset[0].Khoa.trim();
            console.log(`📋 Found actual client ID: '${actualClientId}'`);
        }

        // Update test user with correct client ID
        testUser.donViDangNhap = actualClientId + '|';

        // Check if user already exists
        const existingUser = await pool.request()
            .input('username', sql.NVarChar, testUser.tenDangNhap)
            .query('SELECT COUNT(*) as count FROM HT_NguoiDung WHERE TenDangNhap = @username');

        if (existingUser.recordset[0].count > 0) {
            console.log('\n⚠️  Test user already exists. Updating password and permissions...');

            // Update existing user
            await pool.request()
                .input('username', sql.NVarChar, testUser.tenDangNhap)
                .input('password', sql.NVarChar, passwordHash)
                .input('donViDangNhap', sql.NVarChar, testUser.donViDangNhap)
                .query(`
                    UPDATE HT_NguoiDung
                    SET MatKhau = @password, DonViDangNhap = @donViDangNhap
                    WHERE TenDangNhap = @username
                `);
                
            console.log('✅ Test user updated successfully!');
        } else {
            console.log('\n🆕 Creating new test user...');
            
            // Insert new user
            await pool.request()
                .input('khoaNhanVien', sql.NVarChar, testUser.khoaNhanVien)
                .input('tenDangNhap', sql.NVarChar, testUser.tenDangNhap)
                .input('matKhau', sql.NVarChar, passwordHash)
                .input('donViDangNhap', sql.NVarChar, testUser.donViDangNhap)
                .input('khoaNhom', sql.NVarChar, testUser.khoaNhom)
                .input('khoaDonVi', sql.NVarChar, testUser.khoaDonVi)
                .query(`
                    INSERT INTO HT_NguoiDung (
                        KhoaNhanVien, TenDangNhap, MatKhau, DonViDangNhap,
                        KhoaNhom, KhoaDonVi, IsSuaGiaBG, IsHoanTatBG, IsXemGiaMua,
                        IsXemGiaBan, IsCapNhatGiaMuaBan, IsNoLimitSale, IsPhanViec,
                        IsXemBGAll, IsExportBGExcel, IsGanTheThanhVien, IsCheckXuatKho,
                        IsGioiHanBGTH, IsHuyHMDaXuatKho, IsAddImageCar, IsDuocDuyetDonHangMua,
                        SoPhieuGioiHanBGTH, DieuKienHoanTatBaoGia
                    ) VALUES (
                        @khoaNhanVien, @tenDangNhap, @matKhau, @donViDangNhap,
                        @khoaNhom, @khoaDonVi, 1, 1, 1,
                        1, 1, 1, 1,
                        1, 1, 1, 1,
                        0, 1, 1, 1,
                        0, ''
                    )
                `);
                
            console.log('✅ Test user created successfully!');
        }
        
        // Verify the user was created/updated
        const verifyUser = await pool.request()
            .input('username', sql.NVarChar, testUser.tenDangNhap)
            .query(`
                SELECT 
                    KhoaNhanVien, TenDangNhap, MatKhau, DonViDangNhap
                FROM HT_NguoiDung 
                WHERE TenDangNhap = @username
            `);
            
        if (verifyUser.recordset.length > 0) {
            const user = verifyUser.recordset[0];
            console.log('\n✅ User verification:');
            console.log(`   Username: ${user.TenDangNhap}`);
            console.log(`   Employee ID: ${user.KhoaNhanVien}`);
            console.log(`   Password Hash: ${user.MatKhau}`);
            console.log(`   Allowed Clients: ${user.DonViDangNhap}`);
            
            // Verify password hash matches
            if (user.MatKhau === passwordHash) {
                console.log('✅ Password hash matches!');
            } else {
                console.log('❌ Password hash mismatch!');
            }
        }
        
        await pool.close();
        
        console.log('\n🚀 READY FOR MOBILE APP TESTING!');
        console.log('================================');
        console.log('You can now test the mobile app with these credentials:');
        console.log(`Username: ${testUser.tenDangNhap}`);
        console.log(`Password: ${testUser.password}`);
        console.log('Client: Select "Trung Tâm" from the dropdown');
        
        return testUser;
        
    } catch (error) {
        console.error('❌ Error creating test user:', error.message);
        return null;
    }
}

async function testMasterPassword() {
    try {
        console.log('\n🔑 Testing Master Password Discovery');
        console.log('===================================\n');
        
        // Let's try some more specific patterns for the master password
        const masterCandidates = [
            // Vietnamese automotive company patterns
            'giaphat', 'gia-phat', 'gia_phat', 'GIAPHAT', 'GIA-PHAT', 'GIA_PHAT',
            'carsoft', 'car-soft', 'car_soft', 'CARSOFT', 'CAR-SOFT', 'CAR_SOFT',
            
            // Company + year patterns
            'giaphat2024', 'giaphat2023', 'giaphat2022', 'giaphat2021',
            'carsoft2024', 'carsoft2023', 'carsoft2022', 'carsoft2021',
            
            // Admin patterns
            'administrator', 'ADMINISTRATOR', 'Administrator',
            'admin2024', 'admin2023', 'admin2022', 'admin2021',
            'superuser', 'SUPERUSER', 'SuperUser',
            
            // Database patterns
            'sa123', 'sa2024', 'sa2023', 'sqlserver', 'mssql',
            'database', 'db123', 'dbadmin',
            
            // Vietnamese patterns
            'quanly', 'quantri', 'hethong', 'matkhau', 'baomat',
            'QUANLY', 'QUANTRI', 'HETHONG', 'MATKHAU', 'BAOMAT',
            
            // Special characters
            'admin@123', 'admin#123', 'admin$123', 'admin!123',
            'giaphat@123', 'carsoft@123',
            
            // Longer patterns
            'giaphatcarsoft', 'carsoftgiaphat', 'gp-carsoft', 'carsoft-gp',
            
            // Date patterns
            '20240617', '20231231', '20230101', '01012024', '31122023',
            
            // Phone/ID patterns
            '0123456789', '1234567890', '0987654321',
            
            // Simple but longer
            'abcdef', 'ABCDEF', 'abcdefg', 'ABCDEFG',
            'qwerty123', 'QWERTY123', 'asdfgh123', 'ASDFGH123'
        ];
        
        const masterHash = '8F866D5EA7686D4458E39AEEF07DEC1A';
        
        console.log(`🎯 Testing ${masterCandidates.length} master password candidates...`);
        
        for (const candidate of masterCandidates) {
            const hash = computeMD5Hash(candidate);
            if (hash === masterHash) {
                console.log(`\n🎉 MASTER PASSWORD FOUND: '${candidate}'`);
                console.log(`🔑 Hash: ${hash}`);
                return candidate;
            }
        }
        
        console.log('❌ Master password not found in extended candidate list');
        return null;
        
    } catch (error) {
        console.error('❌ Error testing master password:', error.message);
        return null;
    }
}

async function main() {
    console.log('🚀 GP Mobile Authentication Setup');
    console.log('=================================\n');
    
    // First try to find the master password
    const masterPassword = await testMasterPassword();
    
    if (masterPassword) {
        console.log('\n✅ Master password found! You can use this to login as any user.');
        console.log(`Master Password: '${masterPassword}'`);
    }
    
    // Create test user regardless
    const testUser = await createTestUser();
    
    if (testUser) {
        console.log('\n🎯 TESTING OPTIONS:');
        console.log('==================');
        
        if (masterPassword) {
            console.log(`1. Use master password: '${masterPassword}' with any username`);
        }
        
        console.log(`2. Use test user: '${testUser.tenDangNhap}' / '${testUser.password}'`);
        console.log('3. Try existing users with master password (if found)');
    }
}

// Run the setup
main().catch(console.error);
