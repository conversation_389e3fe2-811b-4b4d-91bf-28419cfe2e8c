# GP Mobile Development Stop Script
# This script stops all development services

Write-Host "🛑 Stopping GP Mobile Development Environment..." -ForegroundColor Red
Write-Host ""

# Function to kill processes on specific ports
function Stop-ProcessOnPort {
    param([int]$Port)
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
        foreach ($processId in $processes) {
            if ($processId -and $processId -ne 0) {
                Write-Host "🔄 Stopping process on port $Port (PID: $processId)..." -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 1
            }
        }
        Write-Host "✅ Port $Port cleared" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  No process found on port $Port" -ForegroundColor Gray
    }
}

# Stop all development services
Write-Host "🔄 Stopping API Backend (ports 51551, 51552)..." -ForegroundColor Cyan
Stop-ProcessOnPort 51552
Stop-ProcessOnPort 51551

Write-Host ""
Write-Host "🔄 Stopping Frontend (ports 3000, 3001)..." -ForegroundColor Cyan
Stop-ProcessOnPort 3000
Stop-ProcessOnPort 3001

# Also kill any dotnet and node processes related to the project
Write-Host ""
Write-Host "🔄 Stopping related processes..." -ForegroundColor Cyan

try {
    # Stop dotnet processes running GP.Mobile.API
    $dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object {
        $_.CommandLine -like "*GP.Mobile.API*"
    }
    foreach ($process in $dotnetProcesses) {
        Write-Host "🔄 Stopping dotnet process (PID: $($process.Id))..." -ForegroundColor Yellow
        Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
    }
} catch {
    Write-Host "⚠️  Could not stop dotnet processes" -ForegroundColor Gray
}

try {
    # Stop node processes running in gp-web-frontend
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object {
        $_.CommandLine -like "*gp-web-frontend*" -or $_.CommandLine -like "*next*"
    }
    foreach ($process in $nodeProcesses) {
        Write-Host "🔄 Stopping node process (PID: $($process.Id))..." -ForegroundColor Yellow
        Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
    }
} catch {
    Write-Host "⚠️  Could not stop node processes" -ForegroundColor Gray
}

Write-Host ""
Write-Host "✅ All development services stopped!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 To start services again, run: .\start-dev.ps1" -ForegroundColor Cyan
