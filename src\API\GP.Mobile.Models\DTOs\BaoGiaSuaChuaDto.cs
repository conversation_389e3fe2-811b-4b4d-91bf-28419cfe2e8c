using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoGiaSuaChua (Service Quotation Repair) entity
/// Maps exactly to SC_BaoGia table in legacy database
/// Implements ALL properties from clsBaoGiaSuaChua.cs (1,884 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive repair quotation management and service pricing
/// </summary>
public class BaoGiaSuaChuaDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle reception reference
    /// Maps to: mKhoaTiepNhanXe property in legacy class
    /// </summary>
    public string KhoaTiepNhanXe { get; set; } = string.Empty;

    /// <summary>
    /// Document number
    /// Maps to: mSoChungTu property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;

    /// <summary>
    /// Document date (YYYYMMDD format)
    /// Maps to: mNgayChungTu property in legacy class
    /// </summary>
    [Required]
    [StringLength(8)]
    public string NgayChungTu { get; set; } = string.Empty;

    /// <summary>
    /// Employee who created the record
    /// Maps to: mKhoaNhanVienTao property in legacy class
    /// </summary>
    public string KhoaNhanVienTao { get; set; } = string.Empty;

    /// <summary>
    /// Creation date (YYYYMMDD format)
    /// Maps to: mNgayTao property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayTao { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last modified the record
    /// Maps to: mKhoaNhanVienSua property in legacy class
    /// </summary>
    public string KhoaNhanVienSua { get; set; } = string.Empty;

    /// <summary>
    /// Last modification date (YYYYMMDD format)
    /// Maps to: mNgaySua property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgaySua { get; set; } = string.Empty;

    /// <summary>
    /// Employee who approved the record
    /// Maps to: mKhoaNhanVienDuyet property in legacy class
    /// </summary>
    public string KhoaNhanVienDuyet { get; set; } = string.Empty;

    /// <summary>
    /// Approval date (YYYYMMDD format)
    /// Maps to: mNgayDuyet property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayDuyet { get; set; } = string.Empty;

    /// <summary>
    /// Classification (0=Cash, 1=Insurance)
    /// Maps to: mPhanLoai property in legacy class
    /// </summary>
    public int PhanLoai { get; set; } = 0;

    /// <summary>
    /// Repair department (0=Body+Paint, 1=Engine)
    /// Maps to: mBoPhanSuaChua property in legacy class
    /// </summary>
    public int BoPhanSuaChua { get; set; } = 0;

    /// <summary>
    /// Quotation status (0=Draft, 1=Approved)
    /// Maps to: mTinhTrangBaoGia property in legacy class
    /// </summary>
    public int TinhTrangBaoGia { get; set; } = 0;

    /// <summary>
    /// Customer reference
    /// Maps to: mKhoaKhachHang property in legacy class
    /// </summary>
    public string KhoaKhachHang { get; set; } = string.Empty;

    /// <summary>
    /// Customer name
    /// Maps to: mKhachHang property in legacy class
    /// </summary>
    [StringLength(200)]
    public string KhachHang { get; set; } = string.Empty;

    /// <summary>
    /// Customer address
    /// Maps to: mDiaChi property in legacy class
    /// </summary>
    [StringLength(300)]
    public string DiaChi { get; set; } = string.Empty;

    /// <summary>
    /// Customer phone number
    /// Maps to: mDienThoai property in legacy class
    /// </summary>
    [StringLength(50)]
    public string DienThoai { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle reference
    /// Maps to: mKhoaXe property in legacy class
    /// </summary>
    public string KhoaXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle license plate
    /// Maps to: mBienSoXe property in legacy class
    /// </summary>
    [StringLength(20)]
    public string BienSoXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle type reference
    /// Maps to: mKhoaLoaiXe property in legacy class
    /// </summary>
    public string KhoaLoaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle type name
    /// Maps to: mLoaiXe property in legacy class
    /// </summary>
    [StringLength(100)]
    public string LoaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Workshop entry date (YYYYMMDD format)
    /// Maps to: mNgayVaoXuong property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayVaoXuong { get; set; } = string.Empty;

    /// <summary>
    /// Workshop exit date (YYYYMMDD format)
    /// Maps to: mNgayRaXuong property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayRaXuong { get; set; } = string.Empty;

    /// <summary>
    /// Insurance company reference
    /// Maps to: mKhoaBaoHiem property in legacy class
    /// </summary>
    public string KhoaBaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Insurance company name
    /// Maps to: mBaoHiem property in legacy class
    /// </summary>
    [StringLength(200)]
    public string BaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Insurance contact person
    /// Maps to: mLienHe property in legacy class
    /// </summary>
    [StringLength(100)]
    public string LienHe { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Number of days to complete
    /// Maps to: mSoNgayHoanThanh property in legacy class
    /// </summary>
    public int SoNgayHoanThanh { get; set; } = 0;

    /// <summary>
    /// Completion date (YYYYMMDD format)
    /// Maps to: mNgayHoanThanh property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayHoanThanh { get; set; } = string.Empty;

    /// <summary>
    /// Repair service amount
    /// Maps to: mTienSuaChua property in legacy class
    /// </summary>
    public decimal TienSuaChua { get; set; } = 0;

    /// <summary>
    /// Parts amount
    /// Maps to: mTienPhuTung property in legacy class
    /// </summary>
    public decimal TienPhuTung { get; set; } = 0;

    /// <summary>
    /// Tax percentage
    /// Maps to: mTyLeThue property in legacy class
    /// </summary>
    public int TyLeThue { get; set; } = 0;

    /// <summary>
    /// Tax amount 1
    /// Maps to: mTienThue1 property in legacy class
    /// </summary>
    public decimal TienThue1 { get; set; } = 0;

    /// <summary>
    /// Tax amount 2
    /// Maps to: mTienThue2 property in legacy class
    /// </summary>
    public decimal TienThue2 { get; set; } = 0;

    /// <summary>
    /// Tax amount 3
    /// Maps to: mTienThue3 property in legacy class
    /// </summary>
    public decimal TienThue3 { get; set; } = 0;

    /// <summary>
    /// Total amount 1
    /// Maps to: mTongTienHang1 property in legacy class
    /// </summary>
    public decimal TongTienHang1 { get; set; } = 0;

    /// <summary>
    /// Total amount 2
    /// Maps to: mTongTienHang2 property in legacy class
    /// </summary>
    public decimal TongTienHang2 { get; set; } = 0;

    /// <summary>
    /// Total amount 3
    /// Maps to: mTongTienHang3 property in legacy class
    /// </summary>
    public decimal TongTienHang3 { get; set; } = 0;

    /// <summary>
    /// Amount received
    /// Maps to: mDaThuTienHang property in legacy class
    /// </summary>
    public decimal DaThuTienHang { get; set; } = 0;

    /// <summary>
    /// Subcontractor amount
    /// Maps to: mTienKe property in legacy class
    /// </summary>
    public decimal TienKe { get; set; } = 0;

    /// <summary>
    /// Subcontractor amount paid
    /// Maps to: mDaTraTienKe property in legacy class
    /// </summary>
    public decimal DaTraTienKe { get; set; } = 0;

    /// <summary>
    /// Commission amount
    /// Maps to: mTienHoaHong property in legacy class
    /// </summary>
    public decimal TienHoaHong { get; set; } = 0;

    /// <summary>
    /// Commission amount paid
    /// Maps to: mDaTraHoaHong property in legacy class
    /// </summary>
    public decimal DaTraHoaHong { get; set; } = 0;

    /// <summary>
    /// Deductible amount
    /// Maps to: mTienCheTai property in legacy class
    /// </summary>
    public decimal TienCheTai { get; set; } = 0;

    /// <summary>
    /// Deductible amount received
    /// Maps to: mDaThuCheTai property in legacy class
    /// </summary>
    public decimal DaThuCheTai { get; set; } = 0;

    /// <summary>
    /// Discount percentage
    /// Maps to: mTyLeChietKhau property in legacy class
    /// </summary>
    public int TyLeChietKhau { get; set; } = 0;

    /// <summary>
    /// Discount amount 1
    /// Maps to: mTienChietKhau1 property in legacy class
    /// </summary>
    public decimal TienChietKhau1 { get; set; } = 0;

    /// <summary>
    /// Discount amount 2
    /// Maps to: mTienChietKhau2 property in legacy class
    /// </summary>
    public decimal TienChietKhau2 { get; set; } = 0;

    /// <summary>
    /// Discount amount 3
    /// Maps to: mTienChietKhau3 property in legacy class
    /// </summary>
    public decimal TienChietKhau3 { get; set; } = 0;

    /// <summary>
    /// Invoice type reference
    /// Maps to: mKhoaLoaiHoaDon property in legacy class
    /// </summary>
    public string KhoaLoaiHoaDon { get; set; } = string.Empty;

    /// <summary>
    /// Tax entity name
    /// Maps to: mTenDoiTuongThue property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenDoiTuongThue { get; set; } = string.Empty;

    /// <summary>
    /// Tax ID number
    /// Maps to: mMaSoThue property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaSoThue { get; set; } = string.Empty;

    /// <summary>
    /// Tax address
    /// Maps to: mDiaChiThue property in legacy class
    /// </summary>
    [StringLength(300)]
    public string DiaChiThue { get; set; } = string.Empty;

    /// <summary>
    /// Invoice series
    /// Maps to: mSoSeri property in legacy class
    /// </summary>
    [StringLength(20)]
    public string SoSeri { get; set; } = string.Empty;

    /// <summary>
    /// Invoice number
    /// Maps to: mSoHoaDon property in legacy class
    /// </summary>
    [StringLength(50)]
    public string SoHoaDon { get; set; } = string.Empty;

    /// <summary>
    /// Invoice date (YYYYMMDD format)
    /// Maps to: mNgayHoaDon property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayHoaDon { get; set; } = string.Empty;

    /// <summary>
    /// Delivery date (YYYYMMDD format)
    /// Maps to: mNgayGiao property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayGiao { get; set; } = string.Empty;

    /// <summary>
    /// Amount to be deducted
    /// Maps to: mSoTienCanTru property in legacy class
    /// </summary>
    public decimal SoTienCanTru { get; set; } = 0;
}

/// <summary>
/// DTO for BaoGiaSuaChua list display with joined data
/// Optimized for automotive repair quotation lists with related information
/// Used by list and search operations
/// </summary>
public class BaoGiaSuaChuaListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public DateTime? NgayChungTu { get; set; } // Converted from char2date
    public string BienSoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string BaoHiem { get; set; } = string.Empty;
    public string CoVanDichVu { get; set; } = string.Empty; // Service advisor name
    public DateTime? NgayVaoXuong { get; set; }
    public DateTime? NgayRaXuong { get; set; }
    public int PhanLoai { get; set; } = 0;
    public string PhanLoaiText { get; set; } = string.Empty; // Cash/Insurance
    public int BoPhanSuaChua { get; set; } = 0;
    public string BoPhanSuaChuaText { get; set; } = string.Empty; // Body+Paint/Engine
    public int TinhTrangBaoGia { get; set; } = 0;
    public string TinhTrangText { get; set; } = string.Empty; // Draft/Approved
    public decimal TongTienHang3 { get; set; } = 0;
    public decimal TienThue3 { get; set; } = 0;
    public decimal TienChietKhau3 { get; set; } = 0;
    public decimal TienCheTai { get; set; } = 0;
    public decimal TongThanhToan { get; set; } = 0; // Final amount
    public decimal DaThuTienHang { get; set; } = 0;
    public decimal ConLai { get; set; } = 0; // Remaining amount
    public bool IsInsurance { get; set; } = false;
    public bool IsCash { get; set; } = false;
    public bool IsBodyPaint { get; set; } = false;
    public bool IsEngine { get; set; } = false;
    public bool IsApproved { get; set; } = false;
    public bool IsDraft { get; set; } = false;
    public string Status { get; set; } = string.Empty;
    public string StatusColor { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new BaoGiaSuaChua
/// Contains only required fields for creation
/// </summary>
public class CreateBaoGiaSuaChuaDto
{
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;

    [Required]
    [StringLength(8)]
    public string NgayChungTu { get; set; } = string.Empty;

    public string KhoaTiepNhanXe { get; set; } = string.Empty;
    public string KhoaKhachHang { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string KhoaXe { get; set; } = string.Empty;
    public string BienSoXe { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public int PhanLoai { get; set; } = 0;
    public int BoPhanSuaChua { get; set; } = 0;
    public string KhoaBaoHiem { get; set; } = string.Empty;
    public string BaoHiem { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaNhanVienTao { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating BaoGiaSuaChua status
/// Used for approval and status change operations
/// </summary>
public class UpdateBaoGiaSuaChuaStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int TinhTrangBaoGia { get; set; } = 0;
    public string KhoaNhanVienDuyet { get; set; } = string.Empty;
    public string NgayDuyet { get; set; } = string.Empty;
    public decimal TienCheTai { get; set; } = 0;
}

/// <summary>
/// DTO for BaoGiaSuaChua search operations
/// Used for advanced search and filtering
/// </summary>
public class BaoGiaSuaChuaSearchDto
{
    public string? SoChungTu { get; set; }
    public string? NgayChungTuFrom { get; set; }
    public string? NgayChungTuTo { get; set; }
    public string? BienSoXe { get; set; }
    public string? KhoaKhachHang { get; set; }
    public string? KhachHang { get; set; }
    public string? KhoaLoaiXe { get; set; }
    public string? KhoaBaoHiem { get; set; }
    public int? PhanLoai { get; set; }
    public int? BoPhanSuaChua { get; set; }
    public int? TinhTrangBaoGia { get; set; }
    public string? NgayVaoXuongFrom { get; set; }
    public string? NgayVaoXuongTo { get; set; }
    public decimal? TongTienHangFrom { get; set; }
    public decimal? TongTienHangTo { get; set; }
    public bool? HasOutstandingBalance { get; set; }
    public bool? IsInsuranceClaim { get; set; }
}

/// <summary>
/// DTO for automotive repair quotation
/// Specialized for automotive repair quotation management
/// </summary>
public class AutomotiveRepairQuotationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public DateTime? NgayChungTu { get; set; }
    public string BienSoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public bool IsInsuranceClaim { get; set; } = false;
    public string BaoHiem { get; set; } = string.Empty;
    public string LienHe { get; set; } = string.Empty;
    public bool IsBodyPaintRepair { get; set; } = false;
    public bool IsEngineRepair { get; set; } = false;
    public bool IsElectricalRepair { get; set; } = false;
    public bool IsACRepair { get; set; } = false;
    public bool IsBrakeRepair { get; set; } = false;
    public bool IsSuspensionRepair { get; set; } = false;
    public bool IsTransmissionRepair { get; set; } = false;
    public bool IsTireRepair { get; set; } = false;
    public bool IsGlassRepair { get; set; } = false;
    public bool IsInteriorRepair { get; set; } = false;
    public DateTime? NgayVaoXuong { get; set; }
    public DateTime? NgayRaXuong { get; set; }
    public DateTime? NgayHoanThanh { get; set; }
    public int SoNgayHoanThanh { get; set; } = 0;
    public decimal TienSuaChua { get; set; } = 0;
    public decimal TienPhuTung { get; set; } = 0;
    public decimal TongTienHang { get; set; } = 0;
    public decimal TienThue { get; set; } = 0;
    public decimal TienChietKhau { get; set; } = 0;
    public decimal TienCheTai { get; set; } = 0;
    public decimal TongThanhToan { get; set; } = 0;
    public decimal DaThuTienHang { get; set; } = 0;
    public decimal ConLai { get; set; } = 0;
    public int TinhTrangBaoGia { get; set; } = 0;
    public string TinhTrangText { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string NhanVienTao { get; set; } = string.Empty;
    public string NhanVienDuyet { get; set; } = string.Empty;
    public DateTime? NgayTao { get; set; }
    public DateTime? NgayDuyet { get; set; }
}

/// <summary>
/// DTO for repair quotation financial summary
/// Used for financial reporting and analysis
/// </summary>
public class RepairQuotationFinancialDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public decimal TienSuaChua { get; set; } = 0;
    public decimal TienPhuTung { get; set; } = 0;
    public decimal TienKe { get; set; } = 0; // Subcontractor
    public decimal TienHoaHong { get; set; } = 0; // Commission
    public decimal TongTienHang1 { get; set; } = 0; // Before discount
    public decimal TongTienHang2 { get; set; } = 0; // After discount
    public decimal TongTienHang3 { get; set; } = 0; // Final amount
    public decimal TienThue1 { get; set; } = 0;
    public decimal TienThue2 { get; set; } = 0;
    public decimal TienThue3 { get; set; } = 0;
    public decimal TienChietKhau1 { get; set; } = 0;
    public decimal TienChietKhau2 { get; set; } = 0;
    public decimal TienChietKhau3 { get; set; } = 0;
    public decimal TienCheTai { get; set; } = 0; // Deductible
    public decimal TongThanhToan { get; set; } = 0; // Total payment
    public decimal DaThuTienHang { get; set; } = 0; // Amount received
    public decimal DaTraTienKe { get; set; } = 0; // Subcontractor paid
    public decimal DaTraHoaHong { get; set; } = 0; // Commission paid
    public decimal DaThuCheTai { get; set; } = 0; // Deductible received
    public decimal ConLaiTienHang { get; set; } = 0; // Outstanding balance
    public decimal ConLaiTienKe { get; set; } = 0; // Outstanding subcontractor
    public decimal ConLaiHoaHong { get; set; } = 0; // Outstanding commission
    public decimal ConLaiCheTai { get; set; } = 0; // Outstanding deductible
    public bool IsFullyPaid { get; set; } = false;
    public bool HasOutstandingBalance { get; set; } = false;
    public decimal TotalOutstanding { get; set; } = 0;

    // Service Advisor Information (populated via JOINs)
    /// <summary>Service advisor 1 key - Maps to: KhoaCoVan1 property in legacy class</summary>
    public string KhoaCoVan1 { get; set; } = string.Empty;

    /// <summary>Service advisor 1 name - Maps to: CoVanDichVu1 property in legacy class</summary>
    public string CoVanDichVu1 { get; set; } = string.Empty;

    /// <summary>Service advisor 1 phone - Maps to: DienThoaiCoVan1 property in legacy class</summary>
    public string DienThoaiCoVan1 { get; set; } = string.Empty;

    /// <summary>Service advisor 2 key - Maps to: KhoaCoVan2 property in legacy class</summary>
    public string KhoaCoVan2 { get; set; } = string.Empty;

    /// <summary>Service advisor 2 name - Maps to: CoVanDichVu2 property in legacy class</summary>
    public string CoVanDichVu2 { get; set; } = string.Empty;

    /// <summary>Service advisor 2 phone - Maps to: DienThoaiCoVan2 property in legacy class</summary>
    public string DienThoaiCoVan2 { get; set; } = string.Empty;
}
