using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Service interface for Repair Requirements Detail (clsBaoGiaYeuCauSuaChuaChiTiet)
    /// Provides business logic for managing repair requirement details
    /// Implements exact functionality from clsBaoGiaYeuCauSuaChuaChiTiet legacy class (196 lines)
    /// </summary>
    public interface IBaoGiaYeuCauSuaChuaChiTietService
    {
        /// <summary>
        /// Load repair requirement detail by ID
        /// Exact implementation from legacy Load method
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>Repair requirement detail data or null if not found</returns>
        Task<BaoGiaYeuCauSuaChuaChiTietDto?> LoadAsync(string khoa);

        /// <summary>
        /// Create new repair requirement detail
        /// </summary>
        /// <param name="createDto">Create data</param>
        /// <returns>Success result with created ID</returns>
        Task<ServiceResult<string>> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto);

        /// <summary>
        /// Update repair requirement detail
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto);

        /// <summary>
        /// Delete repair requirement detail
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> DeleteAsync(string khoa);

        /// <summary>
        /// Get repair requirement details by quotation ID
        /// Exact implementation from legacy GetDetailsYeuCauSuaChua method
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>List of repair requirement details for the quotation</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByQuotationAsync(string khoaBaoGia);

        /// <summary>
        /// Get repair requirement details by technician
        /// </summary>
        /// <param name="khoaKTV">Technician ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of repair requirement details assigned to technician</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByTechnicianAsync(string khoaKTV, string fromDate, string toDate);

        /// <summary>
        /// Get repair requirement details by work code
        /// </summary>
        /// <param name="maCongViec">Work code</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of repair requirement details with specified work code</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByWorkCodeAsync(string maCongViec, string donViId);

        /// <summary>
        /// Get repair requirement details by status
        /// </summary>
        /// <param name="trangThai">Status (0=Pending, 1=In Progress, 2=Completed, 3=Cancelled)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of repair requirement details with specified status</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByStatusAsync(int trangThai, string donViId);

        /// <summary>
        /// Get repair requirement details by priority
        /// </summary>
        /// <param name="mucDoUuTien">Priority level (1=Low, 2=Normal, 3=High, 4=Critical)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of repair requirement details with specified priority</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByPriorityAsync(int mucDoUuTien, string donViId);

        /// <summary>
        /// Get overdue repair requirement details
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of overdue repair requirement details</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetOverdueAsync(string donViId);

        /// <summary>
        /// Get repair requirement details requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of repair requirement details pending approval</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetPendingApprovalsAsync(string donViId);

        /// <summary>
        /// Get repair requirement statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>Repair requirement statistics</returns>
        Task<ServiceResult<BaoGiaYeuCauSuaChuaChiTietStatisticsDto>> GetStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Update work progress
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="phanTramHoanThanh">Completion percentage</param>
        /// <param name="trangThai">Status</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateProgressAsync(string khoa, decimal phanTramHoanThanh, int trangThai, string nguoiCapNhat);

        /// <summary>
        /// Assign technician to repair requirement detail
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="khoaKTV">Technician ID</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> AssignTechnicianAsync(string khoa, string khoaKTV, string nguoiCapNhat);

        /// <summary>
        /// Bulk update status for multiple repair requirement details
        /// </summary>
        /// <param name="khoaList">List of repair requirement detail IDs</param>
        /// <param name="trangThai">New status</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Number of successfully updated records</returns>
        Task<ServiceResult<int>> BulkUpdateStatusAsync(List<string> khoaList, int trangThai, string nguoiCapNhat);

        /// <summary>
        /// Get repair requirement details for mobile app with pagination
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="searchTerm">Search term (optional)</param>
        /// <returns>Paginated list of repair requirement details</returns>
        Task<ServiceResult<PaginatedResult<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null);

        /// <summary>
        /// Search repair requirement details
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching repair requirement details</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto);

        /// <summary>
        /// Get repair requirement details lookup data
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of repair requirement details for lookup/dropdown</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietLookupDto>>> GetLookupDataAsync(string donViId);

        /// <summary>
        /// Validate repair requirement detail
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<BaoGiaYeuCauSuaChuaChiTietValidationDto>> ValidateAsync(string khoa);

        /// <summary>
        /// Get automotive repair work details
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>List of automotive repair work details</returns>
        Task<ServiceResult<List<AutomotiveRepairWorkDetailDto>>> GetAutomotiveRepairWorkAsync(string khoaBaoGia);

        /// <summary>
        /// Get repair work summary by quotation
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Repair work summary</returns>
        Task<ServiceResult<RepairWorkSummaryDto>> GetRepairWorkSummaryAsync(string khoaBaoGia);

        /// <summary>
        /// Get technician workload analysis
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of technician workload data</returns>
        Task<ServiceResult<List<TechnicianWorkloadDto>>> GetTechnicianWorkloadAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get work item progress tracking
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>Work item progress data</returns>
        Task<ServiceResult<WorkItemProgressDto>> GetWorkItemProgressAsync(string khoa);

        /// <summary>
        /// Update work item progress
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="progressDto">Progress data</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto);

        /// <summary>
        /// Check if repair requirement detail exists
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(string khoa);

        /// <summary>
        /// Check if repair requirement detail can be deleted
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>True if can be deleted, false otherwise</returns>
        Task<bool> CanDeleteAsync(string khoa);

        /// <summary>
        /// Get repair requirement details by date range
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of repair requirement details in date range</returns>
        Task<ServiceResult<List<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByDateRangeAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Validate create data before saving
        /// </summary>
        /// <param name="createDto">Create data to validate</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateCreateDataAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto);

        /// <summary>
        /// Validate update data before saving
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="updateDto">Update data to validate</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateUpdateDataAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto);

        /// <summary>
        /// Send notification for work assignment
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <param name="khoaKTV">Technician ID</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendWorkAssignmentNotificationAsync(string khoa, string khoaKTV);

        /// <summary>
        /// Send notification for work completion
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendWorkCompletionNotificationAsync(string khoa);

        /// <summary>
        /// Export repair requirement details to Excel report
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Excel report data</returns>
        Task<ServiceResult<byte[]>> ExportToExcelAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get repair requirement detail audit trail
        /// </summary>
        /// <param name="khoa">Repair requirement detail ID</param>
        /// <returns>List of audit trail entries</returns>
        Task<ServiceResult<List<RepairRequirementAuditDto>>> GetAuditTrailAsync(string khoa);
    }

    /// <summary>
    /// Repair requirement audit trail
    /// </summary>
    public class RepairRequirementAuditDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string HanhDong { get; set; } = string.Empty;
        public string NguoiThucHien { get; set; } = string.Empty;
        public string ThoiGian { get; set; } = string.Empty;
        public string NoiDung { get; set; } = string.Empty;
        public string TrangThaiCu { get; set; } = string.Empty;
        public string TrangThaiMoi { get; set; } = string.Empty;
        public string GhiChu { get; set; } = string.Empty;
    }
}
