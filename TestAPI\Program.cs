using Microsoft.Data.SqlClient;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS for local development
builder.Services.AddCors(options =>
{
    options.AddPolicy("LocalDevelopment", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("LocalDevelopment");
app.UseAuthorization();
app.MapControllers();

// Test database connection on startup
var connectionString = "Server=localhost;Database=carsoft_giaphat;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true";

Console.WriteLine("🚀 GP Mobile Test API Starting...");
Console.WriteLine("📊 Testing CARSOFT_GIAPHAT database connection...");

try
{
    using var connection = new SqlConnection(connectionString);
    await connection.OpenAsync();
    
    var command = new SqlCommand("SELECT DB_NAME() as DatabaseName, SYSTEM_USER as CurrentUser, @@SERVERNAME as ServerName, GETDATE() as CurrentTime", connection);
    var reader = await command.ExecuteReaderAsync();
    
    if (await reader.ReadAsync())
    {
        Console.WriteLine($"✅ Database connection successful!");
        Console.WriteLine($"   Database: {reader["DatabaseName"]}");
        Console.WriteLine($"   User: {reader["CurrentUser"]}");
        Console.WriteLine($"   Server: {reader["ServerName"]}");
        Console.WriteLine($"   Time: {reader["CurrentTime"]}");
    }
    
    await reader.CloseAsync();
    
    // Test required tables
    var tablesCommand = new SqlCommand(@"
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME IN ('DM_NguoiDung', 'DM_DonVi', 'DM_DieuKhoanbaoGia', 'Temp_BaoGia')
        ORDER BY TABLE_NAME", connection);
    
    var tablesReader = await tablesCommand.ExecuteReaderAsync();
    var foundTables = new List<string>();
    
    while (await tablesReader.ReadAsync())
    {
        foundTables.Add(tablesReader["TABLE_NAME"].ToString());
    }
    
    Console.WriteLine($"📋 Found {foundTables.Count} required tables:");
    foreach (var table in foundTables)
    {
        Console.WriteLine($"   ✅ {table}");
    }
    
    await tablesReader.CloseAsync();
    connection.Close();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Database connection failed: {ex.Message}");
    Console.WriteLine($"Connection string: {connectionString}");
    Console.WriteLine("Please check:");
    Console.WriteLine("1. SQL Server is running");
    Console.WriteLine("2. CARSOFT_GIAPHAT database exists");
    Console.WriteLine("3. Windows Authentication is enabled");
}

// Add simple endpoints
app.MapGet("/api/health", () => new { 
    status = "healthy", 
    timestamp = DateTime.Now,
    version = "1.0.0",
    environment = "development"
});

app.MapGet("/api/test-database", async () => {
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();
        
        var command = new SqlCommand("SELECT DB_NAME() as DatabaseName, SYSTEM_USER as CurrentUser, GETDATE() as CurrentTime", connection);
        var reader = await command.ExecuteReaderAsync();
        
        var result = new Dictionary<string, object>();
        if (await reader.ReadAsync())
        {
            result["DatabaseName"] = reader["DatabaseName"];
            result["CurrentUser"] = reader["CurrentUser"];
            result["CurrentTime"] = reader["CurrentTime"];
            result["Status"] = "Connected Successfully";
        }
        
        return Results.Ok(result);
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { 
            Status = "Connection Failed", 
            Error = ex.Message
        });
    }
});

app.MapGet("/api/test-tables", async () => {
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();
        
        var command = new SqlCommand(@"
            SELECT TABLE_NAME, TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE' 
            AND (TABLE_NAME LIKE 'DM_%' OR TABLE_NAME LIKE 'Temp_%' OR TABLE_NAME LIKE 'SC_%')
            ORDER BY TABLE_NAME", connection);
        
        var reader = await command.ExecuteReaderAsync();
        var tables = new List<object>();
        
        while (await reader.ReadAsync())
        {
            tables.Add(new {
                TableName = reader["TABLE_NAME"].ToString(),
                TableType = reader["TABLE_TYPE"].ToString()
            });
        }
        
        return Results.Ok(new { 
            Status = "Tables Found", 
            Tables = tables,
            Count = tables.Count
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { 
            Status = "Tables Test Failed", 
            Error = ex.Message 
        });
    }
});

app.MapGet("/api/test-users", async () => {
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        // First check if DM_NguoiDung table exists
        var checkTableCommand = new SqlCommand(@"
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = 'DM_NguoiDung'", connection);

        var tableExists = (int)await checkTableCommand.ExecuteScalarAsync() > 0;

        if (!tableExists)
        {
            return Results.Ok(new {
                Status = "Users Table Not Found",
                Message = "DM_NguoiDung table does not exist yet",
                Suggestion = "Run database-test-data.sql to create test users",
                UserCount = 0,
                SampleUsers = new List<object>()
            });
        }

        var command = new SqlCommand("SELECT COUNT(*) as UserCount FROM DM_NguoiDung WHERE Active = 1", connection);
        var userCount = await command.ExecuteScalarAsync();

        var sampleCommand = new SqlCommand("SELECT TOP 3 TenDangNhap, TenViet FROM DM_NguoiDung WHERE Active = 1", connection);
        var reader = await sampleCommand.ExecuteReaderAsync();
        var users = new List<object>();

        while (await reader.ReadAsync())
        {
            users.Add(new {
                Username = reader["TenDangNhap"].ToString(),
                DisplayName = reader["TenViet"].ToString()
            });
        }

        return Results.Ok(new {
            Status = "Users Found",
            UserCount = userCount,
            SampleUsers = users
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new {
            Status = "Users Test Failed",
            Error = ex.Message,
            Suggestion = "Run database-test-data.sql to create test users"
        });
    }
});

app.MapGet("/api/test-databases", async () => {
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqlCommand(@"
            SELECT name, database_id, create_date, collation_name
            FROM sys.databases
            WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')
            ORDER BY name", connection);

        var reader = await command.ExecuteReaderAsync();
        var databases = new List<object>();

        while (await reader.ReadAsync())
        {
            databases.Add(new {
                Name = reader["name"].ToString(),
                DatabaseId = reader["database_id"],
                CreateDate = reader["create_date"],
                Collation = reader["collation_name"].ToString()
            });
        }

        return Results.Ok(new {
            Status = "Databases Found",
            Databases = databases,
            Count = databases.Count,
            CurrentDatabase = "carsoft_giaphat",
            SqlServerVersion = "2014"
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new {
            Status = "Database List Failed",
            Error = ex.Message
        });
    }
});

Console.WriteLine("🚀 GP Mobile Test API started successfully!");
Console.WriteLine("📊 Swagger UI: http://localhost:5001/swagger");
Console.WriteLine("🔍 Health check: http://localhost:5001/api/health");
Console.WriteLine("💾 Database test: http://localhost:5001/api/test-database");
Console.WriteLine("📋 Tables test: http://localhost:5001/api/test-tables");
Console.WriteLine("👥 Users test: http://localhost:5001/api/test-users");
Console.WriteLine("");

app.Run();
