using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Repository interface for Temporary Quotation Data (clsTempBaoGia)
    /// Provides methods for managing temporary quotation data in SC_TempBaoGia table
    /// Implements exact functionality from clsTempBaoGia legacy class usage
    /// </summary>
    public interface ITempBaoGiaRepository
    {
        /// <summary>
        /// Load temporary quotation data by quotation ID
        /// Exact implementation from legacy Load method (line 10929)
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary quotation data or null if not found</returns>
        Task<TempBaoGiaDto?> LoadAsync(string khoaBaoGia);

        /// <summary>
        /// Save temporary quotation data (insert or update)
        /// Exact implementation from legacy Save method (line 9964)
        /// </summary>
        /// <param name="tempBaoGia">Temporary quotation data to save</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveAsync(TempBaoGiaDto tempBaoGia);

        /// <summary>
        /// Save temporary quotation data with specific properties
        /// Exact implementation from legacy Save method usage (lines 9959-9964)
        /// </summary>
        /// <param name="saveDto">Save data with specific properties</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveAsync(SaveTempBaoGiaDto saveDto);

        /// <summary>
        /// Save request for cancellation approval
        /// Exact implementation from legacy SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        /// <param name="requestDto">Request cancellation approval data</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveRequestDuyetHuyAsync(RequestDuyetHuyDto requestDto);

        /// <summary>
        /// Delete temporary quotation data by quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(string khoaBaoGia);

        /// <summary>
        /// Check if temporary quotation data exists
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(string khoaBaoGia);

        /// <summary>
        /// Get temporary quotations requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of temporary quotations pending approval</returns>
        Task<List<TempBaoGiaListDto>> GetPendingApprovalsAsync(string donViId);

        /// <summary>
        /// Get temporary quotations by status
        /// </summary>
        /// <param name="trangThai">Status (0=Draft, 1=Pending, 2=Approved, 3=Cancelled)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of temporary quotations with specified status</returns>
        Task<List<TempBaoGiaListDto>> GetByStatusAsync(int trangThai, string donViId);

        /// <summary>
        /// Get temporary quotations by user
        /// </summary>
        /// <param name="nguoiTao">User ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of temporary quotations by user</returns>
        Task<List<TempBaoGiaListDto>> GetByUserAsync(string nguoiTao, string fromDate, string toDate);

        /// <summary>
        /// Get temporary quotations requiring cancellation approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of quotations with cancellation requests</returns>
        Task<List<TempBaoGiaListDto>> GetCancellationRequestsAsync(string donViId);

        /// <summary>
        /// Update cancellation approval status
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="isDuyetHuy">Approval status</param>
        /// <param name="nguoiDuyet">Approver ID</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateCancellationApprovalAsync(string khoaBaoGia, bool isDuyetHuy, string nguoiDuyet);

        /// <summary>
        /// Get temporary quotation statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>Temporary quotation statistics</returns>
        Task<TempBaoGiaStatisticsDto> GetStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Clear temporary data for completed quotations
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="olderThanDays">Clear data older than specified days</param>
        /// <returns>Number of records cleared</returns>
        Task<int> ClearCompletedDataAsync(string donViId, int olderThanDays = 30);

        /// <summary>
        /// Bulk update temporary quotation status
        /// </summary>
        /// <param name="khoaBaoGiaList">List of quotation IDs</param>
        /// <param name="trangThai">New status</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Number of successfully updated records</returns>
        Task<int> BulkUpdateStatusAsync(List<string> khoaBaoGiaList, int trangThai, string nguoiCapNhat);

        /// <summary>
        /// Get temporary quotations for mobile app with pagination
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="searchTerm">Search term (optional)</param>
        /// <returns>Paginated list of temporary quotations</returns>
        Task<PaginatedResult<TempBaoGiaListDto>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null);

        /// <summary>
        /// Get temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary data content as string</returns>
        Task<string?> GetTempDataAsync(string khoaBaoGia);

        /// <summary>
        /// Update temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="tempData">Temporary data content</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateTempDataAsync(string khoaBaoGia, string tempData, string nguoiCapNhat);

        /// <summary>
        /// Get cancellation request details
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Cancellation request data</returns>
        Task<string?> GetCancellationRequestDataAsync(string khoaBaoGia);

        /// <summary>
        /// Check if quotation has pending cancellation requests
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if has pending requests, false otherwise</returns>
        Task<bool> HasPendingCancellationRequestAsync(string khoaBaoGia);

        /// <summary>
        /// Get temporary quotations by date range
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of temporary quotations in date range</returns>
        Task<List<TempBaoGiaListDto>> GetByDateRangeAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Archive old temporary data
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="olderThanDays">Archive data older than specified days</param>
        /// <returns>Number of records archived</returns>
        Task<int> ArchiveOldDataAsync(string donViId, int olderThanDays = 90);
    }
}
