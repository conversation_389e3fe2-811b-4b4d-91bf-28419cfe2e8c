<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GP Mobile QR Code</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .qr-container {
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .url-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions {
            text-align: left;
            background: #f0f8ff;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 5px 0;
        }
        .credentials {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 GP Mobile Quotation App</h1>
        <h2>📱 iOS Testing QR Code</h2>
        
        <div class="qr-container">
            <canvas id="qrcode"></canvas>
        </div>
        
        <div class="url-info">
            <h3>📡 Connection URL</h3>
            <p><strong>exp://*************:8081</strong></p>
        </div>
        
        <div class="instructions">
            <h3>📲 How to Connect on iPhone:</h3>
            
            <div class="step">
                <strong>Method 1: QR Code Scan</strong>
                <ol>
                    <li>Install <strong>Expo Go</strong> from App Store</li>
                    <li>Open Expo Go app</li>
                    <li>Tap <strong>"Scan QR Code"</strong></li>
                    <li>Point camera at QR code above</li>
                </ol>
            </div>
            
            <div class="step">
                <strong>Method 2: Camera App (iOS 11+)</strong>
                <ol>
                    <li>Open <strong>Camera</strong> app</li>
                    <li>Point camera at QR code above</li>
                    <li>Tap notification that appears</li>
                    <li>Opens in Expo Go automatically</li>
                </ol>
            </div>
            
            <div class="step">
                <strong>Method 3: Manual URL Entry</strong>
                <ol>
                    <li>Open <strong>Expo Go</strong> app</li>
                    <li>Tap <strong>"Enter URL manually"</strong></li>
                    <li>Type: <code>exp://*************:8081</code></li>
                    <li>Tap <strong>"Connect"</strong></li>
                </ol>
            </div>
        </div>
        
        <div class="credentials">
            <h3>🔐 Test Credentials</h3>
            <p><strong>Username:</strong> testuser</p>
            <p><strong>Password:</strong> test123</p>
            <p><strong>Client:</strong> Select "Trung Tâm" from dropdown</p>
        </div>
        
        <div class="instructions">
            <h3>✅ What to Test:</h3>
            <ul>
                <li>Vietnamese text displays correctly</li>
                <li>Login form works properly</li>
                <li>Client dropdown populates</li>
                <li>Authentication succeeds</li>
                <li>Navigation to main app</li>
            </ul>
        </div>
    </div>

    <script>
        // Generate QR code
        const canvas = document.getElementById('qrcode');
        const url = 'exp://*************:8081';
        
        QRCode.toCanvas(canvas, url, {
            width: 300,
            height: 300,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        }, function (error) {
            if (error) {
                console.error(error);
                canvas.parentElement.innerHTML = '<p>Error generating QR code. Use manual URL entry.</p>';
            } else {
                console.log('QR code generated successfully!');
            }
        });
    </script>
</body>
</html>
