using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Authentication DTOs for mobile login system
    /// Based on Frm_Login.cs legacy form functionality
    /// </summary>

    /// <summary>
    /// Login request DTO
    /// Maps to UsernameTextBox, PasswordTextBox, CboClient from legacy form
    /// </summary>
    public class LoginRequestDto
    {
        [Required(ErrorMessage = "Tên đăng nhập không được để trống")]
        [StringLength(50, ErrorMessage = "Tên đăng nhập không được vượt quá 50 ký tự")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "Mật khẩu không được để trống")]
        [StringLength(100, ErrorMessage = "Mật khẩu không được vượt quá 100 ký tự")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Bạn phải chọn đơn vị sử dụng")]
        public string ClientId { get; set; } = string.Empty;

        public string DeviceId { get; set; } = string.Empty;
        public string DeviceType { get; set; } = "Mobile";
        public string AppVersion { get; set; } = "1.0.0";
    }

    /// <summary>
    /// Login response DTO
    /// Maps to successful login data from legacy OK_Click method
    /// </summary>
    public class LoginResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
        public UserInfoDto? UserInfo { get; set; }
        public List<string> Permissions { get; set; } = new();
    }

    /// <summary>
    /// User information DTO
    /// Maps to objU properties from legacy form
    /// </summary>
    public class UserInfoDto
    {
        public string UserId { get; set; } = string.Empty;           // H_USERID / KhoaNhanVien
        public string Username { get; set; } = string.Empty;         // H_USERNAME / TenDangNhap
        public string EmployeeName { get; set; } = string.Empty;     // H_EMPLOYEE / TenViet
        public string UserType { get; set; } = string.Empty;         // H_USERTYPE / LoaiNguoiDung
        public string ClientId { get; set; } = string.Empty;         // H_CLIENT
        public string ClientName { get; set; } = string.Empty;       // DonVi name
        public string Prefix { get; set; } = string.Empty;           // H_PREFIX
        public string Language { get; set; } = "VIET";               // H_LANGUAGE
        public bool IsAdmin { get; set; } = false;                   // KhoaNhanVien == "0000000000"
        public List<string> AllowedClients { get; set; } = new();    // DonViDangNhap
        public DateTime LastLoginTime { get; set; }
        public string Avatar { get; set; } = string.Empty;
    }

    /// <summary>
    /// Client/Branch selection DTO
    /// Maps to CboClient ComboBox from legacy form
    /// </summary>
    public class ClientSelectionDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string Ma { get; set; } = string.Empty;
        public string TenViet { get; set; } = string.Empty;
        public string TenAnh { get; set; } = string.Empty;
        public string Prefix { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;
    }

    /// <summary>
    /// Password change DTO
    /// </summary>
    public class ChangePasswordDto
    {
        [Required(ErrorMessage = "Mật khẩu hiện tại không được để trống")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Mật khẩu mới không được để trống")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Mật khẩu mới phải từ 6-100 ký tự")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Xác nhận mật khẩu không được để trống")]
        [Compare("NewPassword", ErrorMessage = "Xác nhận mật khẩu không khớp")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Token refresh DTO
    /// </summary>
    public class RefreshTokenDto
    {
        [Required]
        public string RefreshToken { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Logout DTO
    /// </summary>
    public class LogoutDto
    {
        public string DeviceId { get; set; } = string.Empty;
        public bool LogoutAllDevices { get; set; } = false;
    }

    /// <summary>
    /// User session DTO
    /// For tracking active sessions
    /// </summary>
    public class UserSessionDto
    {
        public string SessionId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceType { get; set; } = string.Empty;
        public DateTime LoginTime { get; set; }
        public DateTime LastActivity { get; set; }
        public bool IsActive { get; set; } = true;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Authentication validation result
    /// </summary>
    public class AuthValidationResult
    {
        public bool IsValid { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
        public UserInfoDto? UserInfo { get; set; }
    }

    /// <summary>
    /// User permissions DTO
    /// </summary>
    public class UserPermissionsDto
    {
        public string UserId { get; set; } = string.Empty;
        public List<string> Permissions { get; set; } = new();
        public List<string> Roles { get; set; } = new();
        public Dictionary<string, bool> Features { get; set; } = new();
    }

    /// <summary>
    /// Biometric authentication DTO
    /// For fingerprint/face ID login
    /// </summary>
    public class BiometricAuthDto
    {
        public string UserId { get; set; } = string.Empty;
        public string BiometricToken { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public string BiometricType { get; set; } = string.Empty; // "fingerprint", "face", "voice"
    }

    /// <summary>
    /// Two-factor authentication DTO
    /// </summary>
    public class TwoFactorAuthDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty; // "sms", "email", "app"
        public bool RememberDevice { get; set; } = false;
    }
}
