using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Controller for Quotation Terms & Conditions (ClsDMDieuKhoanBaoGia)
    /// Provides REST API endpoints for managing terms and conditions
    /// Implements exact functionality from ClsDMDieuKhoanBaoGia legacy class (442 lines)
    /// Used in form lines 8634-8712 for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DieuKhoanBaoGiaController : ControllerBase
    {
        private readonly IDieuKhoanBaoGiaService _service;
        private readonly ILogger<DieuKhoanBaoGiaController> _logger;

        public DieuKhoanBaoGiaController(IDieuKhoanBaoGiaService service, ILogger<DieuKhoanBaoGiaController> logger)
        {
            _service = service;
            _logger = logger;
        }

        /// <summary>
        /// Load terms & conditions by ID
        /// Exact implementation from legacy Load method
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>Terms & conditions data</returns>
        [HttpGet("{khoa}")]
        public async Task<ActionResult<DieuKhoanBaoGiaDto>> Load(string khoa)
        {
            try
            {
                var result = await _service.LoadAsync(khoa);
                if (result == null)
                {
                    return NotFound($"Không tìm thấy điều khoản báo giá {khoa}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading terms & conditions for {Khoa}", khoa);
                return StatusCode(500, "Lỗi hệ thống khi lấy điều khoản báo giá");
            }
        }

        /// <summary>
        /// Load terms & conditions by code
        /// Exact implementation from legacy LoadByCode method
        /// </summary>
        /// <param name="ma">Terms & conditions code</param>
        /// <returns>Terms & conditions data</returns>
        [HttpGet("by-code/{ma}")]
        public async Task<ActionResult<DieuKhoanBaoGiaDto>> LoadByCode(string ma)
        {
            try
            {
                var result = await _service.LoadByCodeAsync(ma);
                if (result == null)
                {
                    return NotFound($"Không tìm thấy điều khoản báo giá với mã {ma}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading terms & conditions by code {Ma}", ma);
                return StatusCode(500, "Lỗi hệ thống khi lấy điều khoản báo giá");
            }
        }

        /// <summary>
        /// Create new terms & conditions
        /// </summary>
        /// <param name="createDto">Create data</param>
        /// <returns>Created terms & conditions ID</returns>
        [HttpPost]
        public async Task<ActionResult> Create([FromBody] CreateDieuKhoanBaoGiaDto createDto)
        {
            try
            {
                var result = await _service.CreateAsync(createDto);
                if (result.Success)
                {
                    return Ok(new { success = true, khoa = result.Data, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating terms & conditions");
                return StatusCode(500, "Lỗi hệ thống khi tạo điều khoản báo giá");
            }
        }

        /// <summary>
        /// Update terms & conditions
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Update result</returns>
        [HttpPut("{khoa}")]
        public async Task<ActionResult> Update(string khoa, [FromBody] UpdateDieuKhoanBaoGiaDto updateDto)
        {
            try
            {
                var result = await _service.UpdateAsync(khoa, updateDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating terms & conditions for {Khoa}", khoa);
                return StatusCode(500, "Lỗi hệ thống khi cập nhật điều khoản báo giá");
            }
        }

        /// <summary>
        /// Delete terms & conditions
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>Delete result</returns>
        [HttpDelete("{khoa}")]
        public async Task<ActionResult> Delete(string khoa)
        {
            try
            {
                var result = await _service.DeleteAsync(khoa);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting terms & conditions for {Khoa}", khoa);
                return StatusCode(500, "Lỗi hệ thống khi xóa điều khoản báo giá");
            }
        }

        /// <summary>
        /// Get terms & conditions by type
        /// Exact implementation from form SQL usage: SELECT NoiDung FROM SC_DieuKhoanBaoGia WHERE Loai = 'BG'
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of terms & conditions for the specified type</returns>
        [HttpGet("by-type/{loai}/{donViId}")]
        public async Task<ActionResult<List<DieuKhoanBaoGiaListDto>>> GetByType(string loai, string donViId)
        {
            try
            {
                var result = await _service.GetByTypeAsync(loai, donViId);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting terms & conditions by type {Loai} for unit {DonViId}", loai, donViId);
                return StatusCode(500, "Lỗi hệ thống khi lấy điều khoản theo loại");
            }
        }

        /// <summary>
        /// Get terms content by type for form initialization
        /// Used for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan methods
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Terms content data for form initialization</returns>
        [HttpGet("terms-content/{loai}/{donViId}")]
        public async Task<ActionResult<DieuKhoanContentDto>> GetTermsContent(string loai, string donViId)
        {
            try
            {
                var result = await _service.GetTermsContentAsync(loai, donViId);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting terms content for type {Loai} and unit {DonViId}", loai, donViId);
                return StatusCode(500, "Lỗi hệ thống khi lấy nội dung điều khoản");
            }
        }

        /// <summary>
        /// Initialize terms content for quotation form
        /// Exact implementation from form InitDieuKhoanBaoGia method (lines 8634-8650)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Quotation terms content</returns>
        [HttpGet("init-dieu-khoan-bao-gia/{donViId}")]
        public async Task<ActionResult<string>> InitDieuKhoanBaoGia(string donViId)
        {
            try
            {
                var result = await _service.InitDieuKhoanBaoGiaAsync(donViId);
                if (result.Success)
                {
                    return Ok(new { content = result.Data, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing quotation terms for unit {DonViId}", donViId);
                return StatusCode(500, "Lỗi hệ thống khi khởi tạo điều khoản báo giá");
            }
        }

        /// <summary>
        /// Initialize terms content for repair form
        /// Exact implementation from form InitDieuKhoanLSC method (lines 8651-8667)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Repair terms content</returns>
        [HttpGet("init-dieu-khoan-lsc/{donViId}")]
        public async Task<ActionResult<string>> InitDieuKhoanLSC(string donViId)
        {
            try
            {
                var result = await _service.InitDieuKhoanLSCAsync(donViId);
                if (result.Success)
                {
                    return Ok(new { content = result.Data, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing repair terms for unit {DonViId}", donViId);
                return StatusCode(500, "Lỗi hệ thống khi khởi tạo điều khoản sửa chữa");
            }
        }

        /// <summary>
        /// Initialize terms content for settlement form
        /// Exact implementation from form InitDieuKhoanQuyetToan method (lines 8668-8684)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Settlement terms content</returns>
        [HttpGet("init-dieu-khoan-quyet-toan/{donViId}")]
        public async Task<ActionResult<string>> InitDieuKhoanQuyetToan(string donViId)
        {
            try
            {
                var result = await _service.InitDieuKhoanQuyetToanAsync(donViId);
                if (result.Success)
                {
                    return Ok(new { content = result.Data, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing settlement terms for unit {DonViId}", donViId);
                return StatusCode(500, "Lỗi hệ thống khi khởi tạo điều khoản quyết toán");
            }
        }

        /// <summary>
        /// Check if terms & conditions exists
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>True if exists</returns>
        [HttpGet("exists/{khoa}")]
        public async Task<ActionResult<bool>> Exists(string khoa)
        {
            try
            {
                var exists = await _service.ExistsAsync(khoa);
                return Ok(new { exists = exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if terms & conditions exists for {Khoa}", khoa);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra điều khoản báo giá");
            }
        }

        /// <summary>
        /// Check if terms & conditions code exists
        /// </summary>
        /// <param name="ma">Terms & conditions code</param>
        /// <param name="excludeKhoa">Exclude this ID from check (for updates)</param>
        /// <returns>True if exists</returns>
        [HttpGet("code-exists/{ma}")]
        public async Task<ActionResult<bool>> CodeExists(string ma, [FromQuery] string? excludeKhoa = null)
        {
            try
            {
                var exists = await _service.CodeExistsAsync(ma, excludeKhoa);
                return Ok(new { exists = exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if code exists for {Ma}", ma);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra mã điều khoản");
            }
        }

        /// <summary>
        /// Check if terms & conditions can be deleted
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>True if can be deleted</returns>
        [HttpGet("can-delete/{khoa}")]
        public async Task<ActionResult<bool>> CanDelete(string khoa)
        {
            try
            {
                var canDelete = await _service.CanDeleteAsync(khoa);
                return Ok(new { canDelete = canDelete });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if can delete terms & conditions for {Khoa}", khoa);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra quyền xóa điều khoản");
            }
        }

        /// <summary>
        /// Validate create data before saving
        /// </summary>
        /// <param name="createDto">Create data to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate")]
        public async Task<ActionResult> ValidateCreateData([FromBody] CreateDieuKhoanBaoGiaDto createDto)
        {
            try
            {
                var result = await _service.ValidateCreateDataAsync(createDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating create data");
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra dữ liệu");
            }
        }

        /// <summary>
        /// Validate update data before saving
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <param name="updateDto">Update data to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate/{khoa}")]
        public async Task<ActionResult> ValidateUpdateData(string khoa, [FromBody] UpdateDieuKhoanBaoGiaDto updateDto)
        {
            try
            {
                var result = await _service.ValidateUpdateDataAsync(khoa, updateDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating update data");
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra dữ liệu");
            }
        }
    }
}
