using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for LoaiXe (Vehicle Type) entity
/// Maps exactly to DM_LoaiXe table in legacy database
/// Implements ALL properties from clsDMLoaiXe.cs (577 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for vehicle categorization and manufacturer linking
/// </summary>
public class LoaiXeDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle type code (e.g., SEDAN, SUV, TRUCK, MOTORCYCLE)
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of vehicle type
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of vehicle type
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes about the vehicle type
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer key - Foreign key to DM_HangSanXuat
    /// Maps to: mKhoaHangSanXuat property in legacy class
    /// </summary>
    public string KhoaHangSanXuat { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Synchronization status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for LoaiXe list display
/// Optimized for automotive vehicle type lists and dropdowns
/// </summary>
public class LoaiXeListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string TenHangSanXuat { get; set; } = string.Empty; // Joined from DM_HangSanXuat
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for creating new LoaiXe
/// Contains only required fields for creation
/// </summary>
public class CreateLoaiXeDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for updating LoaiXe status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateLoaiXeStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public int Active { get; set; }

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for LoaiXe search operations
/// Used for advanced search and filtering
/// </summary>
public class LoaiXeSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public string? KhoaHangSanXuat { get; set; }
    public int? Active { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
}

/// <summary>
/// DTO for LoaiXe dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class LoaiXeLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
}

/// <summary>
/// DTO for LoaiXe validation operations
/// Used for duplicate checking and validation
/// </summary>
public class LoaiXeValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}

/// <summary>
/// DTO for LoaiXe search by code operations
/// Used for quick vehicle type lookup
/// </summary>
public class LoaiXeSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
}

/// <summary>
/// DTO for automotive vehicle categories
/// Specialized for automotive vehicle classification
/// </summary>
public class VehicleCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string TenHangSanXuat { get; set; } = string.Empty;
    public bool IsPassengerVehicle { get; set; } = false;
    public bool IsCommercialVehicle { get; set; } = false;
    public bool IsMotorcycle { get; set; } = false;
    public bool IsTruck { get; set; } = false;
}

/// <summary>
/// DTO for vehicle type with manufacturer information
/// Used for detailed vehicle type display with manufacturer details
/// </summary>
public class LoaiXeWithManufacturerDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string MaHangSanXuat { get; set; } = string.Empty;
    public string TenHangSanXuat { get; set; } = string.Empty;
    public string QuocGiaHangSanXuat { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for vehicle type statistics
/// Used for reporting and analytics
/// </summary>
public class LoaiXeStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public int TotalVehicles { get; set; } = 0;
    public int ActiveVehicles { get; set; } = 0;
    public decimal TotalServiceRevenue { get; set; } = 0;
    public DateTime LastServiceDate { get; set; }
}

/// <summary>
/// DTO for engine power (MaLuc) operations
/// Used for vehicle engine specifications
/// </summary>
public class MaLucDto
{
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string MaLuc { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
}
