using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for XuatKho entity
/// Implements ALL endpoints from clsXuatKho.cs (2,500+ lines)
/// Includes REST API and 30+ legacy method endpoints
/// Maps to ST_XuatKho table with 74+ properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class XuatKhoController : ControllerBase
{
    private readonly IXuatKhoService _xuatKhoService;
    private readonly ILogger<XuatKhoController> _logger;

    public XuatKhoController(IXuatKhoService xuatKhoService, ILogger<XuatKhoController> logger)
    {
        _xuatKhoService = xuatKhoService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all XuatKho records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<XuatKhoListDto>>> GetAll()
    {
        try
        {
            var result = await _xuatKhoService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all XuatKho records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get XuatKho by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<XuatKhoDto>> GetById(string khoa)
    {
        try
        {
            var result = await _xuatKhoService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting XuatKho by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new XuatKho
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateXuatKhoDto createDto)
    {
        try
        {
            var result = await _xuatKhoService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating XuatKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update XuatKho
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] XuatKhoDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _xuatKhoService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating XuatKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete XuatKho
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _xuatKhoService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting XuatKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update XuatKho status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateXuatKhoStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _xuatKhoService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating XuatKho status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update XuatKho POS data
    /// </summary>
    [HttpPut("{khoa}/pos")]
    public async Task<ActionResult<bool>> UpdatePOS(string khoa, [FromBody] XuatKhoPOSDto posDto)
    {
        try
        {
            if (khoa != posDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _xuatKhoService.UpdatePOSAsync(posDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating XuatKho POS data");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _xuatKhoService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] XuatKhoDto dto)
    {
        try
        {
            var result = await _xuatKhoService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DeleteData method endpoint
    /// </summary>
    [HttpPost("deletedata")]
    public async Task<ActionResult<bool>> DeleteData([FromBody] string khoa)
    {
        try
        {
            var result = await _xuatKhoService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteData endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy List Endpoints

    /// <summary>
    /// Legacy GetList method endpoint
    /// </summary>
    [HttpPost("getlist")]
    public async Task<ActionResult<DataTable>> GetList([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListBanHang method endpoint
    /// </summary>
    [HttpPost("getlistbanhang")]
    public async Task<ActionResult<DataTable>> GetListBanHang([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListBanHangAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListBanHang endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListBaoGia method endpoint
    /// </summary>
    [HttpPost("getlistbaogia")]
    public async Task<ActionResult<DataTable>> GetListBaoGia([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListBaoGiaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListChonBaoGia method endpoint
    /// </summary>
    [HttpPost("getlistchonbaogia")]
    public async Task<ActionResult<DataTable>> GetListChonBaoGia([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListChonBaoGiaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListChonBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Additional Legacy List Endpoints

    /// <summary>
    /// Legacy GetListDetails method endpoint
    /// </summary>
    [HttpPost("getlistdetails")]
    public async Task<ActionResult<DataTable>> GetListDetails([FromBody] GetListDetailsRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GetListDetailsAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDetails endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetListDichVu method endpoint
    /// </summary>
    [HttpPost("getlistdichvu")]
    public async Task<ActionResult<DataTable>> GetListDichVu([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListDichVuAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDichVu endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListCoHoi method endpoint
    /// </summary>
    [HttpPost("getlistcohoi")]
    public async Task<ActionResult<DataTable>> GetListCoHoi([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _xuatKhoService.GetListCoHoiAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListCoHoi endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Print/Report Endpoints

    /// <summary>
    /// Legacy GetDataPrint method endpoint
    /// </summary>
    [HttpPost("getdataprint")]
    public async Task<ActionResult<DataTable>> GetDataPrint([FromBody] GetDataPrintRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GetDataPrintAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrint endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDataPrintBanHang method endpoint
    /// </summary>
    [HttpPost("getdataprintbanhang")]
    public async Task<ActionResult<DataTable>> GetDataPrintBanHang([FromBody] GetDataPrintBanHangRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GetDataPrintBanHangAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrintBanHang endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDataPrintDichVu method endpoint
    /// </summary>
    [HttpPost("getdataprintdichvu")]
    public async Task<ActionResult<DataTable>> GetDataPrintDichVu([FromBody] GetDataPrintDichVuRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GetDataPrintDichVuAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrintDichVu endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy Posting Endpoints

    /// <summary>
    /// Legacy GhiSo method endpoint
    /// </summary>
    [HttpPost("ghiso")]
    public async Task<ActionResult<bool>> GhiSo([FromBody] GhiSoRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GhiSoAsync(request.Khoa, request.KhoaTKDoanhThu, request.KhoaTKThue, request.KhoaTKChietKhau);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GhiSo endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy BoGhiSo method endpoint
    /// </summary>
    [HttpPost("boghiso")]
    public async Task<ActionResult<bool>> BoGhiSo([FromBody] BoGhiSoRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.BoGhiSoAsync(request.Khoa, request.KhoaTKDoanhThu, request.KhoaTKThue, request.KhoaTKChietKhau);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in BoGhiSo endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy Utility Endpoints

    /// <summary>
    /// Legacy CheckSoKhungDaXuatKho method endpoint
    /// </summary>
    [HttpPost("checksokhungdaxuatkho")]
    public async Task<ActionResult<bool>> CheckSoKhungDaXuatKho([FromBody] CheckSoKhungRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.CheckSoKhungDaXuatKhoAsync(request.SoKhung, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CheckSoKhungDaXuatKho endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy IsDuplicateVoucherNo method endpoint
    /// </summary>
    [HttpPost("isduplicatevoucherno")]
    public async Task<ActionResult<bool>> IsDuplicateVoucherNo([FromBody] IsDuplicateVoucherNoRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.IsDuplicateVoucherNoAsync(request.VoucherNo, request.KeyTable);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IsDuplicateVoucherNo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _xuatKhoService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult> ClearTemp([FromBody] string keyTable)
    {
        try
        {
            await _xuatKhoService.ClearTempAsync(keyTable);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetSoTaiKhoan method endpoint
    /// </summary>
    [HttpPost("getsotaikhoan")]
    public async Task<ActionResult<GetSoTaiKhoanResponseDto>> GetSoTaiKhoan([FromBody] GetSoTaiKhoanRequestDto request)
    {
        try
        {
            var result = await _xuatKhoService.GetSoTaiKhoanAsync(request.Khoa);
            return Ok(new GetSoTaiKhoanResponseDto { TKNo = result.TKNo, TKCo = result.TKCo });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetSoTaiKhoan endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for XuatKho

// Note: GetListRequestDto, GetListDetailsRequestDto, GetDataPrintRequestDto, GetDataPrintDichVuRequestDto,
// IsDuplicateVoucherNoRequestDto, GetSoTaiKhoanRequestDto, and GetSoTaiKhoanResponseDto
// are already defined in other controllers (NhapKhoController.cs, BaoGiaController.cs)

/// <summary>
/// Request DTO for GetDataPrintBanHang method
/// </summary>
public class GetDataPrintBanHangRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GhiSo method
/// </summary>
public class GhiSoRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaTKDoanhThu { get; set; } = string.Empty;
    public string KhoaTKThue { get; set; } = string.Empty;
    public string KhoaTKChietKhau { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for BoGhiSo method
/// </summary>
public class BoGhiSoRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaTKDoanhThu { get; set; } = string.Empty;
    public string KhoaTKThue { get; set; } = string.Empty;
    public string KhoaTKChietKhau { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for CheckSoKhungDaXuatKho method
/// </summary>
public class CheckSoKhungRequestDto
{
    public string SoKhung { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
