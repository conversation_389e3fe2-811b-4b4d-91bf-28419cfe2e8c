using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for NhomHangHoa (Product Groups) entity
/// Implements ALL endpoints from clsDMNhomHangHoa.cs (781 lines)
/// Includes REST API and 12+ legacy method endpoints
/// Maps to DM_NhomHangHoa table with 13 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts categorization and product group management
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class NhomHangHoaController : ControllerBase
{
    private readonly INhomHangHoaService _nhomHangHoaService;
    private readonly ILogger<NhomHangHoaController> _logger;

    public NhomHangHoaController(INhomHangHoaService nhomHangHoaService, ILogger<NhomHangHoaController> logger)
    {
        _nhomHangHoaService = nhomHangHoaService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all product groups
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<NhomHangHoaListDto>>> GetAll()
    {
        try
        {
            var result = await _nhomHangHoaService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all product groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product group by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<NhomHangHoaDto>> GetById(string khoa)
    {
        try
        {
            var result = await _nhomHangHoaService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product group by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product group by code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<NhomHangHoaDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _nhomHangHoaService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product group by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product group by name
    /// </summary>
    [HttpGet("name/{tenViet}")]
    public async Task<ActionResult<NhomHangHoaDto>> GetByName(string tenViet)
    {
        try
        {
            var result = await _nhomHangHoaService.GetByNameAsync(tenViet);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product group by name");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new product group
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateNhomHangHoaDto createDto)
    {
        try
        {
            var result = await _nhomHangHoaService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo nhóm hàng hóa");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product group");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update product group
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] NhomHangHoaDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhomHangHoaService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product group");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete product group
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _nhomHangHoaService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product group");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update product group status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateNhomHangHoaStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhomHangHoaService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product group status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search product groups
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<NhomHangHoaListDto>>> Search([FromBody] NhomHangHoaSearchDto searchDto)
    {
        try
        {
            var result = await _nhomHangHoaService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching product groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product group lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<NhomHangHoaLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _nhomHangHoaService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product group lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate product group data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<NhomHangHoaValidationDto>> Validate([FromBody] NhomHangHoaValidationRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.ValidateAsync(request.Khoa, request.Ma, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating product group");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search product group by code (modern)
    /// </summary>
    [HttpPost("search-by-code")]
    public async Task<ActionResult<NhomHangHoaSearchByCodeDto>> SearchByCodeModern([FromBody] NhomHangHoaSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.SearchByCodeModernAsync(request.Code, request.KeyFilter, request.FieldNameFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching product group by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive parts groups
    /// </summary>
    [HttpGet("automotive-parts")]
    public async Task<ActionResult<IEnumerable<AutomotivePartsGroupDto>>> GetAutomotivePartsGroups()
    {
        try
        {
            var result = await _nhomHangHoaService.GetAutomotivePartsGroupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive parts groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product groups with statistics
    /// </summary>
    [HttpGet("with-stats")]
    public async Task<ActionResult<IEnumerable<NhomHangHoaWithStatsDto>>> GetGroupsWithStats()
    {
        try
        {
            var result = await _nhomHangHoaService.GetGroupsWithStatsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting groups with stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get raw material groups
    /// </summary>
    [HttpGet("raw-materials")]
    public async Task<ActionResult<IEnumerable<RawMaterialGroupDto>>> GetRawMaterialGroups()
    {
        try
        {
            var result = await _nhomHangHoaService.GetRawMaterialGroupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting raw material groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get menu groups
    /// </summary>
    [HttpGet("menu")]
    public async Task<ActionResult<IEnumerable<MenuGroupDto>>> GetMenuGroups()
    {
        try
        {
            var result = await _nhomHangHoaService.GetMenuGroupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting menu groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _nhomHangHoaService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadName method endpoint
    /// </summary>
    [HttpPost("loadname")]
    public async Task<ActionResult<bool>> LoadName([FromBody] string tenViet)
    {
        try
        {
            var result = await _nhomHangHoaService.LoadNameAsync(tenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadName endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] NhomHangHoaDto dto)
    {
        try
        {
            var result = await _nhomHangHoaService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _nhomHangHoaService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] NhomHangHoaShowListRequestDto? request = null)
    {
        try
        {
            var conditions = request?.Conditions ?? "";
            var result = await _nhomHangHoaService.ShowListAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllListNhomHangHoa method endpoint
    /// </summary>
    [HttpPost("showalllistnhomhanghoa")]
    public async Task<ActionResult<DataTable>> ShowAllListNhomHangHoa([FromBody] NhomHangHoaShowListRequestDto? request = null)
    {
        try
        {
            var conditions = request?.Conditions ?? "";
            var result = await _nhomHangHoaService.ShowAllListNhomHangHoaAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListNhomHangHoa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllListNguyenLieu method endpoint
    /// </summary>
    [HttpPost("showalllistnguyenlieu")]
    public async Task<ActionResult<DataTable>> ShowAllListNguyenLieu()
    {
        try
        {
            var result = await _nhomHangHoaService.ShowAllListNguyenLieuAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListNguyenLieu endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllListThucDon method endpoint
    /// </summary>
    [HttpPost("showalllistthucdon")]
    public async Task<ActionResult<DataTable>> ShowAllListThucDon()
    {
        try
        {
            var result = await _nhomHangHoaService.ShowAllListThucDonAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListThucDon endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] NhomHangHoaShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByFieldThucDon method endpoint
    /// </summary>
    [HttpPost("showlistbyfieldthucdon")]
    public async Task<ActionResult<DataTable>> ShowListByFieldThucDon([FromBody] NhomHangHoaShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.ShowListByFieldThucDonAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByFieldThucDon endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] NhomHangHoaSearchByCodeLegacyRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.SearchByCodeAsync(request.Code, request.KeyFilter, request.FieldNameFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] NhomHangHoaTrungMaRequestDto request)
    {
        try
        {
            var result = await _nhomHangHoaService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _nhomHangHoaService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for NhomHangHoa

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class NhomHangHoaValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for search by code (modern) endpoint
/// </summary>
public class NhomHangHoaSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string KeyFilter { get; set; } = string.Empty;
    public string FieldNameFilter { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class NhomHangHoaShowListRequestDto
{
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class NhomHangHoaShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method (legacy)
/// </summary>
public class NhomHangHoaSearchByCodeLegacyRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string KeyFilter { get; set; } = string.Empty;
    public string FieldNameFilter { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class NhomHangHoaTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
