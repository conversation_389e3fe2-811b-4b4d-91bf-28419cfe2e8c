using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for LoaiDichVu (Service Type) service
/// Defines business logic operations for LoaiDichVu entity
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
public interface ILoaiDichVuService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(LoaiDichVuDto dto);
    Task<bool> DelDataAsync(string khoa);
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LoaiDichVuListDto>> GetAllAsync();
    Task<LoaiDichVuDto?> GetByIdAsync(string khoa);
    Task<LoaiDichVuDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateLoaiDichVuDto createDto);
    Task<bool> UpdateAsync(LoaiDichVuDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateLoaiDichVuStatusDto statusDto);
    Task<IEnumerable<LoaiDichVuListDto>> SearchAsync(LoaiDichVuSearchDto searchDto);
    Task<IEnumerable<LoaiDichVuLookupDto>> GetLookupAsync(string language = "vi");
    Task<LoaiDichVuValidationDto> ValidateAsync(string khoa, string ma);
    Task<LoaiDichVuSearchByCodeDto?> SearchServiceByCodeAsync(string code, string condition = "");
    Task<IEnumerable<ServiceCategoryDto>> GetServiceCategoriesAsync();
    Task<LoaiDichVuStatsDto?> GetServiceStatsAsync(string khoa);
    
    #endregion
}

/// <summary>
/// Complete Service for LoaiDichVu entity
/// Implements ALL business logic from clsDMLoaiDichVu.cs (468 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
public class LoaiDichVuService : ILoaiDichVuService
{
    private readonly ILoaiDichVuRepository _repository;
    private readonly ILogger<LoaiDichVuService> _logger;

    public LoaiDichVuService(ILoaiDichVuRepository repository, ILogger<LoaiDichVuService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiDichVu");
            throw;
        }
    }

    public async Task<bool> SaveAsync(LoaiDichVuDto dto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto);

            return await _repository.SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving LoaiDichVu");
            throw;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa loại dịch vụ đã được sử dụng");
            }

            return await _repository.DelDataAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiDichVu");
            throw;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Apply security filters
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.ShowListAsync(secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiDichVu list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            return await _repository.ShowAllListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all LoaiDichVu list");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiDichVu by code");
            return "";
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fieldList);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.ShowListByFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiDichVu list by field");
            return new DataTable();
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                return false;
            }

            return await _repository.TrungMaAsync(ma, khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate service type code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                return false;
            }

            return await _repository.WasUsedAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if LoaiDichVu was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LoaiDichVuListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiDichVu records");
            return new List<LoaiDichVuListDto>();
        }
    }

    public async Task<LoaiDichVuDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by ID");
            return null;
        }
    }

    public async Task<LoaiDichVuDto?> GetByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
                return null;

            return await _repository.GetByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by code");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateLoaiDichVuDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiDichVu");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(LoaiDichVuDto dto)
    {
        return await SaveAsync(dto);
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DelDataAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateLoaiDichVuStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiDichVu status");
            throw;
        }
    }

    public async Task<IEnumerable<LoaiDichVuListDto>> SearchAsync(LoaiDichVuSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiDichVu");
            return new List<LoaiDichVuListDto>();
        }
    }

    public async Task<IEnumerable<LoaiDichVuLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu lookup");
            return new List<LoaiDichVuLookupDto>();
        }
    }

    public async Task<LoaiDichVuValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiDichVu");
            return new LoaiDichVuValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<LoaiDichVuSearchByCodeDto?> SearchServiceByCodeAsync(string code, string condition = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;

            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchServiceByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching service by code");
            return null;
        }
    }

    public async Task<IEnumerable<ServiceCategoryDto>> GetServiceCategoriesAsync()
    {
        try
        {
            return await _repository.GetServiceCategoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service categories");
            return new List<ServiceCategoryDto>();
        }
    }

    public async Task<LoaiDichVuStatsDto?> GetServiceStatsAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetServiceStatsAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service stats");
            return null;
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic for Automotive Services)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(LoaiDichVuDto dto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã loại dịch vụ không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên loại dịch vụ (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate service type codes
        if (!string.IsNullOrEmpty(dto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(dto.Ma, dto.Khoa);
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại dịch vụ đã tồn tại");
            }
        }

        // Length validation
        if (dto.Ma.Length > 20)
            result.Errors.Add("Mã loại dịch vụ không được vượt quá 20 ký tự");

        if (dto.TenViet.Length > 200)
            result.Errors.Add("Tên loại dịch vụ (Tiếng Việt) không được vượt quá 200 ký tự");

        if (dto.TenAnh.Length > 200)
            result.Errors.Add("Tên loại dịch vụ (Tiếng Anh) không được vượt quá 200 ký tự");

        if (dto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Business validation
        if (dto.Active != 0 && dto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Date validation
        if (!string.IsNullOrEmpty(dto.TuNgay) && !IsValidDateFormat(dto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Service type code format validation for automotive services
        if (!string.IsNullOrEmpty(dto.Ma) && !IsValidServiceCode(dto.Ma))
            result.Errors.Add("Mã loại dịch vụ phải là chữ cái và số, không có ký tự đặc biệt");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateLoaiDichVuDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.Ma))
            result.Errors.Add("Mã loại dịch vụ không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên loại dịch vụ (Tiếng Việt) không được để trống");

        // Check for duplicate service type codes
        if (!string.IsNullOrEmpty(createDto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(createDto.Ma, "");
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại dịch vụ đã tồn tại");
            }
        }

        // Length validation
        if (createDto.Ma.Length > 20)
            result.Errors.Add("Mã loại dịch vụ không được vượt quá 20 ký tự");

        if (createDto.TenViet.Length > 200)
            result.Errors.Add("Tên loại dịch vụ (Tiếng Việt) không được vượt quá 200 ký tự");

        if (createDto.TenAnh.Length > 200)
            result.Errors.Add("Tên loại dịch vụ (Tiếng Anh) không được vượt quá 200 ký tự");

        if (createDto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Date validation
        if (!string.IsNullOrEmpty(createDto.TuNgay) && !IsValidDateFormat(createDto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Service type code format validation
        if (!string.IsNullOrEmpty(createDto.Ma) && !IsValidServiceCode(createDto.Ma))
            result.Errors.Add("Mã loại dịch vụ phải là chữ cái và số, không có ký tự đặc biệt");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the service type is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateLoaiDichVuStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (statusDto.Active != 0 && statusDto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Business rule: Cannot deactivate if being used
        if (statusDto.Active == 0)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động loại dịch vụ đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(LoaiDichVuDto dto)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa))
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim().ToUpper(); // Service codes should be uppercase
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        dto.Send = 0; // Not synchronized yet

        // Set default effective date if empty
        if (string.IsNullOrEmpty(dto.TuNgay))
        {
            dto.TuNgay = DateTime.Now.ToString("yyyyMMdd");
        }

        // Automotive service specific business rules
        await ApplyAutomotiveServiceRulesAsync(dto);
    }

    private async Task ApplyAutomotiveServiceRulesAsync(LoaiDichVuDto dto)
    {
        // Automotive service specific validations and rules
        var serviceCode = dto.Ma.ToUpper();

        // Standardize common automotive service codes
        if (serviceCode.Contains("MAINTENANCE") || serviceCode.Contains("BAODUNG"))
        {
            // Ensure maintenance services have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Dịch vụ bảo dưỡng định kỳ";
            }
        }
        else if (serviceCode.Contains("REPAIR") || serviceCode.Contains("SUACHUA"))
        {
            // Ensure repair services have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Dịch vụ sửa chữa";
            }
        }
        else if (serviceCode.Contains("INSPECTION") || serviceCode.Contains("KIEMTRA"))
        {
            // Ensure inspection services have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Dịch vụ kiểm tra, đăng kiểm";
            }
        }
        else if (serviceCode.Contains("WARRANTY") || serviceCode.Contains("BAOHANH"))
        {
            // Ensure warranty services have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Dịch vụ bảo hành";
            }
        }
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show service types user has access to
        // - Filter by user's service center/branch

        return conditions;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DienGiai", "TuNgay",
            "KhoaNhanVienCapNhat", "Active", "Send"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    private bool IsValidDateFormat(string date)
    {
        // Validate YYYYMMDD format
        if (string.IsNullOrEmpty(date) || date.Length != 8)
            return false;

        return DateTime.TryParseExact(date, "yyyyMMdd", null,
            System.Globalization.DateTimeStyles.None, out _);
    }

    private bool IsValidServiceCode(string code)
    {
        // Service codes should be alphanumeric (letters and numbers only)
        if (string.IsNullOrEmpty(code))
            return false;

        return code.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-');
    }

    #endregion
}
