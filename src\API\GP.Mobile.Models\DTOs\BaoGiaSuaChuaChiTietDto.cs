using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoGiaSuaChuaChiTiet (Service Quotation Repair Detail) entity
/// Maps exactly to SC_BaoGiaChiTiet table in legacy database
/// Implements ALL properties from clsBaoGiaSuaChuaChiTiet.cs (551 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive repair quotation detail line items and service pricing breakdown
/// </summary>
public class BaoGiaSuaChuaChiTietDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Parent quotation reference
    /// Maps to: mKhoaBaoGia property in legacy class
    /// </summary>
    [Required]
    public string KhoaBaoGia { get; set; } = string.Empty;

    /// <summary>
    /// Repair category reference
    /// Maps to: mKhoaHangMuc property in legacy class
    /// </summary>
    public string KhoaHangMuc { get; set; } = string.Empty;

    /// <summary>
    /// Content/Description of repair item
    /// Maps to: mNoiDung property in legacy class
    /// </summary>
    [StringLength(500)]
    public string NoiDung { get; set; } = string.Empty;

    /// <summary>
    /// Discount percentage 1
    /// Maps to: mTyLeChietKhau1 property in legacy class
    /// </summary>
    public int TyLeChietKhau1 { get; set; } = 0;

    /// <summary>
    /// Discount percentage 2
    /// Maps to: mTyLeChietKhau2 property in legacy class
    /// </summary>
    public int TyLeChietKhau2 { get; set; } = 0;

    /// <summary>
    /// Quantity 1 (primary quantity)
    /// Maps to: mSoLuong1 property in legacy class
    /// </summary>
    public decimal SoLuong1 { get; set; } = 0;

    /// <summary>
    /// Unit price 1 (primary price)
    /// Maps to: mDonGia1 property in legacy class
    /// </summary>
    public decimal DonGia1 { get; set; } = 0;

    /// <summary>
    /// Amount 1 (primary amount = SoLuong1 * DonGia1)
    /// Maps to: mThanhTien1 property in legacy class
    /// </summary>
    public decimal ThanhTien1 { get; set; } = 0;

    /// <summary>
    /// Discount amount 1
    /// Maps to: mTienChietKhau1 property in legacy class
    /// </summary>
    public decimal TienChietKhau1 { get; set; } = 0;

    /// <summary>
    /// Quantity 2 (secondary quantity)
    /// Maps to: mSoLuong2 property in legacy class
    /// </summary>
    public decimal SoLuong2 { get; set; } = 0;

    /// <summary>
    /// Unit price 2 (secondary price)
    /// Maps to: mDonGia2 property in legacy class
    /// </summary>
    public decimal DonGia2 { get; set; } = 0;

    /// <summary>
    /// Amount 2 (secondary amount = SoLuong2 * DonGia2)
    /// Maps to: mThanhTien2 property in legacy class
    /// </summary>
    public decimal ThanhTien2 { get; set; } = 0;

    /// <summary>
    /// Discount amount 2
    /// Maps to: mTienChietKhau2 property in legacy class
    /// </summary>
    public decimal TienChietKhau2 { get; set; } = 0;

    /// <summary>
    /// Description/Notes for this line item
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Type/Category (0=Service, 1=Parts)
    /// Maps to: mLoai property in legacy class
    /// </summary>
    public int Loai { get; set; } = 0;

    /// <summary>
    /// Product/Item reference
    /// Maps to: mKhoaHangHoa property in legacy class
    /// </summary>
    public string KhoaHangHoa { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGiaSuaChuaChiTiet list display with joined data
/// Optimized for automotive repair quotation detail lists with related information
/// Used by list and search operations
/// </summary>
public class BaoGiaSuaChuaChiTietListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty; // From parent quotation
    public string KhoaHangMuc { get; set; } = string.Empty;
    public string HangMuc { get; set; } = string.Empty; // Repair category name
    public string NoiDung { get; set; } = string.Empty;
    public decimal SoLuong1 { get; set; } = 0;
    public decimal DonGia1 { get; set; } = 0;
    public decimal ThanhTien1 { get; set; } = 0;
    public decimal TienChietKhau1 { get; set; } = 0;
    public decimal SoLuong2 { get; set; } = 0;
    public decimal DonGia2 { get; set; } = 0;
    public decimal ThanhTien2 { get; set; } = 0;
    public decimal TienChietKhau2 { get; set; } = 0;
    public int TyLeChietKhau1 { get; set; } = 0;
    public int TyLeChietKhau2 { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string LoaiText { get; set; } = string.Empty; // Service/Parts
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string HangHoa { get; set; } = string.Empty; // Product name
    public decimal TongThanhTien { get; set; } = 0; // ThanhTien1 + ThanhTien2
    public decimal TongChietKhau { get; set; } = 0; // TienChietKhau1 + TienChietKhau2
    public decimal ThanhTienSauChietKhau { get; set; } = 0; // After discount
    public bool IsService { get; set; } = false;
    public bool IsParts { get; set; } = false;
    public string ItemType { get; set; } = string.Empty;
    public string ItemTypeColor { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new BaoGiaSuaChuaChiTiet
/// Contains only required fields for creation
/// </summary>
public class CreateBaoGiaSuaChuaChiTietDto
{
    [Required]
    public string KhoaBaoGia { get; set; } = string.Empty;

    public string KhoaHangMuc { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string NoiDung { get; set; } = string.Empty;

    public decimal SoLuong1 { get; set; } = 0;
    public decimal DonGia1 { get; set; } = 0;
    public int TyLeChietKhau1 { get; set; } = 0;
    public decimal SoLuong2 { get; set; } = 0;
    public decimal DonGia2 { get; set; } = 0;
    public int TyLeChietKhau2 { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string KhoaHangHoa { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating BaoGiaSuaChuaChiTiet
/// Used for modification operations
/// </summary>
public class UpdateBaoGiaSuaChuaChiTietDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public string KhoaHangMuc { get; set; } = string.Empty;
    public string NoiDung { get; set; } = string.Empty;
    public decimal SoLuong1 { get; set; } = 0;
    public decimal DonGia1 { get; set; } = 0;
    public int TyLeChietKhau1 { get; set; } = 0;
    public decimal SoLuong2 { get; set; } = 0;
    public decimal DonGia2 { get; set; } = 0;
    public int TyLeChietKhau2 { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string KhoaHangHoa { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGiaSuaChuaChiTiet search operations
/// Used for advanced search and filtering
/// </summary>
public class BaoGiaSuaChuaChiTietSearchDto
{
    public string? KhoaBaoGia { get; set; }
    public string? KhoaHangMuc { get; set; }
    public string? NoiDung { get; set; }
    public int? Loai { get; set; }
    public string? KhoaHangHoa { get; set; }
    public decimal? SoLuong1From { get; set; }
    public decimal? SoLuong1To { get; set; }
    public decimal? DonGia1From { get; set; }
    public decimal? DonGia1To { get; set; }
    public decimal? ThanhTien1From { get; set; }
    public decimal? ThanhTien1To { get; set; }
    public bool? IsService { get; set; }
    public bool? IsParts { get; set; }
}

/// <summary>
/// DTO for automotive repair detail
/// Specialized for automotive repair quotation detail management
/// </summary>
public class AutomotiveRepairDetailDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public string BienSoXe { get; set; } = string.Empty;
    public string HangMuc { get; set; } = string.Empty;
    public string NoiDung { get; set; } = string.Empty;
    public bool IsBodyPaintWork { get; set; } = false;
    public bool IsEngineWork { get; set; } = false;
    public bool IsElectricalWork { get; set; } = false;
    public bool IsACWork { get; set; } = false;
    public bool IsBrakeWork { get; set; } = false;
    public bool IsSuspensionWork { get; set; } = false;
    public bool IsTransmissionWork { get; set; } = false;
    public bool IsTireWork { get; set; } = false;
    public bool IsGlassWork { get; set; } = false;
    public bool IsInteriorWork { get; set; } = false;
    public bool IsServiceWork { get; set; } = false;
    public bool IsPartsWork { get; set; } = false;
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public decimal TienChietKhau { get; set; } = 0;
    public decimal ThanhTienSauChietKhau { get; set; } = 0;
    public int TyLeChietKhau { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public string WorkType { get; set; } = string.Empty;
    public string WorkCategory { get; set; } = string.Empty;
}

/// <summary>
/// DTO for repair quotation detail summary
/// Used for financial reporting and analysis
/// </summary>
public class RepairQuotationDetailSummaryDto
{
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public int TotalItems { get; set; } = 0;
    public int ServiceItems { get; set; } = 0;
    public int PartsItems { get; set; } = 0;
    public decimal TotalServiceAmount { get; set; } = 0;
    public decimal TotalPartsAmount { get; set; } = 0;
    public decimal TotalAmount { get; set; } = 0;
    public decimal TotalDiscount { get; set; } = 0;
    public decimal TotalAmountAfterDiscount { get; set; } = 0;
    public decimal AverageServicePrice { get; set; } = 0;
    public decimal AveragePartsPrice { get; set; } = 0;
    public decimal ServicePercentage { get; set; } = 0;
    public decimal PartsPercentage { get; set; } = 0;
    public decimal DiscountPercentage { get; set; } = 0;
    public bool HasHighValueItems { get; set; } = false;
    public bool HasDiscountedItems { get; set; } = false;
}

/// <summary>
/// DTO for repair content/description
/// Used for repair content management and templates
/// </summary>
public class RepairContentDto
{
    public string Khoa { get; set; } = string.Empty;
    public string HangMuc { get; set; } = string.Empty;
    public string NoiDung { get; set; } = string.Empty;
    public decimal SoLuong { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public bool IsTemplate { get; set; } = false;
    public bool IsFrequentlyUsed { get; set; } = false;
    public int UsageCount { get; set; } = 0;
    public DateTime? LastUsed { get; set; }
}

/// <summary>
/// DTO for bulk operations on repair quotation details
/// Used for batch create/update/delete operations
/// </summary>
public class BulkBaoGiaSuaChuaChiTietDto
{
    public string KhoaBaoGia { get; set; } = string.Empty;
    public List<CreateBaoGiaSuaChuaChiTietDto> ItemsToCreate { get; set; } = new();
    public List<UpdateBaoGiaSuaChuaChiTietDto> ItemsToUpdate { get; set; } = new();
    public List<string> ItemsToDelete { get; set; } = new();
    public bool ClearExistingItems { get; set; } = false;
    public bool RecalculateAmounts { get; set; } = true;
}
