<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GP Mobile API Test - Web Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 20px;
        }
        .test-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #E8F5E8;
            border: 1px solid #4CAF50;
            color: #2E7D32;
        }
        .error {
            background: #FFEBEE;
            border: 1px solid #F44336;
            color: #C62828;
        }
        .info {
            background: #E3F2FD;
            border: 1px solid #2196F3;
            color: #1565C0;
        }
        .config {
            background: #F3E5F5;
            border: 1px solid #9C27B0;
            color: #6A1B9A;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1 class="title">🔧 GP Mobile API Test - Web Version</h1>
    
    <div class="config">
        <h3>🌐 Configuration</h3>
        <div><strong>API URL:</strong> http://localhost:5001</div>
        <div><strong>Database:</strong> carsoft_giaphat (SQL Server 2014)</div>
        <div><strong>Authentication:</strong> Windows Authentication</div>
        <div><strong>Platform:</strong> Web Browser</div>
        <div><strong>Your IP:</strong> ************</div>
    </div>

    <div class="container">
        <h3>🔍 API Connection Tests</h3>
        
        <div class="test-section">
            <button class="test-button" onclick="testHealth()">Test API Health</button>
            <button class="test-button" onclick="testDatabase()">Test Database Connection</button>
            <button class="test-button" onclick="testTables()">Test Tables</button>
            <button class="test-button" onclick="testUsers()">Test Users</button>
            <button class="test-button" onclick="testDatabases()">List Databases</button>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        </div>

        <div id="results"></div>
    </div>

    <div class="container">
        <h3>📊 API Endpoints</h3>
        <div class="test-section">
            <button class="test-button" onclick="openSwagger()">Open Swagger UI</button>
            <button class="test-button" onclick="openHealthDirect()">Open Health Check</button>
            <button class="test-button" onclick="openDatabaseTest()">Open Database Test</button>
        </div>
    </div>

    <div class="container">
        <h3>📱 Mobile Testing</h3>
        <div class="info">
            <strong>For iPhone Testing:</strong><br>
            1. Make sure your iPhone and computer are on the same WiFi<br>
            2. Install "Expo Go" app from App Store<br>
            3. Scan the QR code from the Expo terminal<br>
            4. The mobile app will connect to: http://************:5001<br><br>
            
            <strong>Current Status:</strong><br>
            ✅ Backend API: Running on http://localhost:5001<br>
            ✅ Database: carsoft_giaphat connected (SQL Server 2014)<br>
            ✅ Expo: Running on exp://************:8081<br>
            📱 Ready for iPhone testing!
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';
        
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.innerHTML = `<h4>${title}</h4><div class="result ${type}">${content}</div>`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testHealth() {
            try {
                addResult('🔍 Testing API Health...', 'Connecting to API...', 'info');
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                addResult('✅ API Health Check', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ API Health Check Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function testDatabase() {
            try {
                addResult('🔍 Testing Database Connection...', 'Connecting to CARSOFT_GIAPHAT...', 'info');
                const response = await fetch(`${API_BASE}/api/test-database`);
                const data = await response.json();
                addResult('✅ Database Connection', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ Database Connection Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function testTables() {
            try {
                addResult('🔍 Testing Tables...', 'Checking required tables...', 'info');
                const response = await fetch(`${API_BASE}/api/test-tables`);
                const data = await response.json();
                addResult('✅ Tables Check', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ Tables Check Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function testUsers() {
            try {
                addResult('🔍 Testing Users...', 'Checking user data...', 'info');
                const response = await fetch(`${API_BASE}/api/test-users`);
                const data = await response.json();
                addResult('✅ Users Check', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ Users Check Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function testDatabases() {
            try {
                addResult('🔍 Testing Database List...', 'Checking available databases...', 'info');
                const response = await fetch(`${API_BASE}/api/test-databases`);
                const data = await response.json();
                addResult('✅ Database List', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ Database List Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            document.getElementById('results').innerHTML = '';
            addResult('🚀 Running All Tests', 'Starting comprehensive API tests...', 'info');
            
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDatabase();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTables();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUsers();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testDatabases();

            addResult('🎉 All Tests Complete', 'Check results above for any issues.', 'success');
        }

        function openSwagger() {
            window.open(`${API_BASE}/swagger`, '_blank');
        }

        function openHealthDirect() {
            window.open(`${API_BASE}/api/health`, '_blank');
        }

        function openDatabaseTest() {
            window.open(`${API_BASE}/api/test-database`, '_blank');
        }

        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(runAllTests, 1000);
        };
    </script>
</body>
</html>
