'use client'

import { useState } from 'react'
import { Plus, Search, Package, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { withAuth } from '@/contexts/AuthContext'

// Mock inventory data
const mockInventory = [
  {
    id: 'SP001',
    code: 'PH001',
    name: '<PERSON><PERSON> phanh trước Toyota Camry',
    category: 'Phanh',
    brand: 'Toyota',
    unit: 'Bộ',
    currentStock: 15,
    minStock: 5,
    maxStock: 50,
    unitPrice: 450000,
    location: 'Kệ A1-01',
    status: 'Còn hàng',
    lastUpdated: '2024-06-15'
  },
  {
    id: 'SP002',
    code: 'DM001',
    name: 'Dầu máy 5W-30 Castrol',
    category: 'Dầu nhờn',
    brand: 'Castrol',
    unit: 'Lít',
    currentStock: 3,
    minStock: 10,
    maxStock: 100,
    unitPrice: 120000,
    location: 'Kệ B2-05',
    status: 'Sắp hết',
    lastUpdated: '2024-06-14'
  },
  {
    id: 'SP003',
    code: 'LD001',
    name: 'Lọc dầu Honda City',
    category: 'Lọc',
    brand: 'Honda',
    unit: 'Cái',
    currentStock: 25,
    minStock: 8,
    maxStock: 40,
    unitPrice: 80000,
    location: 'Kệ A2-03',
    status: 'Còn hàng',
    lastUpdated: '2024-06-13'
  },
  {
    id: 'SP004',
    code: 'AC001',
    name: 'Compressor điều hòa Mazda 3',
    category: 'Điều hòa',
    brand: 'Mazda',
    unit: 'Cái',
    currentStock: 0,
    minStock: 2,
    maxStock: 10,
    unitPrice: 2500000,
    location: 'Kệ C1-01',
    status: 'Hết hàng',
    lastUpdated: '2024-06-10'
  }
]

function InventoryPage() {
  const [inventory, setInventory] = useState(mockInventory)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')

  // Get unique categories
  const categories = Array.from(new Set(inventory.map(item => item.category)))

  // Filter inventory
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.brand.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const getStatusBadge = (status: string, currentStock: number, minStock: number) => {
    if (currentStock === 0) {
      return <Badge variant="destructive">Hết hàng</Badge>
    } else if (currentStock <= minStock) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Sắp hết</Badge>
    } else {
      return <Badge variant="default" className="bg-green-100 text-green-800">Còn hàng</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  // Calculate stats
  const totalItems = inventory.length
  const inStock = inventory.filter(item => item.currentStock > item.minStock).length
  const lowStock = inventory.filter(item => item.currentStock <= item.minStock && item.currentStock > 0).length
  const outOfStock = inventory.filter(item => item.currentStock === 0).length
  const totalValue = inventory.reduce((sum, item) => sum + (item.currentStock * item.unitPrice), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản lý kho hàng</h1>
          <p className="text-muted-foreground">
            Quản lý tồn kho phụ tùng và vật tư
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingDown className="mr-2 h-4 w-4" />
            Nhập kho
          </Button>
          <Button variant="outline">
            <TrendingUp className="mr-2 h-4 w-4" />
            Xuất kho
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Thêm sản phẩm
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng sản phẩm</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Còn hàng</CardTitle>
            <Package className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inStock}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sắp hết</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lowStock}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hết hàng</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{outOfStock}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Giá trị tồn kho</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{formatCurrency(totalValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="inventory" className="space-y-4">
        <TabsList>
          <TabsTrigger value="inventory">Tồn kho</TabsTrigger>
          <TabsTrigger value="movements">Xuất nhập kho</TabsTrigger>
          <TabsTrigger value="reports">Báo cáo</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Tìm kiếm và lọc</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm theo tên, mã sản phẩm, hãng..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Danh mục" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả danh mục</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả trạng thái</SelectItem>
                    <SelectItem value="Còn hàng">Còn hàng</SelectItem>
                    <SelectItem value="Sắp hết">Sắp hết</SelectItem>
                    <SelectItem value="Hết hàng">Hết hàng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Inventory List */}
          <Card>
            <CardHeader>
              <CardTitle>Danh sách tồn kho</CardTitle>
              <CardDescription>
                Tổng cộng {filteredInventory.length} sản phẩm
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Mã SP</TableHead>
                      <TableHead>Tên sản phẩm</TableHead>
                      <TableHead>Danh mục</TableHead>
                      <TableHead>Hãng</TableHead>
                      <TableHead className="text-center">Tồn kho</TableHead>
                      <TableHead className="text-center">Tối thiểu</TableHead>
                      <TableHead>Trạng thái</TableHead>
                      <TableHead className="text-right">Đơn giá</TableHead>
                      <TableHead>Vị trí</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.code}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-muted-foreground">ĐVT: {item.unit}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{item.category}</Badge>
                        </TableCell>
                        <TableCell>{item.brand}</TableCell>
                        <TableCell className="text-center font-medium">
                          <span className={item.currentStock <= item.minStock ? 'text-red-600' : ''}>
                            {item.currentStock}
                          </span>
                        </TableCell>
                        <TableCell className="text-center">{item.minStock}</TableCell>
                        <TableCell>{getStatusBadge(item.status, item.currentStock, item.minStock)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                        <TableCell>{item.location}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="movements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lịch sử xuất nhập kho</CardTitle>
              <CardDescription>
                Theo dõi các giao dịch xuất nhập kho
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Lịch sử xuất nhập kho sẽ được hiển thị tại đây
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Báo cáo tồn kho</CardTitle>
                <CardDescription>Thống kê tình hình tồn kho</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Biểu đồ tồn kho sẽ được hiển thị tại đây</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Báo cáo xuất nhập</CardTitle>
                <CardDescription>Thống kê xuất nhập theo thời gian</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Biểu đồ xuất nhập sẽ được hiển thị tại đây</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default withAuth(InventoryPage)
