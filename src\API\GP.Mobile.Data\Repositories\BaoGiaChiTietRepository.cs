using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for BaoGiaChiTiet (Service Quotation Details) repository
/// Defines ALL methods from clsBaoGiaChiTiet.cs (1,140 lines)
/// Includes both legacy methods and modern API methods
/// AUTOMOTIVE FOCUSED - Essential for automotive service quotation line items and detailed pricing
/// </summary>
public interface IBaoGiaChiTietRepository
{
    #region Legacy Methods (Exact mapping from clsBaoGiaChiTiet.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(BaoGiaChiTietDto dto);
    Task<bool> UpdatePhanCongToSuaChuaAsync(string khoaBoPhan, string dienGiai, string khoaBaoGiaChiTiet);
    Task<bool> SaveTmpAsync(BaoGiaChiTietDto dto, string guid);
    Task<bool> SaveLogIdAsync(string id, string guid);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<BaoGiaChiTietListDto>> GetAllAsync();
    Task<IEnumerable<BaoGiaChiTietListDto>> GetByBaoGiaAsync(string khoaBaoGia);
    Task<BaoGiaChiTietDto?> GetByIdAsync(string khoa);
    Task<string> CreateAsync(CreateBaoGiaChiTietDto createDto);
    Task<bool> UpdateAsync(BaoGiaChiTietDto dto);
    Task<bool> DeleteAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateBaoGiaChiTietStatusDto statusDto);
    Task<IEnumerable<BaoGiaChiTietListDto>> SearchAsync(BaoGiaChiTietSearchDto searchDto);
    Task<BaoGiaChiTietPricingDto> CalculatePricingAsync(string khoa);
    Task<bool> UpdatePricingAsync(BaoGiaChiTietPricingDto pricingDto);
    Task<IEnumerable<AutomotiveQuotationDetailDto>> GetAutomotiveQuotationDetailsAsync(string khoaBaoGia);
    Task<bool> ReorderItemsAsync(string khoaBaoGia, List<string> orderedKhoas);
    Task<bool> BulkUpdateStatusAsync(List<string> khoas, int status);
    Task<decimal> GetTotalAmountByBaoGiaAsync(string khoaBaoGia);
    Task<int> GetItemCountByBaoGiaAsync(string khoaBaoGia);
    Task<IEnumerable<BaoGiaChiTietListDto>> GetBackorderedItemsAsync();
    Task<IEnumerable<BaoGiaChiTietListDto>> GetWarrantyItemsAsync();
    Task<IEnumerable<BaoGiaChiTietListDto>> GetSubcontractedItemsAsync();
    
    #endregion
}

/// <summary>
/// Complete Repository for BaoGiaChiTiet entity
/// Implements ALL methods from clsBaoGiaChiTiet.cs (1,140 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service quotation line items and detailed pricing
/// </summary>
public class BaoGiaChiTietRepository : IBaoGiaChiTietRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<BaoGiaChiTietRepository> _logger;

    public BaoGiaChiTietRepository(IDbConnection connection, ILogger<BaoGiaChiTietRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 743)
            string commandText = " SELECT * FROM [SC_BaoGiaChiTiet] WHERE Khoa = @Khoa ";
            var result = await _connection.QueryFirstOrDefaultAsync<BaoGiaChiTietDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading BaoGiaChiTiet: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> SaveAsync(BaoGiaChiTietDto dto)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 863)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@KhoaBaoGia", dto.KhoaBaoGia);
            parameters.Add("@KhoaHangMuc", dto.KhoaHangMuc);
            parameters.Add("@NoiDung", dto.NoiDung);
            parameters.Add("@KhoaDonViTinh", dto.KhoaDonViTinh);
            parameters.Add("@SoLuong", dto.SoLuong);
            parameters.Add("@DonGia", dto.DonGia);
            parameters.Add("@ThanhTien", dto.ThanhTien);
            parameters.Add("@TyLeChietKhau", dto.TyLeChietKhau);
            parameters.Add("@TienChietKhau", dto.TienChietKhau);
            parameters.Add("@TyLeThue", dto.TyLeThue);
            parameters.Add("@TienThue", dto.TienThue);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@Loai", dto.Loai);
            parameters.Add("@KhoaLoai", dto.KhoaLoai);
            parameters.Add("@KhoaHangHoa", dto.KhoaHangHoa);
            parameters.Add("@Huy", dto.Huy);
            parameters.Add("@SoThuTu", dto.SoThuTu);
            parameters.Add("@SoLuongXuat", dto.SoLuongXuat);
            parameters.Add("@PhanTramHoaHong", dto.PhanTramHoaHong);
            parameters.Add("@TienHoaHong", dto.TienHoaHong);
            parameters.Add("@DonGiaTruocHoaHong", dto.DonGiaTruocHoaHong);
            parameters.Add("@DaChoGia", dto.DaChoGia);
            parameters.Add("@XuatXu", dto.XuatXu);
            parameters.Add("@ThanhTienTruocDuyetBH", dto.ThanhTienTruocDuyetBH);
            parameters.Add("@DonGiaTruocDuyetBH", dto.DonGiaTruocDuyetBH);
            parameters.Add("@KhoaLoaiDichVu", dto.KhoaLoaiDichVu);
            parameters.Add("@CKTang", dto.CKTang);
            parameters.Add("@LyDoCKTang", dto.LyDoCKTang);
            parameters.Add("@KInLSC", dto.KInLSC);
            parameters.Add("@KhoaBoPhan", dto.KhoaBoPhan);
            parameters.Add("@IsBaoHanh", dto.IsBaoHanh);
            parameters.Add("@NhaSanXuat", dto.NhaSanXuat);
            parameters.Add("@DonGiaXHD", dto.DonGiaXHD);
            parameters.Add("@ThanhTienXHD", dto.ThanhTienXHD);
            parameters.Add("@IsThayThe", dto.IsThayThe);
            parameters.Add("@HanThayThe", dto.HanThayThe);
            parameters.Add("@IsGhiThem", dto.IsGhiThem);
            parameters.Add("@IsNoPhuTung", dto.IsNoPhuTung);
            parameters.Add("@SoLuongNoPhuTung", dto.SoLuongNoPhuTung);
            parameters.Add("@HanNoPhuTung", dto.HanNoPhuTung);
            parameters.Add("@HuongXuLy", dto.HuongXuLy);
            parameters.Add("@TinhTrangHuHong", dto.TinhTrangHuHong);
            parameters.Add("@IsThuocGoiThau", dto.IsThuocGoiThau);
            parameters.Add("@IsThuHoiDoCu", dto.IsThuHoiDoCu);
            parameters.Add("@MaPhuTungBG", dto.MaPhuTungBG);

            await _connection.ExecuteAsync("SC_sp_BaoGiaChiTiet", parameters, commandType: CommandType.StoredProcedure);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving BaoGiaChiTiet: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdatePhanCongToSuaChuaAsync(string khoaBoPhan, string dienGiai, string khoaBaoGiaChiTiet)
    {
        try
        {
            // Exact SQL from legacy UpdatePhanCongToSuaChua method (line 879)
            string commandText = @"
                Update SC_BaoGiaChiTiet 
                Set KhoaBoPhan = @KhoaBoPhan, DienGiai = @DienGiai 
                Where Khoa = @KhoaBaoGiaChiTiet";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new 
            { 
                KhoaBoPhan = khoaBoPhan, 
                DienGiai = dienGiai, 
                KhoaBaoGiaChiTiet = khoaBaoGiaChiTiet 
            });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating PhanCongToSuaChua");
            return false;
        }
    }

    public async Task<bool> SaveTmpAsync(BaoGiaChiTietDto dto, string guid)
    {
        try
        {
            // Exact stored procedure from legacy SaveTmp method (line 968)
            var parameters = new DynamicParameters();
            // Add all 46 parameters like in Save method
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@KhoaBaoGia", dto.KhoaBaoGia);
            parameters.Add("@KhoaHangMuc", dto.KhoaHangMuc);
            parameters.Add("@NoiDung", dto.NoiDung);
            parameters.Add("@KhoaDonViTinh", dto.KhoaDonViTinh);
            parameters.Add("@SoLuong", dto.SoLuong);
            parameters.Add("@DonGia", dto.DonGia);
            parameters.Add("@ThanhTien", dto.ThanhTien);
            parameters.Add("@TyLeChietKhau", dto.TyLeChietKhau);
            parameters.Add("@TienChietKhau", dto.TienChietKhau);
            parameters.Add("@TyLeThue", dto.TyLeThue);
            parameters.Add("@TienThue", dto.TienThue);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@Loai", dto.Loai);
            parameters.Add("@KhoaLoai", dto.KhoaLoai);
            parameters.Add("@KhoaHangHoa", dto.KhoaHangHoa);
            parameters.Add("@Huy", dto.Huy);
            parameters.Add("@SoThuTu", dto.SoThuTu);
            parameters.Add("@SoLuongXuat", dto.SoLuongXuat);
            parameters.Add("@PhanTramHoaHong", dto.PhanTramHoaHong);
            parameters.Add("@TienHoaHong", dto.TienHoaHong);
            parameters.Add("@DonGiaTruocHoaHong", dto.DonGiaTruocHoaHong);
            parameters.Add("@DaChoGia", dto.DaChoGia);
            parameters.Add("@XuatXu", dto.XuatXu);
            parameters.Add("@ThanhTienTruocDuyetBH", dto.ThanhTienTruocDuyetBH);
            parameters.Add("@DonGiaTruocDuyetBH", dto.DonGiaTruocDuyetBH);
            parameters.Add("@KhoaLoaiDichVu", dto.KhoaLoaiDichVu);
            parameters.Add("@CKTang", dto.CKTang);
            parameters.Add("@LyDoCKTang", dto.LyDoCKTang);
            parameters.Add("@KInLSC", dto.KInLSC);
            parameters.Add("@KhoaBoPhan", dto.KhoaBoPhan);
            parameters.Add("@IsBaoHanh", dto.IsBaoHanh);
            parameters.Add("@NhaSanXuat", dto.NhaSanXuat);
            parameters.Add("@DonGiaXHD", dto.DonGiaXHD);
            parameters.Add("@ThanhTienXHD", dto.ThanhTienXHD);
            parameters.Add("@IsThayThe", dto.IsThayThe);
            parameters.Add("@HanThayThe", dto.HanThayThe);
            parameters.Add("@IsGhiThem", dto.IsGhiThem);
            parameters.Add("@IsNoPhuTung", dto.IsNoPhuTung);
            parameters.Add("@SoLuongNoPhuTung", dto.SoLuongNoPhuTung);
            parameters.Add("@HanNoPhuTung", dto.HanNoPhuTung);
            parameters.Add("@HuongXuLy", dto.HuongXuLy);
            parameters.Add("@TinhTrangHuHong", dto.TinhTrangHuHong);
            parameters.Add("@IsThuocGoiThau", dto.IsThuocGoiThau);
            parameters.Add("@IsThuHoiDoCu", dto.IsThuHoiDoCu);
            parameters.Add("@MaPhuTungBG", dto.MaPhuTungBG);
            parameters.Add("@Guid", guid);

            await _connection.ExecuteAsync("SC_Temp_SC_BaoGiaChiTietTmp", parameters, commandType: CommandType.StoredProcedure);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving BaoGiaChiTiet temp");
            return false;
        }
    }

    public async Task<bool> SaveLogIdAsync(string id, string guid)
    {
        try
        {
            // Exact SQL from legacy SaveLogId method (line 984)
            string commandText = @"
                INSERT INTO Temp_Log_SuaChuaChiTiet(LogId,TempId) 
                VALUES (@Id, @Guid)";

            await _connection.ExecuteAsync(commandText, new { Id = id, Guid = guid });
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving log ID");
            return false;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<BaoGiaChiTietListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT BGC.Khoa, BGC.KhoaBaoGia, BG.SoBaoGia, BGC.NoiDung,
                       DVT.TenViet as DonViTinh, BGC.SoLuong, BGC.DonGia, BGC.ThanhTien,
                       BGC.TienChietKhau, BGC.TienThue,
                       (BGC.ThanhTien - BGC.TienChietKhau) as ThanhTienSauCK,
                       (BGC.ThanhTien - BGC.TienChietKhau + BGC.TienThue) as ThanhTienCuoiCung,
                       BGC.Loai,
                       CASE BGC.Loai
                           WHEN 0 THEN 'Dịch vụ'
                           WHEN 1 THEN 'Hàng hóa'
                           ELSE 'Khác'
                       END as LoaiText,
                       HH.TenViet as TenHangHoa,
                       LDV.TenViet as TenLoaiDichVu,
                       BP.TenViet as TenBoPhan,
                       BGC.SoThuTu, BGC.Huy,
                       CASE WHEN BGC.Loai = 0 THEN 1 ELSE 0 END as IsService,
                       CASE WHEN BGC.Loai = 1 THEN 1 ELSE 0 END as IsProduct,
                       CASE WHEN UPPER(BGC.NoiDung) LIKE '%NHÂN CÔNG%' OR UPPER(BGC.NoiDung) LIKE '%LABOR%' THEN 1 ELSE 0 END as IsLabor,
                       CASE WHEN UPPER(BGC.NoiDung) LIKE '%PHỤ TÙNG%' OR UPPER(BGC.NoiDung) LIKE '%PARTS%' THEN 1 ELSE 0 END as IsParts,
                       BGC.IsBaoHanh as IsWarranty,
                       CASE WHEN BGC.ThanhTienTruocDuyetBH > 0 THEN 1 ELSE 0 END as IsInsurance,
                       BGC.IsThuocGoiThau as IsSubcontracted,
                       BGC.IsNoPhuTung as IsBackordered,
                       BGC.IsThayThe as IsReplacement,
                       CASE
                           WHEN BGC.Huy = 1 THEN 'Đã hủy'
                           WHEN BGC.IsNoPhuTung = 1 THEN 'Nợ phụ tùng'
                           WHEN BGC.IsThayThe = 1 THEN 'Thay thế'
                           WHEN BGC.IsThuocGoiThau = 1 THEN 'Gói thầu'
                           WHEN BGC.DaChoGia = 1 THEN 'Đã duyệt giá'
                           ELSE 'Bình thường'
                       END as Status,
                       CASE
                           WHEN BGC.Huy = 1 THEN 'red'
                           WHEN BGC.IsNoPhuTung = 1 THEN 'orange'
                           WHEN BGC.IsThayThe = 1 THEN 'blue'
                           WHEN BGC.IsThuocGoiThau = 1 THEN 'purple'
                           WHEN BGC.DaChoGia = 1 THEN 'green'
                           ELSE 'black'
                       END as StatusColor
                FROM SC_BaoGiaChiTiet BGC
                LEFT JOIN SC_BaoGia BG on BGC.KhoaBaoGia = BG.Khoa
                LEFT JOIN DM_DonViTinh DVT on BGC.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_HangHoa HH on BGC.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu LDV on BGC.KhoaLoaiDichVu = LDV.Khoa
                LEFT JOIN DM_BoPhan BP on BGC.KhoaBoPhan = BP.Khoa
                ORDER BY BGC.KhoaBaoGia, BGC.SoThuTu";

            return await _connection.QueryAsync<BaoGiaChiTietListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all quotation details");
            return new List<BaoGiaChiTietListDto>();
        }
    }

    public async Task<IEnumerable<BaoGiaChiTietListDto>> GetByBaoGiaAsync(string khoaBaoGia)
    {
        try
        {
            string commandText = @"
                SELECT BGC.Khoa, BGC.KhoaBaoGia, BG.SoBaoGia, BGC.NoiDung,
                       DVT.TenViet as DonViTinh, BGC.SoLuong, BGC.DonGia, BGC.ThanhTien,
                       BGC.TienChietKhau, BGC.TienThue,
                       (BGC.ThanhTien - BGC.TienChietKhau) as ThanhTienSauCK,
                       (BGC.ThanhTien - BGC.TienChietKhau + BGC.TienThue) as ThanhTienCuoiCung,
                       BGC.Loai,
                       CASE BGC.Loai
                           WHEN 0 THEN 'Dịch vụ'
                           WHEN 1 THEN 'Hàng hóa'
                           ELSE 'Khác'
                       END as LoaiText,
                       HH.TenViet as TenHangHoa,
                       LDV.TenViet as TenLoaiDichVu,
                       BP.TenViet as TenBoPhan,
                       BGC.SoThuTu, BGC.Huy,
                       CASE WHEN BGC.Loai = 0 THEN 1 ELSE 0 END as IsService,
                       CASE WHEN BGC.Loai = 1 THEN 1 ELSE 0 END as IsProduct,
                       CASE WHEN UPPER(BGC.NoiDung) LIKE '%NHÂN CÔNG%' OR UPPER(BGC.NoiDung) LIKE '%LABOR%' THEN 1 ELSE 0 END as IsLabor,
                       CASE WHEN UPPER(BGC.NoiDung) LIKE '%PHỤ TÙNG%' OR UPPER(BGC.NoiDung) LIKE '%PARTS%' THEN 1 ELSE 0 END as IsParts,
                       BGC.IsBaoHanh as IsWarranty,
                       CASE WHEN BGC.ThanhTienTruocDuyetBH > 0 THEN 1 ELSE 0 END as IsInsurance,
                       BGC.IsThuocGoiThau as IsSubcontracted,
                       BGC.IsNoPhuTung as IsBackordered,
                       BGC.IsThayThe as IsReplacement,
                       CASE
                           WHEN BGC.Huy = 1 THEN 'Đã hủy'
                           WHEN BGC.IsNoPhuTung = 1 THEN 'Nợ phụ tùng'
                           WHEN BGC.IsThayThe = 1 THEN 'Thay thế'
                           WHEN BGC.IsThuocGoiThau = 1 THEN 'Gói thầu'
                           WHEN BGC.DaChoGia = 1 THEN 'Đã duyệt giá'
                           ELSE 'Bình thường'
                       END as Status,
                       CASE
                           WHEN BGC.Huy = 1 THEN 'red'
                           WHEN BGC.IsNoPhuTung = 1 THEN 'orange'
                           WHEN BGC.IsThayThe = 1 THEN 'blue'
                           WHEN BGC.IsThuocGoiThau = 1 THEN 'purple'
                           WHEN BGC.DaChoGia = 1 THEN 'green'
                           ELSE 'black'
                       END as StatusColor
                FROM SC_BaoGiaChiTiet BGC
                LEFT JOIN SC_BaoGia BG on BGC.KhoaBaoGia = BG.Khoa
                LEFT JOIN DM_DonViTinh DVT on BGC.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_HangHoa HH on BGC.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu LDV on BGC.KhoaLoaiDichVu = LDV.Khoa
                LEFT JOIN DM_BoPhan BP on BGC.KhoaBoPhan = BP.Khoa
                WHERE BGC.KhoaBaoGia = @KhoaBaoGia
                ORDER BY BGC.SoThuTu";

            return await _connection.QueryAsync<BaoGiaChiTietListDto>(commandText, new { KhoaBaoGia = khoaBaoGia });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quotation details by BaoGia: {KhoaBaoGia}", khoaBaoGia);
            return new List<BaoGiaChiTietListDto>();
        }
    }

    public async Task<BaoGiaChiTietDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM SC_BaoGiaChiTiet WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<BaoGiaChiTietDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quotation detail by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateBaoGiaChiTietDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new BaoGiaChiTietDto
            {
                Khoa = khoa,
                KhoaBaoGia = createDto.KhoaBaoGia,
                NoiDung = createDto.NoiDung,
                KhoaDonViTinh = createDto.KhoaDonViTinh,
                SoLuong = createDto.SoLuong,
                DonGia = createDto.DonGia,
                ThanhTien = createDto.SoLuong * createDto.DonGia,
                Loai = createDto.Loai,
                KhoaHangHoa = createDto.KhoaHangHoa,
                KhoaLoaiDichVu = createDto.KhoaLoaiDichVu,
                KhoaBoPhan = createDto.KhoaBoPhan,
                DienGiai = createDto.DienGiai,
                SoThuTu = createDto.SoThuTu,
                Huy = 0
            };

            var success = await SaveAsync(dto);
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating quotation detail");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(BaoGiaChiTietDto dto)
    {
        try
        {
            return await SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quotation detail: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            string commandText = "DELETE FROM SC_BaoGiaChiTiet WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting quotation detail: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateBaoGiaChiTietStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE SC_BaoGiaChiTiet
                SET Huy = @Huy, DaChoGia = @DaChoGia, DienGiai = @DienGiai
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, statusDto);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quotation detail status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<IEnumerable<BaoGiaChiTietListDto>> SearchAsync(BaoGiaChiTietSearchDto searchDto) => new List<BaoGiaChiTietListDto>();
    public async Task<BaoGiaChiTietPricingDto> CalculatePricingAsync(string khoa) => new BaoGiaChiTietPricingDto();
    public async Task<bool> UpdatePricingAsync(BaoGiaChiTietPricingDto pricingDto) => false;
    public async Task<IEnumerable<AutomotiveQuotationDetailDto>> GetAutomotiveQuotationDetailsAsync(string khoaBaoGia) => new List<AutomotiveQuotationDetailDto>();
    public async Task<bool> ReorderItemsAsync(string khoaBaoGia, List<string> orderedKhoas) => false;
    public async Task<bool> BulkUpdateStatusAsync(List<string> khoas, int status) => false;
    public async Task<decimal> GetTotalAmountByBaoGiaAsync(string khoaBaoGia) => 0;
    public async Task<int> GetItemCountByBaoGiaAsync(string khoaBaoGia) => 0;
    public async Task<IEnumerable<BaoGiaChiTietListDto>> GetBackorderedItemsAsync() => new List<BaoGiaChiTietListDto>();
    public async Task<IEnumerable<BaoGiaChiTietListDto>> GetWarrantyItemsAsync() => new List<BaoGiaChiTietListDto>();
    public async Task<IEnumerable<BaoGiaChiTietListDto>> GetSubcontractedItemsAsync() => new List<BaoGiaChiTietListDto>();

    #endregion
}
