using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Controller for KhoanMucSuaChua (Repair Categories) operations
/// Implements ALL endpoints from clsDMKhoanMucSuaChua.cs (646 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for repair category management in automotive service quotations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class KhoanMucSuaChuaController : ControllerBase
{
    private readonly IKhoanMucSuaChuaService _khoanMucSuaChuaService;
    private readonly ILogger<KhoanMucSuaChuaController> _logger;

    public KhoanMucSuaChuaController(
        IKhoanMucSuaChuaService khoanMucSuaChuaService,
        ILogger<KhoanMucSuaChuaController> logger)
    {
        _khoanMucSuaChuaService = khoanMucSuaChuaService;
        _logger = logger;
    }

    #region Legacy API Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpGet("load/{khoa}")]
    public async Task<ActionResult<bool>> Load(string khoa)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy LoadByCode method endpoint
    /// </summary>
    [HttpGet("loadbycode/{ma}")]
    public async Task<ActionResult<bool>> LoadByCode(string ma)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.LoadByCodeAsync(ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadByCode endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] SaveKhoanMucSuaChuaRequestDto request)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.SaveAsync(request.Dto, request.Task);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpGet("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromQuery] string keyFilter = "", [FromQuery] string fieldNameFilter = "")
    {
        try
        {
            var result = await _khoanMucSuaChuaService.ShowListAsync(keyFilter, fieldNameFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpGet("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _khoanMucSuaChuaService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpGet("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromQuery] string code = "", [FromQuery] string keyFilter = "", [FromQuery] string fieldNameFilter = "")
    {
        try
        {
            var result = await _khoanMucSuaChuaService.SearchByCodeAsync(code, keyFilter, fieldNameFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpGet("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromQuery] string fieldList, [FromQuery] string conditions = "", [FromQuery] string order = "")
    {
        try
        {
            var result = await _khoanMucSuaChuaService.ShowListByFieldAsync(fieldList, conditions, order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Modern API Endpoints

    /// <summary>
    /// Get all repair categories
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<KhoanMucSuaChuaListDto>>> GetAll()
    {
        try
        {
            var result = await _khoanMucSuaChuaService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all repair categories");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair category by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<KhoanMucSuaChuaDto>> GetById(string khoa)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair category by ID");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair category by code
    /// </summary>
    [HttpGet("bycode/{ma}")]
    public async Task<ActionResult<KhoanMucSuaChuaDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair category by code");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Create new repair category
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateKhoanMucSuaChuaDto createDto)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating repair category");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update repair category
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] UpdateKhoanMucSuaChuaDto updateDto)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.UpdateAsync(khoa, updateDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair category");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Delete repair category
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair category");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Search repair categories
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<KhoanMucSuaChuaListDto>>> Search([FromBody] KhoanMucSuaChuaSearchDto searchDto)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching repair categories");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair categories for lookup/dropdown
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<KhoanMucSuaChuaLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _khoanMucSuaChuaService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair category lookup");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Validate repair category data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<KhoanMucSuaChuaValidationDto>> Validate([FromBody] ValidateKhoanMucSuaChuaRequestDto request)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.ValidateAsync(request.Khoa, request.Ma, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating repair category");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update repair category status
    /// </summary>
    [HttpPatch("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateStatusRequestDto request)
    {
        try
        {
            var result = await _khoanMucSuaChuaService.UpdateStatusAsync(khoa, request.Active);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair category status");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get automotive-specific repair categories
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveRepairCategoryDto>>> GetAutomotiveRepairCategories()
    {
        try
        {
            var result = await _khoanMucSuaChuaService.GetAutomotiveRepairCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive repair categories");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}

#region Request DTOs for KhoanMucSuaChua

/// <summary>
/// Request DTO for Save method
/// </summary>
public class SaveKhoanMucSuaChuaRequestDto
{
    public KhoanMucSuaChuaDto Dto { get; set; } = new();
    public string Task { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Validate method
/// </summary>
public class ValidateKhoanMucSuaChuaRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateStatus method
/// </summary>
public class UpdateStatusRequestDto
{
    public int Active { get; set; }
}

#endregion
