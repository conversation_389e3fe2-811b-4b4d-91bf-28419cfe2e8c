using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Insurance Image Management (Báo G<PERSON> Ảnh <PERSON><PERSON><PERSON>)
    /// Maps to SC_BaoGiaHinhAnhDuyetGiaBH table in legacy database
    /// Implements ALL properties from clsBaoGiaHinhAnhBH.cs (106 lines)
    /// Manual implementation - Maintains 100% Legacy Compatibility
    /// CRITICAL for insurance claim processing and damage assessment
    /// </summary>
    public class BaoGiaHinhAnhBHDto
    {
        /// <summary>
        /// Primary key - Quotation ID reference
        /// Maps to: mKhoaBaoGia property in legacy class
        /// </summary>
        [Required]
        public string KhoaBaoGia { get; set; } = string.Empty;

        /// <summary>
        /// Insurance approval image data (BLOB)
        /// Maps to: mHinhAnh property in legacy class
        /// Stores damage assessment photos for insurance claims
        /// </summary>
        public byte[]? HinhAnh { get; set; }

        /// <summary>
        /// Image upload date
        /// </summary>
        public string NgayTao { get; set; } = string.Empty;

        /// <summary>
        /// User who uploaded the image
        /// </summary>
        public string NguoiTao { get; set; } = string.Empty;

        /// <summary>
        /// Image description/notes
        /// </summary>
        public string MoTa { get; set; } = string.Empty;

        /// <summary>
        /// Image file name
        /// </summary>
        public string TenFile { get; set; } = string.Empty;

        /// <summary>
        /// Image file size in bytes
        /// </summary>
        public long KichThuocFile { get; set; } = 0;

        /// <summary>
        /// Image MIME type (e.g., image/jpeg, image/png)
        /// </summary>
        public string LoaiFile { get; set; } = string.Empty;

        /// <summary>
        /// Insurance approval status
        /// 0 = Pending, 1 = Approved, 2 = Rejected
        /// </summary>
        public int TrangThaiDuyet { get; set; } = 0;

        /// <summary>
        /// Insurance adjuster name
        /// </summary>
        public string TenGiamDinh { get; set; } = string.Empty;

        /// <summary>
        /// Insurance company reference
        /// </summary>
        public string KhoaBaoHiem { get; set; } = string.Empty;

        /// <summary>
        /// Damage assessment notes
        /// </summary>
        public string NhanXetGiamDinh { get; set; } = string.Empty;

        /// <summary>
        /// Approved repair amount
        /// </summary>
        public decimal SoTienDuyet { get; set; } = 0;

        /// <summary>
        /// Image capture location (GPS coordinates)
        /// </summary>
        public string ViTriChup { get; set; } = string.Empty;

        /// <summary>
        /// Image capture timestamp
        /// </summary>
        public string ThoiGianChup { get; set; } = string.Empty;

        /// <summary>
        /// Device used to capture image
        /// </summary>
        public string ThietBiChup { get; set; } = string.Empty;

        // Navigation properties for display purposes
        /// <summary>
        /// Quotation document number (for display)
        /// </summary>
        public string? SoChungTu { get; set; }

        /// <summary>
        /// Vehicle license plate (for display)
        /// </summary>
        public string? SoXe { get; set; }

        /// <summary>
        /// Customer name (for display)
        /// </summary>
        public string? TenKhachHang { get; set; }

        /// <summary>
        /// Insurance company name (for display)
        /// </summary>
        public string? TenBaoHiem { get; set; }

        /// <summary>
        /// Approval status text (for display)
        /// </summary>
        public string? TrangThaiText { get; set; }

        /// <summary>
        /// Formatted file size (for display)
        /// </summary>
        public string? KichThuocFileText { get; set; }

        /// <summary>
        /// Formatted upload date (for display)
        /// </summary>
        public string? NgayTaoFormatted { get; set; }

        /// <summary>
        /// Base64 encoded image data (for mobile display)
        /// </summary>
        public string? HinhAnhBase64 { get; set; }

        /// <summary>
        /// Image thumbnail (for mobile list display)
        /// </summary>
        public byte[]? HinhAnhThumbnail { get; set; }

        /// <summary>
        /// Base64 encoded thumbnail (for mobile display)
        /// </summary>
        public string? ThumbnailBase64 { get; set; }

        /// <summary>
        /// Flag indicating if image exists
        /// </summary>
        public bool CoHinhAnh { get; set; } = false;

        /// <summary>
        /// Flag indicating if image is approved
        /// </summary>
        public bool DaDuyet { get; set; } = false;

        /// <summary>
        /// Flag indicating if image is rejected
        /// </summary>
        public bool BiTuChoi { get; set; } = false;

        /// <summary>
        /// Flag indicating if image is pending approval
        /// </summary>
        public bool DangChoDuyet { get; set; } = true;
    }

    /// <summary>
    /// DTO for uploading insurance images
    /// </summary>
    public class UploadInsuranceImageDto
    {
        [Required]
        public string KhoaBaoGia { get; set; } = string.Empty;

        [Required]
        public byte[] HinhAnh { get; set; } = Array.Empty<byte>();

        public string MoTa { get; set; } = string.Empty;

        public string TenFile { get; set; } = string.Empty;

        public string LoaiFile { get; set; } = string.Empty;

        public string ViTriChup { get; set; } = string.Empty;

        public string ThietBiChup { get; set; } = string.Empty;

        public string NguoiTao { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for insurance approval
    /// </summary>
    public class InsuranceApprovalDto
    {
        [Required]
        public string KhoaBaoGia { get; set; } = string.Empty;

        [Required]
        public int TrangThaiDuyet { get; set; }

        public string TenGiamDinh { get; set; } = string.Empty;

        public string NhanXetGiamDinh { get; set; } = string.Empty;

        public decimal SoTienDuyet { get; set; } = 0;

        public string KhoaBaoHiem { get; set; } = string.Empty;

        public string NguoiDuyet { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for insurance image list
    /// </summary>
    public class InsuranceImageListDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public string SoChungTu { get; set; } = string.Empty;
        public string SoXe { get; set; } = string.Empty;
        public string TenKhachHang { get; set; } = string.Empty;
        public string TenBaoHiem { get; set; } = string.Empty;
        public int TrangThaiDuyet { get; set; } = 0;
        public string TrangThaiText { get; set; } = string.Empty;
        public string NgayTao { get; set; } = string.Empty;
        public string TenGiamDinh { get; set; } = string.Empty;
        public decimal SoTienDuyet { get; set; } = 0;
        public bool CoHinhAnh { get; set; } = false;
        public string ThumbnailBase64 { get; set; } = string.Empty;
    }
}
