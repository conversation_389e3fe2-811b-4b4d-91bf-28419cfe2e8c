/**
 * Custom hook for managing quotation data with real API integration
 * Handles loading, saving, and managing quotation state
 */

import { useState, useEffect } from 'react'
import { baoGiaSuaChuaApi, baoGiaSuaChuaChiTietApi, type BaoGiaSuaChuaDto, type BaoGiaSuaChuaChiTietDto } from '@/services/api'

export interface QuotationFormData {
  // Header information
  khoa: string
  soChungTu: string
  ngayChungTu: string
  khoaTiepNhanXe: string
  khoaKhachHang: string
  khachHang: string
  khoaXe: string
  bienSoXe: string
  khoaLoaiXe: string
  loaiXe: string
  phanLoai: number // 0=Cash, 1=Insurance
  boPhanSuaChua: number // 0=Body+Paint, 1=Engine
  tinhTrangBaoGia: number // 0=Draft, 1=Approved
  khoaBaoHiem: string
  baoHiem: string
  dienGiai: string
  
  // Financial calculations
  tongTienHang: number
  tienThue: number
  tienChietKhau: number
  tongThanhToan: number
  
  // Line items
  lineItems: QuotationLineItem[]
  
  // Insurance specific
  mienThuong: number
  khauHao: number
  cheTai: number
  cpsc: number // Chi phí sửa chữa
  
  // Commission
  hoaHong: number
  tyLeHoaHong: number
}

export interface QuotationLineItem {
  khoa: string
  khoaBaoGia: string
  noiDung: string
  soLuong: number
  donGia: number
  thanhTien: number
  tyLeCK: number
  tienCK: number
  tyLeThue: number
  tienThue: number
  loai: number // 0=Labor, 1=Parts
  maPhuTung: string
  khoaHangHoa: string
  dvt: string
  khoaDVT: string
  ghiChu: string
}

export function useQuotationData(quotationId?: string) {
  const [quotation, setQuotation] = useState<QuotationFormData>({
    khoa: '',
    soChungTu: '',
    ngayChungTu: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
    khoaTiepNhanXe: '',
    khoaKhachHang: '',
    khachHang: '',
    khoaXe: '',
    bienSoXe: '',
    khoaLoaiXe: '',
    loaiXe: '',
    phanLoai: 0,
    boPhanSuaChua: 0,
    tinhTrangBaoGia: 0,
    khoaBaoHiem: '',
    baoHiem: '',
    dienGiai: '',
    tongTienHang: 0,
    tienThue: 0,
    tienChietKhau: 0,
    tongThanhToan: 0,
    lineItems: [],
    mienThuong: 0,
    khauHao: 0,
    cheTai: 0,
    cpsc: 0,
    hoaHong: 0,
    tyLeHoaHong: 0
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)

  // Load quotation data when quotationId changes
  useEffect(() => {
    if (quotationId) {
      loadQuotation(quotationId)
    } else {
      // Reset for new quotation
      generateNewQuotationNumber()
    }
  }, [quotationId])

  const loadQuotation = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      
      // Load main quotation data
      const quotationData = await baoGiaSuaChuaApi.getById(id)
      
      // Load line items
      const lineItems = await baoGiaSuaChuaChiTietApi.getByQuotation(id)
      
      // Convert API data to form data
      setQuotation({
        khoa: quotationData.khoa,
        soChungTu: quotationData.soChungTu,
        ngayChungTu: quotationData.ngayChungTu,
        khoaTiepNhanXe: quotationData.khoaTiepNhanXe || '',
        khoaKhachHang: quotationData.khoaKhachHang || '',
        khachHang: quotationData.khachHang || '',
        khoaXe: quotationData.khoaXe || '',
        bienSoXe: quotationData.bienSoXe || '',
        khoaLoaiXe: quotationData.khoaLoaiXe || '',
        loaiXe: quotationData.loaiXe || '',
        phanLoai: quotationData.phanLoai || 0,
        boPhanSuaChua: quotationData.boPhanSuaChua || 0,
        tinhTrangBaoGia: quotationData.tinhTrangBaoGia || 0,
        khoaBaoHiem: quotationData.khoaBaoHiem || '',
        baoHiem: quotationData.baoHiem || '',
        dienGiai: quotationData.dienGiai || '',
        tongTienHang: quotationData.tongTienHang || 0,
        tienThue: quotationData.tienThue || 0,
        tienChietKhau: quotationData.tienChietKhau || 0,
        tongThanhToan: quotationData.tongThanhToan || 0,
        lineItems: lineItems.map(item => ({
          khoa: item.khoa,
          khoaBaoGia: item.khoaBaoGia,
          noiDung: item.noiDung,
          soLuong: item.soLuong1 || 0,
          donGia: item.donGia1 || 0,
          thanhTien: item.thanhTien1 || 0,
          tyLeCK: item.tyLeChietKhau1 || 0,
          tienCK: item.tienChietKhau1 || 0,
          tyLeThue: 0, // Not in API
          tienThue: 0, // Not in API
          loai: item.loai || 0,
          maPhuTung: item.hangHoa || '',
          khoaHangHoa: item.khoaHangHoa || '',
          dvt: item.dvt || '',
          khoaDVT: '', // Not in API
          ghiChu: item.ghiChu || ''
        })),
        mienThuong: 0, // These would come from additional API calls
        khauHao: 0,
        cheTai: 0,
        cpsc: 0,
        hoaHong: 0,
        tyLeHoaHong: 0
      })
      
      console.log('✅ Loaded quotation data:', quotationData)
      console.log('✅ Loaded line items:', lineItems.length)
      
    } catch (err) {
      console.error('❌ Error loading quotation:', err)
      setError('Không thể tải dữ liệu báo giá')
    } finally {
      setLoading(false)
    }
  }

  const generateNewQuotationNumber = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const time = String(today.getHours()).padStart(2, '0') + String(today.getMinutes()).padStart(2, '0')
    
    const newNumber = `BG${year}${month}${day}${time}`
    
    setQuotation(prev => ({
      ...prev,
      soChungTu: newNumber,
      khoa: `${newNumber}_${Date.now()}` // Unique key
    }))
  }

  const saveQuotation = async (): Promise<boolean> => {
    try {
      setSaving(true)
      setError(null)
      
      // Prepare data for API
      const quotationDto: BaoGiaSuaChuaDto = {
        khoa: quotation.khoa,
        soChungTu: quotation.soChungTu,
        ngayChungTu: quotation.ngayChungTu,
        khoaTiepNhanXe: quotation.khoaTiepNhanXe,
        khoaKhachHang: quotation.khoaKhachHang,
        khachHang: quotation.khachHang,
        khoaXe: quotation.khoaXe,
        bienSoXe: quotation.bienSoXe,
        khoaLoaiXe: quotation.khoaLoaiXe,
        loaiXe: quotation.loaiXe,
        phanLoai: quotation.phanLoai,
        boPhanSuaChua: quotation.boPhanSuaChua,
        tinhTrangBaoGia: quotation.tinhTrangBaoGia,
        khoaBaoHiem: quotation.khoaBaoHiem,
        baoHiem: quotation.baoHiem,
        dienGiai: quotation.dienGiai,
        tongTienHang: quotation.tongTienHang,
        tienThue: quotation.tienThue,
        tienChietKhau: quotation.tienChietKhau,
        tongThanhToan: quotation.tongThanhToan,
        khoaNhanVienTao: 'USER001', // Would come from auth context
        ngayTao: new Date().toISOString().slice(0, 10).replace(/-/g, '')
      } as BaoGiaSuaChuaDto
      
      // Save main quotation
      let result: boolean
      if (quotationId) {
        result = await baoGiaSuaChuaApi.update(quotation.khoa, quotationDto)
      } else {
        const newId = await baoGiaSuaChuaApi.create({
          soChungTu: quotation.soChungTu,
          ngayChungTu: quotation.ngayChungTu,
          khoaTiepNhanXe: quotation.khoaTiepNhanXe,
          khoaKhachHang: quotation.khoaKhachHang,
          khachHang: quotation.khachHang,
          khoaXe: quotation.khoaXe,
          bienSoXe: quotation.bienSoXe,
          khoaLoaiXe: quotation.khoaLoaiXe,
          loaiXe: quotation.loaiXe,
          phanLoai: quotation.phanLoai,
          boPhanSuaChua: quotation.boPhanSuaChua,
          khoaBaoHiem: quotation.khoaBaoHiem,
          baoHiem: quotation.baoHiem,
          dienGiai: quotation.dienGiai,
          khoaNhanVienTao: 'USER001'
        })
        result = !!newId
        if (newId) {
          setQuotation(prev => ({ ...prev, khoa: newId }))
        }
      }
      
      // Save line items
      if (result && quotation.lineItems.length > 0) {
        // Delete existing items first
        await baoGiaSuaChuaChiTietApi.deleteByQuotation(quotation.khoa)
        
        // Create new items
        for (const item of quotation.lineItems) {
          await baoGiaSuaChuaChiTietApi.create({
            khoaBaoGia: quotation.khoa,
            noiDung: item.noiDung,
            soLuong1: item.soLuong,
            donGia1: item.donGia,
            thanhTien1: item.thanhTien,
            tyLeChietKhau1: item.tyLeCK,
            tienChietKhau1: item.tienCK,
            loai: item.loai,
            hangHoa: item.maPhuTung,
            khoaHangHoa: item.khoaHangHoa,
            dvt: item.dvt,
            ghiChu: item.ghiChu
          })
        }
      }
      
      console.log('✅ Saved quotation successfully')
      return result
      
    } catch (err) {
      console.error('❌ Error saving quotation:', err)
      setError('Không thể lưu báo giá')
      return false
    } finally {
      setSaving(false)
    }
  }

  const updateQuotation = (updates: Partial<QuotationFormData>) => {
    setQuotation(prev => ({ ...prev, ...updates }))
  }

  const addLineItem = (item: Omit<QuotationLineItem, 'khoa' | 'khoaBaoGia'>) => {
    const newItem: QuotationLineItem = {
      ...item,
      khoa: `ITEM_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      khoaBaoGia: quotation.khoa
    }
    
    setQuotation(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, newItem]
    }))
  }

  const updateLineItem = (index: number, updates: Partial<QuotationLineItem>) => {
    setQuotation(prev => ({
      ...prev,
      lineItems: prev.lineItems.map((item, i) => 
        i === index ? { ...item, ...updates } : item
      )
    }))
  }

  const removeLineItem = (index: number) => {
    setQuotation(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter((_, i) => i !== index)
    }))
  }

  return {
    quotation,
    loading,
    error,
    saving,
    updateQuotation,
    addLineItem,
    updateLineItem,
    removeLineItem,
    saveQuotation,
    loadQuotation
  }
}
