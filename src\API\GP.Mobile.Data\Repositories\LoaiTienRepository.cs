using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for LoaiTien (Currency Type) repository
/// Defines ALL methods from clsDMLoaiTien.cs (552 lines)
/// Includes both legacy methods and modern API methods
/// </summary>
public interface ILoaiTienRepository
{
    #region Legacy Methods (Exact mapping from clsDMLoaiTien.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadByCodeAsync(string ma);
    Task<bool> SaveAsync(LoaiTienDto dto, string action);
    Task<bool> DelDataAsync(string khoa);
    
    // List and search methods
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    
    // Utility methods
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LoaiTienListDto>> GetAllAsync();
    Task<LoaiTienDto?> GetByIdAsync(string khoa);
    Task<LoaiTienDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateLoaiTienDto createDto);
    Task<bool> UpdateAsync(LoaiTienDto dto);
    Task<bool> UpdateStatusAsync(UpdateLoaiTienStatusDto statusDto);
    Task<IEnumerable<LoaiTienListDto>> SearchAsync(LoaiTienSearchDto searchDto);
    Task<IEnumerable<LoaiTienLookupDto>> GetLookupAsync(string language = "vi");
    Task<LoaiTienValidationDto> ValidateAsync(string khoa, string ma);
    Task<ExchangeRateDto?> GetExchangeRateAsync(string maLoaiTien, string ngay);
    Task<LoaiTienSearchByCodeDto?> SearchCurrencyByCodeAsync(string code, string condition = "");
    
    #endregion
}

/// <summary>
/// Complete Repository for LoaiTien entity
/// Implements ALL methods from clsDMLoaiTien.cs (552 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class LoaiTienRepository : ILoaiTienRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<LoaiTienRepository> _logger;

    public LoaiTienRepository(IDbConnection connection, ILogger<LoaiTienRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 215)
            string commandText = "SELECT * FROM DM_LoaiTien WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<LoaiTienDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiTien: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> LoadByCodeAsync(string ma)
    {
        try
        {
            // Exact SQL from legacy LoadByCode method (line 486)
            string commandText = "SELECT * FROM DM_LoaiTien WHERE RTRIM(Ma) = @Ma";
            var result = await _connection.QueryFirstOrDefaultAsync<LoaiTienDto>(commandText, new { Ma = ma.Trim() });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiTien by code: {Ma}", ma);
            return false;
        }
    }

    public async Task<bool> SaveAsync(LoaiTienDto dto, string action)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 250)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@Ma", dto.Ma);
            parameters.Add("@TenViet", dto.TenViet);
            parameters.Add("@TenAnh", dto.TenAnh);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@TienViet", dto.TienViet);
            parameters.Add("@SoLe", dto.SoLe);
            parameters.Add("@TyGia", dto.TyGia);
            parameters.Add("@TuNgay", dto.TuNgay);
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@Active", dto.Active);
            parameters.Add("@Send", dto.Send);
            parameters.Add("@pAction", action);
            parameters.Add("@pError", dbType: DbType.Int16, direction: ParameterDirection.Output);

            await _connection.ExecuteAsync("sp_DM_LoaiTien", parameters, commandType: CommandType.StoredProcedure);
            
            var errorCode = parameters.Get<int>("@pError");
            return errorCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving LoaiTien: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy DelData method (line 422)
            string commandText = "DELETE FROM DM_LoaiTien WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiTien: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Exact SQL from legacy ShowList method (line 286)
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                whereClause = " AND " + condition;
            }

            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string commandText = $@"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM(TenViet) as Ten  
                FROM DM_LoaiTien 
                WHERE Active = 1 {whereClause} 
                ORDER BY Ma";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiTien list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            // Exact SQL from legacy ShowAllList method (line 310)
            string commandText = @"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM(TenViet) as Ten  
                FROM DM_LoaiTien 
                ORDER BY Ma";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all LoaiTien list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Exact SQL from legacy ShowListByField method (line 444)
            fieldList = fieldList.Replace("|", ",");
            
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(conditions))
            {
                whereClause = " WHERE " + conditions;
            }

            string orderClause = "";
            if (!string.IsNullOrWhiteSpace(order))
            {
                orderClause = " ORDER BY " + order;
            }

            string commandText = $"SELECT {fieldList} FROM DM_LoaiTien {whereClause} {orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiTien list by field");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            // Exact SQL from legacy SearchByCode method (line 332)
            string codeFilter = "";
            string conditionFilter = "";

            if (!string.IsNullOrWhiteSpace(code))
            {
                codeFilter = " AND RTRIM(Ma) = @Code";
            }

            if (!string.IsNullOrWhiteSpace(condition))
            {
                conditionFilter = " AND " + condition;
            }

            string commandText = $@"
                SELECT Khoa, Ma, TenViet as Ten  
                FROM DM_LoaiTien 
                WHERE Active = 1 {codeFilter} {conditionFilter}";

            var result = await _connection.QueryFirstOrDefaultAsync(commandText, new { Code = code.Trim() });
            
            if (result != null)
            {
                // Return in legacy format: "Khoa|Ma|Ten"
                return $"{result.Khoa}|{result.Ma}|{result.Ten}";
            }

            return "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiTien by code");
            return "";
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            // Exact SQL from legacy TrungMa method (line 371)
            string commandText = @"
                SELECT * FROM DM_LoaiTien 
                WHERE RTRIM(Ma) = @Ma AND RTRIM(Khoa) <> @Khoa";
            
            var result = await _connection.QueryAsync(commandText, new { Ma = ma.Trim(), Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate currency code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy WasUsed method (line 400)
            // Note: The legacy method checks DM_HangHoa.KhoaNhom which seems incorrect for currency
            // It should check tables that actually use currency like transactions, invoices, etc.
            // For now, keeping the legacy logic but this might need correction
            string commandText = "SELECT * FROM DM_HangHoa WHERE RTRIM(KhoaNhom) = @Khoa";
            var result = await _connection.QueryAsync(commandText, new { Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if LoaiTien was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LoaiTienListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT Khoa, Ma, TenViet, TenAnh, TyGia, TienViet, SoLe, Active
                FROM DM_LoaiTien
                WHERE Active = 1
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiTienListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiTien records");
            return new List<LoaiTienListDto>();
        }
    }

    public async Task<LoaiTienDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_LoaiTien WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<LoaiTienDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<LoaiTienDto?> GetByCodeAsync(string ma)
    {
        try
        {
            string commandText = "SELECT * FROM DM_LoaiTien WHERE RTRIM(Ma) = @Ma";
            return await _connection.QueryFirstOrDefaultAsync<LoaiTienDto>(commandText, new { Ma = ma.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by code: {Ma}", ma);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateLoaiTienDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new LoaiTienDto
            {
                Khoa = khoa,
                Ma = createDto.Ma,
                TenViet = createDto.TenViet,
                TenAnh = createDto.TenAnh,
                DienGiai = createDto.DienGiai,
                TienViet = createDto.TienViet,
                TyGia = createDto.TyGia,
                TuNgay = createDto.TuNgay,
                SoLe = createDto.SoLe,
                Active = createDto.Active,
                Send = 0
            };

            var success = await SaveAsync(dto, "INSERT");
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiTien");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(LoaiTienDto dto)
    {
        try
        {
            return await SaveAsync(dto, "UPDATE");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiTien: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateLoaiTienStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE DM_LoaiTien
                SET Active = @Active,
                    KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                statusDto.Khoa,
                statusDto.Active,
                statusDto.KhoaNhanVienCapNhat
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiTien status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<LoaiTienListDto>> SearchAsync(LoaiTienSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.Ma))
            {
                conditions.Add("Ma LIKE @Ma");
                parameters.Add("@Ma", $"%{searchDto.Ma}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenViet))
            {
                conditions.Add("TenViet LIKE @TenViet");
                parameters.Add("@TenViet", $"%{searchDto.TenViet}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenAnh))
            {
                conditions.Add("TenAnh LIKE @TenAnh");
                parameters.Add("@TenAnh", $"%{searchDto.TenAnh}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DienGiai))
            {
                conditions.Add("DienGiai LIKE @DienGiai");
                parameters.Add("@DienGiai", $"%{searchDto.DienGiai}%");
            }

            if (searchDto.TienViet.HasValue)
            {
                conditions.Add("TienViet = @TienViet");
                parameters.Add("@TienViet", searchDto.TienViet.Value);
            }

            if (searchDto.Active.HasValue)
            {
                conditions.Add("Active = @Active");
                parameters.Add("@Active", searchDto.Active.Value);
            }

            if (searchDto.TyGiaFrom.HasValue)
            {
                conditions.Add("TyGia >= @TyGiaFrom");
                parameters.Add("@TyGiaFrom", searchDto.TyGiaFrom.Value);
            }

            if (searchDto.TyGiaTo.HasValue)
            {
                conditions.Add("TyGia <= @TyGiaTo");
                parameters.Add("@TyGiaTo", searchDto.TyGiaTo.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayFrom))
            {
                conditions.Add("TuNgay >= @TuNgayFrom");
                parameters.Add("@TuNgayFrom", searchDto.TuNgayFrom);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayTo))
            {
                conditions.Add("TuNgay <= @TuNgayTo");
                parameters.Add("@TuNgayTo", searchDto.TuNgayTo);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT Khoa, Ma, TenViet, TenAnh, TyGia, TienViet, SoLe, Active
                FROM DM_LoaiTien
                {whereClause}
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiTienListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiTien");
            return new List<LoaiTienListDto>();
        }
    }

    public async Task<IEnumerable<LoaiTienLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            string nameField = language.ToLower() == "en" ? "TenAnh" : "TenViet";
            string commandText = $@"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM({nameField}) as Ten, TyGia, SoLe
                FROM DM_LoaiTien
                WHERE Active = 1
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiTienLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien lookup");
            return new List<LoaiTienLookupDto>();
        }
    }

    public async Task<LoaiTienValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            var result = new LoaiTienValidationDto
            {
                Khoa = khoa,
                Ma = ma
            };

            // Check if duplicate
            result.IsDuplicate = await TrungMaAsync(ma, khoa);

            // Check if used (only if not creating new)
            if (!string.IsNullOrEmpty(khoa))
            {
                result.IsUsed = await WasUsedAsync(khoa);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiTien");
            return new LoaiTienValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<ExchangeRateDto?> GetExchangeRateAsync(string maLoaiTien, string ngay)
    {
        try
        {
            string commandText = @"
                SELECT Khoa as KhoaLoaiTien, Ma as MaLoaiTien, TyGia, TuNgay, '' as DenNgay
                FROM DM_LoaiTien
                WHERE RTRIM(Ma) = @MaLoaiTien
                AND Active = 1
                AND (TuNgay <= @Ngay OR TuNgay = '' OR TuNgay IS NULL)
                ORDER BY TuNgay DESC";

            return await _connection.QueryFirstOrDefaultAsync<ExchangeRateDto>(commandText,
                new { MaLoaiTien = maLoaiTien.Trim(), Ngay = ngay });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exchange rate");
            return null;
        }
    }

    public async Task<LoaiTienSearchByCodeDto?> SearchCurrencyByCodeAsync(string code, string condition = "")
    {
        try
        {
            string conditionFilter = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                conditionFilter = " AND " + condition;
            }

            string commandText = $@"
                SELECT Khoa, Ma, TenViet as Ten
                FROM DM_LoaiTien
                WHERE Active = 1
                AND RTRIM(Ma) = @Code {conditionFilter}";

            return await _connection.QueryFirstOrDefaultAsync<LoaiTienSearchByCodeDto>(commandText,
                new { Code = code.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching currency by code");
            return null;
        }
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    #endregion
}
