using System;
using System.IO;
using System.Linq;

namespace GP.Mobile.TrackingTool
{
    /// <summary>
    /// Simple tracking tool to monitor legacy migration progress
    /// Scans the project and updates tracking documents
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("GP Mobile Legacy Migration Tracking Tool");
            Console.WriteLine("=======================================");
            Console.WriteLine();

            try
            {
                var currentDir = Directory.GetCurrentDirectory();
                var projectRoot = FindProjectRoot(currentDir);
                
                if (string.IsNullOrEmpty(projectRoot))
                {
                    Console.WriteLine("Error: Could not find project root");
                    return;
                }

                Console.WriteLine($"Project Root: {projectRoot}");
                Console.WriteLine();

                // Scan current implementation status
                var status = ScanImplementationStatus(projectRoot);
                
                // Display current status
                DisplayStatus(status);
                
                // Update tracking documents
                UpdateTrackingDocuments(projectRoot, status);
                
                Console.WriteLine();
                Console.WriteLine("Tracking update completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static string FindProjectRoot(string currentPath)
        {
            var directory = new DirectoryInfo(currentPath);
            
            while (directory != null)
            {
                if (Directory.Exists(Path.Combine(directory.FullName, "Base", "Business")) ||
                    File.Exists(Path.Combine(directory.FullName, "GP.Mobile.sln")) ||
                    Directory.Exists(Path.Combine(directory.FullName, "src", "API")))
                {
                    return directory.FullName;
                }
                
                directory = directory.Parent;
            }
            
            return "";
        }

        static ImplementationStatus ScanImplementationStatus(string projectRoot)
        {
            var status = new ImplementationStatus();
            
            // Get all legacy classes
            var legacyPath = Path.Combine(projectRoot, "Base", "Business");
            if (Directory.Exists(legacyPath))
            {
                var legacyFiles = Directory.GetFiles(legacyPath, "cls*.cs", SearchOption.TopDirectoryOnly);
                status.TotalLegacyClasses = legacyFiles.Length;
                
                Console.WriteLine($"Found {status.TotalLegacyClasses} legacy classes in Base/Business/");
            }
            
            // Scan modern implementation
            var dtoPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Models", "DTOs");
            var repositoryPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Data", "Repositories");
            var servicePath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Core", "Services");
            var controllerPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.API", "Controllers");
            
            if (Directory.Exists(dtoPath))
            {
                var dtoFiles = Directory.GetFiles(dtoPath, "*Dto.cs", SearchOption.TopDirectoryOnly);
                status.CompletedDTOs = dtoFiles.Length;
                Console.WriteLine($"Found {status.CompletedDTOs} DTO files");
                
                foreach (var file in dtoFiles)
                {
                    var className = Path.GetFileNameWithoutExtension(file).Replace("Dto", "");
                    status.ImplementedClasses.Add(className);
                }
            }
            
            if (Directory.Exists(repositoryPath))
            {
                var repoFiles = Directory.GetFiles(repositoryPath, "*Repository.cs", SearchOption.TopDirectoryOnly);
                status.CompletedRepositories = repoFiles.Length;
                Console.WriteLine($"Found {status.CompletedRepositories} Repository files");
            }
            
            if (Directory.Exists(servicePath))
            {
                var serviceFiles = Directory.GetFiles(servicePath, "*Service.cs", SearchOption.TopDirectoryOnly);
                status.CompletedServices = serviceFiles.Length;
                Console.WriteLine($"Found {status.CompletedServices} Service files");
            }
            
            if (Directory.Exists(controllerPath))
            {
                var controllerFiles = Directory.GetFiles(controllerPath, "*Controller.cs", SearchOption.TopDirectoryOnly);
                status.CompletedControllers = controllerFiles.Length;
                Console.WriteLine($"Found {status.CompletedControllers} Controller files");
            }
            
            // Calculate completion status
            status.CalculateCompletion();
            
            return status;
        }

        static void DisplayStatus(ImplementationStatus status)
        {
            Console.WriteLine();
            Console.WriteLine("CURRENT IMPLEMENTATION STATUS");
            Console.WriteLine("=============================");
            Console.WriteLine($"Total Legacy Classes: {status.TotalLegacyClasses}");
            Console.WriteLine($"Completed Classes: {status.CompletedClasses}");
            Console.WriteLine($"Completion Rate: {status.CompletionPercentage:F1}%");
            Console.WriteLine();
            
            Console.WriteLine("COMPONENT STATUS:");
            Console.WriteLine($"  DTOs: {status.CompletedDTOs}");
            Console.WriteLine($"  Repositories: {status.CompletedRepositories}");
            Console.WriteLine($"  Services: {status.CompletedServices}");
            Console.WriteLine($"  Controllers: {status.CompletedControllers}");
            Console.WriteLine();
            
            if (status.ImplementedClasses.Any())
            {
                Console.WriteLine("IMPLEMENTED CLASSES:");
                foreach (var className in status.ImplementedClasses.OrderBy(c => c))
                {
                    var hasRepo = status.CompletedRepositories > 0;
                    var hasService = status.CompletedServices > 0;
                    var hasController = status.CompletedControllers > 0;
                    
                    var completionStatus = "DTO";
                    if (hasRepo && hasService && hasController)
                        completionStatus = "COMPLETE";
                    else if (hasRepo && hasService)
                        completionStatus = "DTO+Repo+Service";
                    else if (hasRepo)
                        completionStatus = "DTO+Repo";
                    
                    Console.WriteLine($"  ✅ {className} ({completionStatus})");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("PROGRESS ANALYSIS:");
            if (status.CompletionPercentage < 5)
                Console.WriteLine("  🔴 CRITICAL: Very low completion rate - need immediate action");
            else if (status.CompletionPercentage < 25)
                Console.WriteLine("  🟡 WARNING: Low completion rate - need to accelerate");
            else if (status.CompletionPercentage < 75)
                Console.WriteLine("  🟢 GOOD: Making progress - maintain momentum");
            else
                Console.WriteLine("  🎉 EXCELLENT: High completion rate - almost done!");
            
            var remainingClasses = status.TotalLegacyClasses - status.CompletedClasses;
            Console.WriteLine($"  Remaining classes: {remainingClasses}");
            
            if (status.CompletedClasses > 0)
            {
                var weeksToComplete = remainingClasses / (status.CompletedClasses / 4.0); // Assume 4 weeks so far
                Console.WriteLine($"  Estimated weeks to completion: {weeksToComplete:F1}");
            }
        }

        static void UpdateTrackingDocuments(string projectRoot, ImplementationStatus status)
        {
            Console.WriteLine();
            Console.WriteLine("UPDATING TRACKING DOCUMENTS...");
            
            // Update main tracking document
            var trackingFile = Path.Combine(projectRoot, "docs", "Legacy-Migration-Tracking.md");
            if (File.Exists(trackingFile))
            {
                var content = File.ReadAllText(trackingFile);
                
                // Update summary statistics
                content = UpdateSummaryStatistics(content, status);
                
                File.WriteAllText(trackingFile, content);
                Console.WriteLine("✅ Updated Legacy-Migration-Tracking.md");
            }
            
            // Create current week's progress report
            var weeklyTemplate = Path.Combine(projectRoot, "docs", "Weekly-Progress-Template.md");
            if (File.Exists(weeklyTemplate))
            {
                var template = File.ReadAllText(weeklyTemplate);
                var currentWeekReport = template.Replace("[DATE]", DateTime.Now.ToString("yyyy-MM-dd"));
                
                var weeklyReportFile = Path.Combine(projectRoot, "docs", $"Weekly-Progress-{DateTime.Now:yyyy-MM-dd}.md");
                File.WriteAllText(weeklyReportFile, currentWeekReport);
                Console.WriteLine($"✅ Created {Path.GetFileName(weeklyReportFile)}");
            }
        }

        static string UpdateSummaryStatistics(string content, ImplementationStatus status)
        {
            // Update the summary statistics section
            var lines = content.Split('\n');
            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].Contains("**Total Legacy Classes**:"))
                    lines[i] = $"- **Total Legacy Classes**: {status.TotalLegacyClasses}";
                else if (lines[i].Contains("**Completed**:"))
                    lines[i] = $"- **Completed**: {status.CompletedClasses} ({status.CompletionPercentage:F1}%)";
                else if (lines[i].Contains("**Not Started**:"))
                {
                    var notStarted = status.TotalLegacyClasses - status.CompletedClasses;
                    var notStartedPercentage = (double)notStarted / status.TotalLegacyClasses * 100;
                    lines[i] = $"- **Not Started**: {notStarted} ({notStartedPercentage:F1}%)";
                }
            }
            
            return string.Join('\n', lines);
        }
    }

    public class ImplementationStatus
    {
        public int TotalLegacyClasses { get; set; }
        public int CompletedDTOs { get; set; }
        public int CompletedRepositories { get; set; }
        public int CompletedServices { get; set; }
        public int CompletedControllers { get; set; }
        public int CompletedClasses { get; set; }
        public double CompletionPercentage { get; set; }
        public List<string> ImplementedClasses { get; set; } = new();

        public void CalculateCompletion()
        {
            // A class is considered complete if it has DTO + Repository + Service + Controller
            CompletedClasses = Math.Min(Math.Min(CompletedDTOs, CompletedRepositories), 
                                      Math.Min(CompletedServices, CompletedControllers));
            
            if (TotalLegacyClasses > 0)
                CompletionPercentage = (double)CompletedClasses / TotalLegacyClasses * 100;
        }
    }
}
