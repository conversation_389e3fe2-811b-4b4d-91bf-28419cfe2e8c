using GP.Mobile.Core.Services;
using GP.Mobile.Core.Repositories;
using GP.Mobile.Core.Interfaces;
using GP.Mobile.Data;
using GP.Mobile.Data.Repositories;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Microsoft.Data.SqlClient;
using System.Data;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/gp-mobile-api-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database connection - using same connection as legacy system
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddScoped<IDbConnection>(provider => new SqlConnection(connectionString));

// Entity Framework (if needed for new tables)
builder.Services.AddDbContext<GPMobileDbContext>(options =>
    options.UseSqlServer(connectionString));

// Database connection for repositories
builder.Services.AddScoped<IDbConnection>(provider => new SqlConnection(connectionString));

// AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Repository pattern - Implemented repositories from Data layer
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IDoiTuongRepository, GP.Mobile.Data.Repositories.DoiTuongRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.ICoHoiRepository, GP.Mobile.Data.Repositories.CoHoiRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IBaoGiaRepository, GP.Mobile.Data.Repositories.BaoGiaRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IBaoGiaChiTietRepository, GP.Mobile.Data.Repositories.BaoGiaChiTietRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IBaoGiaSuaChuaRepository, GP.Mobile.Data.Repositories.BaoGiaSuaChuaRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IBaoGiaSuaChuaChiTietRepository, GP.Mobile.Data.Repositories.BaoGiaSuaChuaChiTietRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.INhapKhoRepository, GP.Mobile.Data.Repositories.NhapKhoRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IXuatKhoRepository, GP.Mobile.Data.Repositories.XuatKhoRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IDonViTinhRepository, GP.Mobile.Data.Repositories.DonViTinhRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.ILoaiTienRepository, GP.Mobile.Data.Repositories.LoaiTienRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IDonViRepository, GP.Mobile.Data.Repositories.DonViRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.ILoaiDichVuRepository, GP.Mobile.Data.Repositories.LoaiDichVuRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.ILoaiXeRepository, GP.Mobile.Data.Repositories.LoaiXeRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IHangSanXuatRepository, GP.Mobile.Data.Repositories.HangSanXuatRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IBaoDuongRepository, GP.Mobile.Data.Repositories.BaoDuongRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IXeRepository, GP.Mobile.Data.Repositories.XeRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IKhoRepository, GP.Mobile.Data.Repositories.KhoRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IHangHoaRepository, GP.Mobile.Data.Repositories.HangHoaRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.INhomHangHoaRepository, GP.Mobile.Data.Repositories.NhomHangHoaRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.INhanVienRepository, GP.Mobile.Data.Repositories.NhanVienRepository>();
builder.Services.AddScoped<GP.Mobile.Data.Repositories.IKhoanMucChiPhiRepository, GP.Mobile.Data.Repositories.KhoanMucChiPhiRepository>();
builder.Services.AddScoped<GP.Mobile.Core.Repositories.KhoanMucSuaChuaRepository>();
builder.Services.AddScoped<GP.Mobile.Core.Repositories.SoChungTuRepository>();
builder.Services.AddScoped<GP.Mobile.Core.Repositories.LogRepository>();
builder.Services.AddScoped<GP.Mobile.Models.Interfaces.IBaoGiaYeuCauSuaChuaChiTietRepository, GP.Mobile.Data.Repositories.BaoGiaYeuCauSuaChuaChiTietRepository>();
builder.Services.AddScoped<CongLaoDongRepository>();

// Authentication repositories and services
builder.Services.AddScoped<GP.Mobile.Models.Interfaces.ITempBaoGiaRepository, GP.Mobile.Data.Repositories.TempBaoGiaRepository>();
builder.Services.AddScoped<GP.Mobile.Models.Interfaces.IBaoGiaHinhAnhBHRepository, GP.Mobile.Data.Repositories.BaoGiaHinhAnhBHRepository>();
builder.Services.AddScoped<GP.Mobile.Models.Interfaces.IDieuKhoanBaoGiaRepository, GP.Mobile.Data.Repositories.DieuKhoanBaoGiaRepository>();
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();

// Business services
builder.Services.AddScoped<IDoiTuongService, DoiTuongService>();
builder.Services.AddScoped<ICoHoiService, CoHoiService>();
builder.Services.AddScoped<IBaoGiaService, BaoGiaService>();
builder.Services.AddScoped<IBaoGiaChiTietService, BaoGiaChiTietService>();
builder.Services.AddScoped<IBaoGiaSuaChuaService, BaoGiaSuaChuaService>();
builder.Services.AddScoped<IBaoGiaSuaChuaChiTietService, BaoGiaSuaChuaChiTietService>();
builder.Services.AddScoped<INhapKhoService, NhapKhoService>();
builder.Services.AddScoped<IXuatKhoService, XuatKhoService>();
builder.Services.AddScoped<IDonViTinhService, DonViTinhService>();
builder.Services.AddScoped<ILoaiTienService, LoaiTienService>();
builder.Services.AddScoped<IDonViService, DonViService>();
builder.Services.AddScoped<ILoaiDichVuService, LoaiDichVuService>();
builder.Services.AddScoped<ILoaiXeService, LoaiXeService>();
builder.Services.AddScoped<IHangSanXuatService, HangSanXuatService>();
// TODO: Fix interface mismatches for these services
// builder.Services.AddScoped<IBaoDuongService, BaoDuongService>();
builder.Services.AddScoped<IXeService, XeService>();
builder.Services.AddScoped<IKhoService, KhoService>();
builder.Services.AddScoped<IHangHoaService, HangHoaService>();
builder.Services.AddScoped<INhomHangHoaService, NhomHangHoaService>();
builder.Services.AddScoped<INhanVienService, NhanVienService>();
builder.Services.AddScoped<IKhoanMucChiPhiService, KhoanMucChiPhiService>();
// builder.Services.AddScoped<IKhoanMucSuaChuaService, KhoanMucSuaChuaService>();
// builder.Services.AddScoped<ISoChungTuService, SoChungTuService>();
// builder.Services.AddScoped<ILogService, LogService>();
// builder.Services.AddScoped<GP.Mobile.Core.Services.IBaoGiaYeuCauSuaChuaChiTietService, GP.Mobile.Core.Services.BaoGiaYeuCauSuaChuaChiTietService>();
builder.Services.AddScoped<CongLaoDongService>();

// TODO: Fix interface mismatches for authentication services
// builder.Services.AddScoped<ITempBaoGiaService, TempBaoGiaService>();
// builder.Services.AddScoped<IBaoGiaHinhAnhBHService, BaoGiaHinhAnhBHService>();
// builder.Services.AddScoped<IDieuKhoanBaoGiaService, DieuKhoanBaoGiaService>();

// CORS for frontend and mobile app
builder.Services.AddCors(options =>
{
    options.AddPolicy("LocalDevelopment", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://********:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("LocalDevelopment");
app.UseAuthorization();
app.MapControllers();

// Test database connection on startup
try
{
    using var connection = new SqlConnection(connectionString);
    connection.Open();
    Console.WriteLine($"✅ Database connection successful: {connection.Database}");
    Console.WriteLine($"✅ Current user: {connection.DataSource}");
    connection.Close();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Database connection failed: {ex.Message}");
    Console.WriteLine($"Connection string: {connectionString?.Replace("Password=", "Password=***")}");
}

Log.Information("🚀 GP Mobile API started successfully!");
Console.WriteLine("📊 Swagger UI: https://localhost:7001/swagger");
Console.WriteLine("🔍 Health check: https://localhost:7001/api/health");
Console.WriteLine("💾 Database test: https://localhost:7001/api/health/database");

app.Run();
