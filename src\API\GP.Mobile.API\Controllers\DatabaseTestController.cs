using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Dapper;
using System.Security.Cryptography;
using System.Text;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Database connection test controller
    /// Tests connection to CARSOFT_GIAPHAT database with Windows authentication
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DatabaseTestController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseTestController> _logger;

        public DatabaseTestController(IConfiguration configuration, ILogger<DatabaseTestController> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        [HttpGet("connection-test")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var command = new SqlCommand(@"
                    SELECT 
                        DB_NAME() as DatabaseName, 
                        SYSTEM_USER as CurrentUser, 
                        @@SERVERNAME as ServerName,
                        @@VERSION as SqlVersion,
                        GETDATE() as CurrentTime", connection);
                
                var reader = await command.ExecuteReaderAsync();
                
                var result = new Dictionary<string, object>();
                if (await reader.ReadAsync())
                {
                    result["DatabaseName"] = reader["DatabaseName"];
                    result["CurrentUser"] = reader["CurrentUser"];
                    result["ServerName"] = reader["ServerName"];
                    result["SqlVersion"] = reader["SqlVersion"].ToString().Split('\n')[0]; // First line only
                    result["CurrentTime"] = reader["CurrentTime"];
                    result["ConnectionString"] = _connectionString.Replace("Password=", "Password=***");
                    result["Status"] = "Connected Successfully";
                    result["AuthenticationType"] = "Windows Authentication";
                }
                
                _logger.LogInformation("Database connection test successful for {DatabaseName}", result["DatabaseName"]);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed");
                return BadRequest(new { 
                    Status = "Connection Failed", 
                    Error = ex.Message,
                    ConnectionString = _connectionString.Replace("Password=", "Password=***"),
                    Suggestion = "Please check if SQL Server is running and CARSOFT_GIAPHAT database exists"
                });
            }
        }

        /// <summary>
        /// Test required tables for authentication
        /// </summary>
        [HttpGet("tables-test")]
        public async Task<IActionResult> TestTables()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var command = new SqlCommand(@"
                    SELECT TABLE_NAME, TABLE_TYPE
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_TYPE = 'BASE TABLE' 
                    AND TABLE_NAME IN (
                        'DM_NguoiDung', 'DM_DonVi', 'DM_DieuKhoanbaoGia', 
                        'Temp_BaoGia', 'SC_BaoGiaHinhAnhBH', 'SC_BaoGiaYeuCauSuaChuaChiTiet'
                    )
                    ORDER BY TABLE_NAME", connection);
                
                var tables = new List<object>();
                var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    tables.Add(new {
                        TableName = reader["TABLE_NAME"].ToString(),
                        TableType = reader["TABLE_TYPE"].ToString()
                    });
                }
                
                var requiredTables = new[] { 
                    "DM_NguoiDung", "DM_DonVi", "DM_DieuKhoanbaoGia", 
                    "Temp_BaoGia", "SC_BaoGiaHinhAnhBH", "SC_BaoGiaYeuCauSuaChuaChiTiet" 
                };
                
                var foundTables = tables.Select(t => ((dynamic)t).TableName).ToList();
                var missingTables = requiredTables.Where(t => !foundTables.Contains(t)).ToList();
                
                return Ok(new { 
                    Status = missingTables.Any() ? "Some Tables Missing" : "All Tables Found", 
                    FoundTables = tables,
                    MissingTables = missingTables,
                    TotalFound = tables.Count,
                    TotalRequired = requiredTables.Length
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database tables test failed");
                return BadRequest(new { 
                    Status = "Tables Test Failed", 
                    Error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Test authentication data
        /// </summary>
        [HttpGet("auth-data-test")]
        public async Task<IActionResult> TestAuthData()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Test users (HT_NguoiDung = System Users table)
                var userCount = await connection.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM HT_NguoiDung");

                // Test branches (DM_DonVi = Units/Departments table)
                var branchCount = await connection.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM DM_DonVi");
                
                // Test terms
                var termsCount = await connection.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM DM_DieuKhoanbaoGia");
                
                // Test quotations
                var quotationCount = await connection.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM Temp_BaoGia");
                
                // Get sample user (HT_NguoiDung = System Users table)
                var sampleUsers = await connection.QueryAsync<dynamic>(
                    "SELECT TOP 3 TenDangNhap, TenViet, LoaiNguoiDung FROM HT_NguoiDung");
                
                return Ok(new {
                    Status = "Authentication Data Available",
                    UserCount = userCount,
                    BranchCount = branchCount,
                    TermsCount = termsCount,
                    QuotationCount = quotationCount,
                    SampleUsers = sampleUsers,
                    DatabaseReady = userCount > 0 && branchCount > 0
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Authentication data test failed");
                return BadRequest(new { 
                    Status = "Auth Data Test Failed", 
                    Error = ex.Message,
                    Suggestion = "Run database-test-data.sql to create test data"
                });
            }
        }

        /// <summary>
        /// Test MD5 hash generation for master password (REMOVE IN PRODUCTION)
        /// </summary>
        [HttpGet("test-md5")]
        public IActionResult TestMD5()
        {
            try
            {
                // Test MD5 hash generation for master password
                var password = "123456";
                var computedHash = ComputeMD5Hash(password);
                var expectedHash = "E10ADC3949BA59ABBE56E057F20F883E";

                return Ok(new {
                    Password = password,
                    ComputedHash = computedHash,
                    ExpectedHash = expectedHash,
                    Match = string.Equals(computedHash, expectedHash, StringComparison.OrdinalIgnoreCase),
                    Note = "This is for debugging only - remove in production"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MD5 test failed");
                return BadRequest(new {
                    Status = "MD5 Test Failed",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// List available usernames for debugging (REMOVE IN PRODUCTION)
        /// </summary>
        [HttpGet("list-users")]
        public async Task<IActionResult> ListUsers()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Get sample usernames from HT_NguoiDung
                var users = await connection.QueryAsync<dynamic>(
                    "SELECT TOP 10 TenDangNhap FROM HT_NguoiDung ORDER BY TenDangNhap");

                return Ok(new {
                    Status = "Available Users",
                    Users = users,
                    Note = "This is for debugging only - remove in production"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "List users failed");
                return BadRequest(new {
                    Status = "List Users Failed",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Test units and user permissions (REMOVE IN PRODUCTION)
        /// </summary>
        [HttpGet("test-units")]
        public async Task<IActionResult> TestUnits()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Get all units (first check what columns exist)
                var allUnits = await connection.QueryAsync(@"
                    SELECT Khoa, Ma, TenViet, TenAnh, Prefix
                    FROM DM_DonVi
                    ORDER BY Ma");

                // Get user's allowed units for 'ngan'
                var userUnits = await connection.QueryFirstOrDefaultAsync<string>(@"
                    SELECT DonViDangNhap
                    FROM HT_NguoiDung
                    WHERE TenDangNhap = 'ngan'");

                // Check if TT unit exists
                var ttUnit = await connection.QueryFirstOrDefaultAsync(@"
                    SELECT Khoa, Ma, TenViet, TenAnh, Prefix
                    FROM DM_DonVi
                    WHERE Khoa = 'TT' OR Ma = 'TT'");

                // Get table structure
                var tableStructure = await connection.QueryAsync(@"
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'DM_DonVi'
                    ORDER BY ORDINAL_POSITION");

                return Ok(new
                {
                    AllUnits = allUnits,
                    UserAllowedUnits = userUnits,
                    TTUnit = ttUnit,
                    TableStructure = tableStructure,
                    Message = "Unit information test",
                    Note = "This is for debugging only - remove in production"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test units failed");
                return BadRequest(new
                {
                    Status = "Test Units Failed",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Health check endpoint
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var dbName = await connection.QuerySingleAsync<string>("SELECT DB_NAME()");

                return Ok(new {
                    status = "healthy",
                    timestamp = DateTime.Now,
                    database = dbName,
                    version = "1.0.0",
                    environment = "development"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new {
                    status = "unhealthy",
                    timestamp = DateTime.Now,
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Compute MD5 hash of a string (same as AuthenticationService)
        /// </summary>
        private string ComputeMD5Hash(string input)
        {
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            return Convert.ToHexString(hashBytes);
        }
    }
}
