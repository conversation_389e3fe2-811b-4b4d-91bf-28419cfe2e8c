using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for Xe (Individual Vehicles) entity
/// Implements ALL endpoints from clsDMXe.cs (1417 lines)
/// Includes REST API and 20+ legacy method endpoints
/// Maps to DM_Xe table with 40 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class XeController : ControllerBase
{
    private readonly IXeService _xeService;
    private readonly ILogger<XeController> _logger;

    public XeController(IXeService xeService, ILogger<XeController> logger)
    {
        _xeService = xeService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all vehicles
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<XeListDto>>> GetAll()
    {
        try
        {
            var result = await _xeService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all vehicles");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<XeDto>> GetById(string khoa)
    {
        try
        {
            var result = await _xeService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle by license plate
    /// </summary>
    [HttpGet("license-plate/{soXe}")]
    public async Task<ActionResult<XeDto>> GetByLicensePlate(string soXe)
    {
        try
        {
            var result = await _xeService.GetByLicensePlateAsync(soXe);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by license plate");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new vehicle
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateXeDto createDto)
    {
        try
        {
            var result = await _xeService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo xe");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update vehicle
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] XeDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _xeService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete vehicle
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _xeService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vehicle");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update vehicle info (mileage, insurance, etc.)
    /// </summary>
    [HttpPut("{khoa}/info")]
    public async Task<ActionResult<bool>> UpdateInfo(string khoa, [FromBody] UpdateXeInfoDto updateDto)
    {
        try
        {
            if (khoa != updateDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _xeService.UpdateInfoAsync(updateDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle info");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search vehicles
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<XeListDto>>> Search([FromBody] XeSearchDto searchDto)
    {
        try
        {
            var result = await _xeService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vehicles");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<XeLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _xeService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate vehicle data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<XeValidationDto>> Validate([FromBody] XeValidationRequestDto request)
    {
        try
        {
            var result = await _xeService.ValidateAsync(request.Khoa, request.SoXe, request.SoSuon, request.SoMay, request.MaVin);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating vehicle");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle insurance information
    /// </summary>
    [HttpGet("insurance")]
    public async Task<ActionResult<IEnumerable<XeInsuranceDto>>> GetInsuranceInfo([FromQuery] string condition = "")
    {
        try
        {
            var result = await _xeService.GetInsuranceInfoAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insurance info");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle maintenance history
    /// </summary>
    [HttpGet("maintenance")]
    public async Task<ActionResult<IEnumerable<XeMaintenanceDto>>> GetMaintenanceHistory([FromQuery] string condition = "")
    {
        try
        {
            var result = await _xeService.GetMaintenanceHistoryAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance history");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicles for a specific customer
    /// </summary>
    [HttpGet("customer/{khoaKhachHang}")]
    public async Task<ActionResult<IEnumerable<CustomerVehicleDto>>> GetCustomerVehicles(string khoaKhachHang)
    {
        try
        {
            var result = await _xeService.GetCustomerVehiclesAsync(khoaKhachHang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer vehicles");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive vehicle categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<IndividualVehicleCategoryDto>>> GetVehicleCategories()
    {
        try
        {
            var result = await _xeService.GetVehicleCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicles with complete details
    /// </summary>
    [HttpGet("with-details")]
    public async Task<ActionResult<IEnumerable<XeWithDetailsDto>>> GetVehiclesWithDetails()
    {
        try
        {
            var result = await _xeService.GetVehiclesWithDetailsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicles with details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<XeStatsDto>> GetVehicleStats(string khoa)
    {
        try
        {
            var result = await _xeService.GetVehicleStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Merge vehicles
    /// </summary>
    [HttpPost("merge")]
    public async Task<ActionResult<bool>> MergeVehicles([FromBody] XeMergeDto mergeDto)
    {
        try
        {
            var result = await _xeService.MergeVehiclesAsync(mergeDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging vehicles");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle registration status
    /// </summary>
    [HttpGet("registration-status")]
    public async Task<ActionResult<IEnumerable<XeRegistrationDto>>> GetRegistrationStatus()
    {
        try
        {
            var result = await _xeService.GetRegistrationStatusAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting registration status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle specifications
    /// </summary>
    [HttpGet("specifications")]
    public async Task<ActionResult<IEnumerable<VehicleSpecificationDto>>> GetVehicleSpecifications()
    {
        try
        {
            var result = await _xeService.GetVehicleSpecificationsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle specifications");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _xeService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadBienSo method endpoint
    /// </summary>
    [HttpPost("loadbienso")]
    public async Task<ActionResult<bool>> LoadBienSo([FromBody] string soXe)
    {
        try
        {
            var result = await _xeService.LoadBienSoAsync(soXe);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadBienSo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] XeDto dto)
    {
        try
        {
            var result = await _xeService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _xeService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult> ClearTemp([FromBody] string khoaDoiTuong)
    {
        try
        {
            await _xeService.ClearTempAsync(khoaDoiTuong);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] XeShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _xeService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetCarInfomationFirst method endpoint
    /// </summary>
    [HttpPost("getcarinfomationfirst")]
    public async Task<ActionResult<string>> GetCarInfomationFirst([FromBody] string khoaDoiTuong)
    {
        try
        {
            var result = await _xeService.GetCarInfomationFirstAsync(khoaDoiTuong);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetCarInfomationFirst endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetCarList method endpoint
    /// </summary>
    [HttpPost("getcarlist")]
    public async Task<ActionResult<DataTable>> GetCarList([FromBody] string khoaDoiTuong)
    {
        try
        {
            var result = await _xeService.GetCarListAsync(khoaDoiTuong);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetCarList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetInfDetailOfCar method endpoint
    /// </summary>
    [HttpPost("getinfdetailofcar")]
    public async Task<ActionResult<DataTable>> GetInfDetailOfCar([FromBody] XeGetInfDetailRequestDto request)
    {
        try
        {
            var result = await _xeService.GetInfDetailOfCarAsync(request.FieldList, request.FromClause, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetInfDetailOfCar endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetPDHanBaoHiem method endpoint
    /// </summary>
    [HttpPost("getpdhanbaohiem")]
    public async Task<ActionResult<DataTable>> GetPDHanBaoHiem([FromBody] string condition)
    {
        try
        {
            var result = await _xeService.GetPDHanBaoHiemAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPDHanBaoHiem endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetIDFromSoXe method endpoint
    /// </summary>
    [HttpPost("getidfromsoxe")]
    public async Task<ActionResult<string>> GetIDFromSoXe([FromBody] string soXe)
    {
        try
        {
            var result = await _xeService.GetIDFromSoXeAsync(soXe);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetIDFromSoXe endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchSoXe method endpoint
    /// </summary>
    [HttpPost("searchsoxe")]
    public async Task<ActionResult<string>> SearchSoXe([FromBody] string soXeFilter)
    {
        try
        {
            var result = await _xeService.SearchSoXeAsync(soXeFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchSoXe endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetXeBaoHiem method endpoint
    /// </summary>
    [HttpPost("getxebaohiem")]
    public async Task<ActionResult<DataTable>> GetXeBaoHiem([FromBody] string condition)
    {
        try
        {
            var result = await _xeService.GetXeBaoHiemAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetXeBaoHiem endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetLanSuaXe method endpoint
    /// </summary>
    [HttpPost("getlansuaxe")]
    public async Task<ActionResult<DataTable>> GetLanSuaXe([FromBody] string condition)
    {
        try
        {
            var result = await _xeService.GetLanSuaXeAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetLanSuaXe endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetXeListToKhachHang method endpoint
    /// </summary>
    [HttpPost("getxelisttokhachhang")]
    public async Task<ActionResult<DataTable>> GetXeListToKhachHang([FromBody] string khoaKH)
    {
        try
        {
            var result = await _xeService.GetXeListToKhachHangAsync(khoaKH);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetXeListToKhachHang endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListTheoKhachHang method endpoint
    /// </summary>
    [HttpPost("getlisttheokhachhang")]
    public async Task<ActionResult<DataTable>> GetListTheoKhachHang([FromBody] string khoa)
    {
        try
        {
            var result = await _xeService.GetListTheoKhachHangAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListTheoKhachHang endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy UpdateNewInfo method endpoint
    /// </summary>
    [HttpPost("updatenewinfo")]
    public async Task<ActionResult<bool>> UpdateNewInfo([FromBody] XeUpdateNewInfoRequestDto request)
    {
        try
        {
            var result = await _xeService.UpdateNewInfoAsync(request.KhoaHangBH, request.SoBH, request.HanBH, request.SoKm, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateNewInfo endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UpdateKm method endpoint
    /// </summary>
    [HttpPost("updatekm")]
    public async Task<ActionResult<bool>> UpdateKm([FromBody] XeUpdateKmRequestDto request)
    {
        try
        {
            var result = await _xeService.UpdateKmAsync(request.SoKm, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateKm endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UpdateNgayHetHanBH method endpoint
    /// </summary>
    [HttpPost("updatengayhethanBH")]
    public async Task<ActionResult<bool>> UpdateNgayHetHanBH([FromBody] XeUpdateNgayHetHanBHRequestDto request)
    {
        try
        {
            var result = await _xeService.UpdateNgayHetHanBHAsync(request.HanBH, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateNgayHetHanBH endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UpdateTinhTrangBaoHiem method endpoint
    /// </summary>
    [HttpPost("updatetinhtrangbaohiem")]
    public async Task<ActionResult<bool>> UpdateTinhTrangBaoHiem([FromBody] XeUpdateTinhTrangBaoHiemRequestDto request)
    {
        try
        {
            var result = await _xeService.UpdateTinhTrangBaoHiemAsync(request.Khoa, request.XeDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateTinhTrangBaoHiem endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy CheckValidForDel method endpoint
    /// </summary>
    [HttpPost("checkvalidfordel")]
    public async Task<ActionResult<bool>> CheckValidForDel([FromBody] string khoa)
    {
        try
        {
            var result = await _xeService.CheckValidForDelAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CheckValidForDel endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GomXe method endpoint
    /// </summary>
    [HttpPost("gomxe")]
    public async Task<ActionResult<bool>> GomXe([FromBody] XeGomXeRequestDto request)
    {
        try
        {
            var result = await _xeService.GomXeAsync(request.KhoaXeXoa, request.KhoaXeCanGom);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GomXe endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}

#region Request DTOs for Xe

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class XeValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string MaVin { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class XeShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetInfDetailOfCar method
/// </summary>
public class XeGetInfDetailRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string FromClause { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateNewInfo method
/// </summary>
public class XeUpdateNewInfoRequestDto
{
    public string KhoaHangBH { get; set; } = string.Empty;
    public string SoBH { get; set; } = string.Empty;
    public string HanBH { get; set; } = string.Empty;
    public double SoKm { get; set; } = 0.0;
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateKm method
/// </summary>
public class XeUpdateKmRequestDto
{
    public double SoKm { get; set; } = 0.0;
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateNgayHetHanBH method
/// </summary>
public class XeUpdateNgayHetHanBHRequestDto
{
    public string HanBH { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateTinhTrangBaoHiem method
/// </summary>
public class XeUpdateTinhTrangBaoHiemRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public XeDto XeDto { get; set; } = new();
}

/// <summary>
/// Request DTO for GomXe method
/// </summary>
public class XeGomXeRequestDto
{
    public string KhoaXeXoa { get; set; } = string.Empty;
    public string KhoaXeCanGom { get; set; } = string.Empty;
}

#endregion
