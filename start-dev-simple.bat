@echo off
echo.
echo ========================================
echo   GP Mobile Development Environment
echo ========================================
echo.

echo Stopping existing processes...
taskkill /f /im dotnet.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

echo.
echo Starting API Backend...
start "GP Mobile API" cmd /k "cd /d d:\Projects\gp-mobile-v1\src\API && dotnet run --project GP.Mobile.API"

echo.
echo Waiting for API to start...
timeout /t 5 >nul

echo.
echo Starting Frontend...
start "GP Mobile Frontend" cmd /k "cd /d d:\Projects\gp-mobile-v1\gp-web-frontend && npm run dev"

echo.
echo ========================================
echo   Services Starting...
echo ========================================
echo.
echo API Backend: http://localhost:51552
echo Frontend: http://localhost:3000 or 3001
echo Login Page: http://localhost:3001/login
echo.
echo Test Credentials:
echo   Username: ngan, adv, hieu
echo   Password: admin (master password)
echo   Unit: TT
echo.
echo Press any key to continue...
pause >nul
