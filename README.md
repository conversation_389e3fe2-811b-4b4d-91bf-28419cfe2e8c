# CARSOFT GIAPHAT - Backend API Project

This is a clean, organized codebase for the CARSOFT GIAPHAT automotive service management system backend API.

## Project Structure

### Core Directories

- **`Base/`** - Legacy system files (DO NOT MODIFY)
  - `Business/` - Legacy business logic classes
  - `Database/` - Legacy database scripts
  - `Forms/` - Legacy Windows Forms

- **`src/`** - Modern .NET Core API implementation
  - `API/` - Modern API controllers, services, and DTOs

- **`TestAPI/`** - Test API project for development

### Organized Resources

- **`docs/`** - All project documentation
  - Implementation guides and analysis
  - Progress tracking and templates
  - Legacy migration documentation

- **`scripts/`** - Development and automation scripts
  - PowerShell build scripts
  - JavaScript test utilities
  - Batch files for development

- **`database/`** - Database-related files
  - Test data scripts
  - Database connection utilities

- **`tools/`** - Development tools
  - Code generators
  - Tracking utilities

- **`web-tools/`** - Web-based testing and utility tools
  - HTML test interfaces
  - Development simulators

## Key Points

1. **Base folder is READ-ONLY** - Never modify anything in the Base directory
2. **Modern API development** happens in `src/API/`
3. **All documentation** is centralized in `docs/`
4. **Scripts and tools** are organized by type
5. **Frontend and mobile projects** have been removed for backend focus

## Development Workflow

1. Use scripts in `scripts/` folder for common development tasks
2. Refer to documentation in `docs/` for implementation guidance
3. Test using utilities in `web-tools/` and `database/`
4. Implement new features in `src/API/`

## Database Configuration

- Server: DESKTOP-J990JBB
- Database: carsoft_giaphat
- Authentication: SQL Server Authentication (sa user)
- Connection details in `database/` folder
