using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Service interface for Temporary Quotation Data (clsTempBaoGia)
    /// Provides business logic for managing temporary quotation data
    /// Implements exact functionality from clsTempBaoGia legacy class usage
    /// </summary>
    public interface ITempBaoGiaService
    {
        /// <summary>
        /// Load temporary quotation data by quotation ID
        /// Exact implementation from legacy Load method (line 10929)
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary quotation data or null if not found</returns>
        Task<TempBaoGiaDto?> LoadAsync(string khoaBaoGia);

        /// <summary>
        /// Save temporary quotation data
        /// Exact implementation from legacy Save method (line 9964)
        /// </summary>
        /// <param name="saveDto">Save data with specific properties</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> SaveAsync(SaveTempBaoGiaDto saveDto);

        /// <summary>
        /// Save request for cancellation approval
        /// Exact implementation from legacy SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        /// <param name="requestDto">Request cancellation approval data</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> SaveRequestDuyetHuyAsync(RequestDuyetHuyDto requestDto);

        /// <summary>
        /// Delete temporary quotation data
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> DeleteAsync(string khoaBaoGia);

        /// <summary>
        /// Check if temporary quotation data exists
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(string khoaBaoGia);

        /// <summary>
        /// Get temporary quotations requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of temporary quotations pending approval</returns>
        Task<ServiceResult<List<TempBaoGiaListDto>>> GetPendingApprovalsAsync(string donViId);

        /// <summary>
        /// Get temporary quotations by status
        /// </summary>
        /// <param name="trangThai">Status (0=Draft, 1=Pending, 2=Approved, 3=Cancelled)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of temporary quotations with specified status</returns>
        Task<ServiceResult<List<TempBaoGiaListDto>>> GetByStatusAsync(int trangThai, string donViId);

        /// <summary>
        /// Get temporary quotations by user
        /// </summary>
        /// <param name="nguoiTao">User ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of temporary quotations by user</returns>
        Task<ServiceResult<List<TempBaoGiaListDto>>> GetByUserAsync(string nguoiTao, string fromDate, string toDate);

        /// <summary>
        /// Get temporary quotations requiring cancellation approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of quotations with cancellation requests</returns>
        Task<ServiceResult<List<TempBaoGiaListDto>>> GetCancellationRequestsAsync(string donViId);

        /// <summary>
        /// Update cancellation approval status
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="isDuyetHuy">Approval status</param>
        /// <param name="nguoiDuyet">Approver ID</param>
        /// <param name="lyDo">Approval reason</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateCancellationApprovalAsync(string khoaBaoGia, bool isDuyetHuy, string nguoiDuyet, string lyDo = "");

        /// <summary>
        /// Get temporary quotation statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>Temporary quotation statistics</returns>
        Task<ServiceResult<TempBaoGiaStatisticsDto>> GetStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Clear temporary data for completed quotations
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="olderThanDays">Clear data older than specified days</param>
        /// <returns>Number of records cleared</returns>
        Task<ServiceResult<int>> ClearCompletedDataAsync(string donViId, int olderThanDays = 30);

        /// <summary>
        /// Bulk update temporary quotation status
        /// </summary>
        /// <param name="khoaBaoGiaList">List of quotation IDs</param>
        /// <param name="trangThai">New status</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Number of successfully updated records</returns>
        Task<ServiceResult<int>> BulkUpdateStatusAsync(List<string> khoaBaoGiaList, int trangThai, string nguoiCapNhat);

        /// <summary>
        /// Get temporary quotations for mobile app with pagination
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="searchTerm">Search term (optional)</param>
        /// <returns>Paginated list of temporary quotations</returns>
        Task<ServiceResult<PaginatedResult<TempBaoGiaListDto>>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null);

        /// <summary>
        /// Get temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary data content as string</returns>
        Task<string?> GetTempDataAsync(string khoaBaoGia);

        /// <summary>
        /// Update temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="tempData">Temporary data content</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateTempDataAsync(string khoaBaoGia, string tempData, string nguoiCapNhat);

        /// <summary>
        /// Get cancellation request details
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Cancellation request data</returns>
        Task<RequestDuyetHuyDto?> GetCancellationRequestAsync(string khoaBaoGia);

        /// <summary>
        /// Check if quotation has pending cancellation requests
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if has pending requests, false otherwise</returns>
        Task<bool> HasPendingCancellationRequestAsync(string khoaBaoGia);

        /// <summary>
        /// Get temporary quotations by date range
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of temporary quotations in date range</returns>
        Task<ServiceResult<List<TempBaoGiaListDto>>> GetByDateRangeAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Archive old temporary data
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="olderThanDays">Archive data older than specified days</param>
        /// <returns>Number of records archived</returns>
        Task<ServiceResult<int>> ArchiveOldDataAsync(string donViId, int olderThanDays = 90);

        /// <summary>
        /// Validate temporary quotation data before save
        /// </summary>
        /// <param name="saveDto">Save data to validate</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateForSaveAsync(SaveTempBaoGiaDto saveDto);

        /// <summary>
        /// Validate cancellation request before save
        /// </summary>
        /// <param name="requestDto">Request data to validate</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateCancellationRequestAsync(RequestDuyetHuyDto requestDto);

        /// <summary>
        /// Get temporary quotation workflow status
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Workflow status information</returns>
        Task<ServiceResult<TempBaoGiaWorkflowStatusDto>> GetWorkflowStatusAsync(string khoaBaoGia);

        /// <summary>
        /// Send notification for cancellation request
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="requestType">Request type</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendCancellationNotificationAsync(string khoaBaoGia, string requestType);

        /// <summary>
        /// Export temporary quotations to Excel report
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Excel report data</returns>
        Task<ServiceResult<byte[]>> ExportToExcelAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get temporary quotation audit trail
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>List of audit trail entries</returns>
        Task<ServiceResult<List<TempBaoGiaAuditDto>>> GetAuditTrailAsync(string khoaBaoGia);
    }

    /// <summary>
    /// Temporary quotation workflow status
    /// </summary>
    public class TempBaoGiaWorkflowStatusDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public int TrangThai { get; set; }
        public string TrangThaiText { get; set; } = string.Empty;
        public bool IsDuyetHuy { get; set; }
        public string NgayTao { get; set; } = string.Empty;
        public string NgayCapNhat { get; set; } = string.Empty;
        public bool CanApprove { get; set; }
        public bool CanReject { get; set; }
        public bool CanEdit { get; set; }
        public bool HasPendingRequest { get; set; }
        public List<string> NextActions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Temporary quotation audit trail
    /// </summary>
    public class TempBaoGiaAuditDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public string HanhDong { get; set; } = string.Empty;
        public string NguoiThucHien { get; set; } = string.Empty;
        public string ThoiGian { get; set; } = string.Empty;
        public string NoiDung { get; set; } = string.Empty;
        public string TrangThaiCu { get; set; } = string.Empty;
        public string TrangThaiMoi { get; set; } = string.Empty;
        public string GhiChu { get; set; } = string.Empty;
    }
}
