using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for Xe (Individual Vehicles) entity
/// Maps exactly to DM_Xe table in legacy database
/// Implements ALL properties from clsDMXe.cs (1417 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
public class XeDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Customer key - Foreign key to DM_DoiTuong
    /// Maps to: mKhoaDoiTuong property in legacy class
    /// </summary>
    [Required]
    public string KhoaDoiTuong { get; set; } = string.Empty;

    /// <summary>
    /// License plate for search (normalized)
    /// Maps to: mSoXeTimKiem property in legacy class
    /// </summary>
    [StringLength(50)]
    public string SoXeTimKiem { get; set; } = string.Empty;

    /// <summary>
    /// License plate number
    /// Maps to: mSoXe property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SoXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle type key - Foreign key to DM_LoaiXe
    /// Maps to: mKhoaLoaiXe property in legacy class
    /// </summary>
    [Required]
    public string KhoaLoaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Engine power
    /// Maps to: mMaLuc property in legacy class
    /// </summary>
    public double MaLuc { get; set; } = 0.0;

    /// <summary>
    /// Vehicle year/model year
    /// Maps to: mDoiXe property in legacy class
    /// </summary>
    [StringLength(20)]
    public string DoiXe { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer key - Foreign key to DM_HangSanXuat
    /// Maps to: mKhoaHangSanXuat property in legacy class
    /// </summary>
    public string KhoaHangSanXuat { get; set; } = string.Empty;

    /// <summary>
    /// Current mileage/kilometers
    /// Maps to: mSoKmHienTai property in legacy class
    /// </summary>
    public double SoKmHienTai { get; set; } = 0.0;

    /// <summary>
    /// Paint color
    /// Maps to: mMauSon property in legacy class
    /// </summary>
    [StringLength(100)]
    public string MauSon { get; set; } = string.Empty;

    /// <summary>
    /// Chassis number
    /// Maps to: mSoSuon property in legacy class
    /// </summary>
    [StringLength(100)]
    public string SoSuon { get; set; } = string.Empty;

    /// <summary>
    /// Engine number
    /// Maps to: mSoMay property in legacy class
    /// </summary>
    [StringLength(100)]
    public string SoMay { get; set; } = string.Empty;

    /// <summary>
    /// VIN number
    /// Maps to: mMaVin property in legacy class
    /// </summary>
    [StringLength(100)]
    public string MaVin { get; set; } = string.Empty;

    /// <summary>
    /// Insurance company key
    /// Maps to: mKhoaHangBaoHiem property in legacy class
    /// </summary>
    public string KhoaHangBaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Insurance policy number
    /// Maps to: mSoBaoHiem property in legacy class
    /// </summary>
    [StringLength(100)]
    public string SoBaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Insurance start date (YYYYMMDD format)
    /// Maps to: mNgayBatDauBaoHiem property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayBatDauBaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Insurance end date (YYYYMMDD format)
    /// Maps to: mNgayHetHanBaoHiem property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayHetHanBaoHiem { get; set; } = string.Empty;

    /// <summary>
    /// Total repair count
    /// Maps to: mTongLanSuaChua property in legacy class
    /// </summary>
    public int TongLanSuaChua { get; set; } = 0;

    /// <summary>
    /// Vehicle model
    /// Maps to: mModel property in legacy class
    /// </summary>
    [StringLength(100)]
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// Latest quotation key
    /// Maps to: mKhoaBaoGiaGanNhat property in legacy class
    /// </summary>
    public string KhoaBaoGiaGanNhat { get; set; } = string.Empty;

    /// <summary>
    /// Latest quotation date (YYYYMMDD format)
    /// Maps to: mNgayBaoGiaGanNhat property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayBaoGiaGanNhat { get; set; } = string.Empty;

    /// <summary>
    /// Previous mileage
    /// Maps to: mSoKMTruoc property in legacy class
    /// </summary>
    public double SoKMTruoc { get; set; } = 0.0;

    /// <summary>
    /// Driver name
    /// Maps to: mTenTaiXe property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenTaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Driver phone number
    /// Maps to: mDienThoaiTaiXe property in legacy class
    /// </summary>
    [StringLength(50)]
    public string DienThoaiTaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle delivery date (YYYYMMDD format)
    /// Maps to: mNgayGiaoXe property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayGiaoXe { get; set; } = string.Empty;

    /// <summary>
    /// Old license plate number
    /// Maps to: mSoXeCu property in legacy class
    /// </summary>
    [StringLength(50)]
    public string SoXeCu { get; set; } = string.Empty;

    /// <summary>
    /// Operating hours
    /// Maps to: mGioHoatDong property in legacy class
    /// </summary>
    public double GioHoatDong { get; set; } = 0.0;

    /// <summary>
    /// Previous operating hours
    /// Maps to: mGioHoatDongTruoc property in legacy class
    /// </summary>
    public double GioHoatDongTruoc { get; set; } = 0.0;

    /// <summary>
    /// Interior code
    /// Maps to: mMaNoiThat property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaNoiThat { get; set; } = string.Empty;

    /// <summary>
    /// Work location
    /// Maps to: mNoiLamViec property in legacy class
    /// </summary>
    [StringLength(200)]
    public string NoiLamViec { get; set; } = string.Empty;

    /// <summary>
    /// Registration expiry date (YYYYMMDD format)
    /// Maps to: mNgayHetHanDangKiem property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgayHetHanDangKiem { get; set; } = string.Empty;

    /// <summary>
    /// Business unit key - Foreign key to DM_DonVi
    /// Maps to: mKhoaDonVi property in legacy class
    /// </summary>
    public string KhoaDonVi { get; set; } = string.Empty;

    /// <summary>
    /// Seat count
    /// Maps to: mSoCho property in legacy class
    /// </summary>
    [StringLength(10)]
    public string SoCho { get; set; } = string.Empty;

    /// <summary>
    /// Internal number
    /// Maps to: mSoNoiBo property in legacy class
    /// </summary>
    [StringLength(50)]
    public string SoNoiBo { get; set; } = string.Empty;

    /// <summary>
    /// Engine type
    /// Maps to: mKieuDongCo property in legacy class
    /// </summary>
    [StringLength(100)]
    public string KieuDongCo { get; set; } = string.Empty;

    /// <summary>
    /// Fuel type key
    /// Maps to: mKhoaNhienLieu property in legacy class
    /// </summary>
    public string KhoaNhienLieu { get; set; } = string.Empty;

    /// <summary>
    /// Transmission type key
    /// Maps to: mKhoaHopSo property in legacy class
    /// </summary>
    public string KhoaHopSo { get; set; } = string.Empty;

    /// <summary>
    /// Tire size
    /// Maps to: mKichThuocLop property in legacy class
    /// </summary>
    [StringLength(50)]
    public string KichThuocLop { get; set; } = string.Empty;

    /// <summary>
    /// Is special purpose vehicle
    /// Maps to: mIsXeChuyenDung property in legacy class
    /// </summary>
    public bool IsXeChuyenDung { get; set; } = false;

    /// <summary>
    /// Battery information
    /// Maps to: mBinhDien property in legacy class
    /// </summary>
    [StringLength(100)]
    public string BinhDien { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Xe list display with joined data
/// Optimized for automotive vehicle lists with customer and manufacturer info
/// Used by GetCarList and similar methods
/// </summary>
public class XeListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaDoiTuong { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string KhoaHangBaoHiem { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty; // From DM_LoaiXe.TenViet
    public string HangSanXuat { get; set; } = string.Empty; // From DM_HangSanXuat.TenViet
    public string MauSon { get; set; } = string.Empty;
    public double SoKmHienTai { get; set; } = 0.0;
    public string SoMay { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string BaoHiem { get; set; } = string.Empty; // From insurance company
    public string SoBaoHiem { get; set; } = string.Empty;
    public DateTime? NgayBatDau { get; set; } // Converted from char2date
    public DateTime? NgayKetThuc { get; set; } // Converted from char2date
    public string KhachHang { get; set; } = string.Empty; // From DM_DoiTuong.TenViet
    public string DienThoai { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public int TongLanSuaChua { get; set; } = 0;
}

/// <summary>
/// DTO for creating new Xe
/// Contains only required fields for creation
/// </summary>
public class CreateXeDto
{
    [Required]
    public string KhoaDoiTuong { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string SoXe { get; set; } = string.Empty;

    [Required]
    public string KhoaLoaiXe { get; set; } = string.Empty;

    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string MauSon { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string MaVin { get; set; } = string.Empty;
    public double SoKmHienTai { get; set; } = 0.0;
    public string TenTaiXe { get; set; } = string.Empty;
    public string DienThoaiTaiXe { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating Xe mileage and basic info
/// Used for quick updates during service
/// </summary>
public class UpdateXeInfoDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public double SoKmHienTai { get; set; } = 0.0;
    public string KhoaHangBaoHiem { get; set; } = string.Empty;
    public string SoBaoHiem { get; set; } = string.Empty;
    public string NgayHetHanBaoHiem { get; set; } = string.Empty;
    public string TenTaiXe { get; set; } = string.Empty;
    public string DienThoaiTaiXe { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Xe search operations
/// Used for advanced search and filtering
/// </summary>
public class XeSearchDto
{
    public string? SoXe { get; set; }
    public string? KhoaDoiTuong { get; set; }
    public string? KhoaLoaiXe { get; set; }
    public string? KhoaHangSanXuat { get; set; }
    public string? DoiXe { get; set; }
    public string? Model { get; set; }
    public string? MauSon { get; set; }
    public string? SoSuon { get; set; }
    public string? SoMay { get; set; }
    public string? MaVin { get; set; }
    public double? SoKmFrom { get; set; }
    public double? SoKmTo { get; set; }
    public string? NgayHetHanBaoHiemFrom { get; set; }
    public string? NgayHetHanBaoHiemTo { get; set; }
    public string? NgayHetHanDangKiemFrom { get; set; }
    public string? NgayHetHanDangKiemTo { get; set; }
    public bool? IsXeChuyenDung { get; set; }
}

/// <summary>
/// DTO for Xe dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class XeLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public double SoKmHienTai { get; set; } = 0.0;
    public string KhachHang { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Xe validation operations
/// Used for duplicate checking and validation
/// </summary>
public class XeValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string MaVin { get; set; } = string.Empty;
    public bool IsDuplicateSoXe { get; set; } = false;
    public bool IsDuplicateSoSuon { get; set; } = false;
    public bool IsDuplicateSoMay { get; set; } = false;
    public bool IsDuplicateMaVin { get; set; } = false;
    public bool IsUsedInQuotations { get; set; } = false;
    public bool CanDelete { get; set; } = true;
}

/// <summary>
/// DTO for insurance information
/// Used by GetPDHanBaoHiem and GetXeBaoHiem methods
/// </summary>
public class XeInsuranceDto
{
    public string KhoaXe { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string KhoaBaoHiem { get; set; } = string.Empty;
    public string HangBaoHiem { get; set; } = string.Empty;
    public string SoBaoHiem { get; set; } = string.Empty;
    public DateTime? NgayBD { get; set; } // Insurance start date
    public DateTime? NgayHH { get; set; } // Insurance end date
    public bool IsExpiringSoon { get; set; } = false;
    public bool IsExpired { get; set; } = false;
    public int DaysToExpiry { get; set; } = 0;
}

/// <summary>
/// DTO for vehicle maintenance history
/// Used by GetLanSuaXe method
/// </summary>
public class XeMaintenanceDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public double SoKmHienTai { get; set; } = 0.0;
    public string KhachHang { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public int TongLanSuaChua { get; set; } = 0;
    public DateTime? NgayBaoGiaGanNhat { get; set; }
    public string KhoaBaoGiaGanNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for customer vehicle list
/// Used by GetXeListToKhachHang and GetListTheoKhachHang methods
/// </summary>
public class CustomerVehicleDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaXe { get; set; } = string.Empty;
    public bool Chon { get; set; } = false;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public double SoKMHienTai { get; set; } = 0.0;
    public decimal TienBaoDuong { get; set; } = 0;
    public int LanSuaChua { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string TenTaiXe { get; set; } = string.Empty;
    public string DienThoaiTaiXe { get; set; } = string.Empty;
}

/// <summary>
/// DTO for individual vehicle categories
/// Specialized for individual vehicle classification and analysis
/// </summary>
public class IndividualVehicleCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public bool IsPassengerCar { get; set; } = false;
    public bool IsCommercialVehicle { get; set; } = false;
    public bool IsMotorcycle { get; set; } = false;
    public bool IsTruck { get; set; } = false;
    public bool IsBus { get; set; } = false;
    public bool IsSpecialPurpose { get; set; } = false;
    public bool IsElectricVehicle { get; set; } = false;
    public bool IsHybridVehicle { get; set; } = false;
    public string FuelType { get; set; } = string.Empty;
    public string TransmissionType { get; set; } = string.Empty;
    public int SeatCount { get; set; } = 0;
    public double EngineCapacity { get; set; } = 0.0;
}

/// <summary>
/// DTO for vehicle with complete details
/// Used for comprehensive vehicle display
/// </summary>
public class XeWithDetailsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string KhoaDoiTuong { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string MauSon { get; set; } = string.Empty;
    public string SoSuon { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string MaVin { get; set; } = string.Empty;
    public double SoKmHienTai { get; set; } = 0.0;
    public string TenTaiXe { get; set; } = string.Empty;
    public string DienThoaiTaiXe { get; set; } = string.Empty;
    public string HangBaoHiem { get; set; } = string.Empty;
    public string SoBaoHiem { get; set; } = string.Empty;
    public DateTime? NgayBatDauBaoHiem { get; set; }
    public DateTime? NgayHetHanBaoHiem { get; set; }
    public DateTime? NgayHetHanDangKiem { get; set; }
    public int TongLanSuaChua { get; set; } = 0;
    public DateTime? NgayBaoGiaGanNhat { get; set; }
    public bool IsInsuranceExpiring { get; set; } = false;
    public bool IsRegistrationExpiring { get; set; } = false;
    public bool NeedsMaintenanceCheck { get; set; } = false;
}

/// <summary>
/// DTO for vehicle statistics
/// Used for reporting and analytics
/// </summary>
public class XeStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public int TotalServiceCount { get; set; } = 0;
    public int TotalMaintenanceCount { get; set; } = 0;
    public int TotalRepairCount { get; set; } = 0;
    public decimal TotalServiceAmount { get; set; } = 0;
    public decimal AverageServiceAmount { get; set; } = 0;
    public double TotalMileage { get; set; } = 0.0;
    public double AverageMileagePerService { get; set; } = 0.0;
    public DateTime? FirstServiceDate { get; set; }
    public DateTime? LastServiceDate { get; set; }
    public int DaysSinceLastService { get; set; } = 0;
    public double MileageSinceLastService { get; set; } = 0.0;
    public string ServiceFrequency { get; set; } = string.Empty; // High, Medium, Low
    public bool IsHighMaintenanceVehicle { get; set; } = false;
}

/// <summary>
/// DTO for vehicle merge operation
/// Used by GomXe method for combining vehicle records
/// </summary>
public class XeMergeDto
{
    [Required]
    public string KhoaXeXoa { get; set; } = string.Empty; // Vehicle to be deleted

    [Required]
    public string KhoaXeCanGom { get; set; } = string.Empty; // Vehicle to merge into

    public string LyDoGom { get; set; } = string.Empty; // Reason for merge
    public bool KeepServiceHistory { get; set; } = true;
    public bool KeepInsuranceInfo { get; set; } = true;
    public bool UpdateMileage { get; set; } = true;
}

/// <summary>
/// DTO for vehicle field-based queries
/// Used by ShowListByField and GetInfDetailOfCar methods
/// </summary>
public class XeFieldQueryDto
{
    public string FieldList { get; set; } = string.Empty; // Pipe-separated field list
    public string FromClause { get; set; } = string.Empty; // Custom FROM clause
    public string Conditions { get; set; } = string.Empty; // WHERE conditions
    public string OrderBy { get; set; } = string.Empty; // ORDER BY clause
    public int MaxRecords { get; set; } = 0; // 0 = no limit
}

/// <summary>
/// DTO for vehicle registration and inspection
/// Used for tracking registration and inspection dates
/// </summary>
public class XeRegistrationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public DateTime? NgayHetHanDangKiem { get; set; }
    public DateTime? NgayHetHanBaoHiem { get; set; }
    public bool IsRegistrationExpired { get; set; } = false;
    public bool IsInsuranceExpired { get; set; } = false;
    public bool IsRegistrationExpiringSoon { get; set; } = false;
    public bool IsInsuranceExpiringSoon { get; set; } = false;
    public int DaysToRegistrationExpiry { get; set; } = 0;
    public int DaysToInsuranceExpiry { get; set; } = 0;
    public string AlertLevel { get; set; } = string.Empty; // Critical, Warning, Normal
}

/// <summary>
/// DTO for vehicle fuel and transmission types
/// Used for automotive specifications
/// </summary>
public class VehicleSpecificationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string KieuDongCo { get; set; } = string.Empty;
    public string NhienLieu { get; set; } = string.Empty; // From fuel type lookup
    public string HopSo { get; set; } = string.Empty; // From transmission type lookup
    public string KichThuocLop { get; set; } = string.Empty;
    public string SoCho { get; set; } = string.Empty;
    public double MaLuc { get; set; } = 0.0;
    public double GioHoatDong { get; set; } = 0.0;
    public string MaNoiThat { get; set; } = string.Empty;
    public string BinhDien { get; set; } = string.Empty;
    public bool IsXeChuyenDung { get; set; } = false;
    public string DonVi { get; set; } = string.Empty; // From business unit
    public string NoiLamViec { get; set; } = string.Empty;
}
