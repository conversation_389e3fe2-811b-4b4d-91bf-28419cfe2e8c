using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for Xe (Individual Vehicles) service
/// Defines business logic operations for Xe entity
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
public interface IXeService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadBienSoAsync(string soXe);
    Task<bool> SaveAsync(XeDto dto);
    Task<bool> DelDataAsync(string khoa);
    Task ClearTempAsync(string khoaDoiTuong);
    Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");
    Task<string> GetCarInfomationFirstAsync(string khoaDoiTuong);
    Task<DataTable> GetCarListAsync(string khoaDoiTuong);
    Task<DataTable> GetInfDetailOfCarAsync(string strFieldList, string strFrom, string strConditions = "", string strOrder = "");
    Task<DataTable> GetPDHanBaoHiemAsync(string pCondition);
    Task<string> GetIDFromSoXeAsync(string pSoXe);
    Task<string> SearchSoXeAsync(string strSoXeFilter);
    Task<DataTable> GetXeBaoHiemAsync(string pCondition);
    Task<DataTable> GetLanSuaXeAsync(string pCondition);
    Task<DataTable> GetXeListToKhachHangAsync(string pKhoaKH);
    Task<DataTable> GetListTheoKhachHangAsync(string pKhoa);
    Task<bool> UpdateNewInfoAsync(string pKhoaHangBH, string pSoBH, string pHanBH, double pSoKm, string pKhoa);
    Task<bool> UpdateKmAsync(double pSoKm, string pKhoa);
    Task<bool> UpdateNgayHetHanBHAsync(string pHanBH, string pKhoa);
    Task<bool> UpdateTinhTrangBaoHiemAsync(string pKhoa, XeDto dto);
    Task<bool> CheckValidForDelAsync(string pKhoa);
    Task<bool> GomXeAsync(string pKhoaXeXoa, string pKhoaXeCanGom);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<XeListDto>> GetAllAsync();
    Task<XeDto?> GetByIdAsync(string khoa);
    Task<XeDto?> GetByLicensePlateAsync(string soXe);
    Task<string> CreateAsync(CreateXeDto createDto);
    Task<bool> UpdateAsync(XeDto dto);
    Task<bool> UpdateInfoAsync(UpdateXeInfoDto updateDto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<IEnumerable<XeListDto>> SearchAsync(XeSearchDto searchDto);
    Task<IEnumerable<XeLookupDto>> GetLookupAsync();
    Task<XeValidationDto> ValidateAsync(string khoa, string soXe, string soSuon, string soMay, string maVin);
    Task<IEnumerable<XeInsuranceDto>> GetInsuranceInfoAsync(string condition = "");
    Task<IEnumerable<XeMaintenanceDto>> GetMaintenanceHistoryAsync(string condition = "");
    Task<IEnumerable<CustomerVehicleDto>> GetCustomerVehiclesAsync(string khoaKhachHang);
    Task<IEnumerable<IndividualVehicleCategoryDto>> GetVehicleCategoriesAsync();
    Task<IEnumerable<XeWithDetailsDto>> GetVehiclesWithDetailsAsync();
    Task<XeStatsDto?> GetVehicleStatsAsync(string khoa);
    Task<bool> MergeVehiclesAsync(XeMergeDto mergeDto);
    Task<IEnumerable<XeRegistrationDto>> GetRegistrationStatusAsync();
    Task<IEnumerable<VehicleSpecificationDto>> GetVehicleSpecificationsAsync();
    
    #endregion
}

/// <summary>
/// Complete Service for Xe entity
/// Implements ALL business logic from clsDMXe.cs (1417 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for individual vehicle management linking customers, vehicle types, and manufacturers
/// </summary>
public class XeService : IXeService
{
    private readonly IXeRepository _repository;
    private readonly ILogger<XeService> _logger;

    public XeService(IXeRepository repository, ILogger<XeService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading vehicle");
            throw;
        }
    }

    public async Task<bool> LoadBienSoAsync(string soXe)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(soXe))
            {
                throw new ArgumentException("Biển số xe không được để trống");
            }

            return await _repository.LoadBienSoAsync(soXe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading vehicle by license plate");
            throw;
        }
    }

    public async Task<bool> SaveAsync(XeDto dto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto);

            return await _repository.SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving vehicle");
            throw;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa xe đã được sử dụng trong báo giá");
            }

            return await _repository.DelDataAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vehicle");
            throw;
        }
    }

    public async Task ClearTempAsync(string khoaDoiTuong)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(khoaDoiTuong))
            {
                await _repository.ClearTempAsync(khoaDoiTuong);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing temp data for customer");
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        try
        {
            // Apply security filters
            var secureConditions = ApplySecurityFilters(strConditions);
            return await _repository.ShowListByFieldAsync(strFieldList, secureConditions, strOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list by field");
            return new DataTable();
        }
    }

    public async Task<string> GetCarInfomationFirstAsync(string khoaDoiTuong)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaDoiTuong))
            {
                return "";
            }

            return await _repository.GetCarInfomationFirstAsync(khoaDoiTuong);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting first car information");
            return "";
        }
    }

    public async Task<DataTable> GetCarListAsync(string khoaDoiTuong)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaDoiTuong))
            {
                return new DataTable();
            }

            return await _repository.GetCarListAsync(khoaDoiTuong);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting car list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetInfDetailOfCarAsync(string strFieldList, string strFrom, string strConditions = "", string strOrder = "")
    {
        try
        {
            // Apply security filters
            var secureConditions = ApplySecurityFilters(strConditions);
            return await _repository.GetInfDetailOfCarAsync(strFieldList, strFrom, secureConditions, strOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting car detail information");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetPDHanBaoHiemAsync(string pCondition)
    {
        try
        {
            return await _repository.GetPDHanBaoHiemAsync(pCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insurance expiry data");
            return new DataTable();
        }
    }

    public async Task<string> GetIDFromSoXeAsync(string pSoXe)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pSoXe))
            {
                return "";
            }

            return await _repository.GetIDFromSoXeAsync(pSoXe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ID from license plate");
            return "";
        }
    }

    public async Task<string> SearchSoXeAsync(string strSoXeFilter)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(strSoXeFilter))
            {
                return "";
            }

            return await _repository.SearchSoXeAsync(strSoXeFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching license plate");
            return "";
        }
    }

    public async Task<DataTable> GetXeBaoHiemAsync(string pCondition)
    {
        try
        {
            return await _repository.GetXeBaoHiemAsync(pCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle insurance data");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetLanSuaXeAsync(string pCondition)
    {
        try
        {
            return await _repository.GetLanSuaXeAsync(pCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle repair history");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetXeListToKhachHangAsync(string pKhoaKH)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoaKH))
            {
                return new DataTable();
            }

            return await _repository.GetXeListToKhachHangAsync(pKhoaKH);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list for customer");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListTheoKhachHangAsync(string pKhoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                return new DataTable();
            }

            return await _repository.GetListTheoKhachHangAsync(pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list by customer");
            return new DataTable();
        }
    }

    public async Task<bool> UpdateNewInfoAsync(string pKhoaHangBH, string pSoBH, string pHanBH, double pSoKm, string pKhoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Validate business rules for update
            var validationResult = await ValidateUpdateInfoAsync(pKhoaHangBH, pSoBH, pHanBH, pSoKm, pKhoa);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateNewInfoAsync(pKhoaHangBH, pSoBH, pHanBH, pSoKm, pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle new info");
            throw;
        }
    }

    public async Task<bool> UpdateKmAsync(double pSoKm, string pKhoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            if (pSoKm < 0)
            {
                throw new ArgumentException("Số km không được âm");
            }

            return await _repository.UpdateKmAsync(pSoKm, pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle mileage");
            throw;
        }
    }

    public async Task<bool> UpdateNgayHetHanBHAsync(string pHanBH, string pKhoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Validate date format
            if (!string.IsNullOrEmpty(pHanBH) && !IsValidDateFormat(pHanBH))
            {
                throw new ArgumentException("Ngày hết hạn bảo hiểm phải có định dạng YYYYMMDD");
            }

            return await _repository.UpdateNgayHetHanBHAsync(pHanBH, pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating insurance expiry date");
            throw;
        }
    }

    public async Task<bool> UpdateTinhTrangBaoHiemAsync(string pKhoa, XeDto dto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Validate insurance information
            var validationResult = await ValidateInsuranceInfoAsync(dto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateTinhTrangBaoHiemAsync(pKhoa, dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating insurance status");
            throw;
        }
    }

    public async Task<bool> CheckValidForDelAsync(string pKhoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoa))
            {
                return false;
            }

            return await _repository.CheckValidForDelAsync(pKhoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if vehicle can be deleted");
            return false;
        }
    }

    public async Task<bool> GomXeAsync(string pKhoaXeXoa, string pKhoaXeCanGom)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pKhoaXeXoa) || string.IsNullOrWhiteSpace(pKhoaXeCanGom))
            {
                throw new ArgumentException("Khóa xe không được để trống");
            }

            if (pKhoaXeXoa == pKhoaXeCanGom)
            {
                throw new ArgumentException("Không thể gộp xe với chính nó");
            }

            // Validate merge operation
            var validationResult = await ValidateMergeAsync(pKhoaXeXoa, pKhoaXeCanGom);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.GomXeAsync(pKhoaXeXoa, pKhoaXeCanGom);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging vehicles");
            throw;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<XeListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all vehicles");
            return new List<XeListDto>();
        }
    }

    public async Task<XeDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by ID");
            return null;
        }
    }

    public async Task<XeDto?> GetByLicensePlateAsync(string soXe)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(soXe))
                return null;

            return await _repository.GetByLicensePlateAsync(soXe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by license plate");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateXeDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(XeDto dto)
    {
        return await SaveAsync(dto);
    }

    public async Task<bool> UpdateInfoAsync(UpdateXeInfoDto updateDto)
    {
        try
        {
            // Validate update data
            var validationResult = await ValidateUpdateInfoDtoAsync(updateDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateInfoAsync(updateDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle info");
            throw;
        }
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DelDataAsync(khoa);
    }

    public async Task<IEnumerable<XeListDto>> SearchAsync(XeSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vehicles");
            return new List<XeListDto>();
        }
    }

    public async Task<IEnumerable<XeLookupDto>> GetLookupAsync()
    {
        try
        {
            return await _repository.GetLookupAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle lookup");
            return new List<XeLookupDto>();
        }
    }

    public async Task<XeValidationDto> ValidateAsync(string khoa, string soXe, string soSuon, string soMay, string maVin)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, soXe, soSuon, soMay, maVin);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating vehicle");
            return new XeValidationDto { Khoa = khoa, SoXe = soXe, SoSuon = soSuon, SoMay = soMay, MaVin = maVin };
        }
    }

    public async Task<IEnumerable<XeInsuranceDto>> GetInsuranceInfoAsync(string condition = "")
    {
        try
        {
            return await _repository.GetInsuranceInfoAsync(condition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insurance info");
            return new List<XeInsuranceDto>();
        }
    }

    public async Task<IEnumerable<XeMaintenanceDto>> GetMaintenanceHistoryAsync(string condition = "")
    {
        try
        {
            return await _repository.GetMaintenanceHistoryAsync(condition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance history");
            return new List<XeMaintenanceDto>();
        }
    }

    public async Task<IEnumerable<CustomerVehicleDto>> GetCustomerVehiclesAsync(string khoaKhachHang)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaKhachHang))
                return new List<CustomerVehicleDto>();

            return await _repository.GetCustomerVehiclesAsync(khoaKhachHang);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer vehicles");
            return new List<CustomerVehicleDto>();
        }
    }

    public async Task<IEnumerable<IndividualVehicleCategoryDto>> GetVehicleCategoriesAsync()
    {
        try
        {
            return await _repository.GetVehicleCategoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle categories");
            return new List<IndividualVehicleCategoryDto>();
        }
    }

    public async Task<IEnumerable<XeWithDetailsDto>> GetVehiclesWithDetailsAsync()
    {
        try
        {
            return await _repository.GetVehiclesWithDetailsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicles with details");
            return new List<XeWithDetailsDto>();
        }
    }

    public async Task<XeStatsDto?> GetVehicleStatsAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetVehicleStatsAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle stats");
            return null;
        }
    }

    public async Task<bool> MergeVehiclesAsync(XeMergeDto mergeDto)
    {
        try
        {
            // Validate merge data
            var validationResult = await ValidateMergeDtoAsync(mergeDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.MergeVehiclesAsync(mergeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error merging vehicles");
            throw;
        }
    }

    public async Task<IEnumerable<XeRegistrationDto>> GetRegistrationStatusAsync()
    {
        try
        {
            return await _repository.GetRegistrationStatusAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting registration status");
            return new List<XeRegistrationDto>();
        }
    }

    public async Task<IEnumerable<VehicleSpecificationDto>> GetVehicleSpecificationsAsync()
    {
        try
        {
            return await _repository.GetVehicleSpecificationsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle specifications");
            return new List<VehicleSpecificationDto>();
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic for Automotive Vehicles)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(XeDto dto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.KhoaDoiTuong))
            result.Errors.Add("Khách hàng không được để trống");

        if (string.IsNullOrWhiteSpace(dto.SoXe))
            result.Errors.Add("Biển số xe không được để trống");

        if (string.IsNullOrWhiteSpace(dto.KhoaLoaiXe))
            result.Errors.Add("Loại xe không được để trống");

        // Business rule: Check for duplicate license plate
        if (!string.IsNullOrEmpty(dto.SoXe))
        {
            var validation = await _repository.ValidateAsync(dto.Khoa, dto.SoXe, dto.SoSuon, dto.SoMay, dto.MaVin);
            if (validation.IsDuplicateSoXe)
            {
                result.Errors.Add("Biển số xe đã tồn tại");
            }
            if (validation.IsDuplicateSoSuon && !string.IsNullOrEmpty(dto.SoSuon))
            {
                result.Errors.Add("Số khung đã tồn tại");
            }
            if (validation.IsDuplicateSoMay && !string.IsNullOrEmpty(dto.SoMay))
            {
                result.Errors.Add("Số máy đã tồn tại");
            }
            if (validation.IsDuplicateMaVin && !string.IsNullOrEmpty(dto.MaVin))
            {
                result.Errors.Add("Mã VIN đã tồn tại");
            }
        }

        // Length validation
        if (dto.SoXe.Length > 50)
            result.Errors.Add("Biển số xe không được vượt quá 50 ký tự");

        if (dto.SoSuon.Length > 100)
            result.Errors.Add("Số khung không được vượt quá 100 ký tự");

        if (dto.SoMay.Length > 100)
            result.Errors.Add("Số máy không được vượt quá 100 ký tự");

        if (dto.MaVin.Length > 100)
            result.Errors.Add("Mã VIN không được vượt quá 100 ký tự");

        // Mileage validation
        if (dto.SoKmHienTai < 0)
            result.Errors.Add("Số km hiện tại không được âm");

        if (dto.SoKMTruoc < 0)
            result.Errors.Add("Số km trước không được âm");

        if (dto.SoKmHienTai < dto.SoKMTruoc)
            result.Errors.Add("Số km hiện tại không được nhỏ hơn số km trước");

        // Date validation
        if (!string.IsNullOrEmpty(dto.NgayBatDauBaoHiem) && !IsValidDateFormat(dto.NgayBatDauBaoHiem))
            result.Errors.Add("Ngày bắt đầu bảo hiểm phải có định dạng YYYYMMDD");

        if (!string.IsNullOrEmpty(dto.NgayHetHanBaoHiem) && !IsValidDateFormat(dto.NgayHetHanBaoHiem))
            result.Errors.Add("Ngày hết hạn bảo hiểm phải có định dạng YYYYMMDD");

        if (!string.IsNullOrEmpty(dto.NgayHetHanDangKiem) && !IsValidDateFormat(dto.NgayHetHanDangKiem))
            result.Errors.Add("Ngày hết hạn đăng kiểm phải có định dạng YYYYMMDD");

        if (!string.IsNullOrEmpty(dto.NgayGiaoXe) && !IsValidDateFormat(dto.NgayGiaoXe))
            result.Errors.Add("Ngày giao xe phải có định dạng YYYYMMDD");

        // Insurance date logic validation
        if (!string.IsNullOrEmpty(dto.NgayBatDauBaoHiem) && !string.IsNullOrEmpty(dto.NgayHetHanBaoHiem))
        {
            if (string.Compare(dto.NgayBatDauBaoHiem, dto.NgayHetHanBaoHiem) > 0)
            {
                result.Errors.Add("Ngày bắt đầu bảo hiểm không được lớn hơn ngày hết hạn");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateXeDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.KhoaDoiTuong))
            result.Errors.Add("Khách hàng không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.SoXe))
            result.Errors.Add("Biển số xe không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.KhoaLoaiXe))
            result.Errors.Add("Loại xe không được để trống");

        // Check for duplicate license plate
        if (!string.IsNullOrEmpty(createDto.SoXe))
        {
            var validation = await _repository.ValidateAsync("", createDto.SoXe, createDto.SoSuon, createDto.SoMay, createDto.MaVin);
            if (validation.IsDuplicateSoXe)
            {
                result.Errors.Add("Biển số xe đã tồn tại");
            }
            if (validation.IsDuplicateSoSuon && !string.IsNullOrEmpty(createDto.SoSuon))
            {
                result.Errors.Add("Số khung đã tồn tại");
            }
            if (validation.IsDuplicateSoMay && !string.IsNullOrEmpty(createDto.SoMay))
            {
                result.Errors.Add("Số máy đã tồn tại");
            }
            if (validation.IsDuplicateMaVin && !string.IsNullOrEmpty(createDto.MaVin))
            {
                result.Errors.Add("Mã VIN đã tồn tại");
            }
        }

        // Length validation
        if (createDto.SoXe.Length > 50)
            result.Errors.Add("Biển số xe không được vượt quá 50 ký tự");

        // Mileage validation
        if (createDto.SoKmHienTai < 0)
            result.Errors.Add("Số km hiện tại không được âm");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the vehicle is being used in quotations
            var isUsed = await _repository.CheckValidForDelAsync(khoa);
            return !isUsed; // Can delete if not used
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateUpdateInfoAsync(string pKhoaHangBH, string pSoBH, string pHanBH, double pSoKm, string pKhoa)
    {
        var result = new ValidationResult();

        // Mileage validation
        if (pSoKm < 0)
            result.Errors.Add("Số km không được âm");

        // Date validation
        if (!string.IsNullOrEmpty(pHanBH) && !IsValidDateFormat(pHanBH))
            result.Errors.Add("Ngày hết hạn bảo hiểm phải có định dạng YYYYMMDD");

        // Insurance policy validation
        if (!string.IsNullOrEmpty(pKhoaHangBH) && string.IsNullOrEmpty(pSoBH))
            result.Errors.Add("Số bảo hiểm không được để trống khi có hãng bảo hiểm");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateUpdateInfoDtoAsync(UpdateXeInfoDto updateDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(updateDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (updateDto.SoKmHienTai < 0)
            result.Errors.Add("Số km hiện tại không được âm");

        if (!string.IsNullOrEmpty(updateDto.NgayHetHanBaoHiem) && !IsValidDateFormat(updateDto.NgayHetHanBaoHiem))
            result.Errors.Add("Ngày hết hạn bảo hiểm phải có định dạng YYYYMMDD");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateInsuranceInfoAsync(XeDto dto)
    {
        var result = new ValidationResult();

        // Insurance date validation
        if (!string.IsNullOrEmpty(dto.NgayBatDauBaoHiem) && !IsValidDateFormat(dto.NgayBatDauBaoHiem))
            result.Errors.Add("Ngày bắt đầu bảo hiểm phải có định dạng YYYYMMDD");

        if (!string.IsNullOrEmpty(dto.NgayHetHanBaoHiem) && !IsValidDateFormat(dto.NgayHetHanBaoHiem))
            result.Errors.Add("Ngày hết hạn bảo hiểm phải có định dạng YYYYMMDD");

        // Insurance logic validation
        if (!string.IsNullOrEmpty(dto.NgayBatDauBaoHiem) && !string.IsNullOrEmpty(dto.NgayHetHanBaoHiem))
        {
            if (string.Compare(dto.NgayBatDauBaoHiem, dto.NgayHetHanBaoHiem) > 0)
            {
                result.Errors.Add("Ngày bắt đầu bảo hiểm không được lớn hơn ngày hết hạn");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateMergeAsync(string pKhoaXeXoa, string pKhoaXeCanGom)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(pKhoaXeXoa))
            result.Errors.Add("Khóa xe cần xóa không được để trống");

        if (string.IsNullOrWhiteSpace(pKhoaXeCanGom))
            result.Errors.Add("Khóa xe cần gộp không được để trống");

        if (pKhoaXeXoa == pKhoaXeCanGom)
            result.Errors.Add("Không thể gộp xe với chính nó");

        // Check if both vehicles exist
        if (!string.IsNullOrEmpty(pKhoaXeXoa))
        {
            var xeXoa = await _repository.GetByIdAsync(pKhoaXeXoa);
            if (xeXoa == null)
                result.Errors.Add("Xe cần xóa không tồn tại");
        }

        if (!string.IsNullOrEmpty(pKhoaXeCanGom))
        {
            var xeGom = await _repository.GetByIdAsync(pKhoaXeCanGom);
            if (xeGom == null)
                result.Errors.Add("Xe cần gộp không tồn tại");
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateMergeDtoAsync(XeMergeDto mergeDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(mergeDto.KhoaXeXoa))
            result.Errors.Add("Khóa xe cần xóa không được để trống");

        if (string.IsNullOrWhiteSpace(mergeDto.KhoaXeCanGom))
            result.Errors.Add("Khóa xe cần gộp không được để trống");

        if (mergeDto.KhoaXeXoa == mergeDto.KhoaXeCanGom)
            result.Errors.Add("Không thể gộp xe với chính nó");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(XeDto dto)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa))
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Normalize license plate for search
        dto.SoXeTimKiem = NormalizeLicensePlate(dto.SoXe);

        // Trim whitespace
        dto.SoXe = dto.SoXe.Trim();
        dto.SoSuon = dto.SoSuon.Trim();
        dto.SoMay = dto.SoMay.Trim();
        dto.MaVin = dto.MaVin.Trim();
        dto.Model = dto.Model.Trim();
        dto.MauSon = dto.MauSon.Trim();

        // Automotive vehicle specific business rules
        await ApplyAutomotiveVehicleRulesAsync(dto);
    }

    private async Task ApplyAutomotiveVehicleRulesAsync(XeDto dto)
    {
        // Automotive vehicle specific validations and rules
        // TODO: Add specific business rules based on vehicle type and manufacturer

        // For example:
        // - Validate VIN format based on manufacturer
        // - Set default values based on vehicle type
        // - Apply mileage validation rules
        // - Set insurance requirements based on vehicle type

        await Task.CompletedTask; // Placeholder for future business rules
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show vehicles user has access to
        // - Filter by user's dealership/branch

        return conditions;
    }

    private bool IsValidDateFormat(string date)
    {
        // Validate YYYYMMDD format
        if (string.IsNullOrEmpty(date) || date.Length != 8)
            return false;

        return DateTime.TryParseExact(date, "yyyyMMdd", null,
            System.Globalization.DateTimeStyles.None, out _);
    }

    private string NormalizeLicensePlate(string soXe)
    {
        if (string.IsNullOrEmpty(soXe))
            return "";

        // Remove spaces, dashes, and convert to uppercase for search
        return soXe.Replace(" ", "").Replace("-", "").Replace(".", "").ToUpper().Trim();
    }

    #endregion
}
