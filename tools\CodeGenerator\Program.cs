using System;
using System.IO;

namespace GP.Mobile.CodeGenerator
{
    /// <summary>
    /// Main console application to automate legacy to modern API generation
    /// Processes all 180+ business classes automatically
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("🚀 GP Mobile Legacy to Modern API Generator");
            Console.WriteLine("=" * 60);
            Console.WriteLine();

            try
            {
                // Configuration
                var currentDirectory = Directory.GetCurrentDirectory();
                var projectRoot = FindProjectRoot(currentDirectory);
                
                if (string.IsNullOrEmpty(projectRoot))
                {
                    Console.WriteLine("❌ Could not find project root directory");
                    Console.WriteLine("Please run this tool from within the GP Mobile project");
                    return;
                }

                var legacyBasePath = Path.Combine(projectRoot, "Base", "Business");
                var modernApiPath = projectRoot;

                Console.WriteLine($"📁 Project Root: {projectRoot}");
                Console.WriteLine($"📁 Legacy Classes: {legacyBasePath}");
                Console.WriteLine($"📁 Modern API Output: {modernApiPath}");
                Console.WriteLine();

                // Validate paths
                if (!Directory.Exists(legacyBasePath))
                {
                    Console.WriteLine($"❌ Legacy Business folder not found: {legacyBasePath}");
                    return;
                }

                // Check command line arguments
                if (args.Length > 0)
                {
                    switch (args[0].ToLower())
                    {
                        case "--single":
                            if (args.Length < 2)
                            {
                                Console.WriteLine("❌ Please specify a class name for single generation");
                                Console.WriteLine("Usage: --single clsClassName");
                                return;
                            }
                            ProcessSingleClass(args[1], legacyBasePath, modernApiPath);
                            break;
                        
                        case "--priority":
                            ProcessPriorityClasses(legacyBasePath, modernApiPath);
                            break;
                        
                        case "--all":
                            ProcessAllClasses(legacyBasePath, modernApiPath);
                            break;
                        
                        case "--help":
                        case "-h":
                            ShowHelp();
                            break;
                        
                        default:
                            Console.WriteLine($"❌ Unknown option: {args[0]}");
                            ShowHelp();
                            break;
                    }
                }
                else
                {
                    // Interactive mode
                    RunInteractiveMode(legacyBasePath, modernApiPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Fatal error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static void RunInteractiveMode(string legacyBasePath, string modernApiPath)
        {
            Console.WriteLine("🎯 Interactive Mode");
            Console.WriteLine("Choose an option:");
            Console.WriteLine("1. Generate single class");
            Console.WriteLine("2. Generate priority classes (top 5)");
            Console.WriteLine("3. Generate all classes (180+)");
            Console.WriteLine("4. Show statistics");
            Console.WriteLine("5. Exit");
            Console.WriteLine();
            Console.Write("Enter your choice (1-5): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    Console.Write("Enter class name (e.g., clsBaoGia): ");
                    var className = Console.ReadLine();
                    if (!string.IsNullOrEmpty(className))
                    {
                        ProcessSingleClass(className, legacyBasePath, modernApiPath);
                    }
                    break;

                case "2":
                    ProcessPriorityClasses(legacyBasePath, modernApiPath);
                    break;

                case "3":
                    Console.WriteLine("⚠️  This will generate code for ALL 180+ classes.");
                    Console.Write("Are you sure? (y/N): ");
                    var confirm = Console.ReadLine();
                    if (confirm?.ToLower() == "y")
                    {
                        ProcessAllClasses(legacyBasePath, modernApiPath);
                    }
                    break;

                case "4":
                    ShowStatistics(legacyBasePath);
                    break;

                case "5":
                    Console.WriteLine("👋 Goodbye!");
                    return;

                default:
                    Console.WriteLine("❌ Invalid choice");
                    break;
            }
        }

        static void ProcessSingleClass(string className, string legacyBasePath, string modernApiPath)
        {
            Console.WriteLine($"🔧 Processing single class: {className}");
            Console.WriteLine();

            var legacyFile = Path.Combine(legacyBasePath, $"{className}.cs");
            
            if (!File.Exists(legacyFile))
            {
                Console.WriteLine($"❌ Class file not found: {legacyFile}");
                return;
            }

            var generator = new LegacyToModernGenerator();
            var result = generator.ProcessSingleClass(legacyFile, modernApiPath);

            if (result.Success)
            {
                Console.WriteLine($"✅ Successfully generated {result.FilesGenerated} files for {className}");
                Console.WriteLine("Generated files:");
                foreach (var file in result.GeneratedFiles)
                {
                    Console.WriteLine($"   📄 {Path.GetRelativePath(modernApiPath, file)}");
                }
            }
            else
            {
                Console.WriteLine($"❌ Failed to generate {className}: {result.ErrorMessage}");
            }
        }

        static void ProcessPriorityClasses(string legacyBasePath, string modernApiPath)
        {
            Console.WriteLine("🔥 Processing Priority Classes (Critical Business Operations)");
            Console.WriteLine();

            var priorityClasses = new[]
            {
                "clsBaoGia",        // Quote Management
                "clsHoaDon",        // Invoice Management  
                "clsNhapKho",       // Inventory In
                "clsXuatKho",       // Inventory Out
                "clsThanhToan"      // Payment Management
            };

            var generator = new LegacyToModernGenerator();
            var successCount = 0;

            foreach (var className in priorityClasses)
            {
                Console.WriteLine($"Processing {className}...");
                
                var legacyFile = Path.Combine(legacyBasePath, $"{className}.cs");
                
                if (!File.Exists(legacyFile))
                {
                    Console.WriteLine($"⚠️  {className}.cs not found, skipping...");
                    continue;
                }

                var result = generator.ProcessSingleClass(legacyFile, modernApiPath);
                
                if (result.Success)
                {
                    Console.WriteLine($"✅ {className} - Generated {result.FilesGenerated} files");
                    successCount++;
                }
                else
                {
                    Console.WriteLine($"❌ {className} - Error: {result.ErrorMessage}");
                }
            }

            Console.WriteLine();
            Console.WriteLine($"🎯 Priority Classes Complete: {successCount}/{priorityClasses.Length} successful");
        }

        static void ProcessAllClasses(string legacyBasePath, string modernApiPath)
        {
            Console.WriteLine("🚀 Processing ALL Legacy Classes (This may take several minutes)");
            Console.WriteLine();

            var generator = new LegacyToModernGenerator();
            generator.GenerateAllClasses(legacyBasePath, modernApiPath);
        }

        static void ShowStatistics(string legacyBasePath)
        {
            Console.WriteLine("📊 Legacy Classes Statistics");
            Console.WriteLine("=" * 40);

            if (!Directory.Exists(legacyBasePath))
            {
                Console.WriteLine("❌ Legacy Business folder not found");
                return;
            }

            var legacyFiles = Directory.GetFiles(legacyBasePath, "cls*.cs", SearchOption.TopDirectoryOnly);
            
            Console.WriteLine($"Total Legacy Classes: {legacyFiles.Length}");
            Console.WriteLine();

            // Categorize by prefix
            var categories = new Dictionary<string, int>();
            
            foreach (var file in legacyFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                
                if (fileName.StartsWith("clsDM"))
                    categories["Master Data (DM)"] = categories.GetValueOrDefault("Master Data (DM)", 0) + 1;
                else if (fileName.StartsWith("clsBH"))
                    categories["Business (BH)"] = categories.GetValueOrDefault("Business (BH)", 0) + 1;
                else if (fileName.StartsWith("clsSC"))
                    categories["Service Center (SC)"] = categories.GetValueOrDefault("Service Center (SC)", 0) + 1;
                else if (fileName.StartsWith("clsST"))
                    categories["Stock/Inventory (ST)"] = categories.GetValueOrDefault("Stock/Inventory (ST)", 0) + 1;
                else if (fileName.StartsWith("clsGL"))
                    categories["General Ledger (GL)"] = categories.GetValueOrDefault("General Ledger (GL)", 0) + 1;
                else
                    categories["Other"] = categories.GetValueOrDefault("Other", 0) + 1;
            }

            Console.WriteLine("Categories:");
            foreach (var category in categories.OrderByDescending(c => c.Value))
            {
                Console.WriteLine($"  {category.Key}: {category.Value} classes");
            }

            Console.WriteLine();
            Console.WriteLine("Priority Classes Status:");
            var priorityClasses = new[] { "clsBaoGia", "clsHoaDon", "clsNhapKho", "clsXuatKho", "clsThanhToan" };
            
            foreach (var className in priorityClasses)
            {
                var exists = File.Exists(Path.Combine(legacyBasePath, $"{className}.cs"));
                Console.WriteLine($"  {className}: {(exists ? "✅ Found" : "❌ Not Found")}");
            }
        }

        static void ShowHelp()
        {
            Console.WriteLine("GP Mobile Legacy to Modern API Generator");
            Console.WriteLine();
            Console.WriteLine("Usage:");
            Console.WriteLine("  CodeGenerator.exe [options]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  --single <className>   Generate code for a single class");
            Console.WriteLine("  --priority             Generate code for priority classes only");
            Console.WriteLine("  --all                  Generate code for all legacy classes");
            Console.WriteLine("  --help, -h             Show this help message");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  CodeGenerator.exe --single clsBaoGia");
            Console.WriteLine("  CodeGenerator.exe --priority");
            Console.WriteLine("  CodeGenerator.exe --all");
            Console.WriteLine();
            Console.WriteLine("Interactive Mode:");
            Console.WriteLine("  Run without arguments for interactive menu");
        }

        static string FindProjectRoot(string currentPath)
        {
            var directory = new DirectoryInfo(currentPath);
            
            while (directory != null)
            {
                // Look for project indicators
                if (Directory.Exists(Path.Combine(directory.FullName, "Base", "Business")) ||
                    File.Exists(Path.Combine(directory.FullName, "GP.Mobile.sln")) ||
                    Directory.Exists(Path.Combine(directory.FullName, "src", "API")))
                {
                    return directory.FullName;
                }
                
                directory = directory.Parent;
            }
            
            return "";
        }
    }
}
