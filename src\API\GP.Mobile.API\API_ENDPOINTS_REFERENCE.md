# GP.Mobile API Endpoints Reference

## Authentication Endpoints
**Controller:** `AuthenticationController`
- `POST /api/authentication/login` - User login
- `POST /api/authentication/logout` - User logout
- `GET /api/authentication/validate` - Validate token

## Master Data Management (DM_) Endpoints

### DoiTuong (Customer/Supplier) Management
**Controller:** `DoiTuongController`
- `GET /api/doituong` - Get all customers/suppliers
- `GET /api/doituong/{khoa}` - Get customer/supplier by ID
- `POST /api/doituong` - Create new customer/supplier
- `PUT /api/doituong/{khoa}` - Update customer/supplier
- `DELETE /api/doituong/{khoa}` - Delete customer/supplier
- `GET /api/doituong/search` - Search customers/suppliers

### DonVi (Business Unit) Management
**Controller:** `DonViController`
- `GET /api/donvi` - Get all business units
- `GET /api/donvi/{khoa}` - Get business unit by ID
- `POST /api/donvi` - Create new business unit
- `PUT /api/donvi/{khoa}` - Update business unit
- `DELETE /api/donvi/{khoa}` - Delete business unit

### DonViTinh (Unit of Measure) Management
**Controller:** `DonViTinhController`
- `GET /api/donvitinh` - Get all units of measure
- `GET /api/donvitinh/{khoa}` - Get unit by ID
- `POST /api/donvitinh` - Create new unit
- `PUT /api/donvitinh/{khoa}` - Update unit
- `DELETE /api/donvitinh/{khoa}` - Delete unit

### LoaiTien (Currency) Management
**Controller:** `LoaiTienController`
- `GET /api/loaitien` - Get all currencies
- `GET /api/loaitien/{khoa}` - Get currency by ID
- `POST /api/loaitien` - Create new currency
- `PUT /api/loaitien/{khoa}` - Update currency
- `DELETE /api/loaitien/{khoa}` - Delete currency

### LoaiDichVu (Service Type) Management
**Controller:** `LoaiDichVuController`
- `GET /api/loaidichvu` - Get all service types
- `GET /api/loaidichvu/{khoa}` - Get service type by ID
- `POST /api/loaidichvu` - Create new service type
- `PUT /api/loaidichvu/{khoa}` - Update service type
- `DELETE /api/loaidichvu/{khoa}` - Delete service type

### LoaiXe (Vehicle Type) Management
**Controller:** `LoaiXeController`
- `GET /api/loaixe` - Get all vehicle types
- `GET /api/loaixe/{khoa}` - Get vehicle type by ID
- `POST /api/loaixe` - Create new vehicle type
- `PUT /api/loaixe/{khoa}` - Update vehicle type
- `DELETE /api/loaixe/{khoa}` - Delete vehicle type

### HangSanXuat (Manufacturer) Management
**Controller:** `HangSanXuatController`
- `GET /api/hangsanxuat` - Get all manufacturers
- `GET /api/hangsanxuat/{khoa}` - Get manufacturer by ID
- `POST /api/hangsanxuat` - Create new manufacturer
- `PUT /api/hangsanxuat/{khoa}` - Update manufacturer
- `DELETE /api/hangsanxuat/{khoa}` - Delete manufacturer

### BaoDuong (Maintenance Service) Management
**Controller:** `BaoDuongController`
- `GET /api/baoduong` - Get all maintenance services
- `GET /api/baoduong/{khoa}` - Get maintenance service by ID
- `POST /api/baoduong` - Create new maintenance service
- `PUT /api/baoduong/{khoa}` - Update maintenance service
- `DELETE /api/baoduong/{khoa}` - Delete maintenance service
- `GET /api/baoduong/vehicle/{khoaLoaiXe}/service/{khoaLoaiDichVu}` - Get by vehicle and service type

### Xe (Individual Vehicle) Management
**Controller:** `XeController`
- `GET /api/xe` - Get all vehicles
- `GET /api/xe/{khoa}` - Get vehicle by ID
- `POST /api/xe` - Create new vehicle
- `PUT /api/xe/{khoa}` - Update vehicle
- `DELETE /api/xe/{khoa}` - Delete vehicle
- `GET /api/xe/customer/{khoaKhachHang}` - Get vehicles by customer

### Kho (Warehouse) Management
**Controller:** `KhoController`
- `GET /api/kho` - Get all warehouses
- `GET /api/kho/{khoa}` - Get warehouse by ID
- `POST /api/kho` - Create new warehouse
- `PUT /api/kho/{khoa}` - Update warehouse
- `DELETE /api/kho/{khoa}` - Delete warehouse

### HangHoa (Product/Parts) Management
**Controller:** `HangHoaController`
- `GET /api/hanghoa` - Get all products/parts
- `GET /api/hanghoa/{khoa}` - Get product by ID
- `POST /api/hanghoa` - Create new product
- `PUT /api/hanghoa/{khoa}` - Update product
- `DELETE /api/hanghoa/{khoa}` - Delete product
- `GET /api/hanghoa/search` - Search products

### NhomHangHoa (Product Category) Management
**Controller:** `NhomHangHoaController`
- `GET /api/nhomhanghoa` - Get all product categories
- `GET /api/nhomhanghoa/{khoa}` - Get category by ID
- `POST /api/nhomhanghoa` - Create new category
- `PUT /api/nhomhanghoa/{khoa}` - Update category
- `DELETE /api/nhomhanghoa/{khoa}` - Delete category

### NhanVien (Employee) Management
**Controller:** `NhanVienController`
- `GET /api/nhanvien` - Get all employees
- `GET /api/nhanvien/{khoa}` - Get employee by ID
- `POST /api/nhanvien` - Create new employee
- `PUT /api/nhanvien/{khoa}` - Update employee
- `DELETE /api/nhanvien/{khoa}` - Delete employee

### KhoanMucChiPhi (Expense Category) Management
**Controller:** `KhoanMucChiPhiController`
- `GET /api/khoanmucchiphi` - Get all expense categories
- `GET /api/khoanmucchiphi/{khoa}` - Get expense category by ID
- `POST /api/khoanmucchiphi` - Create new expense category
- `PUT /api/khoanmucchiphi/{khoa}` - Update expense category
- `DELETE /api/khoanmucchiphi/{khoa}` - Delete expense category

### KhoanMucSuaChua (Repair Category) Management
**Controller:** `KhoanMucSuaChuaController`
- `GET /api/khoanmucsuachua` - Get all repair categories
- `GET /api/khoanmucsuachua/{khoa}` - Get repair category by ID
- `POST /api/khoanmucsuachua` - Create new repair category
- `PUT /api/khoanmucsuachua/{khoa}` - Update repair category
- `DELETE /api/khoanmucsuachua/{khoa}` - Delete repair category

### SoChungTu (Document Numbering) Management
**Controller:** `SoChungTuController`
- `GET /api/sochungtu` - Get all document types
- `GET /api/sochungtu/{loaiChungTu}` - Get document type by ID
- `POST /api/sochungtu` - Create new document type
- `PUT /api/sochungtu/{loaiChungTu}` - Update document type
- `DELETE /api/sochungtu/{loaiChungTu}` - Delete document type
- `POST /api/sochungtu/generate` - Generate document number

### Log (System Audit) Management
**Controller:** `LogController`
- `GET /api/log` - Get all audit logs
- `GET /api/log/{id}` - Get log by ID
- `POST /api/log` - Create new log entry
- `GET /api/log/search` - Search logs
- `GET /api/log/user/{nguoiDung}` - Get logs by user

## Business Process Endpoints

### BaoGia (Service Quotation) Management
**Controller:** `BaoGiaController`
- `GET /api/baogia` - Get all quotations
- `GET /api/baogia/{khoa}` - Get quotation by ID
- `POST /api/baogia` - Create new quotation
- `PUT /api/baogia/{khoa}` - Update quotation
- `DELETE /api/baogia/{khoa}` - Delete quotation
- `GET /api/baogia/customer/{khoaKhachHang}` - Get quotations by customer
- `GET /api/baogia/vehicle/{khoaXe}` - Get quotations by vehicle

### BaoGiaChiTiet (Quotation Details) Management
**Controller:** `BaoGiaChiTietController`
- `GET /api/baogiachitiet/quotation/{khoaBaoGia}` - Get quotation details
- `POST /api/baogiachitiet` - Create quotation detail
- `PUT /api/baogiachitiet/{khoa}` - Update quotation detail
- `DELETE /api/baogiachitiet/{khoa}` - Delete quotation detail

### BaoGiaSuaChua (Repair Quotation) Management
**Controller:** `BaoGiaSuaChuaController`
- `GET /api/baogiasuachua` - Get all repair quotations
- `GET /api/baogiasuachua/{khoa}` - Get repair quotation by ID
- `POST /api/baogiasuachua` - Create new repair quotation
- `PUT /api/baogiasuachua/{khoa}` - Update repair quotation
- `DELETE /api/baogiasuachua/{khoa}` - Delete repair quotation

### BaoGiaSuaChuaChiTiet (Repair Quotation Details) Management
**Controller:** `BaoGiaSuaChuaChiTietController`
- `GET /api/baogiasuachuachitiet/quotation/{khoaBaoGia}` - Get repair quotation details
- `POST /api/baogiasuachuachitiet` - Create repair quotation detail
- `PUT /api/baogiasuachuachitiet/{khoa}` - Update repair quotation detail
- `DELETE /api/baogiasuachuachitiet/{khoa}` - Delete repair quotation detail

## Phase 2 Implementation Endpoints

### BaoGiaYeuCauSuaChuaChiTiet (Repair Requirements Detail) Management
**Controller:** `BaoGiaYeuCauSuaChuaChiTietController`
- `GET /api/baogiayeucausuachuachitiet` - Get all repair requirements
- `GET /api/baogiayeucausuachuachitiet/{khoa}` - Get repair requirement by ID
- `POST /api/baogiayeucausuachuachitiet` - Create new repair requirement
- `PUT /api/baogiayeucausuachuachitiet/{khoa}` - Update repair requirement
- `DELETE /api/baogiayeucausuachuachitiet/{khoa}` - Delete repair requirement
- `GET /api/baogiayeucausuachuachitiet/quotation/{khoaBaoGia}` - Get requirements by quotation

### TempBaoGia (Temporary Quotation Data) Management
**Controller:** `TempBaoGiaController`
- `GET /api/tempbaogia/{khoaBaoGia}` - Get temporary quotation data
- `POST /api/tempbaogia` - Save temporary quotation data
- `PUT /api/tempbaogia/{khoaBaoGia}` - Update temporary quotation data
- `DELETE /api/tempbaogia/{khoaBaoGia}` - Delete temporary quotation data
- `POST /api/tempbaogia/{khoaBaoGia}/approve` - Approve temporary data
- `POST /api/tempbaogia/{khoaBaoGia}/reject` - Reject temporary data

### DieuKhoanBaoGia (Quotation Terms & Conditions) Management
**Controller:** `DieuKhoanBaoGiaController`
- `GET /api/dieukhoanbaogiagia` - Get all terms & conditions
- `GET /api/dieukhoanbaogiagia/{khoa}` - Get terms by ID
- `POST /api/dieukhoanbaogiagia` - Create new terms
- `PUT /api/dieukhoanbaogiagia/{khoa}` - Update terms
- `DELETE /api/dieukhoanbaogiagia/{khoa}` - Delete terms
- `GET /api/dieukhoanbaogiagia/type/{loai}` - Get terms by type

### BaoGiaHinhAnhBH (Insurance Image Management) Management
**Controller:** `BaoGiaHinhAnhBHController`
- `GET /api/baogiahinhanhbh/quotation/{khoaBaoGia}` - Get insurance images by quotation
- `POST /api/baogiahinhanhbh` - Upload insurance image
- `PUT /api/baogiahinhanhbh/{khoa}` - Update insurance image
- `DELETE /api/baogiahinhanhbh/{khoa}` - Delete insurance image
- `POST /api/baogiahinhanhbh/{khoa}/approve` - Approve insurance image
- `POST /api/baogiahinhanhbh/{khoa}/reject` - Reject insurance image

## Inventory Management Endpoints

### NhapKho (Inventory Receipt) Management
**Controller:** `NhapKhoController`
- `GET /api/nhapkho` - Get all inventory receipts
- `GET /api/nhapkho/{khoa}` - Get receipt by ID
- `POST /api/nhapkho` - Create new receipt
- `PUT /api/nhapkho/{khoa}` - Update receipt
- `DELETE /api/nhapkho/{khoa}` - Delete receipt

### XuatKho (Inventory Issue) Management
**Controller:** `XuatKhoController`
- `GET /api/xuatkho` - Get all inventory issues
- `GET /api/xuatkho/{khoa}` - Get issue by ID
- `POST /api/xuatkho` - Create new issue
- `PUT /api/xuatkho/{khoa}` - Update issue
- `DELETE /api/xuatkho/{khoa}` - Delete issue

## Appointment & Service Management Endpoints

### DatLichHen (Appointment Scheduling) Management
**Controller:** `DatLichHenController`
- `GET /api/datlichhen` - Get all appointments
- `GET /api/datlichhen/{khoa}` - Get appointment by ID
- `POST /api/datlichhen` - Create new appointment
- `PUT /api/datlichhen/{khoa}` - Update appointment
- `DELETE /api/datlichhen/{khoa}` - Delete appointment
- `GET /api/datlichhen/today` - Get today's appointments
- `GET /api/datlichhen/upcoming` - Get upcoming appointments
- `GET /api/datlichhen/customer/{khoaKhachHang}` - Get appointments by customer
- `GET /api/datlichhen/vehicle/{khoaXe}` - Get appointments by vehicle

### CongLaoDong (Labor Work) Management
**Controller:** `CongLaoDongController`
- `GET /api/conglaodong` - Get all labor work records
- `GET /api/conglaodong/{khoa}` - Get labor work by ID
- `POST /api/conglaodong` - Create new labor work
- `PUT /api/conglaodong/{khoa}` - Update labor work
- `DELETE /api/conglaodong/{khoa}` - Delete labor work

## Legacy Support Endpoints

### CoHoiLegacy (Legacy Opportunity) Management
**Controller:** `CoHoiLegacyController`
- `GET /api/cohoi-legacy` - Get all legacy opportunities
- `GET /api/cohoi-legacy/{khoa}` - Get legacy opportunity by ID
- `POST /api/cohoi-legacy` - Create new legacy opportunity

### DoiTuongLegacy (Legacy Customer) Management
**Controller:** `DoiTuongLegacyController`
- `GET /api/doituong-legacy` - Get all legacy customers
- `GET /api/doituong-legacy/{khoa}` - Get legacy customer by ID

## System Endpoints

### Health Check
**Controller:** `HealthController`
- `GET /api/health` - System health check
- `GET /api/health/database` - Database connectivity check

### Database Testing
**Controller:** `DatabaseTestController`
- `GET /api/database-test/connection` - Test database connection
- `GET /api/database-test/query` - Test database query

---

## API Base URL
- **Development:** `https://localhost:7001/api`
- **Production:** `[To be configured]`

## Authentication
All endpoints (except health checks) require JWT Bearer token authentication.

## Response Format
All endpoints return JSON responses with consistent error handling and status codes.

## Last Updated
Generated on: 2025-06-17
Total Controllers: 34
Total Endpoints: 200+
