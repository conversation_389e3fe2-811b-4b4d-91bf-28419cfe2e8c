/**
 * API Service Layer for Real Data Integration
 * Connects frontend to actual database via API endpoints
 */

const API_BASE_URL = 'http://localhost:51552'

// Authentication check helper
function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false
  const token = localStorage.getItem('auth_token')
  return !!token
}

// Redirect to login if not authenticated
function redirectToLogin(): void {
  if (typeof window !== 'undefined') {
    window.location.href = '/login'
  }
}

// Types based on actual API DTOs
export interface BaoGiaSuaChuaListDto {
  khoa: string
  soChungTu: string
  ngayChungTu: string
  bienSoXe: string
  khachHang: string
  loaiXe: string
  phanLoai: number
  boPhanSuaChua: number
  tinhTrangBaoGia: number
  tongTienHang: number
  tienThue: number
  tienChietKhau: number
  tongThanhToan: number
  dienGiai: string
  nhanVienTao: string
  ngayTao: string
}

export interface BaoGiaSuaChuaDto {
  khoa: string
  soChungTu: string
  ngayChungTu: string
  khoaTiepNhanXe: string
  khoaKhachHang: string
  khachHang: string
  khoaXe: string
  bienSoXe: string
  khoaLoaiXe: string
  loaiXe: string
  phanLoai: number
  boPhanSuaChua: number
  tinhTrangBaoGia: number
  khoaBaoHiem: string
  baoHiem: string
  tongTienHang: number
  tienThue: number
  tienChietKhau: number
  tongThanhToan: number
  dienGiai: string
  khoaNhanVienTao: string
  ngayTao: string
  // Add other properties as needed
}

export interface BaoGiaSuaChuaChiTietDto {
  khoa: string
  khoaBaoGia: string
  noiDung: string
  soLuong: number
  donGia: number
  thanhTien: number
  tyLeCK: number
  tienCK: number
  tyLeThue: number
  tienThue: number
  loai: number
  maPhuTung: string
  khoaHangHoa: string
  dvt: string
  khoaDVT: string
  ghiChu: string
}

export interface CreateBaoGiaSuaChuaDto {
  soChungTu: string
  ngayChungTu: string
  khoaTiepNhanXe?: string
  khoaKhachHang?: string
  khachHang: string
  khoaXe?: string
  bienSoXe: string
  khoaLoaiXe?: string
  loaiXe?: string
  phanLoai: number
  boPhanSuaChua: number
  khoaBaoHiem?: string
  baoHiem?: string
  dienGiai?: string
  khoaNhanVienTao?: string
}

// Pagination types
export interface PaginatedResponseDto<T> {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
  hasPreviousPage: boolean
  hasNextPage: boolean
  data: T[]
  sortBy?: string
  sortDirection?: string
}

export interface DateRangeFilterDto {
  startDate?: string
  endDate?: string
}

export interface BaoGiaSuaChuaPaginationDto {
  page: number
  pageSize: number
  sortBy?: string
  sortDirection?: string
  dateRange?: DateRangeFilterDto
  bienSoXe?: string
  khachHang?: string
  tinhTrangBaoGia?: number
  boPhanSuaChua?: number
}

// API Helper Functions
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Check authentication before making API calls
  if (!isAuthenticated()) {
    redirectToLogin()
    throw new Error('Authentication required')
  }

  const token = localStorage.getItem('auth_token')

  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config)

  // Handle authentication errors
  if (response.status === 401) {
    // Token expired or invalid
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_data')
    redirectToLogin()
    throw new Error('Session expired. Please login again.')
  }

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Network error' }))
    throw new Error(errorData.message || `HTTP ${response.status}`)
  }

  return response.json()
}

// BaoGiaSuaChua API Functions
export const baoGiaSuaChuaApi = {
  // Get all quotations
  async getAll(): Promise<BaoGiaSuaChuaListDto[]> {
    console.log('🔄 Calling API:', `${API_BASE_URL}/api/BaoGiaSuaChua`)
    const result = await apiRequest<BaoGiaSuaChuaListDto[]>('/api/BaoGiaSuaChua')
    console.log('✅ API Response:', result)
    return result
  },

  // Get paginated quotations
  async getPaginated(request: BaoGiaSuaChuaPaginationDto): Promise<PaginatedResponseDto<BaoGiaSuaChuaListDto>> {
    console.log('🔄 Calling Paginated API:', `${API_BASE_URL}/api/BaoGiaSuaChua/paginated`, request)
    const result = await apiRequest<PaginatedResponseDto<BaoGiaSuaChuaListDto>>('/api/BaoGiaSuaChua/paginated', {
      method: 'POST',
      body: JSON.stringify(request),
    })
    console.log('✅ Paginated API Response:', result)
    return result
  },

  // Get quotation by ID
  async getById(khoa: string): Promise<BaoGiaSuaChuaDto> {
    return apiRequest<BaoGiaSuaChuaDto>(`/api/BaoGiaSuaChua/${khoa}`)
  },

  // Create new quotation
  async create(data: CreateBaoGiaSuaChuaDto): Promise<string> {
    return apiRequest<string>('/api/BaoGiaSuaChua', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // Update quotation
  async update(khoa: string, data: BaoGiaSuaChuaDto): Promise<boolean> {
    return apiRequest<boolean>(`/api/BaoGiaSuaChua/${khoa}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // Delete quotation
  async delete(khoa: string): Promise<boolean> {
    return apiRequest<boolean>(`/api/BaoGiaSuaChua/${khoa}`, {
      method: 'DELETE',
    })
  },

  // Search quotations
  async search(searchParams: any): Promise<BaoGiaSuaChuaListDto[]> {
    const queryString = new URLSearchParams(searchParams).toString()
    return apiRequest<BaoGiaSuaChuaListDto[]>(`/api/BaoGiaSuaChua/search?${queryString}`)
  },

  // Get automotive repair quotations
  async getAutomotiveRepairQuotations(): Promise<any[]> {
    return apiRequest<any[]>('/api/BaoGiaSuaChua/automotive')
  },

  // Legacy save method
  async legacySave(data: BaoGiaSuaChuaDto): Promise<boolean> {
    return apiRequest<boolean>('/api/BaoGiaSuaChua/save', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
}

// BaoGiaSuaChuaChiTiet API Functions
export const baoGiaSuaChuaChiTietApi = {
  // Get all quotation details
  async getAll(): Promise<BaoGiaSuaChuaChiTietDto[]> {
    return apiRequest<BaoGiaSuaChuaChiTietDto[]>('/api/BaoGiaSuaChuaChiTiet')
  },

  // Get quotation details by quotation ID
  async getByQuotation(khoaBaoGia: string): Promise<BaoGiaSuaChuaChiTietDto[]> {
    return apiRequest<BaoGiaSuaChuaChiTietDto[]>(`/api/BaoGiaSuaChuaChiTiet/quotation/${khoaBaoGia}`)
  },

  // Create new quotation detail
  async create(data: Partial<BaoGiaSuaChuaChiTietDto>): Promise<string> {
    return apiRequest<string>('/api/BaoGiaSuaChuaChiTiet', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // Update quotation detail
  async update(khoa: string, data: BaoGiaSuaChuaChiTietDto): Promise<boolean> {
    return apiRequest<boolean>(`/api/BaoGiaSuaChuaChiTiet/${khoa}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // Delete quotation detail
  async delete(khoa: string): Promise<boolean> {
    return apiRequest<boolean>(`/api/BaoGiaSuaChuaChiTiet/${khoa}`, {
      method: 'DELETE',
    })
  },

  // Delete all details by quotation
  async deleteByQuotation(khoaBaoGia: string): Promise<boolean> {
    return apiRequest<boolean>(`/api/BaoGiaSuaChuaChiTiet/quotation/${khoaBaoGia}`, {
      method: 'DELETE',
    })
  }
}

// Test API connection
export async function testApiConnection(): Promise<boolean> {
  try {
    // Test with the health endpoint
    const response = await fetch(`${API_BASE_URL}/api/health`, { method: 'GET' })
    console.log('✅ API Health Check Response:', response.status)
    return response.ok
  } catch (error) {
    console.error('❌ API connection test failed:', error)
    return false
  }
}

// Mock data fallback for development
export const mockQuotations: BaoGiaSuaChuaListDto[] = [
  {
    khoa: 'BG001',
    soChungTu: 'BG2024001',
    ngayChungTu: '20240615',
    bienSoXe: '30A-12345',
    khachHang: 'Nguyễn Văn A',
    loaiXe: 'Toyota Camry',
    phanLoai: 0, // Cash
    boPhanSuaChua: 1, // Engine
    tinhTrangBaoGia: 0, // Draft
    tongTienHang: 15500000,
    tienThue: 1550000,
    tienChietKhau: 500000,
    tongThanhToan: 16550000,
    dienGiai: 'Thay dầu máy, kiểm tra phanh',
    nhanVienTao: 'NV001',
    ngayTao: '20240615'
  },
  {
    khoa: 'BG002',
    soChungTu: 'BG2024002',
    ngayChungTu: '20240614',
    bienSoXe: '29B-67890',
    khachHang: 'Trần Thị B',
    loaiXe: 'Honda Civic',
    phanLoai: 0, // Cash
    boPhanSuaChua: 1, // Engine
    tinhTrangBaoGia: 1, // Approved
    tongTienHang: 8500000,
    tienThue: 850000,
    tienChietKhau: 0,
    tongThanhToan: 9350000,
    dienGiai: 'Bảo dưỡng 10,000km',
    nhanVienTao: 'NV002',
    ngayTao: '20240614'
  },
  {
    khoa: 'BG003',
    soChungTu: 'BG2024003',
    ngayChungTu: '20240613',
    bienSoXe: '51C-11111',
    khachHang: 'Lê Văn C',
    loaiXe: 'Ford Focus',
    phanLoai: 1, // Insurance
    boPhanSuaChua: 0, // Body+Paint
    tinhTrangBaoGia: 1, // Approved
    tongTienHang: 25000000,
    tienThue: 2500000,
    tienChietKhau: 1000000,
    tongThanhToan: 26500000,
    dienGiai: 'Sửa chữa sau tai nạn',
    nhanVienTao: 'NV001',
    ngayTao: '20240613'
  }
]
