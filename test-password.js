// Test script to find the master password
const crypto = require('crypto');

function computeMD5Hash(input) {
    return crypto.createHash('md5').update(input).digest('hex').toUpperCase();
}

const targetHash = '8F866D5EA7686D4458E39AEEF07DEC1A';

// Common passwords to test
const passwords = [
    'admin',
    'password',
    '123456',
    'admin123',
    'carsoft',
    'giaphat',
    'master',
    'gp123',
    'gp2024',
    'admin2024',
    'carsoft123',
    'giaphat123',
    'gp',
    'GP',
    'ADMIN',
    'PASSWORD',
    'CARSOFT',
    'GIAPHAT',
    'gp_admin',
    'gp_master',
    'master123',
    'root',
    'sa',
    'administrator',
    'gp_carsoft',
    'carsoft_gp',
    'giaphat_admin',
    'admin_gp',
    'gp_giaphat'
];

console.log('Testing passwords against hash:', targetHash);
console.log('');

for (const password of passwords) {
    const hash = computeMD5Hash(password);
    console.log(`${password.padEnd(15)} -> ${hash} ${hash === targetHash ? '✅ MATCH!' : ''}`);
}

console.log('');
console.log('If no match found, the master password might be something else.');
