using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for NhapKho entity
/// Implements ALL endpoints from clsNhapKho.cs (2,243 lines)
/// Includes REST API and 25+ legacy method endpoints
/// Maps to ST_NhapKho table with 65+ properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class NhapKhoController : ControllerBase
{
    private readonly INhapKhoService _nhapKhoService;
    private readonly ILogger<NhapKhoController> _logger;

    public NhapKhoController(INhapKhoService nhapKhoService, ILogger<NhapKhoController> logger)
    {
        _nhapKhoService = nhapKhoService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all NhapKho records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<NhapKhoListDto>>> GetAll()
    {
        try
        {
            var result = await _nhapKhoService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all NhapKho records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get NhapKho by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<NhapKhoDto>> GetById(string khoa)
    {
        try
        {
            var result = await _nhapKhoService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting NhapKho by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new NhapKho
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateNhapKhoDto createDto)
    {
        try
        {
            var result = await _nhapKhoService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating NhapKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update NhapKho
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] NhapKhoDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhapKhoService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating NhapKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete NhapKho
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _nhapKhoService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting NhapKho");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update NhapKho status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateNhapKhoStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhapKhoService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating NhapKho status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _nhapKhoService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] NhapKhoDto dto)
    {
        try
        {
            var result = await _nhapKhoService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _nhapKhoService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DeleteData method endpoint
    /// </summary>
    [HttpPost("deletedata")]
    public async Task<ActionResult<bool>> DeleteData([FromBody] string khoa)
    {
        try
        {
            var result = await _nhapKhoService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteData endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy List Endpoints

    /// <summary>
    /// Legacy GetList method endpoint
    /// </summary>
    [HttpPost("getlist")]
    public async Task<ActionResult<DataTable>> GetList([FromBody] GetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _nhapKhoService.GetListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListDetails method endpoint
    /// </summary>
    [HttpPost("getlistdetails")]
    public async Task<ActionResult<DataTable>> GetListDetails([FromBody] GetListDetailsRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetListDetailsAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDetails endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDetailsTraHang method endpoint
    /// </summary>
    [HttpPost("getdetailstrahang")]
    public async Task<ActionResult<DataTable>> GetDetailsTraHang([FromBody] GetDetailsTraHangRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetDetailsTraHangAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDetailsTraHang endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetListDropDown method endpoint
    /// </summary>
    [HttpPost("getlistdropdown")]
    public async Task<ActionResult<DataTable>> GetListDropDown([FromBody] GetListDropDownRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetListDropDownAsync(request.KhoaPhieuXuat);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDropDown endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Print/Report Endpoints

    /// <summary>
    /// Legacy GetDataPrint method endpoint
    /// </summary>
    [HttpPost("getdataprint")]
    public async Task<ActionResult<DataTable>> GetDataPrint([FromBody] GetDataPrintRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetDataPrintAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrint endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDataPrintDichVu method endpoint
    /// </summary>
    [HttpPost("getdataprintdichvu")]
    public async Task<ActionResult<DataTable>> GetDataPrintDichVu([FromBody] GetDataPrintDichVuRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetDataPrintDichVuAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrintDichVu endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDataPrintBKNhapKhoCT method endpoint
    /// </summary>
    [HttpPost("getdataprintbknhapkhoct")]
    public async Task<ActionResult<DataTable>> GetDataPrintBKNhapKhoCT([FromBody] GetDataPrintBKNhapKhoCTRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetDataPrintBKNhapKhoCTAsync(
                request.KhoaDonVi, request.TuNgay, request.DenNgay,
                request.KhoaDoiTuong, request.NguonNhap, request.LoaiNhap);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrintBKNhapKhoCT endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy Detail Endpoints

    /// <summary>
    /// Legacy GetDetailDichVu method endpoint
    /// </summary>
    [HttpPost("getdetaildichvu")]
    public async Task<ActionResult<DataTable>> GetDetailDichVu([FromBody] GetDetailDichVuRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetDetailDichVuAsync(request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDetailDichVu endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Legacy Utility Endpoints

    /// <summary>
    /// Legacy IsDuplicateVoucherNo method endpoint
    /// </summary>
    [HttpPost("isduplicatevoucherno")]
    public async Task<ActionResult<bool>> IsDuplicateVoucherNo([FromBody] IsDuplicateVoucherNoRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.IsDuplicateVoucherNoAsync(request.VoucherNo, request.KeyTable);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IsDuplicateVoucherNo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _nhapKhoService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult> ClearTemp([FromBody] string keyTable)
    {
        try
        {
            await _nhapKhoService.ClearTempAsync(keyTable);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetSoTaiKhoan method endpoint
    /// </summary>
    [HttpPost("getsotaikhoan")]
    public async Task<ActionResult<GetSoTaiKhoanResponseDto>> GetSoTaiKhoan([FromBody] GetSoTaiKhoanRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetSoTaiKhoanAsync(request.Khoa);
            return Ok(new GetSoTaiKhoanResponseDto { TKNo = result.TKNo, TKCo = result.TKCo });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetSoTaiKhoan endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetGiaVon method endpoint
    /// </summary>
    [HttpPost("getgiavon")]
    public async Task<ActionResult<double>> GetGiaVon([FromBody] GetGiaVonRequestDto request)
    {
        try
        {
            var result = await _nhapKhoService.GetGiaVonAsync(request.KhoaHangHoa, request.Ngay);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetGiaVon endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs

// GetListRequestDto is already defined in BaoGiaController.cs

/// <summary>
/// Request DTO for GetListDetails method
/// </summary>
public class GetListDetailsRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetDetailsTraHang method
/// </summary>
public class GetDetailsTraHangRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetListDropDown method
/// </summary>
public class GetListDropDownRequestDto
{
    public string KhoaPhieuXuat { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetDataPrint method
/// </summary>
public class GetDataPrintRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetDataPrintDichVu method
/// </summary>
public class GetDataPrintDichVuRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetDataPrintBKNhapKhoCT method
/// </summary>
public class GetDataPrintBKNhapKhoCTRequestDto
{
    public string KhoaDonVi { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public string DenNgay { get; set; } = string.Empty;
    public string KhoaDoiTuong { get; set; } = string.Empty;
    public string NguonNhap { get; set; } = string.Empty;
    public int LoaiNhap { get; set; } = -1;
}

/// <summary>
/// Request DTO for GetDetailDichVu method
/// </summary>
public class GetDetailDichVuRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for IsDuplicateVoucherNo method
/// </summary>
public class IsDuplicateVoucherNoRequestDto
{
    public string VoucherNo { get; set; } = string.Empty;
    public string KeyTable { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetSoTaiKhoan method
/// </summary>
public class GetSoTaiKhoanRequestDto
{
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Response DTO for GetSoTaiKhoan method
/// </summary>
public class GetSoTaiKhoanResponseDto
{
    public string TKNo { get; set; } = string.Empty;
    public string TKCo { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetGiaVon method
/// </summary>
public class GetGiaVonRequestDto
{
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string Ngay { get; set; } = string.Empty;
}

#endregion
