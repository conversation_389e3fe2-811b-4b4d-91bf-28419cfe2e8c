using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Models.Interfaces
{
    /// <summary>
    /// Repository interface for KhoanMucSuaChua (Repair Item Categories) entity
    /// Defines data access operations for KhoanMucSuaChua
    /// </summary>
    public interface IKhoanMucSuaChuaRepository
    {
        Task<IEnumerable<KhoanMucSuaChuaListDto>> GetAllAsync();
        Task<KhoanMucSuaChuaDto?> GetByIdAsync(string khoa);
        Task<KhoanMucSuaChuaDto?> GetByCodeAsync(string ma);
        Task<string> CreateAsync(CreateKhoanMucSuaChuaDto createDto);
        Task<bool> UpdateAsync(string khoa, UpdateKhoanMucSuaChuaDto updateDto);
        Task<bool> DeleteAsync(string khoa);

        // Legacy methods
        Task<IEnumerable<KhoanMucSuaChuaListDto>> ShowListAsync(string strKeyFilter = "", string strFiledNameFilter = "");
        Task<IEnumerable<KhoanMucSuaChuaListDto>> ShowAllListAsync();
        Task<string> SearchByCodeAsync(string strCode = "", string strKeyFilter = "", string strFiledNameFilter = "");
        Task<System.Data.DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");

        // Core repository methods
        Task<KhoanMucSuaChuaDto?> LoadAsync(string khoa);
        Task<KhoanMucSuaChuaDto?> LoadByCodeAsync(string ma);
        Task<bool> SaveAsync(KhoanMucSuaChuaDto dto, string action);
    }
}
