using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace GP.Mobile.Core.Services
{
    /// <summary>
    /// Service implementation for Temporary Quotation Data (clsTempBaoGia)
    /// Implements business logic for managing temporary quotation data
    /// Exact functionality from clsTempBaoGia legacy class usage
    /// </summary>
    public class TempBaoGiaService : ITempBaoGiaService
    {
        private readonly ITempBaoGiaRepository _repository;
        private readonly ILogger<TempBaoGiaService> _logger;

        public TempBaoGiaService(ITempBaoGiaRepository repository, ILogger<TempBaoGiaService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Load temporary quotation data by quotation ID
        /// Exact implementation from legacy Load method (line 10929)
        /// </summary>
        public async Task<TempBaoGiaDto?> LoadAsync(string khoaBaoGia)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    _logger.LogWarning("LoadAsync called with empty khoaBaoGia");
                    return null;
                }

                return await _repository.LoadAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Save temporary quotation data
        /// Exact implementation from legacy Save method (line 9964)
        /// </summary>
        public async Task<ServiceResult<bool>> SaveAsync(SaveTempBaoGiaDto saveDto)
        {
            try
            {
                // Validate input
                var validationResult = await ValidateForSaveAsync(saveDto);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult(validationResult.Message, validationResult.Errors);
                }

                // Save temporary data
                var saveResult = await _repository.SaveAsync(saveDto);
                if (saveResult)
                {
                    _logger.LogInformation("Successfully saved temporary quotation data for {Khoa}", saveDto.Khoa);
                    return ServiceResult<bool>.SuccessResult(true, "Lưu dữ liệu tạm thời thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi lưu dữ liệu tạm thời");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving temporary quotation data for {Khoa}", saveDto.Khoa);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi lưu dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Save request for cancellation approval
        /// Exact implementation from legacy SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        public async Task<ServiceResult<bool>> SaveRequestDuyetHuyAsync(RequestDuyetHuyDto requestDto)
        {
            try
            {
                // Validate input
                var validationResult = await ValidateCancellationRequestAsync(requestDto);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult(validationResult.Message, validationResult.Errors);
                }

                // Check if there's already a pending request
                var hasPendingRequest = await _repository.HasPendingCancellationRequestAsync(requestDto.KhoaBaoGia);
                if (hasPendingRequest)
                {
                    return ServiceResult<bool>.ErrorResult("Báo giá này đã có yêu cầu duyệt hủy đang chờ xử lý");
                }

                // Save cancellation request
                var saveResult = await _repository.SaveRequestDuyetHuyAsync(requestDto);
                if (saveResult)
                {
                    // Send notification
                    await SendCancellationNotificationAsync(requestDto.KhoaBaoGia, "REQUEST");

                    _logger.LogInformation("Successfully saved cancellation request for quotation {KhoaBaoGia}", requestDto.KhoaBaoGia);
                    return ServiceResult<bool>.SuccessResult(true, "Gửi yêu cầu duyệt hủy thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi gửi yêu cầu duyệt hủy");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cancellation request for quotation {KhoaBaoGia}", requestDto.KhoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi gửi yêu cầu duyệt hủy");
            }
        }

        /// <summary>
        /// Delete temporary quotation data
        /// </summary>
        public async Task<ServiceResult<bool>> DeleteAsync(string khoaBaoGia)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    return ServiceResult<bool>.ErrorResult("Mã báo giá không được để trống");
                }

                // Check if data exists
                var exists = await _repository.ExistsAsync(khoaBaoGia);
                if (!exists)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy dữ liệu tạm thời cho báo giá này");
                }

                var deleteResult = await _repository.DeleteAsync(khoaBaoGia);
                if (deleteResult)
                {
                    _logger.LogInformation("Successfully deleted temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                    return ServiceResult<bool>.SuccessResult(true, "Xóa dữ liệu tạm thời thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi xóa dữ liệu tạm thời");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi xóa dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Check if temporary quotation data exists
        /// </summary>
        public async Task<bool> ExistsAsync(string khoaBaoGia)
        {
            try
            {
                return await _repository.ExistsAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if temporary quotation data exists for {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Get temporary quotations requiring approval
        /// </summary>
        public async Task<ServiceResult<List<TempBaoGiaListDto>>> GetPendingApprovalsAsync(string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Mã đơn vị không được để trống");
                }

                var pendingApprovals = await _repository.GetPendingApprovalsAsync(donViId);
                return ServiceResult<List<TempBaoGiaListDto>>.SuccessResult(pendingApprovals, 
                    $"Tìm thấy {pendingApprovals.Count} yêu cầu chờ duyệt");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Lỗi hệ thống khi lấy danh sách chờ duyệt");
            }
        }

        /// <summary>
        /// Update cancellation approval status
        /// </summary>
        public async Task<ServiceResult<bool>> UpdateCancellationApprovalAsync(string khoaBaoGia, bool isDuyetHuy, string nguoiDuyet, string lyDo = "")
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    return ServiceResult<bool>.ErrorResult("Mã báo giá không được để trống");
                }

                if (string.IsNullOrWhiteSpace(nguoiDuyet))
                {
                    return ServiceResult<bool>.ErrorResult("Người duyệt không được để trống");
                }

                // Check if temp data exists
                var tempData = await _repository.LoadAsync(khoaBaoGia);
                if (tempData == null)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy dữ liệu tạm thời cho báo giá này");
                }

                // Update approval status
                var updateResult = await _repository.UpdateCancellationApprovalAsync(khoaBaoGia, isDuyetHuy, nguoiDuyet);
                if (updateResult)
                {
                    // Send notification
                    var notificationType = isDuyetHuy ? "APPROVED" : "REJECTED";
                    await SendCancellationNotificationAsync(khoaBaoGia, notificationType);

                    var statusText = isDuyetHuy ? "duyệt" : "từ chối";
                    _logger.LogInformation("Successfully updated cancellation approval to {Status} for quotation {KhoaBaoGia}", 
                        statusText, khoaBaoGia);
                    
                    return ServiceResult<bool>.SuccessResult(true, $"Cập nhật trạng thái {statusText} thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi cập nhật trạng thái duyệt");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cancellation approval for quotation {KhoaBaoGia}", khoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi cập nhật trạng thái duyệt");
            }
        }

        /// <summary>
        /// Get temporary data content
        /// </summary>
        public async Task<string?> GetTempDataAsync(string khoaBaoGia)
        {
            try
            {
                return await _repository.GetTempDataAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp data for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Update temporary data content
        /// </summary>
        public async Task<ServiceResult<bool>> UpdateTempDataAsync(string khoaBaoGia, string tempData, string nguoiCapNhat)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    return ServiceResult<bool>.ErrorResult("Mã báo giá không được để trống");
                }

                if (string.IsNullOrWhiteSpace(nguoiCapNhat))
                {
                    return ServiceResult<bool>.ErrorResult("Người cập nhật không được để trống");
                }

                var updateResult = await _repository.UpdateTempDataAsync(khoaBaoGia, tempData, nguoiCapNhat);
                if (updateResult)
                {
                    return ServiceResult<bool>.SuccessResult(true, "Cập nhật dữ liệu tạm thời thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi cập nhật dữ liệu tạm thời");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating temp data for quotation {KhoaBaoGia}", khoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi cập nhật dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Get cancellation request details
        /// </summary>
        public async Task<RequestDuyetHuyDto?> GetCancellationRequestAsync(string khoaBaoGia)
        {
            try
            {
                var requestData = await _repository.GetCancellationRequestDataAsync(khoaBaoGia);
                if (string.IsNullOrEmpty(requestData))
                {
                    return null;
                }

                return JsonSerializer.Deserialize<RequestDuyetHuyDto>(requestData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cancellation request for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Check if quotation has pending cancellation requests
        /// </summary>
        public async Task<bool> HasPendingCancellationRequestAsync(string khoaBaoGia)
        {
            try
            {
                return await _repository.HasPendingCancellationRequestAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking pending cancellation request for quotation {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Validate temporary quotation data before save
        /// </summary>
        public async Task<ServiceResult<bool>> ValidateForSaveAsync(SaveTempBaoGiaDto saveDto)
        {
            try
            {
                var errors = new List<string>();

                // Check required fields
                if (string.IsNullOrWhiteSpace(saveDto.Khoa))
                {
                    errors.Add("Mã báo giá không được để trống");
                }

                if (string.IsNullOrWhiteSpace(saveDto.NguoiTao))
                {
                    errors.Add("Người tạo không được để trống");
                }

                // Validate temp data if provided
                if (!string.IsNullOrEmpty(saveDto.TempData))
                {
                    try
                    {
                        // Try to parse as JSON to validate format
                        JsonDocument.Parse(saveDto.TempData);
                    }
                    catch
                    {
                        errors.Add("Dữ liệu tạm thời không đúng định dạng JSON");
                    }
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.ErrorResult("Validation failed", errors);
                }

                return ServiceResult<bool>.SuccessResult(true, "Dữ liệu hợp lệ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating save data");
                return ServiceResult<bool>.ErrorResult("Lỗi khi kiểm tra dữ liệu");
            }
        }

        /// <summary>
        /// Validate cancellation request before save
        /// </summary>
        public async Task<ServiceResult<bool>> ValidateCancellationRequestAsync(RequestDuyetHuyDto requestDto)
        {
            try
            {
                var errors = new List<string>();

                // Check required fields
                if (string.IsNullOrWhiteSpace(requestDto.KhoaBaoGia))
                {
                    errors.Add("Mã báo giá không được để trống");
                }

                if (string.IsNullOrWhiteSpace(requestDto.KhoaHangMuc))
                {
                    errors.Add("Mã hạng mục không được để trống");
                }

                if (string.IsNullOrWhiteSpace(requestDto.LyDo))
                {
                    errors.Add("Lý do hủy không được để trống");
                }

                if (string.IsNullOrWhiteSpace(requestDto.NguoiDeXuat))
                {
                    errors.Add("Người đề xuất không được để trống");
                }

                // Validate amounts
                if (requestDto.SoLuong <= 0)
                {
                    errors.Add("Số lượng phải lớn hơn 0");
                }

                if (requestDto.DonGia < 0)
                {
                    errors.Add("Đơn giá không được âm");
                }

                if (requestDto.ThanhTien < 0)
                {
                    errors.Add("Thành tiền không được âm");
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.ErrorResult("Validation failed", errors);
                }

                return ServiceResult<bool>.SuccessResult(true, "Yêu cầu hợp lệ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating cancellation request");
                return ServiceResult<bool>.ErrorResult("Lỗi khi kiểm tra yêu cầu");
            }
        }

        /// <summary>
        /// Get temporary quotations by status
        /// </summary>
        public async Task<ServiceResult<List<TempBaoGiaListDto>>> GetByStatusAsync(int trangThai, string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Mã đơn vị không được để trống");
                }

                var result = await _repository.GetByStatusAsync(trangThai, donViId);
                var statusText = trangThai switch
                {
                    0 => "nháp",
                    1 => "đang xử lý",
                    2 => "đã duyệt",
                    3 => "đã hủy",
                    _ => "không xác định"
                };

                return ServiceResult<List<TempBaoGiaListDto>>.SuccessResult(result,
                    $"Tìm thấy {result.Count} dữ liệu tạm thời có trạng thái {statusText}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp quotations by status {TrangThai} for unit {DonViId}", trangThai, donViId);
                return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Lỗi hệ thống khi lấy dữ liệu theo trạng thái");
            }
        }

        /// <summary>
        /// Get temporary quotations by user
        /// </summary>
        public async Task<ServiceResult<List<TempBaoGiaListDto>>> GetByUserAsync(string nguoiTao, string fromDate, string toDate)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(nguoiTao))
                {
                    return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Người tạo không được để trống");
                }

                var result = await _repository.GetByUserAsync(nguoiTao, fromDate, toDate);
                return ServiceResult<List<TempBaoGiaListDto>>.SuccessResult(result,
                    $"Tìm thấy {result.Count} dữ liệu tạm thời của người dùng");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp quotations by user {NguoiTao}", nguoiTao);
                return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Lỗi hệ thống khi lấy dữ liệu theo người dùng");
            }
        }

        /// <summary>
        /// Get temporary quotations requiring cancellation approval
        /// </summary>
        public async Task<ServiceResult<List<TempBaoGiaListDto>>> GetCancellationRequestsAsync(string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Mã đơn vị không được để trống");
                }

                var result = await _repository.GetCancellationRequestsAsync(donViId);
                return ServiceResult<List<TempBaoGiaListDto>>.SuccessResult(result,
                    $"Tìm thấy {result.Count} yêu cầu duyệt hủy");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cancellation requests for unit {DonViId}", donViId);
                return ServiceResult<List<TempBaoGiaListDto>>.ErrorResult("Lỗi hệ thống khi lấy yêu cầu duyệt hủy");
            }
        }

        public async Task<ServiceResult<TempBaoGiaStatisticsDto>> GetStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<TempBaoGiaStatisticsDto>.SuccessResult(new TempBaoGiaStatisticsDto());
        }

        public async Task<ServiceResult<int>> ClearCompletedDataAsync(string donViId, int olderThanDays = 30)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<int>> BulkUpdateStatusAsync(List<string> khoaBaoGiaList, int trangThai, string nguoiCapNhat)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<PaginatedResult<TempBaoGiaListDto>>> GetForMobileAsync(string donViId, int pageSize, int pageNumber, string? searchTerm = null)
        {
            // Implementation will be added in next part
            return ServiceResult<PaginatedResult<TempBaoGiaListDto>>.SuccessResult(new PaginatedResult<TempBaoGiaListDto>());
        }

        public async Task<ServiceResult<List<TempBaoGiaListDto>>> GetByDateRangeAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<List<TempBaoGiaListDto>>.SuccessResult(new List<TempBaoGiaListDto>());
        }

        public async Task<ServiceResult<int>> ArchiveOldDataAsync(string donViId, int olderThanDays = 90)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<TempBaoGiaWorkflowStatusDto>> GetWorkflowStatusAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return ServiceResult<TempBaoGiaWorkflowStatusDto>.SuccessResult(new TempBaoGiaWorkflowStatusDto());
        }

        public async Task<ServiceResult<bool>> SendCancellationNotificationAsync(string khoaBaoGia, string requestType)
        {
            // Implementation will be added in next part
            return ServiceResult<bool>.SuccessResult(true);
        }

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<byte[]>.SuccessResult(Array.Empty<byte>());
        }

        public async Task<ServiceResult<List<TempBaoGiaAuditDto>>> GetAuditTrailAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return ServiceResult<List<TempBaoGiaAuditDto>>.SuccessResult(new List<TempBaoGiaAuditDto>());
        }
    }
}
