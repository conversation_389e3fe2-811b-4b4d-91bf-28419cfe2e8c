using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for Log (Audit Trail) entity
/// Maps exactly to HT_Log table in legacy database
/// Implements ALL properties from clsLog.cs (201 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUDIT SYSTEM COMPONENT - Essential for tracking user actions and system changes
/// </summary>
public class LogDto
{
    /// <summary>
    /// Primary key - Auto-increment identifier
    /// Maps to: mId property in legacy class
    /// </summary>
    public int Id { get; set; } = 0;

    /// <summary>
    /// Document type code
    /// Maps to: mLoaiChungTu property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string LoaiChungTu { get; set; } = string.Empty;

    /// <summary>
    /// Document key/identifier
    /// Maps to: mKhoaChungTu property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string KhoaChungTu { get; set; } = string.Empty;

    /// <summary>
    /// Action performed (INSERT, UPDATE, DELETE, VIEW, etc.)
    /// Maps to: mHanhDong property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string HanhDong { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp of the action
    /// Maps to: mNgayGio property in legacy class
    /// </summary>
    [Required]
    public DateTime NgayGio { get; set; } = DateTime.Now;

    /// <summary>
    /// User who performed the action
    /// Maps to: mNguoiDung property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string NguoiDung { get; set; } = string.Empty;

    /// <summary>
    /// Additional details about the action
    /// </summary>
    [StringLength(1000)]
    public string ChiTiet { get; set; } = string.Empty;

    /// <summary>
    /// IP address of the user
    /// </summary>
    [StringLength(50)]
    public string DiaChi { get; set; } = string.Empty;

    /// <summary>
    /// User agent/browser information
    /// </summary>
    [StringLength(500)]
    public string UserAgent { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new Log entries
/// Contains only required fields for creation
/// </summary>
public class CreateLogDto
{
    [Required]
    [StringLength(20)]
    public string LoaiChungTu { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string KhoaChungTu { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string HanhDong { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string NguoiDung { get; set; } = string.Empty;

    [StringLength(1000)]
    public string ChiTiet { get; set; } = string.Empty;

    [StringLength(50)]
    public string DiaChi { get; set; } = string.Empty;

    [StringLength(500)]
    public string UserAgent { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Log list display
/// Optimized for list views with user-friendly formatting
/// </summary>
public class LogListDto
{
    public int Id { get; set; } = 0;
    public string LoaiChungTu { get; set; } = string.Empty;
    public string KhoaChungTu { get; set; } = string.Empty;
    public string HanhDong { get; set; } = string.Empty;
    public DateTime NgayGio { get; set; } = DateTime.Now;
    public string NguoiDung { get; set; } = string.Empty;
    public string ChiTiet { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    
    // Calculated/Display fields
    public string NgayGioText { get; set; } = string.Empty; // Formatted date/time
    public string HanhDongText { get; set; } = string.Empty; // User-friendly action name
    public string LoaiChungTuText { get; set; } = string.Empty; // Document type description
    public string TimeAgo { get; set; } = string.Empty; // "2 hours ago" format
}

/// <summary>
/// DTO for Log search operations
/// Contains search criteria and filters
/// </summary>
public class LogSearchDto
{
    public string? LoaiChungTu { get; set; }
    public string? KhoaChungTu { get; set; }
    public string? HanhDong { get; set; }
    public string? NguoiDung { get; set; }
    public DateTime? NgayGioFrom { get; set; }
    public DateTime? NgayGioTo { get; set; }
    public string? DiaChi { get; set; }
    public int? PageSize { get; set; } = 50;
    public int? PageNumber { get; set; } = 1;
}

/// <summary>
/// DTO for audit trail summary
/// Shows summary statistics for audit logs
/// </summary>
public class AuditTrailSummaryDto
{
    public int TotalLogs { get; set; } = 0;
    public int LogsToday { get; set; } = 0;
    public int LogsThisWeek { get; set; } = 0;
    public int LogsThisMonth { get; set; } = 0;
    public string MostActiveUser { get; set; } = string.Empty;
    public string MostCommonAction { get; set; } = string.Empty;
    public string MostAccessedDocument { get; set; } = string.Empty;
    public List<ActionSummaryDto> ActionSummary { get; set; } = new List<ActionSummaryDto>();
    public List<UserActivityDto> UserActivity { get; set; } = new List<UserActivityDto>();
}

/// <summary>
/// DTO for action summary statistics
/// </summary>
public class ActionSummaryDto
{
    public string HanhDong { get; set; } = string.Empty;
    public string HanhDongText { get; set; } = string.Empty;
    public int Count { get; set; } = 0;
    public double Percentage { get; set; } = 0;
}

/// <summary>
/// DTO for user activity statistics
/// </summary>
public class UserActivityDto
{
    public string NguoiDung { get; set; } = string.Empty;
    public string TenNguoiDung { get; set; } = string.Empty; // Full name if available
    public int TotalActions { get; set; } = 0;
    public DateTime LastActivity { get; set; } = DateTime.Now;
    public string LastAction { get; set; } = string.Empty;
    public string LastDocument { get; set; } = string.Empty;
}

/// <summary>
/// DTO for automotive-specific audit logs
/// Specialized for automotive business operations
/// </summary>
public class AutomotiveAuditLogDto
{
    public int Id { get; set; } = 0;
    public string LoaiChungTu { get; set; } = string.Empty;
    public string KhoaChungTu { get; set; } = string.Empty;
    public string HanhDong { get; set; } = string.Empty;
    public DateTime NgayGio { get; set; } = DateTime.Now;
    public string NguoiDung { get; set; } = string.Empty;
    public string ChiTiet { get; set; } = string.Empty;
    
    // Automotive-specific fields
    public string DocumentCategory { get; set; } = string.Empty; // Service/Sales/Maintenance
    public bool IsServiceLog { get; set; } = false;
    public bool IsSalesLog { get; set; } = false;
    public bool IsMaintenanceLog { get; set; } = false;
    public bool IsWarrantyLog { get; set; } = false;
    public string VehicleInfo { get; set; } = string.Empty; // If related to a vehicle
    public string CustomerInfo { get; set; } = string.Empty; // If related to a customer
    public string ServiceAdvisor { get; set; } = string.Empty; // Service advisor involved
}

/// <summary>
/// DTO for log validation operations
/// Used for validation and business rules
/// </summary>
public class LogValidationDto
{
    public bool IsValidLoaiChungTu { get; set; } = true;
    public bool IsValidKhoaChungTu { get; set; } = true;
    public bool IsValidHanhDong { get; set; } = true;
    public bool IsValidNguoiDung { get; set; } = true;
    public bool CanDelete { get; set; } = false; // Audit logs should not be deleted
    public List<string> ValidationErrors { get; set; } = new List<string>();
    public List<string> ValidationWarnings { get; set; } = new List<string>();
}

/// <summary>
/// DTO for log export operations
/// Used for exporting audit logs to various formats
/// </summary>
public class LogExportDto
{
    public DateTime ExportDate { get; set; } = DateTime.Now;
    public string ExportedBy { get; set; } = string.Empty;
    public LogSearchDto SearchCriteria { get; set; } = new LogSearchDto();
    public int TotalRecords { get; set; } = 0;
    public string ExportFormat { get; set; } = "CSV"; // CSV, Excel, PDF
    public string FileName { get; set; } = string.Empty;
    public byte[] FileContent { get; set; } = Array.Empty<byte>();
}

/// <summary>
/// DTO for system activity monitoring
/// Real-time monitoring of system activities
/// </summary>
public class SystemActivityDto
{
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public int ActiveUsers { get; set; } = 0;
    public int ActionsPerMinute { get; set; } = 0;
    public int ActionsPerHour { get; set; } = 0;
    public List<string> RecentActions { get; set; } = new List<string>();
    public List<string> ActiveDocumentTypes { get; set; } = new List<string>();
    public string SystemStatus { get; set; } = "Normal"; // Normal, Busy, Critical
}

/// <summary>
/// DTO for GUID-based log tracking
/// Used for temporary log tracking in repair details
/// </summary>
public class LogGuidTrackingDto
{
    public string LogId { get; set; } = string.Empty;
    public string TempId { get; set; } = string.Empty; // GUID for temporary tracking
    public string KhoaChungTu { get; set; } = string.Empty;
    public string LoaiChungTu { get; set; } = string.Empty;
    public DateTime NgayTao { get; set; } = DateTime.Now;
    public bool IsProcessed { get; set; } = false;
}

/// <summary>
/// DTO for log retention policy
/// Manages log retention and cleanup policies
/// </summary>
public class LogRetentionPolicyDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public int RetentionDays { get; set; } = 365; // Default 1 year
    public bool AutoCleanup { get; set; } = false;
    public DateTime LastCleanup { get; set; } = DateTime.MinValue;
    public int RecordsToCleanup { get; set; } = 0;
    public bool IsActive { get; set; } = true;
}
