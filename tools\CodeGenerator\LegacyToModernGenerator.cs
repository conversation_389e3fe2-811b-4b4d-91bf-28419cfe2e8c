using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace GP.Mobile.CodeGenerator
{
    /// <summary>
    /// Main automation tool to convert legacy business classes to modern API
    /// Processes all 180+ classes in Base\Business\ folder automatically
    /// </summary>
    public class LegacyToModernGenerator
    {
        private readonly LegacyClassAnalyzer _analyzer;
        private readonly DtoGenerator _dtoGenerator;
        private readonly RepositoryGenerator _repositoryGenerator;
        private readonly ServiceGenerator _serviceGenerator;
        private readonly ControllerGenerator _controllerGenerator;

        public LegacyToModernGenerator()
        {
            _analyzer = new LegacyClassAnalyzer();
            _dtoGenerator = new DtoGenerator();
            _repositoryGenerator = new RepositoryGenerator();
            _serviceGenerator = new ServiceGenerator();
            _controllerGenerator = new ControllerGenerator();
        }

        /// <summary>
        /// Processes all legacy classes and generates complete modern API
        /// </summary>
        public void GenerateAllClasses(string legacyBasePath, string modernApiPath)
        {
            Console.WriteLine("🚀 Starting Legacy to Modern API Generation...");
            Console.WriteLine($"Legacy Path: {legacyBasePath}");
            Console.WriteLine($"Modern API Path: {modernApiPath}");
            Console.WriteLine();

            // Get all legacy class files
            var legacyFiles = Directory.GetFiles(legacyBasePath, "cls*.cs", SearchOption.TopDirectoryOnly);
            Console.WriteLine($"Found {legacyFiles.Length} legacy classes to process");
            Console.WriteLine();

            var results = new List<GenerationResult>();
            var processed = 0;

            foreach (var legacyFile in legacyFiles)
            {
                try
                {
                    processed++;
                    var fileName = Path.GetFileNameWithoutExtension(legacyFile);
                    Console.WriteLine($"[{processed}/{legacyFiles.Length}] Processing {fileName}...");

                    var result = ProcessSingleClass(legacyFile, modernApiPath);
                    results.Add(result);

                    if (result.Success)
                    {
                        Console.WriteLine($"✅ {fileName} - Generated {result.FilesGenerated} files");
                    }
                    else
                    {
                        Console.WriteLine($"❌ {fileName} - Error: {result.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error processing {Path.GetFileName(legacyFile)}: {ex.Message}");
                    results.Add(new GenerationResult
                    {
                        ClassName = Path.GetFileNameWithoutExtension(legacyFile),
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }
            }

            // Generate summary report
            GenerateSummaryReport(results, modernApiPath);
        }

        /// <summary>
        /// Processes a single legacy class and generates all modern API files
        /// </summary>
        public GenerationResult ProcessSingleClass(string legacyFilePath, string modernApiPath)
        {
            var result = new GenerationResult
            {
                ClassName = Path.GetFileNameWithoutExtension(legacyFilePath)
            };

            try
            {
                // Step 1: Analyze legacy class
                var analysis = _analyzer.AnalyzeClass(legacyFilePath);
                
                if (string.IsNullOrEmpty(analysis.ClassName))
                {
                    result.ErrorMessage = "Could not extract class name from legacy file";
                    return result;
                }

                var entityName = analysis.ClassName.Replace("cls", "");
                result.EntityName = entityName;

                // Step 2: Generate DTOs
                GenerateDtoFiles(analysis, modernApiPath, result);

                // Step 3: Generate Repository
                GenerateRepositoryFiles(analysis, modernApiPath, result);

                // Step 4: Generate Service
                GenerateServiceFiles(analysis, modernApiPath, result);

                // Step 5: Generate Controller
                GenerateControllerFiles(analysis, modernApiPath, result);

                result.Success = true;
                result.Analysis = analysis;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        private void GenerateDtoFiles(LegacyClassAnalyzer.ClassAnalysisResult analysis, string modernApiPath, GenerationResult result)
        {
            var entityName = analysis.ClassName.Replace("cls", "");
            var dtoPath = Path.Combine(modernApiPath, "src", "API", "GP.Mobile.Models", "DTOs");
            Directory.CreateDirectory(dtoPath);

            // Generate main DTO
            var dtoContent = _dtoGenerator.GenerateDto(analysis);
            var dtoFile = Path.Combine(dtoPath, $"{entityName}Dto.cs");
            File.WriteAllText(dtoFile, dtoContent);
            result.GeneratedFiles.Add(dtoFile);

            // Generate List DTO
            var listDtoContent = _dtoGenerator.GenerateListDto(analysis);
            var listDtoFile = Path.Combine(dtoPath, $"{entityName}ListDto.cs");
            File.WriteAllText(listDtoFile, listDtoContent);
            result.GeneratedFiles.Add(listDtoFile);

            // Generate Create DTO
            var createDtoContent = _dtoGenerator.GenerateCreateDto(analysis);
            var createDtoFile = Path.Combine(dtoPath, $"Create{entityName}Dto.cs");
            File.WriteAllText(createDtoFile, createDtoContent);
            result.GeneratedFiles.Add(createDtoFile);

            // Generate Update Status DTO
            var updateStatusDtoContent = _dtoGenerator.GenerateUpdateStatusDto(analysis);
            var updateStatusDtoFile = Path.Combine(dtoPath, $"Update{entityName}StatusDto.cs");
            File.WriteAllText(updateStatusDtoFile, updateStatusDtoContent);
            result.GeneratedFiles.Add(updateStatusDtoFile);
        }

        private void GenerateRepositoryFiles(LegacyClassAnalyzer.ClassAnalysisResult analysis, string modernApiPath, GenerationResult result)
        {
            var entityName = analysis.ClassName.Replace("cls", "");
            var repositoryPath = Path.Combine(modernApiPath, "src", "API", "GP.Mobile.Data", "Repositories");
            Directory.CreateDirectory(repositoryPath);

            // Generate Repository Interface and Implementation
            var interfaceContent = _repositoryGenerator.GenerateInterface(analysis);
            var implementationContent = _repositoryGenerator.GenerateImplementation(analysis);

            var repositoryFile = Path.Combine(repositoryPath, $"{entityName}Repository.cs");
            var combinedContent = interfaceContent + "\n\n" + implementationContent;
            File.WriteAllText(repositoryFile, combinedContent);
            result.GeneratedFiles.Add(repositoryFile);
        }

        private void GenerateServiceFiles(LegacyClassAnalyzer.ClassAnalysisResult analysis, string modernApiPath, GenerationResult result)
        {
            var entityName = analysis.ClassName.Replace("cls", "");
            var servicePath = Path.Combine(modernApiPath, "src", "API", "GP.Mobile.Core", "Services");
            Directory.CreateDirectory(servicePath);

            // Generate Service Interface and Implementation
            var serviceContent = _serviceGenerator.GenerateService(analysis);
            var serviceFile = Path.Combine(servicePath, $"{entityName}Service.cs");
            File.WriteAllText(serviceFile, serviceContent);
            result.GeneratedFiles.Add(serviceFile);
        }

        private void GenerateControllerFiles(LegacyClassAnalyzer.ClassAnalysisResult analysis, string modernApiPath, GenerationResult result)
        {
            var entityName = analysis.ClassName.Replace("cls", "");
            var controllerPath = Path.Combine(modernApiPath, "src", "API", "GP.Mobile.API", "Controllers");
            Directory.CreateDirectory(controllerPath);

            // Generate Legacy Controller
            var controllerContent = _controllerGenerator.GenerateLegacyController(analysis);
            var controllerFile = Path.Combine(controllerPath, $"{entityName}LegacyController.cs");
            File.WriteAllText(controllerFile, controllerContent);
            result.GeneratedFiles.Add(controllerFile);

            // Generate Modern Controller
            var modernControllerContent = _controllerGenerator.GenerateModernController(analysis);
            var modernControllerFile = Path.Combine(controllerPath, $"{entityName}Controller.cs");
            File.WriteAllText(modernControllerFile, modernControllerContent);
            result.GeneratedFiles.Add(modernControllerFile);
        }

        private void GenerateSummaryReport(List<GenerationResult> results, string modernApiPath)
        {
            Console.WriteLine();
            Console.WriteLine("📊 GENERATION SUMMARY REPORT");
            Console.WriteLine("=" * 50);

            var successful = results.Where(r => r.Success).ToList();
            var failed = results.Where(r => !r.Success).ToList();

            Console.WriteLine($"✅ Successful: {successful.Count}");
            Console.WriteLine($"❌ Failed: {failed.Count}");
            Console.WriteLine($"📁 Total Files Generated: {successful.Sum(r => r.FilesGenerated)}");
            Console.WriteLine();

            if (failed.Any())
            {
                Console.WriteLine("❌ FAILED CLASSES:");
                foreach (var failure in failed)
                {
                    Console.WriteLine($"   - {failure.ClassName}: {failure.ErrorMessage}");
                }
                Console.WriteLine();
            }

            // Generate detailed report file
            var reportPath = Path.Combine(modernApiPath, "docs", "GENERATION_REPORT.md");
            Directory.CreateDirectory(Path.GetDirectoryName(reportPath));
            
            var reportContent = GenerateDetailedReport(results);
            File.WriteAllText(reportPath, reportContent);
            
            Console.WriteLine($"📄 Detailed report saved to: {reportPath}");
            Console.WriteLine();
            Console.WriteLine("🎉 Legacy to Modern API Generation Complete!");
        }

        private string GenerateDetailedReport(List<GenerationResult> results)
        {
            var sb = new System.Text.StringBuilder();
            
            sb.AppendLine("# Legacy to Modern API Generation Report");
            sb.AppendLine();
            sb.AppendLine($"**Generated on:** {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"**Total Classes Processed:** {results.Count}");
            sb.AppendLine($"**Successful:** {results.Count(r => r.Success)}");
            sb.AppendLine($"**Failed:** {results.Count(r => !r.Success)}");
            sb.AppendLine();

            sb.AppendLine("## ✅ Successfully Generated Classes");
            sb.AppendLine();
            sb.AppendLine("| Class Name | Entity Name | Properties | Methods | Files Generated |");
            sb.AppendLine("|------------|-------------|------------|---------|-----------------|");
            
            foreach (var success in results.Where(r => r.Success))
            {
                var propCount = success.Analysis?.Properties.Count ?? 0;
                var methodCount = success.Analysis?.Methods.Count ?? 0;
                sb.AppendLine($"| {success.ClassName} | {success.EntityName} | {propCount} | {methodCount} | {success.FilesGenerated} |");
            }

            sb.AppendLine();
            sb.AppendLine("## ❌ Failed Classes");
            sb.AppendLine();
            
            foreach (var failure in results.Where(r => !r.Success))
            {
                sb.AppendLine($"### {failure.ClassName}");
                sb.AppendLine($"**Error:** {failure.ErrorMessage}");
                sb.AppendLine();
            }

            return sb.ToString();
        }
    }

    public class GenerationResult
    {
        public string ClassName { get; set; } = "";
        public string EntityName { get; set; } = "";
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<string> GeneratedFiles { get; set; } = new();
        public LegacyClassAnalyzer.ClassAnalysisResult? Analysis { get; set; }
        
        public int FilesGenerated => GeneratedFiles.Count;
    }
}
