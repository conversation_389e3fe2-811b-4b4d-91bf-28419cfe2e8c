# 🔧 **<PERSON><PERSON>KEND CONNECTION SETUP - CARSOFT_GIAPHAT DATABASE**

## 📋 **YOUR DATABASE CONFIGURATION**

The backend is now configured to use **your existing CARSOFT_GIAPHAT database** with Windows Authentication:

### **Connection String:**
```
Server=localhost;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true
```

### **Authentication Method:**
- ✅ **Windows Authentication** (Trusted_Connection=true)
- ✅ **Local SQL Server** (localhost)
- ✅ **CARSOFT_GIAPHAT Database**
- ✅ **Multiple Active Result Sets** enabled

## 🚀 **QUICK START WITH YOUR DATABASE**

### **1. Test Database Connection (30 seconds)**
```bash
# Test your database connection first
./test-database-connection.bat

# This will:
# ✅ Build the API
# ✅ Test connection to CARSOFT_GIAPHAT
# ✅ Check required tables exist
# ✅ Verify authentication data
```

### **2. Start Full Development Environment**
```bash
# Start both backend and frontend
./dev-start.bat

# This will:
# ✅ Connect to your CARSOFT_GIAPHAT database
# ✅ Start API on http://localhost:5001
# ✅ Start React Native app
# ✅ Test database connection automatically
```

## 🔍 **DATABASE TESTING ENDPOINTS**

Once the API is running, you can test your database connection:

### **Connection Test:**
```bash
curl http://localhost:5001/api/databasetest/connection-test
```
**Expected Response:**
```json
{
  "DatabaseName": "CARSOFT_GIAPHAT",
  "CurrentUser": "YourDomain\\YourUsername",
  "ServerName": "YourServerName",
  "Status": "Connected Successfully",
  "AuthenticationType": "Windows Authentication"
}
```

### **Tables Test:**
```bash
curl http://localhost:5001/api/databasetest/tables-test
```
**Expected Response:**
```json
{
  "Status": "All Tables Found",
  "FoundTables": [
    {"TableName": "DM_NguoiDung", "TableType": "BASE TABLE"},
    {"TableName": "DM_DonVi", "TableType": "BASE TABLE"},
    {"TableName": "DM_DieuKhoanbaoGia", "TableType": "BASE TABLE"},
    {"TableName": "Temp_BaoGia", "TableType": "BASE TABLE"}
  ],
  "TotalFound": 6,
  "TotalRequired": 6
}
```

### **Authentication Data Test:**
```bash
curl http://localhost:5001/api/databasetest/auth-data-test
```
**Expected Response:**
```json
{
  "Status": "Authentication Data Available",
  "UserCount": 5,
  "BranchCount": 3,
  "TermsCount": 10,
  "QuotationCount": 25,
  "DatabaseReady": true
}
```

## 📊 **USING YOUR EXISTING DATA**

### **If You Have Existing Users:**
The authentication system will work with your existing `DM_NguoiDung` table:
- ✅ **Username** → `TenDangNhap` column
- ✅ **Password** → `MatKhau` column  
- ✅ **User Name** → `TenViet` column
- ✅ **User Type** → `LoaiNguoiDung` column
- ✅ **Allowed Branches** → `DonViDangNhap` column

### **If You Need Test Data:**
Run the test data script to create sample users:
```sql
-- Run in SQL Server Management Studio
-- File: database-test-data.sql
-- Creates: admin/admin123, mobile/mobile123
```

## 🔐 **AUTHENTICATION WORKFLOW WITH YOUR DATA**

### **1. Login Process:**
```
1. User enters username in mobile app
2. API queries: SELECT * FROM DM_NguoiDung WHERE TenDangNhap = 'username'
3. App loads available branches from DonViDangNhap column
4. User selects branch and enters password
5. API validates password against MatKhau column
6. Returns JWT token for authenticated session
```

### **2. Branch/Unit Selection:**
```
1. API queries: SELECT * FROM DM_DonVi WHERE Active = 1
2. Filters by user's DonViDangNhap permissions
3. Returns available branches for selection
4. User selects branch for current session
```

## ⚙️ **CONFIGURATION FILES**

### **appsettings.json:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  },
  "Jwt": {
    "Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForGPMobile!",
    "Issuer": "GP.Mobile.API",
    "Audience": "GP.Mobile.App",
    "ExpiryMinutes": "60"
  }
}
```

### **appsettings.Development.json:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CARSOFT_GIAPHAT;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  }
}
```

## 🛠️ **TROUBLESHOOTING**

### **If Connection Fails:**
1. **Check SQL Server is running:**
   ```bash
   services.msc
   # Look for "SQL Server (MSSQLSERVER)" - should be "Running"
   ```

2. **Verify database exists:**
   ```sql
   SELECT name FROM sys.databases WHERE name = 'CARSOFT_GIAPHAT';
   ```

3. **Check Windows Authentication:**
   ```sql
   SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') as IsWindowsAuthOnly;
   -- Should return 1
   ```

4. **Test connection manually:**
   ```bash
   sqlcmd -S localhost -d CARSOFT_GIAPHAT -E
   ```

### **If Tables Missing:**
Run this query to check your table structure:
```sql
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
AND TABLE_NAME LIKE 'DM_%' OR TABLE_NAME LIKE 'Temp_%' OR TABLE_NAME LIKE 'SC_%'
ORDER BY TABLE_NAME;
```

## 🎯 **READY TO USE YOUR DATABASE**

Your backend is now configured to:
- ✅ **Connect to CARSOFT_GIAPHAT** with Windows Authentication
- ✅ **Use existing user accounts** from DM_NguoiDung table
- ✅ **Work with existing branches** from DM_DonVi table
- ✅ **Integrate with existing quotations** from Temp_BaoGia table
- ✅ **Preserve all existing data** and functionality

## 🚀 **START TESTING NOW:**

```bash
# 1. Test database connection
./test-database-connection.bat

# 2. Start full development environment
./dev-start.bat

# 3. Login with your existing users or test users
# Username: [your existing username]
# Password: [your existing password]
```

**Your mobile app is now connected to your existing CARSOFT_GIAPHAT database!** 🎉
