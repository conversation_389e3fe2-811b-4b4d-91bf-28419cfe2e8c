using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoDuong (Maintenance Service) entity
/// Maps exactly to DM_DMBaoDuong table in legacy database
/// Implements ALL properties from clsDMBaoDuong.cs (364 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for maintenance service templates linking vehicle types with service types
/// </summary>
public class BaoDuongDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle type key - Foreign key to DM_LoaiXe
    /// Maps to: mKhoaLoaiXe property in legacy class
    /// </summary>
    [Required]
    public string KhoaLoaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Service type key - Foreign key to DM_LoaiDichVu
    /// Maps to: mKhoaLoaiDichVu property in legacy class
    /// </summary>
    [Required]
    public string KhoaLoaiDichVu { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes about the maintenance service
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Last update date (YYYYMMDD format)
    /// Maps to: mNgayCapNhat property in legacy class
    /// </summary>
    public string NgayCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoDuong list display with joined data
/// Optimized for automotive maintenance service lists
/// Used by GetListDMBD method
/// </summary>
public class BaoDuongListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty; // From DM_LoaiXe.TenViet
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty; // From DM_LoaiDichVu.TenViet
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    public string NhanVienCapNhat { get; set; } = string.Empty; // From DM_DoiTuong.TenViet
    public DateTime? NgayCapNhat { get; set; } // Converted from char2date
}

/// <summary>
/// DTO for creating new BaoDuong
/// Contains only required fields for creation
/// </summary>
public class CreateBaoDuongDto
{
    [Required]
    public string KhoaLoaiXe { get; set; } = string.Empty;

    [Required]
    public string KhoaLoaiDichVu { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating BaoDuong status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateBaoDuongStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoDuong search operations
/// Used for advanced search and filtering
/// </summary>
public class BaoDuongSearchDto
{
    public string? KhoaLoaiXe { get; set; }
    public string? KhoaLoaiDichVu { get; set; }
    public string? DienGiai { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
    public string? NgayCapNhatFrom { get; set; }
    public string? NgayCapNhatTo { get; set; }
}

/// <summary>
/// DTO for BaoDuong dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class BaoDuongLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoDuong validation operations
/// Used for duplicate checking and validation
/// </summary>
public class BaoDuongValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
    public bool Exists { get; set; } = false;
}

/// <summary>
/// DTO for maintenance service details (from DM_DMBaoDuongChiTiet)
/// Used by GetDetailsDMBD and GetDMBD_BaoGia methods
/// </summary>
public class BaoDuongChiTietDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaDMBD { get; set; } = string.Empty;
    public int STT { get; set; } = 0;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public string KhoaHangMuc { get; set; } = string.Empty;
    public int Loai { get; set; } = 0; // 1 = Labor, 2 = Parts
    public string NoiDung { get; set; } = string.Empty;
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string KhoaDonViTinh { get; set; } = string.Empty;
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    
    // Joined fields
    public string MaHangHoa { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty;
    public string KhoanMucSuaChua { get; set; } = string.Empty;
}

/// <summary>
/// DTO for automotive maintenance categories
/// Specialized for automotive maintenance classification
/// </summary>
public class MaintenanceCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public bool IsPreventiveMaintenance { get; set; } = false;
    public bool IsCorrectiveMaintenance { get; set; } = false;
    public bool IsPeriodicMaintenance { get; set; } = false;
    public bool IsEmergencyMaintenance { get; set; } = false;
    public int TotalItems { get; set; } = 0;
    public decimal TotalCost { get; set; } = 0;
}

/// <summary>
/// DTO for maintenance service with details
/// Used for comprehensive maintenance service display
/// </summary>
public class BaoDuongWithDetailsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    public string NhanVienCapNhat { get; set; } = string.Empty;
    public DateTime? NgayCapNhat { get; set; }
    public List<BaoDuongChiTietDto> ChiTiet { get; set; } = new();
    public int TotalItems { get; set; } = 0;
    public decimal TotalCost { get; set; } = 0;
}

/// <summary>
/// DTO for maintenance service statistics
/// Used for reporting and analytics
/// </summary>
public class BaoDuongStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string LoaiDichVu { get; set; } = string.Empty;
    public int TotalMaintenanceItems { get; set; } = 0;
    public int TotalLaborItems { get; set; } = 0;
    public int TotalPartsItems { get; set; } = 0;
    public decimal TotalMaintenanceCost { get; set; } = 0;
    public decimal TotalLaborCost { get; set; } = 0;
    public decimal TotalPartsCost { get; set; } = 0;
    public DateTime LastUpdated { get; set; }
    public int UsageCount { get; set; } = 0;
}

/// <summary>
/// DTO for maintenance service print data
/// Used by GetDataPrintYeuCau method for printing maintenance requests
/// </summary>
public class BaoDuongPrintDto
{
    public string SoChungTu { get; set; } = string.Empty;
    public DateTime? NgayChungTu { get; set; }
    public string DichVu { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public string DiaChiKhach { get; set; } = string.Empty;
    public string DienThoaiKhach { get; set; } = string.Empty;
    public string MaSoThue { get; set; } = string.Empty;
    public string Fax { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string MauSon { get; set; } = string.Empty;
    public string SoKhung { get; set; } = string.Empty;
    public string SoMay { get; set; } = string.Empty;
    public string MaVin { get; set; } = string.Empty;
    public int SoKm { get; set; } = 0;
    public string DoiXe { get; set; } = string.Empty;
    public string GioVaoXuong { get; set; } = string.Empty;
    public DateTime? NgayVaoXuong { get; set; }
    public string GioXuatXuong { get; set; } = string.Empty;
    public DateTime? NgayXuatXuong { get; set; }
    public string KhachHangYeuCau { get; set; } = string.Empty;
    public string GioDuKienHoanThanh { get; set; } = string.Empty;
    public DateTime? NgayDuKienHoanThanh { get; set; }
    public bool KhachChoNhanXe { get; set; } = false;
    public bool KhachLayPhuTung { get; set; } = false;
    public bool LienHeQuaDienThoai { get; set; } = false;
    public bool LienHeQuaThu { get; set; } = false;
    public string TenTaiXe { get; set; } = string.Empty;
    public string DienThoaiTaiXe { get; set; } = string.Empty;
    public string CoVanDichVu { get; set; } = string.Empty;
    public string PhanSuaChua { get; set; } = string.Empty;
    public string NoiDung { get; set; } = string.Empty;
    public string MaPhuTung { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty;
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public decimal TyleChietKhau { get; set; } = 0;
    public decimal TienChietKhau { get; set; } = 0;
    public decimal TyLeThue { get; set; } = 0;
    public decimal TienThue { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
}
