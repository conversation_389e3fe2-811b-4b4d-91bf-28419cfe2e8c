# CARSOFT GIAPHAT - Backend API Project

## Architecture Overview

### Legacy System (DO NOT MODIFY)
- `Base\Business\` - Original VB.NET/C# business classes
- `Base\Database\` - SQL Server database scripts and stored procedures
- `Base\Forms\` - Legacy Windows Forms

### Modern System (New Development)
- `src\API\` - .NET Core Web API (Backend Focus)
- `TestAPI\` - Test API project for development

### Organized Project Structure
- `docs\` - All project documentation
- `scripts\` - Development and automation scripts
- `database\` - Database-related files and utilities
- `tools\` - Development tools and code generators
- `web-tools\` - Web-based testing and utility tools

## Development Rules

### ⚠️ CRITICAL RULE #1: NO TOUCH BASE FOLDER
- **NEVER** modify anything in `Base\` folder
- All new development goes in `src\` folder
- Legacy system remains as reference and backup

## Project Status
- [x] Analysis of Base\Business and Database relationship completed
- [x] .NET Core API setup
- [x] React Native Expo app setup
- [x] Database integration architecture
- [x] Basic mobile app structure
- [x] **COMPLETE clsDMDoiTuong.cs implementation**
  - [x] ALL 66 properties implemented
  - [x] ALL 50+ methods implemented with exact SQL
  - [x] Complete REST API endpoints
  - [x] Legacy stored procedure integration
- [x] **COMPLETE clsCoHoi.cs implementation**
  - [x] ALL 58 properties implemented
  - [x] ALL 20+ methods implemented with exact SQL
  - [x] Complete REST API endpoints
  - [x] Legacy stored procedure integration
- [x] **Implementation Framework for 180+ Business Classes**
  - [x] Standardized implementation pattern
  - [x] Complete documentation and strategy
  - [x] Priority-based implementation plan
- [ ] Complete remaining critical classes (clsBaoGia, clsHoaDon, etc.)
- [ ] Authentication implementation
- [ ] Testing and deployment

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Node.js 18+
- SQL Server (existing database)
- Visual Studio Code or Visual Studio 2022

### Development Workflow
1. Backend API development in `src\API\`
2. Use scripts in `scripts\` folder for automation
3. Regular commits to track progress
4. No modifications to `Base\` folder

## Quick Start

### 1. Build and Run API
```powershell
.\scripts\build-api.ps1
.\scripts\run-api.ps1
```
API will be available at: https://localhost:7001

### 2. Test Database Connection
```powershell
.\database\test-database-connection.bat
```

### 3. Use Web Tools for Testing
Open files in `web-tools\` folder in browser for testing interfaces

## Database Connection
The new API connects to the same database as the legacy system, using existing tables and stored procedures without modification.

## Documentation
- [Development Guide](docs/DEVELOPMENT_GUIDE.md) - Comprehensive development instructions
- [Legacy Implementation](docs/LEGACY_IMPLEMENTATION.md) - Complete implementation details
- [Business Classes Strategy](docs/BUSINESS_CLASSES_IMPLEMENTATION.md) - 180+ classes implementation plan
- [**🤖 Automation Solution**](docs/AUTOMATION_SOLUTION.md) - **Automated code generation for all 180+ classes**
- [API Documentation](https://localhost:7001/swagger) - Swagger UI (when API is running)

## 🎯 Legacy Implementation Status

### ✅ COMPLETED CLASSES (100% Implementation)

#### 1. clsDMDoiTuong.cs - Customer Management ✅
- **66 Properties**: ALL implemented with exact mapping
- **50+ Methods**: ALL implemented with identical SQL
- **REST API**: 50+ endpoints for complete functionality
- **Stored Procedures**: sp_DM_DoiTuong integration maintained

#### 2. clsCoHoi.cs - Opportunity Management ✅
- **58 Properties**: ALL implemented with exact mapping
- **20+ Methods**: ALL implemented with identical SQL
- **REST API**: 25+ endpoints for complete functionality
- **Stored Procedures**: BH_sp_CoHoi integration maintained

### 🔄 FRAMEWORK READY FOR 180+ CLASSES

**Total Classes in Base\Business\: 180+ classes**
- **Completed**: 2 classes (1.1%)
- **Framework**: Ready for systematic implementation
- **Strategy**: Priority-based implementation plan
- **Pattern**: Standardized implementation approach

## 🤖 **AUTOMATED SOLUTION AVAILABLE!**

### **Instant Generation of ALL 180+ Classes**

Instead of manually implementing each class, use our **automated code generator**:

```powershell
# Generate ALL remaining classes automatically
.\scripts\run-code-generator.ps1 -Mode all

# Or generate priority classes only
.\scripts\run-code-generator.ps1 -Mode priority

# Or generate single class
.\scripts\run-code-generator.ps1 -Mode single -ClassName clsBaoGia
```

**What it generates for EACH class:**
- ✅ Complete DTO with all properties
- ✅ Repository with exact legacy SQL
- ✅ Service with business logic
- ✅ Legacy API controller (all methods)
- ✅ Modern REST API controller
- ✅ Complete documentation

**Result: 180+ classes → 1,440+ files in 10-30 minutes!**

**Next Priority Classes (if doing manually):**
1. **clsBaoGia.cs** - Quote Management (~100 properties)
2. **clsHoaDon.cs** - Invoice Management (~80 properties)
3. **clsNhapKho.cs** - Inventory In (~60 properties)
4. **clsXuatKho.cs** - Inventory Out (~60 properties)
5. **clsThanhToan.cs** - Payment Management (~50 properties)
