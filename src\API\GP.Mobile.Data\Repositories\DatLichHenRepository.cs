using Dapper;
using GP.Mobile.Models.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Appointment Scheduling (Đặt Lịch Hẹn)
    /// Implements ALL methods from clsDatLichHen.cs (851 lines)
    /// Maps to SC_DatLichHen table with 24 properties
    /// Manual implementation - Maintains 100% Legacy Compatibility
    /// </summary>
    public class DatLichHenRepository : IDatLichHenRepository
    {
        private readonly IDbConnection _connection;
        private readonly ILogger<DatLichHenRepository> _logger;

        public DatLichHenRepository(IDbConnection connection, ILogger<DatLichHenRepository> logger)
        {
            _connection = connection;
            _logger = logger;
        }

        #region Core Legacy Methods Implementation

        public async Task<DatLichHenDto?> LoadAsync(string khoa)
        {
            try
            {
                // Exact SQL from legacy Load method (line 407)
                string commandText = "SELECT * FROM [SC_DatLichHen] WHERE Khoa = @Khoa";
                return await _connection.QueryFirstOrDefaultAsync<DatLichHenDto>(commandText, new { Khoa = khoa });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading DatLichHen with Khoa: {Khoa}", khoa);
                return null;
            }
        }

        public async Task<DatLichHenDto?> LoadByBaoGiaAsync(string khoaBaoGia)
        {
            try
            {
                // Exact SQL from legacy LoadBaoGia method (line 459)
                string commandText = "SELECT * FROM [SC_DatLichHen] WHERE KhoaBaoGia = @KhoaBaoGia";
                return await _connection.QueryFirstOrDefaultAsync<DatLichHenDto>(commandText, new { KhoaBaoGia = khoaBaoGia });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading DatLichHen by BaoGia: {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        public async Task<bool> SaveAsync(DatLichHenDto appointment)
        {
            try
            {
                // Legacy Save method uses stored procedure with 24 parameters (line 512)
                var parameters = new DynamicParameters();
                parameters.Add("@Khoa", appointment.Khoa);
                parameters.Add("@NgayChungTu", appointment.NgayChungTu);
                parameters.Add("@SoChungTu", appointment.SoChungTu);
                parameters.Add("@KhoaXe", appointment.KhoaXe);
                parameters.Add("@KhoaKhachHang", appointment.KhoaKhachHang);
                parameters.Add("@KhoaLoaiXe", appointment.KhoaLoaiXe);
                parameters.Add("@GioDatHen", appointment.GioDatHen);
                parameters.Add("@NgayDatHen", appointment.NgayDatHen);
                parameters.Add("@NgayVaoThucTe", appointment.NgayVaoThucTe);
                parameters.Add("@YeuCauKhachHang", appointment.YeuCauKhachHang);
                parameters.Add("@KhoaLoaiDichVu", appointment.KhoaLoaiDichVu);
                parameters.Add("@IsGoiLaiNhacNho", appointment.IsGoiLaiNhacNho);
                parameters.Add("@NgayGoiLai", appointment.NgayGoiLai);
                parameters.Add("@KhoaNhanVienTao", appointment.KhoaNhanVienTao);
                parameters.Add("@NgayTao", appointment.NgayTao);
                parameters.Add("@KhoaNhanVienCapNhat", appointment.KhoaNhanVienCapNhat);
                parameters.Add("@NgayCapNhat", appointment.NgayCapNhat);
                parameters.Add("@KhoaBaoGia", appointment.KhoaBaoGia);
                parameters.Add("@KhoaHangBaoHiem", appointment.KhoaHangBaoHiem);
                parameters.Add("@KhoaDonVi", appointment.KhoaDonVi);
                parameters.Add("@IsDaGoiNhacHen", appointment.IsDaGoiNhacHen);
                parameters.Add("@NoiDungGoiNhacHen", appointment.NoiDungGoiNhacHen);
                parameters.Add("@LoaiDichVu", appointment.LoaiDichVu);
                parameters.Add("@KhoaCoVan", appointment.KhoaCoVan);

                int result = await _connection.ExecuteAsync("sp_SC_DatLichHen", 
                    parameters, commandType: CommandType.StoredProcedure);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving DatLichHen with Khoa: {Khoa}", appointment.Khoa);
                return false;
            }
        }

        public async Task<bool> DeleteAsync(string khoa)
        {
            try
            {
                // Exact SQL from legacy DelData method (line 762)
                string commandText = "DELETE FROM SC_DatLichHen WHERE Khoa = @Khoa";
                int result = await _connection.ExecuteAsync(commandText, new { Khoa = khoa });
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting DatLichHen with Khoa: {Khoa}", khoa);
                return false;
            }
        }

        #endregion

        #region Legacy List Methods Implementation

        public async Task<DataTable> GetListNhacHenAsync(string condition = "")
        {
            try
            {
                // Exact SQL from legacy GetListNhacHen method (line 553)
                string commandText = $@"
                    SELECT LH.Khoa, LH.SoChungTu, dbo.Char2Date(LH.NgayChungTu) As NgayLap, 
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) As DatHen,
                           BG.GioVaoXuong + ' ' + dbo.Char2Date(BG.NgayVaoXuong) As NgayVaoXuong,  
                           (CASE WHEN LH.NgayGoiLai = '' THEN 'False' ELSE 'True' END) As NhacHen, 
                           dbo.Char2Date(LH.NgayGoiLai) As NgayNhacHen, LH.NgayGoiLai,  
                           CV.TenViet As CoVan, X.SoXe, LX.TenViet As LoaiXe, HX.TenViet As HangXe, 
                           X.SoSuon, X.SoMay, X.MauSon, LDV.TenViet As LoaiDichVu, 
                           BH.TenViet As BaoHiem, LH.YeuCauKhachHang, 
                           IsNull(LH.IsDaGoiNhacHen,'False') As IsDaGoiNhacHen, 
                           LH.NoiDungGoiNhacHen, LH.LoaiDichVu As LoaiLichHen  
                    FROM SC_DatLichHen LH  
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe  
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = X.KhoaLoaiXe 
                    LEFT JOIN DM_HangSanXuat HX ON HX.Khoa = LX.KhoaHangSanXuat 
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu  
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan  
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem 
                    LEFT JOIN SC_BaoGia BG ON BG.Khoa = LH.KhoaBaoGia 
                    WHERE LH.KhoaDonVi = '{GetCurrentClientId()}' {condition} 
                          AND IsNull(LH.KhoaBaoGia,'') = ''
                    ORDER BY NgayDatHen DESC";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder list");
                return new DataTable();
            }
        }

        public async Task<DataTable> GetListNhacHenTruoc1NgayAsync(string condition = "")
        {
            try
            {
                // Exact SQL from legacy GetListNhacHenTruoc1Ngay method (line 578)
                string commandText = $@"
                    SELECT LH.Khoa, LH.SoChungTu, dbo.Char2Date(LH.NgayChungTu) As NgayLap, 
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) As DatHen,
                           BG.GioVaoXuong + ' ' + dbo.Char2Date(BG.NgayVaoXuong) As NgayVaoXuong,  
                           (CASE WHEN LH.NgayGoiLai = '' THEN 'False' ELSE 'True' END) As NhacHen, 
                           dbo.Char2Date(LH.NgayGoiLai) As NgayNhacHen,  
                           X.SoXe, LX.TenViet As LoaiXe, HX.TenViet As HangXe, 
                           X.SoSuon, X.SoMay, X.MauSon, LDV.TenViet As LoaiDichVu, 
                           BH.TenViet As BaoHiem, KH.TenViet As KhachHang, KH.DienThoai, 
                           LH.YeuCauKhachHang, IsNull(LH.IsDaGoiNhacHen,'False') As IsDaGoiNhacHen, 
                           LH.NoiDungGoiNhacHen  
                    FROM SC_DatLichHen LH  
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe  
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = X.KhoaLoaiXe 
                    LEFT JOIN DM_HangSanXuat HX ON HX.Khoa = LX.KhoaHangSanXuat 
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu  
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem 
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang 
                    LEFT JOIN SC_BaoGia BG ON BG.Khoa = LH.KhoaBaoGia 
                    WHERE LH.KhoaDonVi = '{GetCurrentClientId()}' {condition} 
                          AND IsNull(LH.KhoaBaoGia,'') = '' 
                    ORDER BY NgayDatHen DESC";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting 1-day reminder list");
                return new DataTable();
            }
        }

        public async Task<DataTable> GetListAsync(string condition = "")
        {
            try
            {
                // Exact SQL from legacy GetList method (line 602)
                string commandText = $@"
                    SELECT DISTINCT BG.Khoa, XE.SoXe, ISNULL(KH.TenViet,'') AS KhachHang,
                           dbo.char2date(BG.NgayDatHen) AS NgayDat,
                           dbo.char2date(B.NgayVaoXuong) AS NgayVao, BG.KhoaBaoGia
                    FROM SC_DatLichHen BG
                    LEFT JOIN DM_XE XE ON XE.Khoa = BG.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = BG.KhoaKhachHang
                    LEFT JOIN SC_BaoGia B ON B.Khoa = BG.KhoaBaoGia
                    WHERE 1 = 1 AND BG.KhoaDonVi = '{GetCurrentClientId()}' {condition}
                    ORDER BY NgayDat DESC";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment list");
                return new DataTable();
            }
        }

        public async Task<DataTable> GetListBangKeDatLichHenAsync(string condition = "")
        {
            try
            {
                // Exact SQL from legacy GetListBangKeDatLichHen method (line 626)
                string commandText = $@"
                    SELECT XE.SoXe, LX.TenViet As LoaiXe, ISNULL(KH.TenViet,'') AS KhachHang,
                           LDV.TenViet As LoaiDichVu, BH.TenViet As BaoHiem,
                           BG.GioDatHen + ' ' + dbo.char2date(BG.NgayDatHen) AS NgayDatHen,
                           B.SoChungTu As BaoGiaSo,
                           B.GioVaoXuong + ' ' + dbo.char2date(B.NgayVaoXuong) AS NgayVao,
                           BG.YeuCauKhachHang, BG.KhoaBaoGia
                    FROM SC_DatLichHen BG
                    LEFT JOIN DM_XE XE ON XE.Khoa = BG.KhoaXe
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = BG.KhoaLoaiXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = BG.KhoaKhachHang
                    LEFT JOIN SC_BaoGia B ON B.Khoa = BG.KhoaBaoGia
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = BG.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = BG.KhoaHangBaoHiem
                    WHERE 1 = 1 AND BG.KhoaDonVi = '{GetCurrentClientId()}' {condition}
                    ORDER BY BG.NgayDatHen DESC";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting detailed appointment report");
                return new DataTable();
            }
        }

        public async Task<DataTable> ShowListAsync(string condition = "")
        {
            try
            {
                // Exact SQL from legacy ShowList method (line 656)
                string whereClause = !string.IsNullOrEmpty(condition) ? $" AND {condition}" : "";
                string commandText = $@"
                    SELECT Khoa, RTRIM(Ma) AS Ma, RTRIM(TenViet) AS Ten
                    FROM SC_DatLichHen
                    WHERE Active = 1 AND KhoaDonVi = '{GetCurrentClientId()}' {whereClause}
                    ORDER BY Ma";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing appointment list");
                return new DataTable();
            }
        }

        public async Task<DataTable> ShowAllListAsync()
        {
            try
            {
                // Exact SQL from legacy ShowAllList method (line 683)
                string commandText = $@"
                    SELECT Khoa, RTRIM(Ma) AS Ma, RTRIM(TenViet) AS Ten
                    FROM SC_DatLichHen
                    WHERE KhoaDonVi = '{GetCurrentClientId()}'
                    ORDER BY Ma";

                var result = await _connection.QueryAsync(commandText);
                return ConvertToDataTable(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing all appointments");
                return new DataTable();
            }
        }

        public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
        {
            try
            {
                // Exact SQL from legacy SearchByCode method (line 722)
                string codeClause = !string.IsNullOrEmpty(code) ? $" AND RTRIM(Ma) = '{code.Trim()}'" : "";
                string conditionClause = !string.IsNullOrEmpty(condition) ? $" AND {condition}" : "";

                string commandText = $@"
                    SELECT Khoa, Ma, TenViet AS Ten
                    FROM SC_DatLichHen
                    WHERE Active = 1 AND KhoaDonVi = '{GetCurrentClientId()}' {codeClause} {conditionClause}";

                var result = await _connection.QueryFirstOrDefaultAsync(commandText);
                if (result != null)
                {
                    return $"{result.Khoa}|{result.Ma}|{result.Ten}";
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching appointment by code: {Code}", code);
                return string.Empty;
            }
        }

        #endregion

        #region Modern API Methods Implementation

        public async Task<List<DatLichHenDto>> GetTodayAppointmentsAsync(string donViId)
        {
            try
            {
                string today = DateTime.Now.ToString("yyyyMMdd");
                string commandText = $@"
                    SELECT LH.*, X.SoXe, KH.TenViet AS TenKhachHang, KH.DienThoai,
                           LX.TenViet AS TenLoaiXe, LDV.TenViet AS TenLoaiDichVu,
                           BH.TenViet AS TenBaoHiem, CV.TenViet AS TenCoVan,
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) AS NgayGioDatHen
                    FROM SC_DatLichHen LH
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = LH.KhoaLoaiXe
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan
                    WHERE LH.NgayDatHen = @Today AND LH.KhoaDonVi = @DonViId
                          AND ISNULL(LH.KhoaBaoGia, '') = ''
                    ORDER BY LH.GioDatHen";

                return (await _connection.QueryAsync<DatLichHenDto>(commandText,
                    new { Today = today, DonViId = donViId })).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's appointments");
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetUpcomingAppointmentsAsync(string donViId)
        {
            try
            {
                string today = DateTime.Now.ToString("yyyyMMdd");
                string nextWeek = DateTime.Now.AddDays(7).ToString("yyyyMMdd");

                string commandText = $@"
                    SELECT LH.*, X.SoXe, KH.TenViet AS TenKhachHang, KH.DienThoai,
                           LX.TenViet AS TenLoaiXe, LDV.TenViet AS TenLoaiDichVu,
                           BH.TenViet AS TenBaoHiem, CV.TenViet AS TenCoVan,
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) AS NgayGioDatHen
                    FROM SC_DatLichHen LH
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = LH.KhoaLoaiXe
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan
                    WHERE LH.NgayDatHen BETWEEN @Today AND @NextWeek
                          AND LH.KhoaDonVi = @DonViId
                          AND ISNULL(LH.KhoaBaoGia, '') = ''
                    ORDER BY LH.NgayDatHen, LH.GioDatHen";

                return (await _connection.QueryAsync<DatLichHenDto>(commandText,
                    new { Today = today, NextWeek = nextWeek, DonViId = donViId })).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming appointments");
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByDateRangeAsync(string fromDate, string toDate, string donViId)
        {
            try
            {
                string commandText = $@"
                    SELECT LH.*, X.SoXe, KH.TenViet AS TenKhachHang, KH.DienThoai,
                           LX.TenViet AS TenLoaiXe, LDV.TenViet AS TenLoaiDichVu,
                           BH.TenViet AS TenBaoHiem, CV.TenViet AS TenCoVan,
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) AS NgayGioDatHen
                    FROM SC_DatLichHen LH
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = LH.KhoaLoaiXe
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan
                    WHERE LH.NgayDatHen BETWEEN @FromDate AND @ToDate
                          AND LH.KhoaDonVi = @DonViId
                    ORDER BY LH.NgayDatHen, LH.GioDatHen";

                return (await _connection.QueryAsync<DatLichHenDto>(commandText,
                    new { FromDate = fromDate, ToDate = toDate, DonViId = donViId })).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by date range");
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByVehicleAsync(string khoaXe)
        {
            try
            {
                string commandText = $@"
                    SELECT LH.*, X.SoXe, KH.TenViet AS TenKhachHang, KH.DienThoai,
                           LX.TenViet AS TenLoaiXe, LDV.TenViet AS TenLoaiDichVu,
                           BH.TenViet AS TenBaoHiem, CV.TenViet AS TenCoVan,
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) AS NgayGioDatHen
                    FROM SC_DatLichHen LH
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = LH.KhoaLoaiXe
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan
                    WHERE LH.KhoaXe = @KhoaXe
                    ORDER BY LH.NgayDatHen DESC, LH.GioDatHen DESC";

                return (await _connection.QueryAsync<DatLichHenDto>(commandText,
                    new { KhoaXe = khoaXe })).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by vehicle: {KhoaXe}", khoaXe);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByCustomerAsync(string khoaKhachHang)
        {
            try
            {
                string commandText = $@"
                    SELECT LH.*, X.SoXe, KH.TenViet AS TenKhachHang, KH.DienThoai,
                           LX.TenViet AS TenLoaiXe, LDV.TenViet AS TenLoaiDichVu,
                           BH.TenViet AS TenBaoHiem, CV.TenViet AS TenCoVan,
                           LH.GioDatHen + ' ' + dbo.Char2Date(LH.NgayDatHen) AS NgayGioDatHen
                    FROM SC_DatLichHen LH
                    LEFT JOIN DM_Xe X ON X.Khoa = LH.KhoaXe
                    LEFT JOIN DM_DoiTuong KH ON KH.Khoa = LH.KhoaKhachHang
                    LEFT JOIN DM_LoaiXe LX ON LX.Khoa = LH.KhoaLoaiXe
                    LEFT JOIN DM_LoaiDichVu LDV ON LDV.Khoa = LH.KhoaLoaiDichVu
                    LEFT JOIN DM_DoiTuong BH ON BH.Khoa = LH.KhoaHangBaoHiem
                    LEFT JOIN DM_DoiTuong CV ON CV.Khoa = LH.KhoaCoVan
                    WHERE LH.KhoaKhachHang = @KhoaKhachHang
                    ORDER BY LH.NgayDatHen DESC, LH.GioDatHen DESC";

                return (await _connection.QueryAsync<DatLichHenDto>(commandText,
                    new { KhoaKhachHang = khoaKhachHang })).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by customer: {KhoaKhachHang}", khoaKhachHang);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<bool> UpdateReminderCallStatusAsync(string khoa, string noiDungGoiNhacHen)
        {
            try
            {
                string commandText = @"
                    UPDATE SC_DatLichHen
                    SET IsDaGoiNhacHen = 1,
                        NoiDungGoiNhacHen = @NoiDungGoiNhacHen,
                        NgayCapNhat = @NgayCapNhat
                    WHERE Khoa = @Khoa";

                int result = await _connection.ExecuteAsync(commandText, new
                {
                    Khoa = khoa,
                    NoiDungGoiNhacHen = noiDungGoiNhacHen,
                    NgayCapNhat = DateTime.Now.ToString("yyyyMMdd")
                });
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating reminder call status: {Khoa}", khoa);
                return false;
            }
        }

        public async Task<bool> CheckAppointmentConflictAsync(string ngayDatHen, string gioDatHen, string donViId, string excludeKhoa = "")
        {
            try
            {
                string excludeClause = !string.IsNullOrEmpty(excludeKhoa) ? " AND Khoa <> @ExcludeKhoa" : "";
                string commandText = $@"
                    SELECT COUNT(*)
                    FROM SC_DatLichHen
                    WHERE NgayDatHen = @NgayDatHen
                          AND GioDatHen = @GioDatHen
                          AND KhoaDonVi = @DonViId
                          AND ISNULL(KhoaBaoGia, '') = '' {excludeClause}";

                int count = await _connection.QuerySingleAsync<int>(commandText, new
                {
                    NgayDatHen = ngayDatHen,
                    GioDatHen = gioDatHen,
                    DonViId = donViId,
                    ExcludeKhoa = excludeKhoa
                });
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking appointment conflict");
                return false;
            }
        }

        #endregion

        #region Helper Methods

        private string GetCurrentClientId()
        {
            // TODO: Implement client ID retrieval from context
            return "0000000001"; // Default client ID
        }

        private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
        {
            var dataTable = new DataTable();
            if (data.Any())
            {
                var firstRow = data.First() as IDictionary<string, object>;
                if (firstRow != null)
                {
                    foreach (var key in firstRow.Keys)
                    {
                        dataTable.Columns.Add(key);
                    }

                    foreach (var row in data)
                    {
                        var dataRow = dataTable.NewRow();
                        var rowDict = row as IDictionary<string, object>;
                        if (rowDict != null)
                        {
                            foreach (var key in rowDict.Keys)
                            {
                                dataRow[key] = rowDict[key] ?? DBNull.Value;
                            }
                        }
                        dataTable.Rows.Add(dataRow);
                    }
                }
            }
            return dataTable;
        }

        #endregion
    }
}
