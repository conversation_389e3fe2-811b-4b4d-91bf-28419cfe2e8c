using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for DonViTinh (Unit of Measure) entity
/// Maps exactly to DM_DonViTinh table in legacy database
/// Implements ALL properties from clsDMDonViTinh.cs (504 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class DonViTinhDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of unit of measure
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of unit of measure
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(100)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Last update date (YYYYMMDD format)
    /// Maps to: mNgayCapNhat property in legacy class
    /// </summary>
    public string NgayCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Synchronization status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for DonViTinh list display
/// Optimized for list views and dropdowns
/// </summary>
public class DonViTinhListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for creating new DonViTinh
/// Contains only required fields for creation
/// </summary>
public class CreateDonViTinhDto
{
    [Required]
    [StringLength(100)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(100)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for updating DonViTinh status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateDonViTinhStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public int Active { get; set; }

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for DonViTinh search operations
/// Used for advanced search and filtering
/// </summary>
public class DonViTinhSearchDto
{
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public int? Active { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
    public string? NgayCapNhatFrom { get; set; }
    public string? NgayCapNhatTo { get; set; }
}

/// <summary>
/// DTO for DonViTinh dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class DonViTinhLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
}

/// <summary>
/// DTO for DonViTinh validation operations
/// Used for duplicate checking and validation
/// </summary>
public class DonViTinhValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}
