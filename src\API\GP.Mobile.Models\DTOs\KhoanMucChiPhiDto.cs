using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for KhoanMucChiPhi (Cost Categories) entity
/// Maps exactly to DM_KhoanMucChiPhi table in legacy database
/// Implements ALL properties from clsDMKhoanMucChiPhi.cs (425 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service cost management and expense categorization
/// </summary>
public class KhoanMucChiPhiDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Cost category code
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// From date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    [StringLength(8)]
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for KhoanMucChiPhi list display with joined data
/// Optimized for automotive cost category lists with employee info
/// Used by ShowList and ShowAllList methods
/// </summary>
public class KhoanMucChiPhiListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public DateTime? TuNgay { get; set; } // Converted from char2date
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    public string NhanVienCapNhat { get; set; } = string.Empty; // From employee lookup
    public int Active { get; set; } = 1;
    public bool IsLaborCost { get; set; } = false;
    public bool IsPartsCost { get; set; } = false;
    public bool IsOverheadCost { get; set; } = false;
    public bool IsServiceCost { get; set; } = false;
    public bool IsMaintenanceCost { get; set; } = false;
    public bool IsRepairCost { get; set; } = false;
    public bool IsWarrantyCost { get; set; } = false;
    public bool IsInsuranceCost { get; set; } = false;
    public string CostType { get; set; } = string.Empty;
    public string CostCategory { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; } = 0;
    public int UsageCount { get; set; } = 0;
}

/// <summary>
/// DTO for creating new KhoanMucChiPhi
/// Contains only required fields for creation
/// </summary>
public class CreateKhoanMucChiPhiDto
{
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public string TuNgay { get; set; } = string.Empty;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating KhoanMucChiPhi status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateKhoanMucChiPhiStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int Active { get; set; } = 1;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for KhoanMucChiPhi search operations
/// Used for advanced search and filtering
/// </summary>
public class KhoanMucChiPhiSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public int? Active { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
    public bool? IsLaborCost { get; set; }
    public bool? IsPartsCost { get; set; }
    public bool? IsOverheadCost { get; set; }
    public bool? IsServiceCost { get; set; }
    public bool? IsMaintenanceCost { get; set; }
    public bool? IsRepairCost { get; set; }
    public bool? IsWarrantyCost { get; set; }
    public bool? IsInsuranceCost { get; set; }
    public string? CostType { get; set; }
    public string? CostCategory { get; set; }
}

/// <summary>
/// DTO for KhoanMucChiPhi dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class KhoanMucChiPhiLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsLaborCost { get; set; } = false;
    public bool IsPartsCost { get; set; } = false;
    public bool IsServiceCost { get; set; } = false;
    public string CostType { get; set; } = string.Empty;
    public string CostCategory { get; set; } = string.Empty;
}

/// <summary>
/// DTO for KhoanMucChiPhi validation operations
/// Used for duplicate checking and validation
/// </summary>
public class KhoanMucChiPhiValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsDuplicateName { get; set; } = false;
    public bool IsUsedInExpenses { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for cost category search by code
/// Used by SearchByCode method
/// </summary>
public class KhoanMucChiPhiSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public bool Found { get; set; } = false;
}

/// <summary>
/// DTO for automotive cost categories
/// Specialized for automotive service cost classification
/// </summary>
public class AutomotiveCostCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsLaborCost { get; set; } = false;
    public bool IsPartsCost { get; set; } = false;
    public bool IsOverheadCost { get; set; } = false;
    public bool IsServiceCost { get; set; } = false;
    public bool IsMaintenanceCost { get; set; } = false;
    public bool IsRepairCost { get; set; } = false;
    public bool IsWarrantyCost { get; set; } = false;
    public bool IsInsuranceCost { get; set; } = false;
    public bool IsDiagnosticCost { get; set; } = false;
    public bool IsBodyworkCost { get; set; } = false;
    public bool IsPaintingCost { get; set; } = false;
    public bool IsElectricalCost { get; set; } = false;
    public bool IsEngineCost { get; set; } = false;
    public bool IsTransmissionCost { get; set; } = false;
    public bool IsBrakeCost { get; set; } = false;
    public bool IsSuspensionCost { get; set; } = false;
    public bool IsACCost { get; set; } = false;
    public bool IsTireCost { get; set; } = false;
    public bool IsOilChangeCost { get; set; } = false;
    public bool IsInspectionCost { get; set; } = false;
    public string CostType { get; set; } = string.Empty; // Direct, Indirect, Fixed, Variable
    public string CostCategory { get; set; } = string.Empty; // Labor, Material, Overhead, Service
    public decimal StandardRate { get; set; } = 0;
    public decimal MinimumRate { get; set; } = 0;
    public decimal MaximumRate { get; set; } = 0;
    public string BillingUnit { get; set; } = string.Empty; // Hour, Job, Piece, Flat Rate
    public bool IsWarrantyApplicable { get; set; } = false;
    public bool IsInsuranceClaimable { get; set; } = false;
    public bool RequiresApproval { get; set; } = false;
    public string ApprovalLevel { get; set; } = string.Empty;
}

/// <summary>
/// DTO for cost category with statistics
/// Used for comprehensive cost category display with usage information
/// </summary>
public class KhoanMucChiPhiWithStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public DateTime? TuNgay { get; set; }
    public int Active { get; set; } = 1;
    public int TotalTransactions { get; set; } = 0;
    public decimal TotalAmount { get; set; } = 0;
    public decimal AverageAmount { get; set; } = 0;
    public decimal MinAmount { get; set; } = 0;
    public decimal MaxAmount { get; set; } = 0;
    public DateTime? LastUsedDate { get; set; }
    public DateTime? LastUpdatedDate { get; set; }
    public int MonthlyUsage { get; set; } = 0;
    public int YearlyUsage { get; set; } = 0;
    public bool IsHighUsage { get; set; } = false;
    public bool IsLowUsage { get; set; } = false;
    public string UsageLevel { get; set; } = string.Empty; // High, Medium, Low, None
    public string TrendDirection { get; set; } = string.Empty; // Increasing, Decreasing, Stable
}

/// <summary>
/// DTO for cost category field-based queries
/// Used by ShowListByField method
/// </summary>
public class KhoanMucChiPhiFieldQueryDto
{
    public string FieldList { get; set; } = string.Empty; // Pipe-separated field list
    public string Conditions { get; set; } = string.Empty; // WHERE conditions
    public string OrderBy { get; set; } = string.Empty; // ORDER BY clause
}

/// <summary>
/// DTO for cost category reporting
/// Used for cost analysis and reporting
/// </summary>
public class CostCategoryReportDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string CostType { get; set; } = string.Empty;
    public string CostCategory { get; set; } = string.Empty;
    public decimal TotalCost { get; set; } = 0;
    public decimal BudgetAmount { get; set; } = 0;
    public decimal VarianceAmount { get; set; } = 0;
    public decimal VariancePercentage { get; set; } = 0;
    public int TransactionCount { get; set; } = 0;
    public DateTime? PeriodStart { get; set; }
    public DateTime? PeriodEnd { get; set; }
    public string Department { get; set; } = string.Empty;
    public string CostCenter { get; set; } = string.Empty;
    public bool IsOverBudget { get; set; } = false;
    public bool RequiresAttention { get; set; } = false;
}
