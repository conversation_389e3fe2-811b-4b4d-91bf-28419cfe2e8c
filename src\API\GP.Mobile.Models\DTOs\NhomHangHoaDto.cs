using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for NhomHangHoa (Product Groups) entity
/// Maps exactly to DM_NhomHangHoa table in legacy database
/// Implements ALL properties from clsDMNhomHangHoa.cs (781 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts categorization and product group management
/// </summary>
public class NhomHangHoaDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Industry/Sector key - Foreign key to DM_NganhHang
    /// Maps to: mKhoaNganh property in legacy class
    /// </summary>
    public string KhoaNganh { get; set; } = string.Empty;

    /// <summary>
    /// Product group code
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Product account key - Foreign key to DM_TaiKhoan
    /// Maps to: mKhoaTKHangHoa property in legacy class
    /// </summary>
    public string KhoaTKHangHoa { get; set; } = string.Empty;

    /// <summary>
    /// Cost account key - Foreign key to DM_TaiKhoan
    /// Maps to: mKhoaTKGiaVon property in legacy class
    /// </summary>
    public string KhoaTKGiaVon { get; set; } = string.Empty;

    /// <summary>
    /// Raw material flag (1 = Yes, 0 = No)
    /// Maps to: mNguyenLieu property in legacy class
    /// </summary>
    public int NguyenLieu { get; set; } = 0;

    /// <summary>
    /// Finished product flag (1 = Yes, 0 = No)
    /// Maps to: mThanhPham property in legacy class
    /// </summary>
    public int ThanhPham { get; set; } = 0;

    /// <summary>
    /// Menu item flag (1 = Yes, 0 = No)
    /// Maps to: mThucDon property in legacy class
    /// </summary>
    public int ThucDon { get; set; } = 0;

    /// <summary>
    /// From date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    [StringLength(8)]
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhap property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhap { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Send status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for NhomHangHoa list display with joined data
/// Optimized for automotive parts group lists with industry info
/// Used by ShowList and ShowAllListNhomHangHoa methods
/// </summary>
public class NhomHangHoaListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty; // From DM_NganhHang.TenViet
    public string KhoaNganh { get; set; } = string.Empty;
    public DateTime? TuNgay { get; set; } // Converted from char2date
    public string KhoaNhanVienCapNhap { get; set; } = string.Empty;
    public string NhanVienCapNhap { get; set; } = string.Empty; // From employee lookup
    public int NguyenLieu { get; set; } = 0;
    public int ThanhPham { get; set; } = 0;
    public int ThucDon { get; set; } = 0;
    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;
    public bool IsAutomotiveGroup { get; set; } = false;
    public bool IsPartsGroup { get; set; } = false;
    public bool IsServiceGroup { get; set; } = false;
    public int TotalProducts { get; set; } = 0;
}

/// <summary>
/// DTO for creating new NhomHangHoa
/// Contains only required fields for creation
/// </summary>
public class CreateNhomHangHoaDto
{
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaNganh { get; set; } = string.Empty;
    public string KhoaTKHangHoa { get; set; } = string.Empty;
    public string KhoaTKGiaVon { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int NguyenLieu { get; set; } = 0;
    public int ThanhPham { get; set; } = 0;
    public int ThucDon { get; set; } = 0;
}

/// <summary>
/// DTO for updating NhomHangHoa status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateNhomHangHoaStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;
    public string KhoaNhanVienCapNhap { get; set; } = string.Empty;
}

/// <summary>
/// DTO for NhomHangHoa search operations
/// Used for advanced search and filtering
/// </summary>
public class NhomHangHoaSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public string? KhoaNganh { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public int? Active { get; set; }
    public int? Send { get; set; }
    public int? NguyenLieu { get; set; }
    public int? ThanhPham { get; set; }
    public int? ThucDon { get; set; }
    public bool? IsAutomotiveGroup { get; set; }
    public bool? IsPartsGroup { get; set; }
    public bool? IsServiceGroup { get; set; }
}

/// <summary>
/// DTO for NhomHangHoa dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class NhomHangHoaLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsAutomotiveGroup { get; set; } = false;
    public bool IsPartsGroup { get; set; } = false;
    public int TotalProducts { get; set; } = 0;
}

/// <summary>
/// DTO for NhomHangHoa validation operations
/// Used for duplicate checking and validation
/// </summary>
public class NhomHangHoaValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsDuplicateName { get; set; } = false;
    public bool IsUsedInProducts { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for product group search by code
/// Used by SearchByCode method
/// </summary>
public class NhomHangHoaSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public bool Found { get; set; } = false;
}

/// <summary>
/// DTO for automotive parts group categories
/// Specialized for automotive parts group classification
/// </summary>
public class AutomotivePartsGroupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty;
    public bool IsEnginePartsGroup { get; set; } = false;
    public bool IsBodyPartsGroup { get; set; } = false;
    public bool IsElectricalPartsGroup { get; set; } = false;
    public bool IsBrakePartsGroup { get; set; } = false;
    public bool IsSuspensionPartsGroup { get; set; } = false;
    public bool IsTransmissionPartsGroup { get; set; } = false;
    public bool IsExhaustPartsGroup { get; set; } = false;
    public bool IsInteriorPartsGroup { get; set; } = false;
    public bool IsExteriorPartsGroup { get; set; } = false;
    public bool IsMaintenancePartsGroup { get; set; } = false;
    public bool IsOilFluidGroup { get; set; } = false;
    public bool IsFilterGroup { get; set; } = false;
    public bool IsTireGroup { get; set; } = false;
    public bool IsAccessoryGroup { get; set; } = false;
    public bool IsToolGroup { get; set; } = false;
    public int TotalProducts { get; set; } = 0;
    public decimal TotalValue { get; set; } = 0;
    public string PreferredSuppliers { get; set; } = string.Empty;
    public string SpecialRequirements { get; set; } = string.Empty;
}

/// <summary>
/// DTO for product group with statistics
/// Used for comprehensive group display with product information
/// </summary>
public class NhomHangHoaWithStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty;
    public DateTime? TuNgay { get; set; }
    public int Active { get; set; } = 1;
    public int TotalProducts { get; set; } = 0;
    public int ActiveProducts { get; set; } = 0;
    public int InactiveProducts { get; set; } = 0;
    public decimal TotalInventoryValue { get; set; } = 0;
    public decimal AverageProductPrice { get; set; } = 0;
    public DateTime? LastProductAdded { get; set; }
    public DateTime? LastProductUpdated { get; set; }
    public bool HasLowStockProducts { get; set; } = false;
    public bool HasExpiredProducts { get; set; } = false;
    public string TopSellingProduct { get; set; } = string.Empty;
    public string ActivityLevel { get; set; } = string.Empty; // High, Medium, Low
}

/// <summary>
/// DTO for product group field-based queries
/// Used by ShowListByField method
/// </summary>
public class NhomHangHoaFieldQueryDto
{
    public string FieldList { get; set; } = string.Empty; // Pipe-separated field list
    public string Conditions { get; set; } = string.Empty; // WHERE conditions
    public string OrderBy { get; set; } = string.Empty; // ORDER BY clause
}

/// <summary>
/// DTO for raw material groups
/// Used by ShowAllListNguyenLieu method
/// </summary>
public class RawMaterialGroupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty;
    public bool IsRawMaterial { get; set; } = true;
    public bool IsFinishedProduct { get; set; } = false;
    public int TotalRawMaterials { get; set; } = 0;
    public decimal TotalRawMaterialValue { get; set; } = 0;
    public string PreferredSuppliers { get; set; } = string.Empty;
    public string QualityRequirements { get; set; } = string.Empty;
}

/// <summary>
/// DTO for menu groups
/// Used by ShowAllListThucDon method
/// </summary>
public class MenuGroupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string NganhHang { get; set; } = string.Empty;
    public bool IsMenuGroup { get; set; } = true;
    public bool IsFinishedProduct { get; set; } = false;
    public int TotalMenuItems { get; set; } = 0;
    public decimal AverageMenuPrice { get; set; } = 0;
    public string MenuCategory { get; set; } = string.Empty;
    public string SpecialDietaryInfo { get; set; } = string.Empty;
}
