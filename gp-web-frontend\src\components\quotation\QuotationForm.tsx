'use client'

import { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
// import { Textarea } from '@/components/ui/textarea'
// import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Plus, Trash2, Save, Calculator } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

// Interface based on exact legacy Frm_BaoGiaSuaChua.cs structure
interface QuotationLineItem {
  khoa: string
  khoaBaoGia: string
  noiDung: string // Content/Description
  soLuong: number // Quantity
  donGia: number // Unit Price
  thanhTien: number // Line Total (SoLuong * DonGia)
  tyLeCK: number // Discount Rate %
  tienCK: number // Discount Amount
  tyLeThue: number // Tax Rate %
  tienThue: number // Tax Amount
  loai: number // 0=Parts, 1=Labor
  maPhuTung: string // Part Code
  khoaHangHoa: string // Product Key
  dvt: string // Unit
  khoaDVT: string // Unit Key
  ghiChu: string // Notes
  chon: boolean // Selected for calculation
}

interface QuotationData {
  khoa: string
  soChungTu: string
  ngayChungTu: string
  bienSoXe: string
  khachHang: string
  loaiXe: string
  phanLoai: number // 0=Cash, 1=Insurance
  boPhanSuaChua: number // 0=Body+Paint, 1=Engine
  tinhTrangBaoGia: number // 0=Draft, 1=Approved, 2=Scheduled, 3=Cancelled
  
  // Financial calculations - exact from legacy TinhTong() method
  tongTien: number // Total before discount
  tyLeChietKhau: number // Overall discount rate
  tongChietKhau: number // Total discount amount
  sauChietKhau: number // After discount
  tyLeThue: number // Tax rate
  tienThue: number // Tax amount
  thanhToan: number // Final payment amount
  
  // Insurance specific calculations
  tienMienThuong: number // Deductible
  tienKhauHao: number // Depreciation
  tienCheTai: number // Processing fee
  tienCPSC: number // Repair cost reduction
  baoHiemThanhToan: number // Insurance payment
  
  // Commission
  phanTramHoaHong: number // Commission rate
  tongTienHoaHong: number // Commission amount
  
  dienGiai: string // Description
  lineItems: QuotationLineItem[]
}

interface QuotationFormProps {
  quotationId?: string
  onSave?: (data: QuotationData) => void
  onCancel?: () => void
}

export default function QuotationForm({ quotationId, onSave, onCancel }: QuotationFormProps) {
  const { user } = useAuth()
  const [quotation, setQuotation] = useState<QuotationData>({
    khoa: '',
    soChungTu: '',
    ngayChungTu: new Date().toISOString().split('T')[0].replace(/-/g, ''),
    bienSoXe: '',
    khachHang: '',
    loaiXe: '',
    phanLoai: 0,
    boPhanSuaChua: 1,
    tinhTrangBaoGia: 0,
    tongTien: 0,
    tyLeChietKhau: 0,
    tongChietKhau: 0,
    sauChietKhau: 0,
    tyLeThue: 10, // Default 10% VAT
    tienThue: 0,
    thanhToan: 0,
    tienMienThuong: 0,
    tienKhauHao: 0,
    tienCheTai: 0,
    tienCPSC: 0,
    baoHiemThanhToan: 0,
    phanTramHoaHong: 0,
    tongTienHoaHong: 0,
    dienGiai: '',
    lineItems: []
  })

  const [isDiscountEnabled, setIsDiscountEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // EXACT calculation methods from legacy Frm_BaoGiaSuaChua.cs

  // TinhTienHang - Calculate line total (Line 11848)
  const calculateLineTotal = useCallback((item: QuotationLineItem): number => {
    return Math.round(item.soLuong * item.donGia)
  }, [])

  // TinhChietKhau - Calculate line discount (Line 11985)
  const calculateLineDiscount = useCallback((item: QuotationLineItem): number => {
    const lineTotal = item.soLuong * item.donGia
    return Math.round((lineTotal / 100) * item.tyLeCK)
  }, [])

  // TinhThue - Calculate line tax (Line 11994)
  const calculateLineTax = useCallback((item: QuotationLineItem): number => {
    const lineTotal = item.soLuong * item.donGia
    const discountAmount = item.tienCK
    const taxableAmount = lineTotal - discountAmount
    return (taxableAmount / 100) * item.tyLeThue
  }, [])

  // TongTienHang - Calculate total amount (Line 11931)
  const calculateTotalAmount = useCallback((items: QuotationLineItem[]): number => {
    return items
      .filter(item => !item.chon) // Only include non-selected items
      .reduce((total, item) => total + item.thanhTien, 0)
  }, [])

  // TongTienChietKhau - Calculate total discount (Line 11857)
  const calculateTotalDiscount = useCallback((items: QuotationLineItem[]): number => {
    return items
      .filter(item => !item.chon)
      .reduce((total, item) => total + item.tienCK, 0)
  }, [])

  // TongTienThue - Calculate total tax (Line 11958)
  const calculateTotalTax = useCallback((items: QuotationLineItem[]): number => {
    return items
      .filter(item => !item.chon)
      .reduce((total, item) => total + item.tienThue, 0)
  }, [])

  // TinhTong - Main calculation method (Line 11911)
  const calculateTotals = useCallback(() => {
    const tongTien = calculateTotalAmount(quotation.lineItems)
    const tongChietKhau = calculateTotalDiscount(quotation.lineItems)
    const sauChietKhau = tongTien - tongChietKhau
    
    // Tax calculation: (After Discount) * Tax Rate / 100
    const tienThue = Math.round(sauChietKhau * quotation.tyLeThue / 100)
    const thanhToan = sauChietKhau + tienThue

    // Commission calculation
    const tongTienHoaHong = quotation.phanTramHoaHong > 0 
      ? Math.round(sauChietKhau * quotation.phanTramHoaHong / 100)
      : 0

    // Insurance payment calculation (TongTienBaoHiemThanhToan - Line 12260)
    let baoHiemThanhToan = thanhToan
    if (quotation.phanLoai === 1) { // Insurance
      const totalDeductions = quotation.tienMienThuong + quotation.tienKhauHao + 
                             quotation.tienCheTai + quotation.tienCPSC
      baoHiemThanhToan = thanhToan - totalDeductions
    }

    setQuotation(prev => ({
      ...prev,
      tongTien,
      tongChietKhau,
      sauChietKhau,
      tienThue,
      thanhToan,
      tongTienHoaHong,
      baoHiemThanhToan
    }))
  }, [quotation.lineItems, quotation.tyLeThue, quotation.phanTramHoaHong, 
      quotation.phanLoai, quotation.tienMienThuong, quotation.tienKhauHao,
      quotation.tienCheTai, quotation.tienCPSC, calculateTotalAmount, 
      calculateTotalDiscount])

  // Update line item calculations when values change
  const updateLineItem = useCallback((index: number, field: keyof QuotationLineItem, value: any) => {
    setQuotation(prev => {
      const newItems = [...prev.lineItems]
      const item = { ...newItems[index], [field]: value }
      
      // Recalculate line totals
      item.thanhTien = calculateLineTotal(item)
      item.tienCK = calculateLineDiscount(item)
      item.tienThue = calculateLineTax(item)
      
      newItems[index] = item
      return { ...prev, lineItems: newItems }
    })
  }, [calculateLineTotal, calculateLineDiscount, calculateLineTax])

  // Add new line item
  const addLineItem = useCallback(() => {
    const newItem: QuotationLineItem = {
      khoa: '',
      khoaBaoGia: quotation.khoa,
      noiDung: '',
      soLuong: 1,
      donGia: 0,
      thanhTien: 0,
      tyLeCK: quotation.tyLeChietKhau,
      tienCK: 0,
      tyLeThue: quotation.tyLeThue,
      tienThue: 0,
      loai: 0, // Default to parts
      maPhuTung: '',
      khoaHangHoa: '',
      dvt: '',
      khoaDVT: '',
      ghiChu: '',
      chon: false
    }
    
    setQuotation(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, newItem]
    }))
  }, [quotation.khoa, quotation.tyLeChietKhau, quotation.tyLeThue])

  // Remove line item
  const removeLineItem = useCallback((index: number) => {
    setQuotation(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter((_, i) => i !== index)
    }))
  }, [])

  // Recalculate totals when line items change
  useEffect(() => {
    calculateTotals()
  }, [calculateTotals])

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  const handleSave = async () => {
    if (!quotation.bienSoXe.trim()) {
      alert('Vui lòng nhập biển số xe')
      return
    }
    
    if (!quotation.khachHang.trim()) {
      alert('Vui lòng nhập tên khách hàng')
      return
    }

    setIsLoading(true)
    try {
      // Call API to save quotation
      onSave?.(quotation)
    } catch (error) {
      console.error('Error saving quotation:', error)
      alert('Lỗi khi lưu báo giá')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle>Thông tin báo giá</CardTitle>
          <CardDescription>
            Nhập thông tin cơ bản của báo giá sửa chữa
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="soChungTu">Số báo giá</Label>
              <Input
                id="soChungTu"
                value={quotation.soChungTu}
                onChange={(e) => setQuotation(prev => ({ ...prev, soChungTu: e.target.value }))}
                placeholder="Tự động tạo"
              />
            </div>
            <div>
              <Label htmlFor="ngayChungTu">Ngày báo giá</Label>
              <Input
                id="ngayChungTu"
                type="date"
                value={quotation.ngayChungTu.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')}
                onChange={(e) => setQuotation(prev => ({ 
                  ...prev, 
                  ngayChungTu: e.target.value.replace(/-/g, '') 
                }))}
              />
            </div>
            <div>
              <Label htmlFor="tinhTrangBaoGia">Trạng thái</Label>
              <Select 
                value={quotation.tinhTrangBaoGia.toString()} 
                onValueChange={(value) => setQuotation(prev => ({ 
                  ...prev, 
                  tinhTrangBaoGia: parseInt(value) 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Chưa thực hiện</SelectItem>
                  <SelectItem value="1">Đang thực hiện</SelectItem>
                  <SelectItem value="2">Báo giá hẹn</SelectItem>
                  <SelectItem value="3">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="bienSoXe">Biển số xe *</Label>
              <Input
                id="bienSoXe"
                value={quotation.bienSoXe}
                onChange={(e) => setQuotation(prev => ({ ...prev, bienSoXe: e.target.value.toUpperCase() }))}
                placeholder="30A-12345"
                className="uppercase"
              />
            </div>
            <div>
              <Label htmlFor="khachHang">Khách hàng *</Label>
              <Input
                id="khachHang"
                value={quotation.khachHang}
                onChange={(e) => setQuotation(prev => ({ ...prev, khachHang: e.target.value }))}
                placeholder="Tên khách hàng"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="loaiXe">Loại xe</Label>
              <Input
                id="loaiXe"
                value={quotation.loaiXe}
                onChange={(e) => setQuotation(prev => ({ ...prev, loaiXe: e.target.value }))}
                placeholder="Toyota Camry"
              />
            </div>
            <div>
              <Label htmlFor="phanLoai">Phân loại</Label>
              <Select 
                value={quotation.phanLoai.toString()} 
                onValueChange={(value) => setQuotation(prev => ({ 
                  ...prev, 
                  phanLoai: parseInt(value) 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Tiền mặt</SelectItem>
                  <SelectItem value="1">Bảo hiểm</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="boPhanSuaChua">Bộ phận sửa chữa</Label>
              <Select 
                value={quotation.boPhanSuaChua.toString()} 
                onValueChange={(value) => setQuotation(prev => ({ 
                  ...prev, 
                  boPhanSuaChua: parseInt(value) 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Thân vỏ + Sơn</SelectItem>
                  <SelectItem value="1">Máy móc</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="dienGiai">Diễn giải</Label>
            <textarea
              id="dienGiai"
              value={quotation.dienGiai}
              onChange={(e) => setQuotation(prev => ({ ...prev, dienGiai: e.target.value }))}
              placeholder="Mô tả công việc cần thực hiện..."
              rows={3}
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
        </CardContent>
      </Card>

      {/* Line Items */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Hạng mục sửa chữa</CardTitle>
              <CardDescription>
                Danh sách phụ tùng và công lao động
              </CardDescription>
            </div>
            <Button onClick={addLineItem} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Thêm hạng mục
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead className="w-20">Loại</TableHead>
                  <TableHead className="min-w-[200px]">Nội dung</TableHead>
                  <TableHead className="w-20">SL</TableHead>
                  <TableHead className="w-24">Đơn giá</TableHead>
                  <TableHead className="w-24">Thành tiền</TableHead>
                  <TableHead className="w-20">CK%</TableHead>
                  <TableHead className="w-24">Tiền CK</TableHead>
                  <TableHead className="w-20">Thuế%</TableHead>
                  <TableHead className="w-24">Tiền thuế</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {quotation.lineItems.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <Select
                        value={item.loai.toString()}
                        onValueChange={(value) => updateLineItem(index, 'loai', parseInt(value))}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">Phụ tùng</SelectItem>
                          <SelectItem value="1">Công lao động</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Input
                        value={item.noiDung}
                        onChange={(e) => updateLineItem(index, 'noiDung', e.target.value)}
                        placeholder="Mô tả công việc..."
                        className="min-w-[200px]"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.soLuong}
                        onChange={(e) => updateLineItem(index, 'soLuong', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.donGia}
                        onChange={(e) => updateLineItem(index, 'donGia', parseFloat(e.target.value) || 0)}
                        min="0"
                        className="w-24"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(item.thanhTien)}
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.tyLeCK}
                        onChange={(e) => updateLineItem(index, 'tyLeCK', parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(item.tienCK)}
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.tyLeThue}
                        onChange={(e) => updateLineItem(index, 'tyLeThue', parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(item.tienThue)}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeLineItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {quotation.lineItems.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={11} className="text-center text-muted-foreground py-8">
                      Chưa có hạng mục nào. Nhấn "Thêm hạng mục" để bắt đầu.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Tổng kết tài chính
          </CardTitle>
          <CardDescription>
            Tính toán tự động theo logic legacy Frm_BaoGiaSuaChua.cs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Basic Calculations */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>Tổng tiền hàng:</Label>
                <span className="font-bold text-lg">{formatCurrency(quotation.tongTien)}</span>
              </div>

              <div className="flex items-center gap-4">
                <input
                  type="checkbox"
                  id="enableDiscount"
                  checked={isDiscountEnabled}
                  onChange={(e) => setIsDiscountEnabled(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Label htmlFor="enableDiscount">Áp dụng chiết khấu</Label>
              </div>

              {isDiscountEnabled && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="tyLeChietKhau">Tỷ lệ chiết khấu (%):</Label>
                    <Input
                      id="tyLeChietKhau"
                      type="number"
                      value={quotation.tyLeChietKhau}
                      onChange={(e) => setQuotation(prev => ({
                        ...prev,
                        tyLeChietKhau: parseFloat(e.target.value) || 0
                      }))}
                      min="0"
                      max="100"
                      step="0.1"
                      className="w-24"
                    />
                  </div>
                  <div className="flex justify-between items-center">
                    <Label>Tổng chiết khấu:</Label>
                    <span className="font-medium text-red-600">{formatCurrency(quotation.tongChietKhau)}</span>
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center border-t pt-2">
                <Label>Sau chiết khấu:</Label>
                <span className="font-bold">{formatCurrency(quotation.sauChietKhau)}</span>
              </div>

              <div className="flex justify-between items-center">
                <Label htmlFor="tyLeThue">Tỷ lệ thuế (%):</Label>
                <Input
                  id="tyLeThue"
                  type="number"
                  value={quotation.tyLeThue}
                  onChange={(e) => setQuotation(prev => ({
                    ...prev,
                    tyLeThue: parseFloat(e.target.value) || 0
                  }))}
                  min="0"
                  max="100"
                  step="0.1"
                  className="w-24"
                />
              </div>

              <div className="flex justify-between items-center">
                <Label>Tiền thuế:</Label>
                <span className="font-medium">{formatCurrency(quotation.tienThue)}</span>
              </div>

              <div className="flex justify-between items-center border-t pt-2">
                <Label className="text-lg">Tổng thanh toán:</Label>
                <span className="font-bold text-xl text-green-600">{formatCurrency(quotation.thanhToan)}</span>
              </div>
            </div>

            {/* Right Column - Insurance & Commission */}
            <div className="space-y-4">
              {quotation.phanLoai === 1 && (
                <div className="space-y-4 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800">Tính toán bảo hiểm</h4>

                  <div className="flex justify-between items-center">
                    <Label htmlFor="tienMienThuong">Miễn thường:</Label>
                    <Input
                      id="tienMienThuong"
                      type="number"
                      value={quotation.tienMienThuong}
                      onChange={(e) => setQuotation(prev => ({
                        ...prev,
                        tienMienThuong: parseFloat(e.target.value) || 0
                      }))}
                      min="0"
                      className="w-32"
                    />
                  </div>

                  <div className="flex justify-between items-center">
                    <Label htmlFor="tienKhauHao">Khấu hao:</Label>
                    <Input
                      id="tienKhauHao"
                      type="number"
                      value={quotation.tienKhauHao}
                      onChange={(e) => setQuotation(prev => ({
                        ...prev,
                        tienKhauHao: parseFloat(e.target.value) || 0
                      }))}
                      min="0"
                      className="w-32"
                    />
                  </div>

                  <div className="flex justify-between items-center">
                    <Label htmlFor="tienCheTai">Chế tài:</Label>
                    <Input
                      id="tienCheTai"
                      type="number"
                      value={quotation.tienCheTai}
                      onChange={(e) => setQuotation(prev => ({
                        ...prev,
                        tienCheTai: parseFloat(e.target.value) || 0
                      }))}
                      min="0"
                      className="w-32"
                    />
                  </div>

                  <div className="flex justify-between items-center border-t pt-2">
                    <Label className="font-semibold text-blue-800">Bảo hiểm thanh toán:</Label>
                    <span className="font-bold text-blue-600">{formatCurrency(quotation.baoHiemThanhToan)}</span>
                  </div>
                </div>
              )}

              <div className="space-y-4 p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Hoa hồng</h4>

                <div className="flex justify-between items-center">
                  <Label htmlFor="phanTramHoaHong">Tỷ lệ hoa hồng (%):</Label>
                  <Input
                    id="phanTramHoaHong"
                    type="number"
                    value={quotation.phanTramHoaHong}
                    onChange={(e) => setQuotation(prev => ({
                      ...prev,
                      phanTramHoaHong: parseFloat(e.target.value) || 0
                    }))}
                    min="0"
                    max="100"
                    step="0.1"
                    className="w-24"
                  />
                </div>

                <div className="flex justify-between items-center">
                  <Label>Tổng hoa hồng:</Label>
                  <span className="font-bold text-green-600">{formatCurrency(quotation.tongTienHoaHong)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4">
        <Button variant="outline" onClick={onCancel}>
          Hủy
        </Button>
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? 'Đang lưu...' : 'Lưu báo giá'}
        </Button>
      </div>
    </div>
  )
}
