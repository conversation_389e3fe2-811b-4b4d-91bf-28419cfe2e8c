using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoGia (Service Quotation) entity
/// Maps exactly to SC_BaoGia table in legacy database
/// Implements ALL 133+ properties from clsBaoGia.cs (4,148 lines)
/// This is the most complex class in the system - handle with extreme care
/// Manual implementation based on exact legacy class analysis
/// </summary>
public class BaoGiaDto
{
    #region Core Identification Properties
    
    /// <summary>Primary key - Legacy field: mKhoa</summary>
    public string Khoa { get; set; } = string.Empty;
    
    /// <summary>Document number - Legacy field: mSoChungtu</summary>
    [Required]
    [StringLength(50)]
    public string SoChungtu { get; set; } = string.Empty;
    
    /// <summary>Document date - Legacy field: mNgayChungTu</summary>
    public string NgayChungTu { get; set; } = string.Empty;
    
    #endregion

    #region Vehicle Information
    
    /// <summary>Vehicle key - Legacy field: mKhoaXe</summary>
    public string KhoaXe { get; set; } = string.Empty;
    
    /// <summary>Vehicle type key - Legacy field: mKhoaLoaiXe</summary>
    public string KhoaLoaiXe { get; set; } = string.Empty;
    
    /// <summary>License plate - Legacy field: mSoXe</summary>
    [StringLength(20)]
    public string SoXe { get; set; } = string.Empty;
    
    /// <summary>Current odometer reading - Legacy field: mSoKmHienTai</summary>
    public double SoKmHienTai { get; set; } = 0.0;
    
    /// <summary>Previous odometer reading - Legacy field: mSoKmTruoc</summary>
    public double SoKmTruoc { get; set; } = 0.0;
    
    #endregion

    #region Customer Information
    
    /// <summary>Customer key - Legacy field: mKhoaKhachHang</summary>
    public string KhoaKhachHang { get; set; } = string.Empty;
    
    /// <summary>Customer special requests - Legacy field: mKhachHangYeuCau</summary>
    public string KhachHangYeuCau { get; set; } = string.Empty;
    
    /// <summary>Insurance company key - Legacy field: mKhoaHangBaoHiem</summary>
    public string KhoaHangBaoHiem { get; set; } = string.Empty;
    
    /// <summary>Insurance contact person - Legacy field: mLienHeBaoHiem</summary>
    public string LienHeBaoHiem { get; set; } = string.Empty;
    
    /// <summary>Insurance contact phone - Legacy field: mDienThoaiLienHe</summary>
    public string DienThoaiLienHe { get; set; } = string.Empty;
    
    #endregion

    #region Driver Information
    
    /// <summary>Driver name - Legacy field: mTenTaiXe</summary>
    public string TenTaiXe { get; set; } = string.Empty;
    
    /// <summary>Driver phone - Legacy field: mDienThoaiTaiXe</summary>
    public string DienThoaiTaiXe { get; set; } = string.Empty;
    
    /// <summary>Driver address - Legacy field: mDiaChiTaiXe</summary>
    public string DiaChiTaiXe { get; set; } = string.Empty;
    
    #endregion

    #region Workshop Schedule
    
    /// <summary>Urgent work needed - Legacy field: mCongViecCanLamSom</summary>
    public string CongViecCanLamSom { get; set; } = string.Empty;
    
    /// <summary>Workshop entry date - Legacy field: mNgayVaoXuong</summary>
    public string NgayVaoXuong { get; set; } = string.Empty;
    
    /// <summary>Workshop entry time - Legacy field: mGioVaoXuong</summary>
    public string GioVaoXuong { get; set; } = string.Empty;
    
    /// <summary>Expected completion date - Legacy field: mNgayDuKienHoanThanh</summary>
    public string NgayDuKienHoanThanh { get; set; } = string.Empty;
    
    /// <summary>Expected completion time - Legacy field: mGioDuKienHoanThanh</summary>
    public string GioDuKienHoanThanh { get; set; } = string.Empty;
    
    /// <summary>Repair start date - Legacy field: mNgayBatDauSuaChua</summary>
    public string NgayBatDauSuaChua { get; set; } = string.Empty;
    
    /// <summary>Repair start time - Legacy field: mGioBatDauSuaChua</summary>
    public string GioBatDauSuaChua { get; set; } = string.Empty;
    
    /// <summary>Completion date - Legacy field: mNgayHoanTat</summary>
    public string NgayHoanTat { get; set; } = string.Empty;
    
    /// <summary>Completion time - Legacy field: mGioHoanTat</summary>
    public string GioHoanTat { get; set; } = string.Empty;
    
    /// <summary>Workshop exit date - Legacy field: mNgayXuatXuong</summary>
    public string NgayXuatXuong { get; set; } = string.Empty;
    
    /// <summary>Workshop exit time - Legacy field: mGioXuatXuong</summary>
    public string GioXuatXuong { get; set; } = string.Empty;
    
    /// <summary>Workshop exit notes - Legacy field: mDienGiaiXuatXuong</summary>
    public string DienGiaiXuatXuong { get; set; } = string.Empty;
    
    #endregion

    #region Status Fields
    
    /// <summary>Workshop entry status - Legacy field: mTinhTrangNhapXuong</summary>
    public int TinhTrangNhapXuong { get; set; } = 0;
    
    /// <summary>Workshop exit status - Legacy field: mTinhTrangXuatXuong</summary>
    public int TinhTrangXuatXuong { get; set; } = 0;
    
    /// <summary>Quotation status - Legacy field: mTinhTrangBaoGia</summary>
    public int TinhTrangBaoGia { get; set; } = 0;
    
    /// <summary>Repair status - Legacy field: mTinhTrangSuaChua</summary>
    public int TinhTrangSuaChua { get; set; } = 0;
    
    /// <summary>Vehicle condition - Legacy field: mTinhTrangXe</summary>
    public int TinhTrangXe { get; set; } = 0;
    
    /// <summary>Reception status - Legacy field: mTinhTrangTiepNhan</summary>
    public string TinhTrangTiepNhan { get; set; } = string.Empty;
    
    #endregion

    #region Time and Labor
    
    /// <summary>Repair time required - Legacy field: mThoiGianSuaChua</summary>
    public double ThoiGianSuaChua { get; set; } = 0.0;
    
    /// <summary>Time type - Legacy field: mLoaiThoiGian</summary>
    public int LoaiThoiGian { get; set; } = 0;
    
    /// <summary>Working hours - Legacy field: mGioHoatDong</summary>
    public double GioHoatDong { get; set; } = 0.0;
    
    /// <summary>Previous working hours - Legacy field: mGioHoatDongTruoc</summary>
    public double GioHoatDongTruoc { get; set; } = 0.0;
    
    #endregion

    #region Location and Permissions
    
    /// <summary>Current location key - Legacy field: mKhoaViTriHienTai</summary>
    public string KhoaViTriHienTai { get; set; } = string.Empty;
    
    /// <summary>Allowed to leave workshop - Legacy field: mDuocPhepRaCong</summary>
    public int DuocPhepRaCong { get; set; } = 0;
    
    #endregion

    #region Financial Information
    
    /// <summary>Total repair cost - Legacy field: mTongTienSuaChua</summary>
    public double TongTienSuaChua { get; set; } = 0.0;
    
    /// <summary>Discount percentage - Legacy field: mTyLeChietKhau</summary>
    public int TyLeChietKhau { get; set; } = 0;
    
    /// <summary>Discount amount - Legacy field: mTienChietKhau</summary>
    public double TienChietKhau { get; set; } = 0.0;
    
    /// <summary>Tax percentage - Legacy field: mTyLeThue</summary>
    public int TyLeThue { get; set; } = 0;
    
    /// <summary>Tax amount - Legacy field: mTienThue</summary>
    public double TienThue { get; set; } = 0.0;
    
    /// <summary>Deposit amount - Legacy field: mTienCheTai</summary>
    public double TienCheTai { get; set; } = 0.0;
    
    /// <summary>Deposit paid - Legacy field: mDaThuCheTai</summary>
    public double DaThuCheTai { get; set; } = 0.0;
    
    /// <summary>Repair payment received - Legacy field: mDaThuSuaChua</summary>
    public double DaThuSuaChua { get; set; } = 0.0;
    
    /// <summary>Advance payment - Legacy field: mTraTruoc</summary>
    public double TraTruoc { get; set; } = 0.0;
    
    /// <summary>Labor promotion - Legacy field: mKhuyenMaiNhanCong</summary>
    public double KhuyenMaiNhanCong { get; set; } = 0.0;
    
    /// <summary>Parts promotion - Legacy field: mKhuyenMaiPhuTung</summary>
    public double KhuyenMaiPhuTung { get; set; } = 0.0;
    
    /// <summary>Reward level - Legacy field: mMucMienThuong</summary>
    public double MucMienThuong { get; set; } = 0.0;
    
    /// <summary>Commission amount - Legacy field: mTienHoaHong</summary>
    public double TienHoaHong { get; set; } = 0.0;
    
    /// <summary>Commission paid - Legacy field: mTienHoaHongDaChi</summary>
    public double TienHoaHongDaChi { get; set; } = 0.0;
    
    /// <summary>Additional discount - Legacy field: mTienCKTang</summary>
    public double TienCKTang { get; set; } = 0.0;
    
    /// <summary>Total input cost - Legacy field: mTongChiPhiDauVao</summary>
    public double TongChiPhiDauVao { get; set; } = 0.0;
    
    /// <summary>Service cost percentage - Legacy field: mTyLeCPSC</summary>
    public int TyLeCPSC { get; set; } = 0;
    
    /// <summary>Service cost amount - Legacy field: mTienCPSC</summary>
    public double TienCPSC { get; set; } = 0.0;
    
    /// <summary>Depreciation amount - Legacy field: mTienKhauHao</summary>
    public double TienKhauHao { get; set; } = 0.0;
    
    #endregion

    #region Customer Communication
    
    /// <summary>Customer waiting for pickup - Legacy field: mKhachChoNhanXe</summary>
    public int KhachChoNhanXe { get; set; } = 0;
    
    /// <summary>Customer getting parts - Legacy field: mKhachLayPhuTung</summary>
    public int KhachLayPhuTung { get; set; } = 0;
    
    /// <summary>Phone contact made - Legacy field: mLienHeQuaDienThoai</summary>
    public int LienHeQuaDienThoai { get; set; } = 0;
    
    /// <summary>Letter contact made - Legacy field: mLienHeQuaThu</summary>
    public int LienHeQuaThu { get; set; } = 0;
    
    /// <summary>Customer requested car wash - Legacy field: mKhachYeuCauRuaXe</summary>
    public int KhachYeuCauRuaXe { get; set; } = 0;
    
    #endregion

    #region Service and Staff
    
    /// <summary>Service type key - Legacy field: mKhoaLoaiDichVu</summary>
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    
    /// <summary>Advisor 1 key - Legacy field: mKhoaCoVan1</summary>
    public string KhoaCoVan1 { get; set; } = string.Empty;
    
    /// <summary>Advisor 1 name - Legacy field: mCoVanDichVu1</summary>
    public string CoVanDichVu1 { get; set; } = string.Empty;
    
    /// <summary>Advisor 1 phone - Legacy field: mDienThoaiCoVan1</summary>
    public string DienThoaiCoVan1 { get; set; } = string.Empty;
    
    /// <summary>Advisor 2 key - Legacy field: mKhoaCoVan2</summary>
    public string KhoaCoVan2 { get; set; } = string.Empty;
    
    /// <summary>Advisor 2 name - Legacy field: mCoVanDichVu2</summary>
    public string CoVanDichVu2 { get; set; } = string.Empty;
    
    /// <summary>Advisor 2 phone - Legacy field: mDienThoaiCoVan2</summary>
    public string DienThoaiCoVan2 { get; set; } = string.Empty;
    
    /// <summary>Sales staff key - Legacy field: mKhoaNhanVienKinhDoanh</summary>
    public string KhoaNhanVienKinhDoanh { get; set; } = string.Empty;
    
    /// <summary>Creator staff key - Legacy field: mKhoaNhanVienTao</summary>
    public string KhoaNhanVienTao { get; set; } = string.Empty;
    
    /// <summary>Creation date - Legacy field: mNgayTao</summary>
    public string NgayTao { get; set; } = string.Empty;
    
    /// <summary>Updater staff key - Legacy field: mKhoaNhanVienCapNhat</summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    
    /// <summary>Update date - Legacy field: mNgayCapNhat</summary>
    public string NgayCapNhat { get; set; } = string.Empty;
    
    #endregion

    #region Parts and Tracking
    
    /// <summary>Needs monitoring - Legacy field: mCanTheoDoi</summary>
    public int CanTheoDoi { get; set; } = 0;
    
    /// <summary>Parts ordered - Legacy field: mDaDatPhuTung</summary>
    public int DaDatPhuTung { get; set; } = 0;
    
    /// <summary>Parts sufficient - Legacy field: mPhuTungVeDu</summary>
    public int PhuTungVeDu { get; set; } = 0;
    
    /// <summary>Re-confirmed - Legacy field: mDaXacNhanLai</summary>
    public int DaXacNhanLai { get; set; } = 0;
    
    #endregion

    #region Payment and Invoice

    /// <summary>Payment object - Legacy field: mDoiTuongThanhToan</summary>
    public string DoiTuongThanhToan { get; set; } = string.Empty;

    /// <summary>Issue invoice flag - Legacy field: mXuatHoaDon</summary>
    public int XuatHoaDon { get; set; } = 0;

    /// <summary>Payment confirmation staff - Legacy field: mKhoaNhanVienXacNhanThanhToan</summary>
    public string KhoaNhanVienXacNhanThanhToan { get; set; } = string.Empty;

    /// <summary>Payment confirmation date - Legacy field: mNgayXacNhanThanhToan</summary>
    public string NgayXacNhanThanhToan { get; set; } = string.Empty;

    /// <summary>Payment confirmation notes - Legacy field: mDienGiaiXacNhanThanhToan</summary>
    public string DienGiaiXacNhanThanhToan { get; set; } = string.Empty;

    /// <summary>Invoice type key - Legacy field: mKhoaLoaiHoaDon</summary>
    public string KhoaLoaiHoaDon { get; set; } = string.Empty;

    /// <summary>Invoice number - Legacy field: mSoHoaDon</summary>
    public string SoHoaDon { get; set; } = string.Empty;

    /// <summary>Invoice date - Legacy field: mNgayHoaDon</summary>
    public string NgayHoaDon { get; set; } = string.Empty;

    /// <summary>Receipt key - Legacy field: mKhoaPhieuThu</summary>
    public string KhoaPhieuThu { get; set; } = string.Empty;

    /// <summary>Receipt date - Legacy field: mNgayPhieuThu</summary>
    public string NgayPhieuThu { get; set; } = string.Empty;

    #endregion

    #region Maintenance and Service

    /// <summary>Next service odometer - Legacy field: mSoKmBaoDuongDotSau</summary>
    public double SoKmBaoDuongDotSau { get; set; } = 0.0;

    /// <summary>Next service date - Legacy field: mNgayBaoDuongDotSau</summary>
    public string NgayBaoDuongDotSau { get; set; } = string.Empty;

    /// <summary>Recording document key - Legacy field: mKhoaChungTuGhiSo</summary>
    public string KhoaChungTuGhiSo { get; set; } = string.Empty;

    /// <summary>Recording date - Legacy field: mNgayGhiSo</summary>
    public string NgayGhiSo { get; set; } = string.Empty;

    #endregion

    #region Insurance and Approval

    /// <summary>Insurance price approved - Legacy field: mIsDuyetGiaBH</summary>
    public int IsDuyetGiaBH { get; set; } = 0;

    /// <summary>Insurance approval date - Legacy field: mNgayDuyetGiaBH</summary>
    public string NgayDuyetGiaBH { get; set; } = string.Empty;

    /// <summary>Insurance approval staff - Legacy field: mKhoaNhanVienDuyetGiaBH</summary>
    public string KhoaNhanVienDuyetGiaBH { get; set; } = string.Empty;

    /// <summary>Dealer key - Legacy field: mKhoaDaiLy</summary>
    public string KhoaDaiLy { get; set; } = string.Empty;

    #endregion

    #region Boolean Flags

    /// <summary>Has discount - Legacy field: mIsChietKhau</summary>
    public bool IsChietKhau { get; set; } = false;

    /// <summary>Is warranty repair - Legacy field: mIsSuaChuaBaoHanh</summary>
    public bool IsSuaChuaBaoHanh { get; set; } = false;

    /// <summary>Customer pays only - Legacy field: mIsKhachHangChiTra</summary>
    public bool IsKhachHangChiTra { get; set; } = false;

    /// <summary>Deferred payment - Legacy field: mIsNoThanhToanLanSau</summary>
    public bool IsNoThanhToanLanSau { get; set; } = false;

    /// <summary>Calculate before tax - Legacy field: mIsTinhTruocThue</summary>
    public bool IsTinhTruocThue { get; set; } = false;

    /// <summary>Calculate after tax - Legacy field: mIsTinhSauThue</summary>
    public bool IsTinhSauThue { get; set; } = false;

    /// <summary>Is supplementary quotation - Legacy field: mIsBaoGiaPhu</summary>
    public bool IsBaoGiaPhu { get; set; } = false;

    #endregion

    #region Diagnosis and Repair Description

    /// <summary>Diagnosis result - Legacy field: mKetQuaChanDoan</summary>
    public string KetQuaChanDoan { get; set; } = string.Empty;

    /// <summary>Repair description - Legacy field: mMoTaSuaChua</summary>
    public string MoTaSuaChua { get; set; } = string.Empty;

    /// <summary>Damage status - Legacy field: mTinhTrangHuHong</summary>
    public string TinhTrangHuHong { get; set; } = string.Empty;

    /// <summary>Damage cause - Legacy field: mNguyenNhanHuHong</summary>
    public string NguyenNhanHuHong { get; set; } = string.Empty;

    #endregion

    #region Commission Information

    /// <summary>Commission beneficiary - Legacy field: mHH_NguoiThuHuong</summary>
    public string HH_NguoiThuHuong { get; set; } = string.Empty;

    /// <summary>Commission phone - Legacy field: mHH_DienThoai</summary>
    public string HH_DienThoai { get; set; } = string.Empty;

    /// <summary>Commission address - Legacy field: mHH_DiaChi</summary>
    public string HH_DiaChi { get; set; } = string.Empty;

    /// <summary>Commission account number - Legacy field: mHH_STK</summary>
    public string HH_STK { get; set; } = string.Empty;

    /// <summary>Commission bank - Legacy field: mHH_NganHang</summary>
    public string HH_NganHang { get; set; } = string.Empty;

    /// <summary>Commission reason - Legacy field: mHH_LyDo</summary>
    public string HH_LyDo { get; set; } = string.Empty;

    #endregion

    #region TPR and Special Codes

    /// <summary>TPR number - Legacy field: mSoTPR</summary>
    public string SoTPR { get; set; } = string.Empty;

    /// <summary>TPR approval number - Legacy field: mSoXetDuyetTPR</summary>
    public string SoXetDuyetTPR { get; set; } = string.Empty;

    #endregion

    #region Rescue Service

    /// <summary>Rescue staff key - Legacy field: mKhoaNhanVienCuuPan</summary>
    public string KhoaNhanVienCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue department key - Legacy field: mKhoaBoPhanCuuPan</summary>
    public string KhoaBoPhanCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue purpose - Legacy field: mMucDichCuuPan</summary>
    public string MucDichCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue departure time - Legacy field: mGioRaCuuPan</summary>
    public string GioRaCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue departure date - Legacy field: mNgayRaCuuPan</summary>
    public string NgayRaCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue arrival time - Legacy field: mGioVaoCuuPan</summary>
    public string GioVaoCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue arrival date - Legacy field: mNgayVaoCuuPan</summary>
    public string NgayVaoCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue date - Legacy field: mNgayCuuPan</summary>
    public string NgayCuuPan { get; set; } = string.Empty;

    /// <summary>Rescue document number - Legacy field: mSoChungTuCuuPan</summary>
    public string SoChungTuCuuPan { get; set; } = string.Empty;

    #endregion

    #region Outstanding Payment Information

    /// <summary>Outstanding payment info - Legacy field: mThongTinBaoGiaConNo</summary>
    public string ThongTinBaoGiaConNo { get; set; } = string.Empty;

    #endregion

    #region Invoice Tax Information

    /// <summary>Tax entity name - Legacy field: mXHD_TenDoiTuongThue</summary>
    public string XHD_TenDoiTuongThue { get; set; } = string.Empty;

    /// <summary>Tax entity address - Legacy field: mXHD_DiaChiThue</summary>
    public string XHD_DiaChiThue { get; set; } = string.Empty;

    /// <summary>Tax code - Legacy field: mXHD_MST</summary>
    public string XHD_MST { get; set; } = string.Empty;

    /// <summary>Bank account - Legacy field: mXHD_TKNH</summary>
    public string XHD_TKNH { get; set; } = string.Empty;

    /// <summary>Bank name - Legacy field: mXHD_NganHang</summary>
    public string XHD_NganHang { get; set; } = string.Empty;

    #endregion

    #region Unit and Department

    /// <summary>Unit key - Legacy field: mKhoaDonVi</summary>
    public string KhoaDonVi { get; set; } = string.Empty;

    #endregion
}

/// <summary>
/// List DTO for BaoGia entity - Used for list operations and search results
/// Contains key properties for display in quotation lists
/// </summary>
public class BaoGiaListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungtu { get; set; } = string.Empty;
    public string NgayChungTu { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string TenKhachHang { get; set; } = string.Empty;
    public string NgayVaoXuong { get; set; } = string.Empty;
    public int TinhTrangSuaChua { get; set; } = 0;
    public int TinhTrangBaoGia { get; set; } = 0;
    public double TongTienSuaChua { get; set; } = 0.0;
    public double TienThue { get; set; } = 0.0;
    public double TienChietKhau { get; set; } = 0.0;
    public int DuocPhepRaCong { get; set; } = 0;
    public string NgayDuKienHoanThanh { get; set; } = string.Empty;
}

/// <summary>
/// Create DTO for BaoGia entity - Used for creating new quotations
/// Excludes auto-generated fields like Khoa
/// </summary>
public class CreateBaoGiaDto
{
    [Required]
    [StringLength(50)]
    public string SoChungtu { get; set; } = string.Empty;

    public string NgayChungTu { get; set; } = string.Empty;

    [Required]
    public string KhoaXe { get; set; } = string.Empty;

    public string KhoaLoaiXe { get; set; } = string.Empty;

    [StringLength(20)]
    public string SoXe { get; set; } = string.Empty;

    public double SoKmHienTai { get; set; } = 0.0;

    [Required]
    public string KhoaKhachHang { get; set; } = string.Empty;

    public string KhachHangYeuCau { get; set; } = string.Empty;

    public string NgayVaoXuong { get; set; } = string.Empty;

    public string GioVaoXuong { get; set; } = string.Empty;

    public string KhoaLoaiDichVu { get; set; } = string.Empty;

    public string KhoaCoVan1 { get; set; } = string.Empty;

    public string TenTaiXe { get; set; } = string.Empty;

    public string DienThoaiTaiXe { get; set; } = string.Empty;

    public string KetQuaChanDoan { get; set; } = string.Empty;

    public string MoTaSuaChua { get; set; } = string.Empty;
}

/// <summary>
/// Update status DTO for BaoGia entity - Used for status updates and workflow changes
/// </summary>
public class UpdateBaoGiaStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int TinhTrangNhapXuong { get; set; } = 0;

    public int TinhTrangXuatXuong { get; set; } = 0;

    public int TinhTrangBaoGia { get; set; } = 0;

    public int TinhTrangSuaChua { get; set; } = 0;

    public int TinhTrangXe { get; set; } = 0;

    public int DuocPhepRaCong { get; set; } = 0;

    public string NgayHoanTat { get; set; } = string.Empty;

    public string GioHoanTat { get; set; } = string.Empty;

    public string NgayXuatXuong { get; set; } = string.Empty;

    public string GioXuatXuong { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGia search operations
/// Contains search criteria and filters
/// </summary>
public class BaoGiaSearchDto
{
    public string? SoChungtu { get; set; }
    public string? SoXe { get; set; }
    public string? KhoaKhachHang { get; set; }
    public string? TenKhachHang { get; set; }
    public string? NgayVaoXuongFrom { get; set; }
    public string? NgayVaoXuongTo { get; set; }
    public int? TinhTrangSuaChua { get; set; }
    public int? TinhTrangBaoGia { get; set; }
    public string? KhoaCoVan1 { get; set; }
    public string? KhoaLoaiDichVu { get; set; }
    public bool? DuocPhepRaCong { get; set; }
    public int? PageSize { get; set; } = 50;
    public int? PageNumber { get; set; } = 1;
}

/// <summary>
/// DTO for BaoGia validation operations
/// Used for validation and business rules
/// </summary>
public class BaoGiaValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public bool IsValidSoChungtu { get; set; } = true;
    public bool IsValidKhoaXe { get; set; } = true;
    public bool IsValidKhoaKhachHang { get; set; } = true;
    public bool IsValidNgayVaoXuong { get; set; } = true;
    public bool CanDelete { get; set; } = true;
    public bool CanEdit { get; set; } = true;
    public bool CanApprove { get; set; } = true;
    public bool IsInUse { get; set; } = false;
    public List<string> ValidationErrors { get; set; } = new List<string>();
    public List<string> ValidationWarnings { get; set; } = new List<string>();
}

/// <summary>
/// DTO for BaoGia financial summary
/// Provides financial overview of a quotation
/// </summary>
public class BaoGiaFinancialSummaryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungtu { get; set; } = string.Empty;
    public double TongTienSuaChua { get; set; } = 0.0;
    public double TienChietKhau { get; set; } = 0.0;
    public double TienThue { get; set; } = 0.0;
    public double TienCheTai { get; set; } = 0.0;
    public double DaThuCheTai { get; set; } = 0.0;
    public double DaThuSuaChua { get; set; } = 0.0;
    public double TraTruoc { get; set; } = 0.0;
    public double TongChiPhiDauVao { get; set; } = 0.0;
    public double TienCPSC { get; set; } = 0.0;
    public double TienKhauHao { get; set; } = 0.0;
    public double TienHoaHong { get; set; } = 0.0;
    public double TienHoaHongDaChi { get; set; } = 0.0;
    public double KhuyenMaiNhanCong { get; set; } = 0.0;
    public double KhuyenMaiPhuTung { get; set; } = 0.0;
    public double TongThanhToan { get; set; } = 0.0;
    public double ConLai { get; set; } = 0.0;
    public string TrangThaiThanhToan { get; set; } = "Chưa thanh toán";
}

/// <summary>
/// DTO for BaoGia workflow tracking
/// Tracks workflow status changes and history
/// </summary>
public class BaoGiaWorkflowDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungtu { get; set; } = string.Empty;
    public string WorkflowStep { get; set; } = string.Empty;
    public string WorkflowStatus { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string NguoiThucHien { get; set; } = string.Empty;
    public string GhiChu { get; set; } = string.Empty;
    public int TinhTrangBaoGia { get; set; } = 0;
    public int TinhTrangSuaChua { get; set; } = 0;
    public int TinhTrangXe { get; set; } = 0;
    public bool IsCurrentStep { get; set; } = false;
}
