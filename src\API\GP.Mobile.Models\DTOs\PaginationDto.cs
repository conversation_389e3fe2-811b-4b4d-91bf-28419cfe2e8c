using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Pagination request parameters
/// </summary>
public class PaginationRequestDto
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Sort field
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    /// <summary>
    /// Calculate skip count for database queries
    /// </summary>
    public int Skip => (Page - 1) * PageSize;
}

/// <summary>
/// Paginated response wrapper
/// </summary>
/// <typeparam name="T">Type of data items</typeparam>
public class PaginatedResponseDto<T>
{
    /// <summary>
    /// Current page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of items
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage => Page > 1;

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage => Page < TotalPages;

    /// <summary>
    /// Data items for current page
    /// </summary>
    public IEnumerable<T> Data { get; set; } = new List<T>();

    /// <summary>
    /// Sort field used
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Sort direction used
    /// </summary>
    public string? SortDirection { get; set; }
}

/// <summary>
/// Date range filter for quotations
/// </summary>
public class DateRangeFilterDto
{
    /// <summary>
    /// Start date (NgayChungTu >= StartDate)
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date (NgayChungTu <= EndDate)
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Validate date range
    /// </summary>
    public bool IsValid()
    {
        if (StartDate.HasValue && EndDate.HasValue)
        {
            return StartDate.Value <= EndDate.Value;
        }
        return true;
    }
}

/// <summary>
/// BaoGiaSuaChua pagination and filtering request
/// </summary>
public class BaoGiaSuaChuaPaginationDto : PaginationRequestDto
{
    /// <summary>
    /// Date range filter
    /// </summary>
    public DateRangeFilterDto? DateRange { get; set; }

    /// <summary>
    /// Search by vehicle license plate
    /// </summary>
    public string? BienSoXe { get; set; }

    /// <summary>
    /// Search by customer name
    /// </summary>
    public string? KhachHang { get; set; }

    /// <summary>
    /// Filter by quotation status
    /// </summary>
    public int? TinhTrangBaoGia { get; set; }

    /// <summary>
    /// Filter by repair department
    /// </summary>
    public int? BoPhanSuaChua { get; set; }

    /// <summary>
    /// Filter by payment type (0=Cash, 1=Insurance)
    /// </summary>
    public int? PhanLoai { get; set; }
}
