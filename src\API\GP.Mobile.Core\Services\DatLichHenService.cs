using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services
{
    /// <summary>
    /// Service implementation for Appointment Scheduling (Đặt Lịch Hẹn) business logic
    /// Provides high-level business operations for appointment management
    /// </summary>
    public class DatLichHenService : IDatLichHenService
    {
        private readonly IDatLichHenRepository _repository;
        private readonly ILogger<DatLichHenService> _logger;

        public DatLichHenService(IDatLichHenRepository repository, ILogger<DatLichHenService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        #region Core CRUD Operations

        public async Task<DatLichHenDto?> GetByIdAsync(string khoa)
        {
            try
            {
                if (string.IsNullOrEmpty(khoa))
                {
                    _logger.LogWarning("GetByIdAsync called with empty khoa");
                    return null;
                }

                return await _repository.LoadAsync(khoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment by ID: {Khoa}", khoa);
                return null;
            }
        }

        public async Task<DatLichHenDto?> GetByBaoGiaIdAsync(string khoaBaoGia)
        {
            try
            {
                if (string.IsNullOrEmpty(khoaBaoGia))
                {
                    _logger.LogWarning("GetByBaoGiaIdAsync called with empty khoaBaoGia");
                    return null;
                }

                return await _repository.LoadByBaoGiaAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment by BaoGia ID: {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        public async Task<string> CreateAsync(DatLichHenDto appointment)
        {
            try
            {
                // Validate appointment data
                var validationResult = await ValidateAppointmentAsync(appointment);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Appointment validation failed: {Errors}", 
                        string.Join(", ", validationResult.ErrorMessages));
                    return string.Empty;
                }

                // Check for time conflicts
                bool hasConflict = await CheckAppointmentConflictAsync(
                    appointment.NgayDatHen, appointment.GioDatHen, appointment.KhoaDonVi);
                if (hasConflict)
                {
                    _logger.LogWarning("Appointment time conflict detected for {Date} {Time}", 
                        appointment.NgayDatHen, appointment.GioDatHen);
                    return string.Empty;
                }

                // Generate new appointment ID and document number
                appointment.Khoa = Guid.NewGuid().ToString();
                appointment.SoChungTu = await GenerateDocumentNumberAsync(appointment.KhoaDonVi, appointment.NgayChungTu);
                appointment.NgayTao = DateTime.Now.ToString("yyyyMMdd");

                // Save appointment
                bool success = await _repository.SaveAsync(appointment);
                if (success)
                {
                    _logger.LogInformation("Created new appointment: {Khoa}", appointment.Khoa);
                    return appointment.Khoa;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating appointment");
                return string.Empty;
            }
        }

        public async Task<bool> UpdateAsync(DatLichHenDto appointment)
        {
            try
            {
                // Validate appointment data
                var validationResult = await ValidateAppointmentAsync(appointment);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Appointment validation failed: {Errors}", 
                        string.Join(", ", validationResult.ErrorMessages));
                    return false;
                }

                // Check for time conflicts (excluding current appointment)
                bool hasConflict = await CheckAppointmentConflictAsync(
                    appointment.NgayDatHen, appointment.GioDatHen, appointment.KhoaDonVi, appointment.Khoa);
                if (hasConflict)
                {
                    _logger.LogWarning("Appointment time conflict detected for {Date} {Time}", 
                        appointment.NgayDatHen, appointment.GioDatHen);
                    return false;
                }

                // Update timestamp
                appointment.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");

                // Save appointment
                bool success = await _repository.SaveAsync(appointment);
                if (success)
                {
                    _logger.LogInformation("Updated appointment: {Khoa}", appointment.Khoa);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating appointment: {Khoa}", appointment.Khoa);
                return false;
            }
        }

        public async Task<bool> DeleteAsync(string khoa)
        {
            try
            {
                if (string.IsNullOrEmpty(khoa))
                {
                    _logger.LogWarning("DeleteAsync called with empty khoa");
                    return false;
                }

                bool success = await _repository.DeleteAsync(khoa);
                if (success)
                {
                    _logger.LogInformation("Deleted appointment: {Khoa}", khoa);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting appointment: {Khoa}", khoa);
                return false;
            }
        }

        #endregion

        #region Appointment Queries

        public async Task<List<DatLichHenDto>> GetTodayAppointmentsAsync(string donViId)
        {
            try
            {
                return await _repository.GetTodayAppointmentsAsync(donViId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's appointments for donViId: {DonViId}", donViId);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetUpcomingAppointmentsAsync(string donViId)
        {
            try
            {
                return await _repository.GetUpcomingAppointmentsAsync(donViId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming appointments for donViId: {DonViId}", donViId);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByDateRangeAsync(string fromDate, string toDate, string donViId)
        {
            try
            {
                // Convert date format from YYYY-MM-DD to YYYYMMDD
                string formattedFromDate = fromDate.Replace("-", "");
                string formattedToDate = toDate.Replace("-", "");

                return await _repository.GetAppointmentsByDateRangeAsync(formattedFromDate, formattedToDate, donViId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by date range: {FromDate} to {ToDate}", fromDate, toDate);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByVehicleAsync(string khoaXe)
        {
            try
            {
                return await _repository.GetAppointmentsByVehicleAsync(khoaXe);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by vehicle: {KhoaXe}", khoaXe);
                return new List<DatLichHenDto>();
            }
        }

        public async Task<List<DatLichHenDto>> GetAppointmentsByCustomerAsync(string khoaKhachHang)
        {
            try
            {
                return await _repository.GetAppointmentsByCustomerAsync(khoaKhachHang);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by customer: {KhoaKhachHang}", khoaKhachHang);
                return new List<DatLichHenDto>();
            }
        }

        #endregion

        #region Reminder Operations

        public async Task<DataTable> GetReminderAppointmentsAsync(string donViId)
        {
            try
            {
                string condition = $" AND LH.KhoaDonVi = '{donViId}'";
                return await _repository.GetListNhacHenAsync(condition);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder appointments for donViId: {DonViId}", donViId);
                return new DataTable();
            }
        }

        public async Task<DataTable> GetOneDayReminderAppointmentsAsync(string donViId)
        {
            try
            {
                string tomorrow = DateTime.Now.AddDays(1).ToString("yyyyMMdd");
                string condition = $" AND LH.NgayDatHen = '{tomorrow}' AND LH.KhoaDonVi = '{donViId}'";
                return await _repository.GetListNhacHenTruoc1NgayAsync(condition);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting one-day reminder appointments for donViId: {DonViId}", donViId);
                return new DataTable();
            }
        }

        public async Task<bool> UpdateReminderCallStatusAsync(string khoa, string noiDungGoiNhacHen)
        {
            try
            {
                return await _repository.UpdateReminderCallStatusAsync(khoa, noiDungGoiNhacHen);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating reminder call status: {Khoa}", khoa);
                return false;
            }
        }

        #endregion

        #region Business Logic Methods

        public async Task<bool> CheckAppointmentConflictAsync(string ngayDatHen, string gioDatHen, string donViId, string excludeKhoa = "")
        {
            try
            {
                // Convert date format from YYYY-MM-DD to YYYYMMDD if needed
                string formattedDate = ngayDatHen.Replace("-", "");
                return await _repository.CheckAppointmentConflictAsync(formattedDate, gioDatHen, donViId, excludeKhoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking appointment conflict");
                return false;
            }
        }

        public async Task<GP.Mobile.Core.Interfaces.ValidationResult> ValidateAppointmentAsync(DatLichHenDto appointment)
        {
            var result = new GP.Mobile.Core.Interfaces.ValidationResult { IsValid = true };

            try
            {
                // Required field validations
                if (string.IsNullOrEmpty(appointment.KhoaXe))
                    result.ErrorMessages.Add("Vehicle ID is required");

                if (string.IsNullOrEmpty(appointment.KhoaKhachHang))
                    result.ErrorMessages.Add("Customer ID is required");

                if (string.IsNullOrEmpty(appointment.NgayDatHen))
                    result.ErrorMessages.Add("Appointment date is required");

                if (string.IsNullOrEmpty(appointment.GioDatHen))
                    result.ErrorMessages.Add("Appointment time is required");

                if (string.IsNullOrEmpty(appointment.KhoaDonVi))
                    result.ErrorMessages.Add("Branch/Unit ID is required");

                // Date validation
                if (!string.IsNullOrEmpty(appointment.NgayDatHen))
                {
                    if (appointment.NgayDatHen.Length != 8 || !appointment.NgayDatHen.All(char.IsDigit))
                        result.ErrorMessages.Add("Invalid appointment date format (YYYYMMDD required)");
                    else
                    {
                        // Check if appointment date is in the past
                        string today = DateTime.Now.ToString("yyyyMMdd");
                        if (string.Compare(appointment.NgayDatHen, today) < 0)
                            result.ErrorMessages.Add("Appointment date cannot be in the past");
                    }
                }

                // Time validation
                if (!string.IsNullOrEmpty(appointment.GioDatHen))
                {
                    if (!TimeSpan.TryParse(appointment.GioDatHen, out _))
                        result.ErrorMessages.Add("Invalid appointment time format (HH:mm required)");
                }

                result.IsValid = result.ErrorMessages.Count == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating appointment");
                result.IsValid = false;
                result.ErrorMessages.Add("Validation error occurred");
            }

            return result;
        }

        public async Task<string> GenerateDocumentNumberAsync(string donViId, string ngayChungTu)
        {
            try
            {
                // Simple document number generation - can be enhanced with proper sequence
                string datePrefix = ngayChungTu.Substring(2, 6); // YYMMDD
                string timeStamp = DateTime.Now.ToString("HHmmss");
                return $"LH{datePrefix}{timeStamp}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating document number");
                return $"LH{DateTime.Now:yyMMddHHmmss}";
            }
        }

        public async Task<string> ConvertToQuotationAsync(string khoa, string userId)
        {
            try
            {
                // Load appointment data
                var appointment = await _repository.LoadAsync(khoa);
                if (appointment == null)
                {
                    _logger.LogWarning("Appointment not found for conversion: {Khoa}", khoa);
                    return string.Empty;
                }

                // TODO: Implement quotation creation logic
                // This would involve creating a new BaoGia record based on appointment data
                _logger.LogInformation("Converting appointment to quotation: {Khoa}", khoa);

                // For now, return a placeholder quotation ID
                string quotationId = Guid.NewGuid().ToString();

                // Update appointment with quotation reference
                appointment.KhoaBaoGia = quotationId;
                appointment.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
                await _repository.SaveAsync(appointment);

                return quotationId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting appointment to quotation: {Khoa}", khoa);
                return string.Empty;
            }
        }

        public async Task<AppointmentStatisticsDto> GetAppointmentStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            try
            {
                var statistics = new AppointmentStatisticsDto();

                // Get appointments in date range
                var appointments = await GetAppointmentsByDateRangeAsync(fromDate, toDate, donViId);
                statistics.TotalAppointments = appointments.Count;

                // Get today's appointments
                var todayAppointments = await GetTodayAppointmentsAsync(donViId);
                statistics.TodayAppointments = todayAppointments.Count;

                // Get upcoming appointments
                var upcomingAppointments = await GetUpcomingAppointmentsAsync(donViId);
                statistics.UpcomingAppointments = upcomingAppointments.Count;

                // Calculate completed appointments (those with quotations)
                statistics.CompletedAppointments = appointments.Count(a => !string.IsNullOrEmpty(a.KhoaBaoGia));

                // Calculate average appointments per day
                if (DateTime.TryParseExact(fromDate, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var startDate) &&
                    DateTime.TryParseExact(toDate, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var endDate))
                {
                    int daysDiff = (endDate - startDate).Days + 1;
                    statistics.AverageAppointmentsPerDay = daysDiff > 0 ? (double)statistics.TotalAppointments / daysDiff : 0;
                }

                // Group by service type
                var serviceTypeGroups = appointments
                    .Where(a => !string.IsNullOrEmpty(a.TenLoaiDichVu))
                    .GroupBy(a => a.TenLoaiDichVu)
                    .Select(g => new AppointmentByServiceTypeDto
                    {
                        ServiceTypeName = g.Key ?? "Unknown",
                        Count = g.Count(),
                        Percentage = statistics.TotalAppointments > 0 ? (double)g.Count() / statistics.TotalAppointments * 100 : 0
                    })
                    .OrderByDescending(x => x.Count)
                    .ToList();

                statistics.AppointmentsByServiceType = serviceTypeGroups;

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment statistics");
                return new AppointmentStatisticsDto();
            }
        }

        public async Task<bool> SendReminderNotificationAsync(string khoa, ReminderType reminderType)
        {
            try
            {
                var appointment = await _repository.LoadAsync(khoa);
                if (appointment == null)
                {
                    _logger.LogWarning("Appointment not found for reminder: {Khoa}", khoa);
                    return false;
                }

                // TODO: Implement SMS/Email notification logic
                _logger.LogInformation("Sending reminder notification for appointment: {Khoa}, Type: {Type}",
                    khoa, reminderType);

                // Update reminder status
                string reminderContent = $"Reminder sent via {reminderType} at {DateTime.Now:yyyy-MM-dd HH:mm}";
                return await UpdateReminderCallStatusAsync(khoa, reminderContent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending reminder notification: {Khoa}", khoa);
                return false;
            }
        }

        public async Task<List<TimeSlotDto>> GetAvailableTimeSlotsAsync(string ngayDatHen, string donViId)
        {
            try
            {
                var timeSlots = new List<TimeSlotDto>();

                // Generate time slots from 8:00 to 17:00 (working hours)
                for (int hour = 8; hour <= 17; hour++)
                {
                    for (int minute = 0; minute < 60; minute += 30) // 30-minute intervals
                    {
                        string timeSlot = $"{hour:D2}:{minute:D2}";

                        // Check if this time slot has conflicts
                        bool hasConflict = await CheckAppointmentConflictAsync(ngayDatHen, timeSlot, donViId);

                        timeSlots.Add(new TimeSlotDto
                        {
                            Time = timeSlot,
                            IsAvailable = !hasConflict,
                            BookedCount = hasConflict ? 1 : 0,
                            MaxCapacity = 1 // Assuming 1 appointment per time slot
                        });
                    }
                }

                return timeSlots;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available time slots for date: {Date}", ngayDatHen);
                return new List<TimeSlotDto>();
            }
        }

        public async Task<bool> RescheduleAppointmentAsync(string khoa, string newNgayDatHen, string newGioDatHen, string reason)
        {
            try
            {
                var appointment = await _repository.LoadAsync(khoa);
                if (appointment == null)
                {
                    _logger.LogWarning("Appointment not found for rescheduling: {Khoa}", khoa);
                    return false;
                }

                // Check for conflicts at new time
                bool hasConflict = await CheckAppointmentConflictAsync(newNgayDatHen, newGioDatHen, appointment.KhoaDonVi, khoa);
                if (hasConflict)
                {
                    _logger.LogWarning("Time conflict for rescheduling appointment: {Khoa}", khoa);
                    return false;
                }

                // Update appointment with new date/time
                appointment.NgayDatHen = newNgayDatHen.Replace("-", "");
                appointment.GioDatHen = newGioDatHen;
                appointment.YeuCauKhachHang = $"{appointment.YeuCauKhachHang}\n[Rescheduled: {reason}]";
                appointment.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");

                bool success = await _repository.SaveAsync(appointment);
                if (success)
                {
                    _logger.LogInformation("Rescheduled appointment: {Khoa} to {Date} {Time}",
                        khoa, newNgayDatHen, newGioDatHen);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rescheduling appointment: {Khoa}", khoa);
                return false;
            }
        }

        public async Task<bool> CancelAppointmentAsync(string khoa, string reason, string userId)
        {
            try
            {
                var appointment = await _repository.LoadAsync(khoa);
                if (appointment == null)
                {
                    _logger.LogWarning("Appointment not found for cancellation: {Khoa}", khoa);
                    return false;
                }

                // Mark appointment as cancelled by adding cancellation info
                appointment.YeuCauKhachHang = $"{appointment.YeuCauKhachHang}\n[CANCELLED: {reason}]";
                appointment.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
                appointment.KhoaNhanVienCapNhat = userId;

                // Save the updated appointment
                bool success = await _repository.SaveAsync(appointment);
                if (success)
                {
                    _logger.LogInformation("Cancelled appointment: {Khoa}, Reason: {Reason}", khoa, reason);

                    // Optionally delete the appointment record
                    // await _repository.DeleteAsync(khoa);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling appointment: {Khoa}", khoa);
                return false;
            }
        }

        #endregion
    }
}
