/**
 * Complete Authentication Flow Test
 * Tests login and logout functionality with real API
 */

const testUsername = 'testuser';
const testPassword = 'test123';
const apiBaseUrl = 'http://localhost:5000';

async function testAuthenticationFlow() {
    console.log('🧪 Testing Complete Authentication Flow');
    console.log('=====================================\n');

    try {
        // Step 1: Get available clients for user
        console.log('1️⃣ Testing client retrieval...');
        const clientsResponse = await fetch(`${apiBaseUrl}/authentication/clients/${testUsername}`);
        
        if (!clientsResponse.ok) {
            throw new Error(`Failed to get clients: ${clientsResponse.status}`);
        }
        
        const clients = await clientsResponse.json();
        console.log(`✅ Found ${clients.length} available clients:`);
        clients.forEach(client => {
            console.log(`   - ${client.tenViet} (${client.khoa})`);
        });
        
        if (clients.length === 0) {
            throw new Error('No clients available for user');
        }
        
        const selectedClient = clients[0]; // Use first available client
        console.log(`📋 Selected client: ${selectedClient.tenViet}\n`);

        // Step 2: Test login
        console.log('2️⃣ Testing login...');
        const loginResponse = await fetch(`${apiBaseUrl}/authentication/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                Username: testUsername,
                Password: testPassword,
                ClientId: selectedClient.khoa,
                DeviceId: 'test-browser',
                DeviceType: 'web',
                RememberMe: false
            })
        });

        if (!loginResponse.ok) {
            const errorData = await loginResponse.json();
            throw new Error(`Login failed: ${errorData.message || 'Unknown error'}`);
        }

        const loginData = await loginResponse.json();
        console.log('✅ Login successful!');
        console.log(`   User: ${loginData.userInfo.employeeName} (${loginData.userInfo.username})`);
        console.log(`   Client: ${loginData.userInfo.clientName}`);
        console.log(`   Token: ${loginData.accessToken.substring(0, 20)}...`);
        console.log(`   Expires: ${loginData.expiresAt}\n`);

        // Step 3: Test logout
        console.log('3️⃣ Testing logout...');
        const logoutResponse = await fetch(`${apiBaseUrl}/authentication/logout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${loginData.accessToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (!logoutResponse.ok) {
            throw new Error(`Logout failed: ${logoutResponse.status}`);
        }

        const logoutData = await logoutResponse.json();
        console.log('✅ Logout successful!');
        console.log(`   Message: ${logoutData.message}\n`);

        // Step 4: Test authentication summary
        console.log('🎉 AUTHENTICATION FLOW TEST COMPLETE!');
        console.log('====================================');
        console.log('✅ Client retrieval: WORKING');
        console.log('✅ User login: WORKING');
        console.log('✅ Token generation: WORKING');
        console.log('✅ User logout: WORKING');
        console.log('\n🚀 Ready for frontend testing!');
        console.log('Frontend URL: http://localhost:3000/login');
        console.log(`Test credentials: ${testUsername} / ${testPassword}`);

    } catch (error) {
        console.error('❌ Authentication test failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('- Check if API is running on http://localhost:5000');
        console.log('- Verify database connection');
        console.log('- Ensure test user exists in database');
        return false;
    }

    return true;
}

// Run the test
testAuthenticationFlow();
