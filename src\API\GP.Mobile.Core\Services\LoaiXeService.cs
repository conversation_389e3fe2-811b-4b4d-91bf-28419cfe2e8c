using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for LoaiXe (Vehicle Type) service
/// Defines business logic operations for LoaiXe entity
/// AUTOMOTIVE FOCUSED - Essential for vehicle categorization and manufacturer linking
/// </summary>
public interface ILoaiXeService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadNameAsync(string tenViet);
    Task<bool> SaveAsync(LoaiXeDto dto, string action);
    Task<bool> DelDataAsync(string khoa);
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    Task<string> GetHangSanXuatAsync(string khoaLoaiXe);
    Task<string> GetKhoaAsync(string ma);
    Task<IEnumerable<MaLucDto>> GetMaLucListAsync(string khoaLoaiXe);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LoaiXeListDto>> GetAllAsync();
    Task<LoaiXeDto?> GetByIdAsync(string khoa);
    Task<LoaiXeDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateLoaiXeDto createDto);
    Task<bool> UpdateAsync(LoaiXeDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateLoaiXeStatusDto statusDto);
    Task<IEnumerable<LoaiXeListDto>> SearchAsync(LoaiXeSearchDto searchDto);
    Task<IEnumerable<LoaiXeLookupDto>> GetLookupAsync(string language = "vi");
    Task<LoaiXeValidationDto> ValidateAsync(string khoa, string ma);
    Task<LoaiXeSearchByCodeDto?> SearchVehicleByCodeAsync(string code, string condition = "");
    Task<IEnumerable<VehicleCategoryDto>> GetVehicleCategoriesAsync();
    Task<IEnumerable<LoaiXeWithManufacturerDto>> GetVehicleTypesWithManufacturerAsync();
    Task<LoaiXeStatsDto?> GetVehicleStatsAsync(string khoa);
    
    #endregion
}

/// <summary>
/// Complete Service for LoaiXe entity
/// Implements ALL business logic from clsDMLoaiXe.cs (577 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for vehicle categorization and manufacturer linking
/// </summary>
public class LoaiXeService : ILoaiXeService
{
    private readonly ILoaiXeRepository _repository;
    private readonly ILogger<LoaiXeService> _logger;

    public LoaiXeService(ILoaiXeRepository repository, ILogger<LoaiXeService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiXe");
            throw;
        }
    }

    public async Task<bool> LoadNameAsync(string tenViet)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(tenViet))
            {
                throw new ArgumentException("Tên loại xe không được để trống");
            }

            return await _repository.LoadNameAsync(tenViet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiXe by name");
            throw;
        }
    }

    public async Task<bool> SaveAsync(LoaiXeDto dto, string action)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto, action);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto, action);

            return await _repository.SaveAsync(dto, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving LoaiXe");
            throw;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa loại xe đã được sử dụng");
            }

            return await _repository.DelDataAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiXe");
            throw;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Apply security filters
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.ShowListAsync(secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiXe list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            return await _repository.ShowAllListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all LoaiXe list");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiXe by code");
            return "";
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fieldList);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.ShowListByFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiXe list by field");
            return new DataTable();
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                return false;
            }

            return await _repository.TrungMaAsync(ma, khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate vehicle type code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                return false;
            }

            return await _repository.WasUsedAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if LoaiXe was used");
            return true; // Return true to be safe
        }
    }

    public async Task<string> GetHangSanXuatAsync(string khoaLoaiXe)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaLoaiXe))
            {
                return "";
            }

            return await _repository.GetHangSanXuatAsync(khoaLoaiXe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer for vehicle type");
            return "";
        }
    }

    public async Task<string> GetKhoaAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                return "";
            }

            return await _repository.GetKhoaAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Khoa by Ma");
            return "";
        }
    }

    public async Task<IEnumerable<MaLucDto>> GetMaLucListAsync(string khoaLoaiXe)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaLoaiXe))
            {
                return new List<MaLucDto>();
            }

            return await _repository.GetMaLucListAsync(khoaLoaiXe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting MaLuc list");
            return new List<MaLucDto>();
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LoaiXeListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiXe records");
            return new List<LoaiXeListDto>();
        }
    }

    public async Task<LoaiXeDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe by ID");
            return null;
        }
    }

    public async Task<LoaiXeDto?> GetByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
                return null;

            return await _repository.GetByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe by code");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateLoaiXeDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiXe");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(LoaiXeDto dto)
    {
        return await SaveAsync(dto, "UPDATE");
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DelDataAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateLoaiXeStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiXe status");
            throw;
        }
    }

    public async Task<IEnumerable<LoaiXeListDto>> SearchAsync(LoaiXeSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiXe");
            return new List<LoaiXeListDto>();
        }
    }

    public async Task<IEnumerable<LoaiXeLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe lookup");
            return new List<LoaiXeLookupDto>();
        }
    }

    public async Task<LoaiXeValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiXe");
            return new LoaiXeValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<LoaiXeSearchByCodeDto?> SearchVehicleByCodeAsync(string code, string condition = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;

            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchVehicleByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vehicle by code");
            return null;
        }
    }

    public async Task<IEnumerable<VehicleCategoryDto>> GetVehicleCategoriesAsync()
    {
        try
        {
            return await _repository.GetVehicleCategoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle categories");
            return new List<VehicleCategoryDto>();
        }
    }

    public async Task<IEnumerable<LoaiXeWithManufacturerDto>> GetVehicleTypesWithManufacturerAsync()
    {
        try
        {
            return await _repository.GetVehicleTypesWithManufacturerAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle types with manufacturer");
            return new List<LoaiXeWithManufacturerDto>();
        }
    }

    public async Task<LoaiXeStatsDto?> GetVehicleStatsAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetVehicleStatsAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle stats");
            return null;
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic for Automotive Vehicles)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(LoaiXeDto dto, string action)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã loại xe không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên loại xe (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate vehicle type codes
        if (!string.IsNullOrEmpty(dto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(dto.Ma, dto.Khoa);
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại xe đã tồn tại");
            }
        }

        // Length validation
        if (dto.Ma.Length > 20)
            result.Errors.Add("Mã loại xe không được vượt quá 20 ký tự");

        if (dto.TenViet.Length > 200)
            result.Errors.Add("Tên loại xe (Tiếng Việt) không được vượt quá 200 ký tự");

        if (dto.TenAnh.Length > 200)
            result.Errors.Add("Tên loại xe (Tiếng Anh) không được vượt quá 200 ký tự");

        if (dto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Business validation
        if (dto.Active != 0 && dto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Vehicle type code format validation for automotive vehicles
        if (!string.IsNullOrEmpty(dto.Ma) && !IsValidVehicleCode(dto.Ma))
            result.Errors.Add("Mã loại xe phải là chữ cái và số, không có ký tự đặc biệt");

        // Manufacturer validation (if provided)
        if (!string.IsNullOrEmpty(dto.KhoaHangSanXuat))
        {
            // TODO: Validate manufacturer exists
            // var manufacturerExists = await ValidateManufacturerExistsAsync(dto.KhoaHangSanXuat);
            // if (!manufacturerExists)
            //     result.Errors.Add("Hãng sản xuất không tồn tại");
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateLoaiXeDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.Ma))
            result.Errors.Add("Mã loại xe không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên loại xe (Tiếng Việt) không được để trống");

        // Check for duplicate vehicle type codes
        if (!string.IsNullOrEmpty(createDto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(createDto.Ma, "");
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại xe đã tồn tại");
            }
        }

        // Length validation
        if (createDto.Ma.Length > 20)
            result.Errors.Add("Mã loại xe không được vượt quá 20 ký tự");

        if (createDto.TenViet.Length > 200)
            result.Errors.Add("Tên loại xe (Tiếng Việt) không được vượt quá 200 ký tự");

        if (createDto.TenAnh.Length > 200)
            result.Errors.Add("Tên loại xe (Tiếng Anh) không được vượt quá 200 ký tự");

        if (createDto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Vehicle type code format validation
        if (!string.IsNullOrEmpty(createDto.Ma) && !IsValidVehicleCode(createDto.Ma))
            result.Errors.Add("Mã loại xe phải là chữ cái và số, không có ký tự đặc biệt");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the vehicle type is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateLoaiXeStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (statusDto.Active != 0 && statusDto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Business rule: Cannot deactivate if being used
        if (statusDto.Active == 0)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động loại xe đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(LoaiXeDto dto, string action)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa) && action.ToUpper() == "INSERT")
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim().ToUpper(); // Vehicle codes should be uppercase
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        dto.Send = 0; // Not synchronized yet

        // Automotive vehicle specific business rules
        await ApplyAutomotiveVehicleRulesAsync(dto);
    }

    private async Task ApplyAutomotiveVehicleRulesAsync(LoaiXeDto dto)
    {
        // Automotive vehicle specific validations and rules
        var vehicleCode = dto.Ma.ToUpper();

        // Standardize common automotive vehicle type codes
        if (vehicleCode.Contains("SEDAN") || vehicleCode.Contains("CAR"))
        {
            // Ensure passenger vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe du lịch, xe con";
            }
        }
        else if (vehicleCode.Contains("SUV") || vehicleCode.Contains("CROSSOVER"))
        {
            // Ensure SUV vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe SUV, xe đa dụng";
            }
        }
        else if (vehicleCode.Contains("TRUCK") || vehicleCode.Contains("TAI"))
        {
            // Ensure truck vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe tải, xe chở hàng";
            }
        }
        else if (vehicleCode.Contains("MOTOR") || vehicleCode.Contains("BIKE"))
        {
            // Ensure motorcycle vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe máy, xe mô tô";
            }
        }
        else if (vehicleCode.Contains("VAN") || vehicleCode.Contains("MINIBUS"))
        {
            // Ensure van/minibus vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe van, xe khách nhỏ";
            }
        }
        else if (vehicleCode.Contains("BUS") || vehicleCode.Contains("COACH"))
        {
            // Ensure bus vehicles have proper categorization
            if (string.IsNullOrEmpty(dto.DienGiai))
            {
                dto.DienGiai = "Xe khách, xe buýt";
            }
        }
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show vehicle types user has access to
        // - Filter by user's dealership/branch

        return conditions;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DienGiai", "KhoaHangSanXuat",
            "KhoaNhanVienCapNhat", "Active", "Send"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    private bool IsValidVehicleCode(string code)
    {
        // Vehicle codes should be alphanumeric (letters and numbers only)
        if (string.IsNullOrEmpty(code))
            return false;

        return code.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-');
    }

    #endregion
}
