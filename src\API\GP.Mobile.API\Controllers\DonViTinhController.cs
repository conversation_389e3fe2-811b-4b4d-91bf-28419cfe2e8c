using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for DonViTinh (Unit of Measure) entity
/// Implements ALL endpoints from clsDMDonViTinh.cs (504 lines)
/// Includes REST API and 12+ legacy method endpoints
/// Maps to DM_DonViTinh table with 8 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DonViTinhController : ControllerBase
{
    private readonly IDonViTinhService _donViTinhService;
    private readonly ILogger<DonViTinhController> _logger;

    public DonViTinhController(IDonViTinhService donViTinhService, ILogger<DonViTinhController> logger)
    {
        _donViTinhService = donViTinhService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all DonViTinh records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DonViTinhListDto>>> GetAll()
    {
        try
        {
            var result = await _donViTinhService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all DonViTinh records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonViTinh by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<DonViTinhDto>> GetById(string khoa)
    {
        try
        {
            var result = await _donViTinhService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new DonViTinh
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateDonViTinhDto createDto)
    {
        try
        {
            var result = await _donViTinhService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo đơn vị tính");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DonViTinh");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update DonViTinh
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] DonViTinhDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _donViTinhService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonViTinh");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete DonViTinh
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _donViTinhService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DonViTinh");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update DonViTinh status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateDonViTinhStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _donViTinhService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonViTinh status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search DonViTinh records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<DonViTinhListDto>>> Search([FromBody] DonViTinhSearchDto searchDto)
    {
        try
        {
            var result = await _donViTinhService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonViTinh");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonViTinh lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<DonViTinhLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _donViTinhService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate DonViTinh data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<DonViTinhValidationDto>> Validate([FromBody] DonViTinhValidationRequestDto request)
    {
        try
        {
            var result = await _donViTinhService.ValidateAsync(request.Khoa, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating DonViTinh");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _donViTinhService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadName method endpoint
    /// </summary>
    [HttpPost("loadname")]
    public async Task<ActionResult<bool>> LoadName([FromBody] string tenViet)
    {
        try
        {
            var result = await _donViTinhService.LoadNameAsync(tenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadName endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] SaveDonViTinhRequestDto request)
    {
        try
        {
            var result = await _donViTinhService.SaveAsync(request.Data, request.Action);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetList method endpoint
    /// </summary>
    [HttpPost("getlist")]
    public async Task<ActionResult<DataTable>> GetList()
    {
        try
        {
            var result = await _donViTinhService.GetListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] ShowListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _donViTinhService.ShowListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListField method endpoint
    /// </summary>
    [HttpPost("getlistfield")]
    public async Task<ActionResult<DataTable>> GetListField([FromBody] GetListFieldRequestDto request)
    {
        try
        {
            var result = await _donViTinhService.GetListFieldAsync(request.Fields, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] ShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _donViTinhService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy IsDuplicateCode method endpoint
    /// </summary>
    [HttpPost("isduplicatecode")]
    public async Task<ActionResult<bool>> IsDuplicateCode([FromBody] IsDuplicateCodeRequestDto request)
    {
        try
        {
            var result = await _donViTinhService.IsDuplicateCodeAsync(request.Khoa, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IsDuplicateCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _donViTinhService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByName method endpoint
    /// </summary>
    [HttpPost("searchbyname")]
    public async Task<ActionResult<string>> SearchByName([FromBody] string name)
    {
        try
        {
            var result = await _donViTinhService.SearchByNameAsync(name);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByName endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for DonViTinh

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class DonViTinhValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class SaveDonViTinhRequestDto
{
    public DonViTinhDto Data { get; set; } = new();
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class ShowListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetListField method
/// </summary>
public class GetListFieldRequestDto
{
    public string Fields { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class ShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for IsDuplicateCode method
/// </summary>
public class IsDuplicateCodeRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

#endregion
