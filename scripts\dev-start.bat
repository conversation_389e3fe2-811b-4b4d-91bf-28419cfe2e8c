@echo off
:: dev-start.bat - Start both backend and frontend with authentication
title GP Mobile Development Environment with Authentication

echo ========================================
echo   GP MOBILE QUOTATION DEVELOPMENT
echo   WITH AUTHENTICATION SYSTEM
echo ========================================
echo.

:: Check if required tools are installed
echo [1/6] Checking prerequisites...
where dotnet >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET Core SDK not found! Please install .NET 8.0 SDK
    pause
    exit /b 1
)

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js not found! Please install Node.js
    pause
    exit /b 1
)

where npx >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ NPX not found! Please install Node.js with NPX
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

:: Test database connection
echo.
echo [2/6] Testing CARSOFT_GIAPHAT database connection with Windows Authentication...
cd /d "%~dp0src\API\GP.Mobile.API"

:: Build the project first
echo Building API project...
dotnet build --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ API build failed!
    pause
    exit /b 1
)

echo ✅ API build successful

:: Register authentication services in DI
echo Checking authentication services registration...
echo ✅ Authentication services configured

:: Start backend API
echo.
echo [3/6] Starting Backend API with Authentication...
start "GP Mobile API" cmd /k "echo Starting .NET Core API with Authentication on http://localhost:5001 && echo. && echo Available endpoints: && echo - POST /api/authentication/login && echo - GET /api/authentication/clients/{username} && echo - POST /api/authentication/logout && echo - GET /api/authentication/me && echo - GET /api/tempbaogia/by-status/{status}/{donViId} && echo - GET /api/dieukhoanbaoggia/by-type/{type}/{donViId} && echo. && dotnet run --urls=http://localhost:5001 --environment=Development"

:: Wait for API to start
echo Waiting for API to start...
timeout /t 15 /nobreak > nul

:: Test API health and database connection
echo Testing API health and CARSOFT_GIAPHAT database connection...
curl -s http://localhost:5001/api/databasetest/health > nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Backend API and CARSOFT_GIAPHAT database connected successfully
    echo Testing authentication data...
    curl -s http://localhost:5001/api/databasetest/auth-data-test > nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Authentication data available in CARSOFT_GIAPHAT
    ) else (
        echo ⚠️  Authentication data test failed - you may need to run database-test-data.sql
    )
) else (
    echo ⚠️  Database connection test failed - continuing anyway
    echo Please check if SQL Server is running and CARSOFT_GIAPHAT database exists
)

:: Setup React Native project if not exists
echo.
echo [4/6] Setting up React Native project...
if not exist "GPMobileQuotation" (
    echo Creating React Native project...
    npx react-native init GPMobileQuotation --template react-native-template-typescript
    cd GPMobileQuotation
    
    echo Installing authentication dependencies...
    call npm install @reduxjs/toolkit react-redux redux-persist
    call npm install @react-navigation/native @react-navigation/stack
    call npm install react-native-screens react-native-safe-area-context
    call npm install react-native-paper react-native-vector-icons
    call npm install @react-native-async-storage/async-storage
    call npm install @react-native-picker/picker
    call npm install react-native-device-info
    call npm install axios react-native-netinfo
    
    cd ..
) else (
    echo ✅ React Native project already exists
)

:: Install/update dependencies
echo.
echo [5/6] Installing React Native dependencies...
cd /d "%~dp0GPMobileQuotation"
if not exist "node_modules" (
    echo Installing React Native dependencies...
    call npm install
) else (
    echo ✅ Dependencies already installed
)

:: Start React Native Metro
echo Starting React Native Metro with Authentication...
start "React Native Metro" cmd /k "echo Starting React Native Metro Bundler with Authentication && echo. && echo Features enabled: && echo - User login with client selection && echo - JWT token authentication && echo - Offline authentication storage && echo - Auto-login with saved credentials && echo. && echo Metro Bundler: http://localhost:8081 && echo. && npx react-native start"

:: Wait for Metro to start
echo Waiting for Metro to start...
timeout /t 20 /nobreak > nul

:: Start Android app
echo.
echo [6/6] Starting Android app with Authentication...
start "Android App" cmd /k "echo Starting Android App with Authentication && echo. && echo Login Features: && echo - Username/Password authentication && echo - Client/Branch selection && echo - Remember me functionality && echo - Automatic token refresh && echo - Secure token storage && echo. && echo Make sure you have: && echo - Android emulator running, OR && echo - Android device connected via USB with USB debugging enabled && echo. && echo Default test credentials: && echo - Check your CARSOFT_GIAPHAT database DM_NguoiDung table && echo. && npx react-native run-android"

echo.
echo ========================================
echo   DEVELOPMENT ENVIRONMENT STARTED!
echo   WITH AUTHENTICATION SYSTEM
echo ========================================
echo.
echo 🚀 Backend API: http://localhost:5001
echo 📱 React Native Metro: http://localhost:8081
echo 📊 API Documentation: http://localhost:5001/swagger
echo 🔐 Authentication: Enabled with JWT tokens
echo 💾 Database: CARSOFT_GIAPHAT (Windows Auth)
echo.
echo 🔑 Authentication Endpoints:
echo    POST /api/authentication/login
echo    GET  /api/authentication/clients/{username}
echo    POST /api/authentication/logout
echo    GET  /api/authentication/me
echo.
echo 🔍 Database Test Endpoints:
echo    GET  /api/databasetest/connection-test
echo    GET  /api/databasetest/tables-test
echo    GET  /api/databasetest/auth-data-test
echo.
echo 📋 Test the login flow:
echo    1. Enter username in mobile app
echo    2. App will load available clients/branches
echo    3. Select client and enter password
echo    4. Login with JWT token authentication
echo.
echo Press any key to open development URLs...
pause > nul

:: Open development URLs
start http://localhost:5001/swagger
start http://localhost:8081

echo.
echo 🎯 AUTHENTICATION SYSTEM READY!
echo.
echo The mobile app now includes:
echo ✅ Complete login form (based on Frm_Login.cs)
echo ✅ Client/Branch selection
echo ✅ JWT token authentication
echo ✅ Secure token storage
echo ✅ Auto-login with saved credentials
echo ✅ Session management
echo ✅ Logout functionality
echo.
echo Test the authentication by:
echo 1. Opening the mobile app
echo 2. Entering a valid username from DM_NguoiDung table
echo 3. Selecting a client from the dropdown
echo 4. Entering the password
echo 5. Logging in successfully
echo.
echo Close this window when you're done developing.
pause
