using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Repository interface for Appointment Scheduling (Đặt Lịch Hẹn) data access
    /// Provides methods for managing appointment data in SC_DatLichHen table
    /// </summary>
    public interface IDatLichHenRepository
    {
        /// <summary>
        /// Load appointment by primary key
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <returns>Appointment data or null if not found</returns>
        Task<DatLichHenDto?> LoadAsync(string khoa);

        /// <summary>
        /// Load appointment by related quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Appointment data or null if not found</returns>
        Task<DatLichHenDto?> LoadByBaoGiaAsync(string khoaBaoGia);

        /// <summary>
        /// Save appointment data (insert or update)
        /// </summary>
        /// <param name="appointment">Appointment data to save</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveAsync(DatLichHenDto appointment);

        /// <summary>
        /// Delete appointment by ID
        /// </summary>
        /// <param name="khoa">Appointment ID to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(string khoa);

        /// <summary>
        /// Get list of appointments with reminder notifications
        /// </summary>
        /// <param name="condition">Additional SQL WHERE conditions</param>
        /// <returns>DataTable with appointment reminder list</returns>
        Task<DataTable> GetListNhacHenAsync(string condition = "");

        /// <summary>
        /// Get list of appointments requiring reminder calls (1 day before)
        /// </summary>
        /// <param name="condition">Additional SQL WHERE conditions</param>
        /// <returns>DataTable with appointments needing reminders</returns>
        Task<DataTable> GetListNhacHenTruoc1NgayAsync(string condition = "");

        /// <summary>
        /// Get general appointment list
        /// </summary>
        /// <param name="condition">Additional SQL WHERE conditions</param>
        /// <returns>DataTable with appointment list</returns>
        Task<DataTable> GetListAsync(string condition = "");

        /// <summary>
        /// Get detailed appointment report list
        /// </summary>
        /// <param name="condition">Additional SQL WHERE conditions</param>
        /// <returns>DataTable with detailed appointment information</returns>
        Task<DataTable> GetListBangKeDatLichHenAsync(string condition = "");

        /// <summary>
        /// Show appointment list with basic information
        /// </summary>
        /// <param name="condition">Additional SQL WHERE conditions</param>
        /// <returns>DataTable with basic appointment list</returns>
        Task<DataTable> ShowListAsync(string condition = "");

        /// <summary>
        /// Show all appointments (including inactive)
        /// </summary>
        /// <returns>DataTable with all appointments</returns>
        Task<DataTable> ShowAllListAsync();

        /// <summary>
        /// Search appointment by code
        /// </summary>
        /// <param name="code">Appointment code to search</param>
        /// <param name="condition">Additional search conditions</param>
        /// <returns>Formatted string with appointment info (Khoa|Ma|Ten)</returns>
        Task<string> SearchByCodeAsync(string code = "", string condition = "");

        /// <summary>
        /// Get appointments for today (for mobile app dashboard)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of today's appointments</returns>
        Task<List<DatLichHenDto>> GetTodayAppointmentsAsync(string donViId);

        /// <summary>
        /// Get upcoming appointments (next 7 days)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of upcoming appointments</returns>
        Task<List<DatLichHenDto>> GetUpcomingAppointmentsAsync(string donViId);

        /// <summary>
        /// Get appointments by date range
        /// </summary>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of appointments in date range</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByDateRangeAsync(string fromDate, string toDate, string donViId);

        /// <summary>
        /// Get appointments by vehicle
        /// </summary>
        /// <param name="khoaXe">Vehicle ID</param>
        /// <returns>List of appointments for the vehicle</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByVehicleAsync(string khoaXe);

        /// <summary>
        /// Get appointments by customer
        /// </summary>
        /// <param name="khoaKhachHang">Customer ID</param>
        /// <returns>List of appointments for the customer</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByCustomerAsync(string khoaKhachHang);

        /// <summary>
        /// Update reminder call status
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="noiDungGoiNhacHen">Reminder call content</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateReminderCallStatusAsync(string khoa, string noiDungGoiNhacHen);

        /// <summary>
        /// Check for appointment conflicts (same time slot)
        /// </summary>
        /// <param name="ngayDatHen">Appointment date</param>
        /// <param name="gioDatHen">Appointment time</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="excludeKhoa">Exclude this appointment ID from conflict check</param>
        /// <returns>True if conflict exists, false otherwise</returns>
        Task<bool> CheckAppointmentConflictAsync(string ngayDatHen, string gioDatHen, string donViId, string excludeKhoa = "");
    }
}
