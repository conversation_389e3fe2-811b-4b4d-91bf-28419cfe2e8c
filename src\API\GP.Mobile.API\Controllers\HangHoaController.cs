using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for HangHoa (Products/Parts) entity
/// Implements ALL endpoints from clsDMHangHoa.cs (2611 lines)
/// Includes REST API and 30+ legacy method endpoints
/// Maps to DM_HangHoa table with 60+ properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and product catalog
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HangHoaController : ControllerBase
{
    private readonly IHangHoaService _hangHoaService;
    private readonly ILogger<HangHoaController> _logger;

    public HangHoaController(IHangHoaService hangHoaService, ILogger<HangHoaController> logger)
    {
        _hangHoaService = hangHoaService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all products
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<HangHoaListDto>>> GetAll()
    {
        try
        {
            var result = await _hangHoaService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all products");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<HangHoaDto>> GetById(string khoa)
    {
        try
        {
            var result = await _hangHoaService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product by code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<HangHoaDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _hangHoaService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new product
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateHangHoaDto createDto)
    {
        try
        {
            var result = await _hangHoaService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo sản phẩm");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update product
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] HangHoaDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _hangHoaService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete product
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _hangHoaService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update product status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateHangHoaStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _hangHoaService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search products
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<HangHoaListDto>>> Search([FromBody] HangHoaSearchDto searchDto)
    {
        try
        {
            var result = await _hangHoaService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching products");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<HangHoaLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _hangHoaService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate product data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<HangHoaValidationDto>> Validate([FromBody] HangHoaValidationRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.ValidateAsync(request.Khoa, request.Ma, request.MaHangNhapKhau, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating product");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search product by code (modern)
    /// </summary>
    [HttpPost("search-by-code")]
    public async Task<ActionResult<HangHoaSearchByCodeDto>> SearchByCodeModern([FromBody] HangHoaSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.SearchByCodeModernAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching product by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive parts categories
    /// </summary>
    [HttpGet("automotive-categories")]
    public async Task<ActionResult<IEnumerable<AutomotivePartsCategoryDto>>> GetAutomotivePartsCategories()
    {
        try
        {
            var result = await _hangHoaService.GetAutomotivePartsCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive parts categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get products with inventory summary
    /// </summary>
    [HttpGet("with-inventory")]
    public async Task<ActionResult<IEnumerable<HangHoaWithInventoryDto>>> GetProductsWithInventory()
    {
        try
        {
            var result = await _hangHoaService.GetProductsWithInventoryAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting products with inventory");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get product pricing information
    /// </summary>
    [HttpGet("{khoa}/pricing")]
    public async Task<ActionResult<IEnumerable<HangHoaPricingDto>>> GetProductPricing(string khoa)
    {
        try
        {
            var result = await _hangHoaService.GetProductPricingAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product pricing");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle products
    /// </summary>
    [HttpGet("vehicles")]
    public async Task<ActionResult<IEnumerable<VehicleProductDto>>> GetVehicleProducts()
    {
        try
        {
            var result = await _hangHoaService.GetVehicleProductsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle products");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get parts compatibility
    /// </summary>
    [HttpGet("compatibility/{khoaLoaiXe}/{doiXe}")]
    public async Task<ActionResult<IEnumerable<PartsCompatibilityDto>>> GetPartsCompatibility(string khoaLoaiXe, string doiXe)
    {
        try
        {
            var result = await _hangHoaService.GetPartsCompatibilityAsync(khoaLoaiXe, doiXe);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting parts compatibility");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _hangHoaService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] HangHoaDto dto)
    {
        try
        {
            var result = await _hangHoaService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy Delete method endpoint
    /// </summary>
    [HttpPost("delete")]
    public async Task<ActionResult<bool>> DeleteLegacy([FromBody] string khoa)
    {
        try
        {
            var result = await _hangHoaService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Delete endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult<bool>> ClearTemp([FromBody] string keyTable)
    {
        try
        {
            var result = await _hangHoaService.ClearTempAsync(keyTable);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UpdateHinhAnh method endpoint
    /// </summary>
    [HttpPost("updatehinhanh")]
    public async Task<ActionResult<bool>> UpdateHinhAnh([FromBody] HangHoaUpdateImageRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.UpdateHinhAnhAsync(request.Khoa, request.HinhAnh);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateHinhAnh endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy LoadGiaBan method endpoint
    /// </summary>
    [HttpPost("loadgiaban")]
    public async Task<ActionResult<bool>> LoadGiaBan([FromBody] HangHoaLoadGiaBanRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.LoadGiaBanAsync(request.Khoa, request.KhoaLoaiGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadGiaBan endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListGiaBan method endpoint
    /// </summary>
    [HttpPost("getlistgiaban")]
    public async Task<ActionResult<DataTable>> GetListGiaBan([FromBody] string khoaHangHoa)
    {
        try
        {
            var result = await _hangHoaService.GetListGiaBanAsync(khoaHangHoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListGiaBan endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadTonKho method endpoint
    /// </summary>
    [HttpPost("loadtonkho")]
    public async Task<ActionResult<bool>> LoadTonKho([FromBody] HangHoaLoadTonKhoRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.LoadTonKhoAsync(request.KhoaHangHoa, request.KhoaKho, request.KhoaQuay, request.SoLoHang, request.NgayNhap, request.HanSuDung, request.SoSeri, request.NamThang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadTonKho endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadGiaNhapGanNhat method endpoint
    /// </summary>
    [HttpPost("loadgianhapgannhat")]
    public async Task<ActionResult<double>> LoadGiaNhapGanNhat([FromBody] string khoaHangHoa)
    {
        try
        {
            var result = await _hangHoaService.LoadGiaNhapGanNhatAsync(khoaHangHoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadGiaNhapGanNhat endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadTonKhoThucTe method endpoint
    /// </summary>
    [HttpPost("loadtonkhothucte")]
    public async Task<ActionResult<double>> LoadTonKhoThucTe([FromBody] HangHoaLoadTonKhoThucTeRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.LoadTonKhoThucTeAsync(request.KhoaHangHoa, request.NamThang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadTonKhoThucTe endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SaveChiTietPhuTungThietBi method endpoint
    /// </summary>
    [HttpPost("savechitietphutungthietbi")]
    public async Task<ActionResult<bool>> SaveChiTietPhuTungThietBi([FromBody] HangHoaSaveChiTietPhuTungRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.SaveChiTietPhuTungThietBiAsync(request.Khoa, request.KhoaThietBi, request.KhoaPhuTung, request.KhoaDVT, request.QuyCach);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveChiTietPhuTungThietBi endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ClearPhuTungThietBi method endpoint
    /// </summary>
    [HttpPost("clearphutungthietbi")]
    public async Task<ActionResult<bool>> ClearPhuTungThietBi([FromBody] string khoa)
    {
        try
        {
            var result = await _hangHoaService.ClearPhuTungThietBiAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearPhuTungThietBi endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetPhuTung method endpoint
    /// </summary>
    [HttpPost("getphutung")]
    public async Task<ActionResult<DataTable>> GetPhuTung([FromBody] string khoaThietBi)
    {
        try
        {
            var result = await _hangHoaService.GetPhuTungAsync(khoaThietBi);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPhuTung endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] HangHoaShowListRequestDto? request = null)
    {
        try
        {
            var conditions = request?.Conditions ?? "";
            var result = await _hangHoaService.ShowListAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] HangHoaSearchByCodeLegacyRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.SearchByCodeAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy isDupplicateCode method endpoint
    /// </summary>
    [HttpPost("isdupplicatecode")]
    public async Task<ActionResult<bool>> IsDupplicateCode([FromBody] HangHoaIsDupplicateCodeRequestDto request)
    {
        try
        {
            var result = await _hangHoaService.isDupplicateCodeAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IsDupplicateCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _hangHoaService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for HangHoa

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class HangHoaValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string MaHangNhapKhau { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for search by code (modern) endpoint
/// </summary>
public class HangHoaSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateHinhAnh method
/// </summary>
public class HangHoaUpdateImageRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public byte[] HinhAnh { get; set; } = Array.Empty<byte>();
}

/// <summary>
/// Request DTO for LoadGiaBan method
/// </summary>
public class HangHoaLoadGiaBanRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiGia { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for LoadTonKho method
/// </summary>
public class HangHoaLoadTonKhoRequestDto
{
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string KhoaKho { get; set; } = string.Empty;
    public string KhoaQuay { get; set; } = string.Empty;
    public string SoLoHang { get; set; } = string.Empty;
    public string NgayNhap { get; set; } = string.Empty;
    public string HanSuDung { get; set; } = string.Empty;
    public string SoSeri { get; set; } = string.Empty;
    public string NamThang { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for LoadTonKhoThucTe method
/// </summary>
public class HangHoaLoadTonKhoThucTeRequestDto
{
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string NamThang { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SaveChiTietPhuTungThietBi method
/// </summary>
public class HangHoaSaveChiTietPhuTungRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaThietBi { get; set; } = string.Empty;
    public string KhoaPhuTung { get; set; } = string.Empty;
    public string KhoaDVT { get; set; } = string.Empty;
    public int QuyCach { get; set; } = 0;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class HangHoaShowListRequestDto
{
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method (legacy)
/// </summary>
public class HangHoaSearchByCodeLegacyRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for IsDupplicateCode method
/// </summary>
public class HangHoaIsDupplicateCodeRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

#endregion
