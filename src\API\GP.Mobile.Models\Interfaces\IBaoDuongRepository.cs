using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Models.Interfaces
{
    /// <summary>
    /// Repository interface for BaoDuong (Maintenance Service) operations
    /// Defines data access operations for BaoDuong entity
    /// AUTOMOTIVE FOCUSED - Essential for maintenance service templates linking vehicle types with service types
    /// </summary>
    public interface IBaoDuongRepository
    {
        #region Legacy Methods
        
        Task<bool> LoadAsync(string khoa);
        Task<bool> SaveAsync(BaoDuongDto dto);
        Task<bool> DelDataAsync(string khoa);
        Task SetBlankAsync();
        Task<bool> IsDupllicateAsync(string soChungTu, string khoa);
        Task<bool> CheckExistsAsync(string khoaLoaiXe, string khoaLoaiDichVu);
        Task ClearTempAsync(string khoa);
        Task<DataTable> GetListDMBDAsync(string condition = "");
        Task<DataTable> GetDetailsDMBDAsync(string khoaBaoGia);
        Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiDichVu);
        Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiXe, string khoaLoaiDichVu);
        Task<DataTable> GetDataPrintYeuCauAsync(string khoa);
        
        #endregion

        #region Modern API Methods
        
        Task<IEnumerable<BaoDuongListDto>> GetAllAsync();
        Task<BaoDuongDto?> GetByIdAsync(string khoa);
        Task<BaoDuongDto?> GetByVehicleAndServiceTypeAsync(string khoaLoaiXe, string khoaLoaiDichVu);
        Task<string> CreateAsync(CreateBaoDuongDto createDto);
        Task<bool> UpdateStatusAsync(UpdateBaoDuongStatusDto statusDto);
        Task<IEnumerable<BaoDuongListDto>> SearchAsync(BaoDuongSearchDto searchDto);
        Task<IEnumerable<BaoDuongLookupDto>> GetLookupAsync();
        Task<BaoDuongValidationDto> ValidateAsync(string khoa, string khoaLoaiXe, string khoaLoaiDichVu);
        Task<IEnumerable<BaoDuongChiTietDto>> GetMaintenanceDetailsAsync(string khoa);
        Task<IEnumerable<MaintenanceCategoryDto>> GetMaintenanceCategoriesAsync();
        Task<IEnumerable<BaoDuongWithDetailsDto>> GetMaintenanceWithDetailsAsync();
        Task<BaoDuongStatsDto?> GetMaintenanceStatsAsync(string khoa);
        Task<IEnumerable<BaoDuongPrintDto>> GetPrintDataAsync(string khoa);
        
        #endregion
    }
}
