using System;
using System.IO;
using System.Linq;

namespace GP.Mobile.SimpleGenerator
{
    /// <summary>
    /// Simple automation tool to demonstrate the concept
    /// Generates basic structure for priority classes
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("GP Mobile Simple Code Generator");
            Console.WriteLine("==============================");
            Console.WriteLine();

            try
            {
                var currentDir = Directory.GetCurrentDirectory();
                var projectRoot = FindProjectRoot(currentDir);
                
                if (string.IsNullOrEmpty(projectRoot))
                {
                    Console.WriteLine("Error: Could not find project root");
                    return;
                }

                Console.WriteLine($"Project Root: {projectRoot}");
                
                var legacyPath = Path.Combine(projectRoot, "Base", "Business");
                var modernPath = projectRoot;

                if (!Directory.Exists(legacyPath))
                {
                    Console.WriteLine($"Error: Legacy path not found: {legacyPath}");
                    return;
                }

                // Generate priority classes
                var priorityClasses = new[] { "clsBaoGia", "clsHoaDon", "clsNhapKho", "clsXuatKho", "clsThanhToan" };
                
                Console.WriteLine("Generating priority classes...");
                Console.WriteLine();

                foreach (var className in priorityClasses)
                {
                    var legacyFile = Path.Combine(legacyPath, $"{className}.cs");
                    
                    if (File.Exists(legacyFile))
                    {
                        Console.WriteLine($"Processing {className}...");
                        GenerateBasicStructure(className, modernPath);
                        Console.WriteLine($"  Generated basic structure for {className}");
                    }
                    else
                    {
                        Console.WriteLine($"  Skipping {className} - file not found");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("Generation completed!");
                Console.WriteLine();
                Console.WriteLine("Next steps:");
                Console.WriteLine("1. Review generated files in src/API/ folders");
                Console.WriteLine("2. Add detailed implementations based on legacy code");
                Console.WriteLine("3. Build and test the API");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static void GenerateBasicStructure(string className, string projectRoot)
        {
            var entityName = className.Replace("cls", "");
            
            // Generate DTO
            GenerateDto(entityName, projectRoot);
            
            // Generate Repository
            GenerateRepository(entityName, projectRoot);
            
            // Generate Service
            GenerateService(entityName, projectRoot);
            
            // Generate Controller
            GenerateController(entityName, projectRoot);
        }

        static void GenerateDto(string entityName, string projectRoot)
        {
            var dtoPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Models", "DTOs");
            Directory.CreateDirectory(dtoPath);

            var content = $@"using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// DTO for {entityName} entity - Generated by automation
/// TODO: Add all properties from legacy cls{entityName}.cs
/// </summary>
public class {entityName}Dto
{{
    public string Khoa {{ get; set; }} = string.Empty;
    
    [Required]
    public string SoChungTu {{ get; set; }} = string.Empty;
    
    public DateTime NgayChungTu {{ get; set; }} = DateTime.Now;
    
    public string GhiChu {{ get; set; }} = string.Empty;
    
    public int Active {{ get; set; }} = 1;
    
    // TODO: Add remaining properties from legacy class
}}

/// <summary>
/// List DTO for {entityName} entity
/// </summary>
public class {entityName}ListDto
{{
    public string Khoa {{ get; set; }} = string.Empty;
    public string SoChungTu {{ get; set; }} = string.Empty;
    public DateTime NgayChungTu {{ get; set; }}
    public string GhiChu {{ get; set; }} = string.Empty;
}}

/// <summary>
/// Create DTO for {entityName} entity
/// </summary>
public class Create{entityName}Dto
{{
    [Required]
    public string SoChungTu {{ get; set; }} = string.Empty;
    
    public DateTime NgayChungTu {{ get; set; }} = DateTime.Now;
    
    public string GhiChu {{ get; set; }} = string.Empty;
    
    // TODO: Add remaining creation properties
}}
";

            File.WriteAllText(Path.Combine(dtoPath, $"{entityName}Dto.cs"), content);
        }

        static void GenerateRepository(string entityName, string projectRoot)
        {
            var repoPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Data", "Repositories");
            Directory.CreateDirectory(repoPath);

            var content = $@"using Dapper;
using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Repository for {entityName} entity - Generated by automation
/// TODO: Implement all methods from legacy cls{entityName}.cs with exact SQL
/// </summary>
public interface I{entityName}Repository
{{
    Task<bool> LoadAsync(string pKhoa);
    Task<bool> SaveAsync({entityName}Dto dto);
    Task<bool> DelDataAsync(string pKhoa);
    Task<DataTable> ShowListAsync(string strConditions = """");
    Task<IEnumerable<{entityName}ListDto>> GetAllAsync();
    Task<{entityName}Dto?> GetByIdAsync(string khoa);
    Task<string> CreateAsync(Create{entityName}Dto createDto);
    Task<bool> UpdateAsync({entityName}Dto dto);
    Task<bool> DeleteAsync(string khoa);
}}

public class {entityName}Repository : I{entityName}Repository
{{
    private readonly IDbConnection _connection;

    public {entityName}Repository(IDbConnection connection)
    {{
        _connection = connection;
    }}

    public async Task<bool> LoadAsync(string pKhoa)
    {{
        try
        {{
            // TODO: Implement exact SQL from legacy Load method
            string commandText = ""SELECT * FROM [TableName] WHERE Khoa = @Khoa"";
            var result = await _connection.QueryFirstOrDefaultAsync(commandText, new {{ Khoa = pKhoa }});
            return result != null;
        }}
        catch (Exception)
        {{
            return false;
        }}
    }}

    public async Task<bool> SaveAsync({entityName}Dto dto)
    {{
        try
        {{
            // TODO: Implement exact stored procedure call from legacy Save method
            var parameters = new DynamicParameters();
            parameters.Add(""@Khoa"", dto.Khoa);
            parameters.Add(""@SoChungTu"", dto.SoChungTu);
            // TODO: Add all parameters from legacy stored procedure
            
            int result = await _connection.ExecuteAsync(""[StoredProcedureName]"", parameters, commandType: CommandType.StoredProcedure);
            return result > 0;
        }}
        catch (Exception)
        {{
            return false;
        }}
    }}

    public async Task<bool> DelDataAsync(string pKhoa)
    {{
        try
        {{
            // TODO: Implement exact SQL from legacy DelData method
            string commandText = ""DELETE FROM [TableName] WHERE Khoa = @Khoa"";
            int result = await _connection.ExecuteAsync(commandText, new {{ Khoa = pKhoa }});
            return result > 0;
        }}
        catch (Exception)
        {{
            return false;
        }}
    }}

    public async Task<DataTable> ShowListAsync(string strConditions = """")
    {{
        try
        {{
            // TODO: Implement exact SQL from legacy ShowList method
            string commandText = ""SELECT * FROM [TableName] WHERE 1=1"";
            if (!string.IsNullOrEmpty(strConditions))
                commandText += "" AND "" + strConditions;
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }}
        catch (Exception)
        {{
            return new DataTable();
        }}
    }}

    // TODO: Implement remaining methods with exact legacy SQL

    public async Task<IEnumerable<{entityName}ListDto>> GetAllAsync()
    {{
        // TODO: Implement modern GetAll method
        return new List<{entityName}ListDto>();
    }}

    public async Task<{entityName}Dto?> GetByIdAsync(string khoa)
    {{
        // TODO: Implement modern GetById method
        return null;
    }}

    public async Task<string> CreateAsync(Create{entityName}Dto createDto)
    {{
        // TODO: Implement modern Create method
        return string.Empty;
    }}

    public async Task<bool> UpdateAsync({entityName}Dto dto)
    {{
        return await SaveAsync(dto);
    }}

    public async Task<bool> DeleteAsync(string khoa)
    {{
        return await DelDataAsync(khoa);
    }}

    private DataTable ConvertToDataTable(IEnumerable<dynamic> result)
    {{
        var dataTable = new DataTable();
        // TODO: Implement DataTable conversion
        return dataTable;
    }}
}}
";

            File.WriteAllText(Path.Combine(repoPath, $"{entityName}Repository.cs"), content);
        }

        static void GenerateService(string entityName, string projectRoot)
        {
            var servicePath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.Core", "Services");
            Directory.CreateDirectory(servicePath);

            var content = $@"using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Service for {entityName} entity - Generated by automation
/// TODO: Implement all business logic from legacy cls{entityName}.cs
/// </summary>
public interface I{entityName}Service
{{
    Task<bool> LoadAsync(string pKhoa);
    Task<bool> SaveAsync({entityName}Dto dto);
    Task<bool> DelDataAsync(string pKhoa);
    Task<IEnumerable<{entityName}ListDto>> GetAllAsync();
    Task<{entityName}Dto?> GetByIdAsync(string khoa);
    Task<string> CreateAsync(Create{entityName}Dto createDto);
    Task<bool> UpdateAsync({entityName}Dto dto);
    Task<bool> DeleteAsync(string khoa);
}}

public class {entityName}Service : I{entityName}Service
{{
    private readonly I{entityName}Repository _repository;

    public {entityName}Service(I{entityName}Repository repository)
    {{
        _repository = repository;
    }}

    public async Task<bool> LoadAsync(string pKhoa)
    {{
        return await _repository.LoadAsync(pKhoa);
    }}

    public async Task<bool> SaveAsync({entityName}Dto dto)
    {{
        // TODO: Add validation logic from legacy class
        if (string.IsNullOrWhiteSpace(dto.SoChungTu))
            throw new ArgumentException(""Số chứng từ không được để trống"");

        return await _repository.SaveAsync(dto);
    }}

    public async Task<bool> DelDataAsync(string pKhoa)
    {{
        if (string.IsNullOrWhiteSpace(pKhoa))
            throw new ArgumentException(""Khoa không được để trống"");

        return await _repository.DelDataAsync(pKhoa);
    }}

    public async Task<IEnumerable<{entityName}ListDto>> GetAllAsync()
    {{
        return await _repository.GetAllAsync();
    }}

    public async Task<{entityName}Dto?> GetByIdAsync(string khoa)
    {{
        return await _repository.GetByIdAsync(khoa);
    }}

    public async Task<string> CreateAsync(Create{entityName}Dto createDto)
    {{
        // TODO: Add validation and business logic
        return await _repository.CreateAsync(createDto);
    }}

    public async Task<bool> UpdateAsync({entityName}Dto dto)
    {{
        return await SaveAsync(dto);
    }}

    public async Task<bool> DeleteAsync(string khoa)
    {{
        return await DelDataAsync(khoa);
    }}
}}
";

            File.WriteAllText(Path.Combine(servicePath, $"{entityName}Service.cs"), content);
        }

        static void GenerateController(string entityName, string projectRoot)
        {
            var controllerPath = Path.Combine(projectRoot, "src", "API", "GP.Mobile.API", "Controllers");
            Directory.CreateDirectory(controllerPath);

            var content = $@"using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Controller for {entityName} entity - Generated by automation
/// TODO: Add all endpoints from legacy cls{entityName}.cs methods
/// </summary>
[ApiController]
[Route(""api/[controller]"")]
public class {entityName}Controller : ControllerBase
{{
    private readonly I{entityName}Service _{entityName.ToLower()}Service;
    private readonly ILogger<{entityName}Controller> _logger;

    public {entityName}Controller(I{entityName}Service {entityName.ToLower()}Service, ILogger<{entityName}Controller> logger)
    {{
        _{entityName.ToLower()}Service = {entityName.ToLower()}Service;
        _logger = logger;
    }}

    /// <summary>
    /// Get all {entityName} records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<{entityName}ListDto>>> GetAll()
    {{
        try
        {{
            var result = await _{entityName.ToLower()}Service.GetAllAsync();
            return Ok(result);
        }}
        catch (Exception ex)
        {{
            _logger.LogError(ex, ""Error getting all {entityName} records"");
            return StatusCode(500, ""Lỗi hệ thống"");
        }}
    }}

    /// <summary>
    /// Get {entityName} by ID
    /// </summary>
    [HttpGet(""{{khoa}}"")]
    public async Task<ActionResult<{entityName}Dto>> GetById(string khoa)
    {{
        try
        {{
            var result = await _{entityName.ToLower()}Service.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }}
        catch (Exception ex)
        {{
            _logger.LogError(ex, ""Error getting {entityName} by ID"");
            return StatusCode(500, ""Lỗi hệ thống"");
        }}
    }}

    /// <summary>
    /// Create new {entityName}
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] Create{entityName}Dto createDto)
    {{
        try
        {{
            var result = await _{entityName.ToLower()}Service.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new {{ khoa = result }}, result);
        }}
        catch (ArgumentException ex)
        {{
            return BadRequest(ex.Message);
        }}
        catch (Exception ex)
        {{
            _logger.LogError(ex, ""Error creating {entityName}"");
            return StatusCode(500, ""Lỗi hệ thống"");
        }}
    }}

    /// <summary>
    /// Update {entityName}
    /// </summary>
    [HttpPut(""{{khoa}}"")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] {entityName}Dto dto)
    {{
        try
        {{
            if (khoa != dto.Khoa)
                return BadRequest(""ID mismatch"");

            var result = await _{entityName.ToLower()}Service.UpdateAsync(dto);
            return Ok(result);
        }}
        catch (ArgumentException ex)
        {{
            return BadRequest(ex.Message);
        }}
        catch (Exception ex)
        {{
            _logger.LogError(ex, ""Error updating {entityName}"");
            return StatusCode(500, ""Lỗi hệ thống"");
        }}
    }}

    /// <summary>
    /// Delete {entityName}
    /// </summary>
    [HttpDelete(""{{khoa}}"")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {{
        try
        {{
            var result = await _{entityName.ToLower()}Service.DeleteAsync(khoa);
            return Ok(result);
        }}
        catch (ArgumentException ex)
        {{
            return BadRequest(ex.Message);
        }}
        catch (Exception ex)
        {{
            _logger.LogError(ex, ""Error deleting {entityName}"");
            return StatusCode(500, ""Lỗi hệ thống"");
        }}
    }}

    // TODO: Add legacy endpoints from cls{entityName}.cs methods
    // Example: Load, Save, DelData, ShowList, etc.
}}
";

            File.WriteAllText(Path.Combine(controllerPath, $"{entityName}Controller.cs"), content);
        }

        static string FindProjectRoot(string currentPath)
        {
            var directory = new DirectoryInfo(currentPath);
            
            while (directory != null)
            {
                if (Directory.Exists(Path.Combine(directory.FullName, "Base", "Business")) ||
                    File.Exists(Path.Combine(directory.FullName, "GP.Mobile.sln")) ||
                    Directory.Exists(Path.Combine(directory.FullName, "src", "API")))
                {
                    return directory.FullName;
                }
                
                directory = directory.Parent;
            }
            
            return "";
        }
    }
}
