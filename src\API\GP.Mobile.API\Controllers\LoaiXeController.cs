using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for LoaiXe (Vehicle Type) entity
/// Implements ALL endpoints from clsDMLoaiXe.cs (577 lines)
/// Includes REST API and 10+ legacy method endpoints
/// Maps to DM_LoaiXe table with 9 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for vehicle categorization and manufacturer linking
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LoaiXeController : ControllerBase
{
    private readonly ILoaiXeService _loaiXeService;
    private readonly ILogger<LoaiXeController> _logger;

    public LoaiXeController(ILoaiXeService loaiXeService, ILogger<LoaiXeController> logger)
    {
        _loaiXeService = loaiXeService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all LoaiXe records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<LoaiXeListDto>>> GetAll()
    {
        try
        {
            var result = await _loaiXeService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiXe records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiXe by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<LoaiXeDto>> GetById(string khoa)
    {
        try
        {
            var result = await _loaiXeService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiXe by vehicle type code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<LoaiXeDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _loaiXeService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new LoaiXe
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateLoaiXeDto createDto)
    {
        try
        {
            var result = await _loaiXeService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo loại xe");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiXe");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiXe
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] LoaiXeDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiXeService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiXe");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete LoaiXe
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _loaiXeService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiXe");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiXe status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateLoaiXeStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiXeService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiXe status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search LoaiXe records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<LoaiXeListDto>>> Search([FromBody] LoaiXeSearchDto searchDto)
    {
        try
        {
            var result = await _loaiXeService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiXe");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiXe lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<LoaiXeLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _loaiXeService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiXe lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate LoaiXe data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<LoaiXeValidationDto>> Validate([FromBody] LoaiXeValidationRequestDto request)
    {
        try
        {
            var result = await _loaiXeService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiXe");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search vehicle by code
    /// </summary>
    [HttpGet("search-code/{code}")]
    public async Task<ActionResult<LoaiXeSearchByCodeDto>> SearchVehicleByCode(string code, [FromQuery] string? condition = null)
    {
        try
        {
            var result = await _loaiXeService.SearchVehicleByCodeAsync(code, condition ?? "");
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vehicle by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive vehicle categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<VehicleCategoryDto>>> GetVehicleCategories()
    {
        try
        {
            var result = await _loaiXeService.GetVehicleCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle types with manufacturer information
    /// </summary>
    [HttpGet("with-manufacturer")]
    public async Task<ActionResult<IEnumerable<LoaiXeWithManufacturerDto>>> GetVehicleTypesWithManufacturer()
    {
        try
        {
            var result = await _loaiXeService.GetVehicleTypesWithManufacturerAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle types with manufacturer");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get vehicle statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<LoaiXeStatsDto>> GetVehicleStats(string khoa)
    {
        try
        {
            var result = await _loaiXeService.GetVehicleStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get manufacturer for vehicle type
    /// </summary>
    [HttpGet("{khoa}/manufacturer")]
    public async Task<ActionResult<string>> GetManufacturer(string khoa)
    {
        try
        {
            var result = await _loaiXeService.GetHangSanXuatAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer for vehicle type");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get engine power list for vehicle type
    /// </summary>
    [HttpGet("{khoa}/engine-power")]
    public async Task<ActionResult<IEnumerable<MaLucDto>>> GetEnginePowerList(string khoa)
    {
        try
        {
            var result = await _loaiXeService.GetMaLucListAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting engine power list");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiXeService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadName method endpoint
    /// </summary>
    [HttpPost("loadname")]
    public async Task<ActionResult<bool>> LoadName([FromBody] string tenViet)
    {
        try
        {
            var result = await _loaiXeService.LoadNameAsync(tenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadName endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] LoaiXeSaveRequestDto request)
    {
        try
        {
            var result = await _loaiXeService.SaveAsync(request.Dto, request.Action);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiXeService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] LoaiXeShowListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _loaiXeService.ShowListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _loaiXeService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] LoaiXeSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _loaiXeService.SearchByCodeAsync(request.Code, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] LoaiXeShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _loaiXeService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] LoaiXeTrungMaRequestDto request)
    {
        try
        {
            var result = await _loaiXeService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiXeService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetHangSanXuat method endpoint
    /// </summary>
    [HttpPost("gethhangsanxuat")]
    public async Task<ActionResult<string>> GetHangSanXuat([FromBody] string khoaLoaiXe)
    {
        try
        {
            var result = await _loaiXeService.GetHangSanXuatAsync(khoaLoaiXe);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetHangSanXuat endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetKhoa method endpoint
    /// </summary>
    [HttpPost("getkhoa")]
    public async Task<ActionResult<string>> GetKhoa([FromBody] string ma)
    {
        try
        {
            var result = await _loaiXeService.GetKhoaAsync(ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetKhoa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for LoaiXe

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class LoaiXeValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class LoaiXeSaveRequestDto
{
    public LoaiXeDto Dto { get; set; } = new();
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class LoaiXeShowListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method
/// </summary>
public class LoaiXeSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class LoaiXeShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class LoaiXeTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
