using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Complete service interface implementing ALL methods from legacy clsDMDoiTuong.cs
/// </summary>
public interface IDoiTuongService
{
    // Core operations - exact legacy functionality
    Task<bool> LoadAsync(string pKhoa);
    Task<bool> LoadByCodeAsync(string pMa);
    Task<bool> SaveAsync(DoiTuongDto dto, string pTask = "");
    Task<bool> DelDataAsync(string pKhoa);

    // List operations - exact legacy SQL
    Task<DataTable> ShowListAsync(string strLoaiDoiTuong = "", string strConditions = "");
    Task<DataTable> ShowAllListAsync(string pStrLoai = "");
    Task<DataTable> ShowAllListMSTAsync(string pStrLoai = "");
    Task<DataTable> ShowAllListWithConditionsAsync(string pStrLoai = "", string pConditions = "");

    // Employee operations
    Task<DataTable> GetListNhanVienAsync();
    Task<DataTable> GetListKyThuatVienAsync(string strKhoaToSuaChua = "");
    Task<DataTable> GetListAllNhanVienTheoToAsync(string strCondition = "");

    // Search and validation
    Task<string> SearchByCodeAsync(string strCode = "", string strLoaiDoiTuong = "");
    Task<bool> TrungMaAsync(string pMa, string pKhoa);
    Task<bool> TrungMaSoThueAsync(string strMaSoThue, string strKhoa);
    Task<bool> TrungDienThoaiVaTenAsync(string pTen, string pDienThoai, string pKhoa);
    Task<bool> TrungDienThoai_KHKDAsync(string pDienThoai, string pKhoa);
    Task<bool> TrungCMNDAsync(string strCMND, string strKhoa);

    // Usage validation
    Task<bool> WasUsedAsync(string pKhoa);
    Task<bool> WasUsedForNhanVienAsync(string pKhoa);
    Task<bool> WasUsedForNhaCungCapAsync(string pKhoa);

    // Contact and relationship data
    Task<DataTable> GetContactorAsync(string pKhoa);
    Task<DataTable> GetGiamDinhVienAsync(string pKhoa);

    // Reporting and printing
    Task<DataTable> GetDataPrintAsync(string strConditions = "");
    Task<DataTable> GetDataPrintNhaCungCapAsync(string strConditions = "");
    Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");

    // Financial operations
    Task<double> GetSoDuCongNoAsync(string KhoaDoiTuong, string NamThang, string Ve, string KhoaDonVi, string SoTaiKhoan);

    // Customer specific operations
    Task<object> GetKhachHangListAsync(string pCondition = "");
    Task<object> GetKhachHangKinhDoanhListAsync(string pCondition = "");
    Task<object> GetKhachHangKinhDoanhListForEmployeeAsync(string pCondition = "");
    Task<object> GetKhachHangQuanTamLoaiXeListAsync(string pCondition = "");
    Task<object> GetKhachHangKhuVucListAsync(string pCondition = "");

    // Special operations
    Task<string> CreateMaKhachHangAsync(string pNamThang);
    Task<DataTable> GetMucNoBaoHiemAsync(int pLoai, string pCondition = "");
    Task<string> GetKhoaByTaxCodeAsync(string strMaSoThue);
    Task<bool> GomKhachHangAsync(string pKhoaKhachHangXoa, string pKhoaKhachHangCanGom);

    // Birthday and insurance tracking
    Task<object> GetSinhNhatKhachHangAsync(string pCondition = "");
    Task<object> GetHanBaoHiemXeListAsync(string pCondition = "");
    Task<object> GetHanDangKiemXeListAsync(string pCondition = "");

    // Phone number operations
    Task<bool> Load_KHKD_ByPhoneNumberAsync(string pSoDienThoai);

    // Care tracking
    Task<DataTable> GetDataKhachHang30NgayKhongChamSocAsync(string pKhoaNhanVienQuanLy);
    Task<bool> DelDataKinhDoanhAsync(string pKhoa);

    // Modern API additions for mobile app
    Task<IEnumerable<DoiTuongListDto>> GetAllCustomersAsync();
    Task<DoiTuongDto?> GetCustomerByIdAsync(string khoa);
    Task<DoiTuongDto?> GetCustomerByCodeAsync(string ma);
    Task<string> CreateCustomerAsync(CreateDoiTuongDto createDto);
    Task<bool> UpdateCustomerAsync(DoiTuongDto dto);
    Task<bool> DeleteCustomerAsync(string khoa);
    Task<IEnumerable<DoiTuongListDto>> SearchCustomersAsync(string searchTerm);
    Task<bool> ValidateCustomerDataAsync(CreateDoiTuongDto dto);
}

/// <summary>
/// Complete service implementation with ALL legacy methods from clsDMDoiTuong.cs
/// Maintains exact business logic and validation rules
/// </summary>
public class DoiTuongService : IDoiTuongService
{
    private readonly IDoiTuongRepository _repository;

    public DoiTuongService(IDoiTuongRepository repository)
    {
        _repository = repository;
    }

    #region Core Operations - Exact Legacy Implementation

    /// <summary>
    /// Load by Khoa - exact legacy functionality
    /// </summary>
    public async Task<bool> LoadAsync(string pKhoa)
    {
        return await _repository.LoadAsync(pKhoa);
    }

    /// <summary>
    /// Load by Code - exact legacy functionality
    /// </summary>
    public async Task<bool> LoadByCodeAsync(string pMa)
    {
        return await _repository.LoadByCodeAsync(pMa);
    }

    /// <summary>
    /// Save - exact legacy functionality with all validation
    /// </summary>
    public async Task<bool> SaveAsync(DoiTuongDto dto, string pTask = "")
    {
        // Apply same validation logic as legacy before saving
        if (string.IsNullOrWhiteSpace(dto.TenViet))
            throw new ArgumentException("Tên không được để trống");

        if (string.IsNullOrWhiteSpace(dto.Ma))
            throw new ArgumentException("Mã không được để trống");

        // Check for duplicates exactly like legacy
        if (await _repository.TrungMaAsync(dto.Ma, dto.Khoa))
            throw new InvalidOperationException("Mã đã tồn tại");

        return await _repository.SaveAsync(dto, pTask);
    }

    /// <summary>
    /// Delete - exact legacy functionality
    /// </summary>
    public async Task<bool> DelDataAsync(string pKhoa)
    {
        // Check if used exactly like legacy
        if (await _repository.WasUsedAsync(pKhoa))
            throw new InvalidOperationException("Không thể xóa vì đã được sử dụng");

        return await _repository.DelDataAsync(pKhoa);
    }

    #endregion

    #region List Operations - Direct Repository Calls

    public async Task<DataTable> ShowListAsync(string strLoaiDoiTuong = "", string strConditions = "")
    {
        return await _repository.ShowListAsync(strLoaiDoiTuong, strConditions);
    }

    public async Task<DataTable> ShowAllListAsync(string pStrLoai = "")
    {
        return await _repository.ShowAllListAsync(pStrLoai);
    }

    public async Task<DataTable> ShowAllListMSTAsync(string pStrLoai = "")
    {
        return await _repository.ShowAllListMSTAsync(pStrLoai);
    }

    public async Task<DataTable> ShowAllListWithConditionsAsync(string pStrLoai = "", string pConditions = "")
    {
        return await _repository.ShowAllListWithConditionsAsync(pStrLoai, pConditions);
    }

    #endregion

    #region Employee Operations - Direct Repository Calls

    public async Task<DataTable> GetListNhanVienAsync()
    {
        return await _repository.GetListNhanVienAsync();
    }

    public async Task<DataTable> GetListKyThuatVienAsync(string strKhoaToSuaChua = "")
    {
        return await _repository.GetListKyThuatVienAsync(strKhoaToSuaChua);
    }

    public async Task<DataTable> GetListAllNhanVienTheoToAsync(string strCondition = "")
    {
        return await _repository.GetListAllNhanVienTheoToAsync(strCondition);
    }

    #endregion

    #region Search and Validation - Direct Repository Calls

    public async Task<string> SearchByCodeAsync(string strCode = "", string strLoaiDoiTuong = "")
    {
        return await _repository.SearchByCodeAsync(strCode, strLoaiDoiTuong);
    }

    public async Task<bool> TrungMaAsync(string pMa, string pKhoa)
    {
        return await _repository.TrungMaAsync(pMa, pKhoa);
    }

    public async Task<bool> TrungMaSoThueAsync(string strMaSoThue, string strKhoa)
    {
        return await _repository.TrungMaSoThueAsync(strMaSoThue, strKhoa);
    }

    public async Task<bool> TrungDienThoaiVaTenAsync(string pTen, string pDienThoai, string pKhoa)
    {
        return await _repository.TrungDienThoaiVaTenAsync(pTen, pDienThoai, pKhoa);
    }

    public async Task<bool> TrungDienThoai_KHKDAsync(string pDienThoai, string pKhoa)
    {
        return await _repository.TrungDienThoai_KHKDAsync(pDienThoai, pKhoa);
    }

    public async Task<bool> TrungCMNDAsync(string strCMND, string strKhoa)
    {
        return await _repository.TrungCMNDAsync(strCMND, strKhoa);
    }

    #endregion

    #region Usage Validation - Direct Repository Calls

    public async Task<bool> WasUsedAsync(string pKhoa)
    {
        return await _repository.WasUsedAsync(pKhoa);
    }

    public async Task<bool> WasUsedForNhanVienAsync(string pKhoa)
    {
        return await _repository.WasUsedForNhanVienAsync(pKhoa);
    }

    public async Task<bool> WasUsedForNhaCungCapAsync(string pKhoa)
    {
        return await _repository.WasUsedForNhaCungCapAsync(pKhoa);
    }

    #endregion

    #region All Remaining Legacy Methods - Direct Repository Calls

    public async Task<DataTable> GetContactorAsync(string pKhoa)
    {
        return await _repository.GetContactorAsync(pKhoa);
    }

    public async Task<DataTable> GetGiamDinhVienAsync(string pKhoa)
    {
        return await _repository.GetGiamDinhVienAsync(pKhoa);
    }

    public async Task<DataTable> GetDataPrintAsync(string strConditions = "")
    {
        return await _repository.GetDataPrintAsync(strConditions);
    }

    public async Task<DataTable> GetDataPrintNhaCungCapAsync(string strConditions = "")
    {
        return await _repository.GetDataPrintNhaCungCapAsync(strConditions);
    }

    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        return await _repository.ShowListByFieldAsync(strFieldList, strConditions, strOrder);
    }

    public async Task<double> GetSoDuCongNoAsync(string KhoaDoiTuong, string NamThang, string Ve, string KhoaDonVi, string SoTaiKhoan)
    {
        return await _repository.GetSoDuCongNoAsync(KhoaDoiTuong, NamThang, Ve, KhoaDonVi, SoTaiKhoan);
    }

    public async Task<object> GetKhachHangListAsync(string pCondition = "")
    {
        return await _repository.GetKhachHangListAsync(pCondition);
    }

    public async Task<object> GetKhachHangKinhDoanhListAsync(string pCondition = "")
    {
        return await _repository.GetKhachHangKinhDoanhListAsync(pCondition);
    }

    public async Task<object> GetKhachHangKinhDoanhListForEmployeeAsync(string pCondition = "")
    {
        return await _repository.GetKhachHangKinhDoanhListForEmployeeAsync(pCondition);
    }

    public async Task<object> GetKhachHangQuanTamLoaiXeListAsync(string pCondition = "")
    {
        return await _repository.GetKhachHangQuanTamLoaiXeListAsync(pCondition);
    }

    public async Task<object> GetKhachHangKhuVucListAsync(string pCondition = "")
    {
        return await _repository.GetKhachHangKhuVucListAsync(pCondition);
    }

    public async Task<string> CreateMaKhachHangAsync(string pNamThang)
    {
        return await _repository.CreateMaKhachHangAsync(pNamThang);
    }

    public async Task<DataTable> GetMucNoBaoHiemAsync(int pLoai, string pCondition = "")
    {
        return await _repository.GetMucNoBaoHiemAsync(pLoai, pCondition);
    }

    public async Task<string> GetKhoaByTaxCodeAsync(string strMaSoThue)
    {
        return await _repository.GetKhoaByTaxCodeAsync(strMaSoThue);
    }

    public async Task<bool> GomKhachHangAsync(string pKhoaKhachHangXoa, string pKhoaKhachHangCanGom)
    {
        return await _repository.GomKhachHangAsync(pKhoaKhachHangXoa, pKhoaKhachHangCanGom);
    }

    public async Task<object> GetSinhNhatKhachHangAsync(string pCondition = "")
    {
        return await _repository.GetSinhNhatKhachHangAsync(pCondition);
    }

    public async Task<object> GetHanBaoHiemXeListAsync(string pCondition = "")
    {
        return await _repository.GetHanBaoHiemXeListAsync(pCondition);
    }

    public async Task<object> GetHanDangKiemXeListAsync(string pCondition = "")
    {
        return await _repository.GetHanDangKiemXeListAsync(pCondition);
    }

    public async Task<bool> Load_KHKD_ByPhoneNumberAsync(string pSoDienThoai)
    {
        return await _repository.Load_KHKD_ByPhoneNumberAsync(pSoDienThoai);
    }

    public async Task<DataTable> GetDataKhachHang30NgayKhongChamSocAsync(string pKhoaNhanVienQuanLy)
    {
        return await _repository.GetDataKhachHang30NgayKhongChamSocAsync(pKhoaNhanVienQuanLy);
    }

    public async Task<bool> DelDataKinhDoanhAsync(string pKhoa)
    {
        return await _repository.DelDataKinhDoanhAsync(pKhoa);
    }

    #endregion

    #region Modern API Methods for Mobile App

    public async Task<IEnumerable<DoiTuongListDto>> GetAllCustomersAsync()
    {
        return await _repository.GetAllAsync();
    }

    public async Task<DoiTuongDto?> GetCustomerByIdAsync(string khoa)
    {
        if (string.IsNullOrWhiteSpace(khoa))
            return null;

        return await _repository.GetByIdAsync(khoa);
    }

    public async Task<DoiTuongDto?> GetCustomerByCodeAsync(string ma)
    {
        if (string.IsNullOrWhiteSpace(ma))
            return null;

        return await _repository.GetByMaAsync(ma);
    }

    public async Task<string> CreateCustomerAsync(CreateDoiTuongDto createDto)
    {
        // Validate business rules
        await ValidateCustomerDataAsync(createDto);

        // Check for duplicate phone number
        var existingCustomers = await _repository.SearchAsync(createDto.DienThoai);
        if (existingCustomers.Any())
        {
            throw new InvalidOperationException("Số điện thoại đã tồn tại trong hệ thống");
        }

        // Check for duplicate email if provided
        if (!string.IsNullOrWhiteSpace(createDto.Email))
        {
            var existingByEmail = await _repository.SearchAsync(createDto.Email);
            if (existingByEmail.Any())
            {
                throw new InvalidOperationException("Email đã tồn tại trong hệ thống");
            }
        }

        return await _repository.CreateAsync(createDto);
    }

    public async Task<bool> UpdateCustomerAsync(DoiTuongDto dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Khoa))
            throw new ArgumentException("Khoa không được để trống");

        // Check if customer exists
        var existing = await _repository.GetByIdAsync(dto.Khoa);
        if (existing == null)
            throw new InvalidOperationException("Khách hàng không tồn tại");

        return await _repository.UpdateAsync(dto);
    }

    public async Task<bool> DeleteCustomerAsync(string khoa)
    {
        if (string.IsNullOrWhiteSpace(khoa))
            throw new ArgumentException("Khoa không được để trống");

        // Check if customer exists
        var existing = await _repository.GetByIdAsync(khoa);
        if (existing == null)
            throw new InvalidOperationException("Khách hàng không tồn tại");

        // TODO: Check if customer has related records (orders, quotes, etc.)
        // This would require additional repository methods

        return await _repository.DeleteAsync(khoa);
    }

    public async Task<IEnumerable<DoiTuongListDto>> SearchCustomersAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return await GetAllCustomersAsync();

        return await _repository.SearchAsync(searchTerm.Trim());
    }

    public async Task<bool> ValidateCustomerDataAsync(CreateDoiTuongDto dto)
    {
        var errors = new List<string>();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.TenViet))
            errors.Add("Tên khách hàng không được để trống");

        if (string.IsNullOrWhiteSpace(dto.DienThoai))
            errors.Add("Số điện thoại không được để trống");

        // Phone number format validation
        if (!string.IsNullOrWhiteSpace(dto.DienThoai))
        {
            var phonePattern = @"^[0-9+\-\s\(\)]{10,15}$";
            if (!System.Text.RegularExpressions.Regex.IsMatch(dto.DienThoai, phonePattern))
                errors.Add("Số điện thoại không đúng định dạng");
        }

        // Email format validation
        if (!string.IsNullOrWhiteSpace(dto.Email))
        {
            var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            if (!System.Text.RegularExpressions.Regex.IsMatch(dto.Email, emailPattern))
                errors.Add("Email không đúng định dạng");
        }

        if (errors.Any())
            throw new ArgumentException(string.Join("; ", errors));

        return true;
    }

    #endregion
}
