#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Automated Legacy to Modern API Code Generator for GP Mobile

.DESCRIPTION
    This script builds and runs the automated code generator that converts
    all 180+ legacy business classes to modern API implementation.

.PARAMETER Mode
    Generation mode: single, priority, all, or interactive

.PARAMETER ClassName
    Specific class name to generate (when Mode is 'single')

.EXAMPLE
    .\scripts\run-code-generator.ps1
    # Runs in interactive mode

.EXAMPLE
    .\scripts\run-code-generator.ps1 -Mode priority
    # Generates the top 5 priority classes

.EXAMPLE
    .\scripts\run-code-generator.ps1 -Mode single -ClassName clsBaoGia
    # Generates only clsBaoGia class

.EXAMPLE
    .\scripts\run-code-generator.ps1 -Mode all
    # Generates ALL 180+ classes (takes time)
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("single", "priority", "all", "interactive")]
    [string]$Mode = "interactive",
    
    [Parameter(Mandatory=$false)]
    [string]$ClassName = ""
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$CodeGeneratorPath = Join-Path $ProjectRoot "tools" "CodeGenerator"
$CodeGeneratorProject = Join-Path $CodeGeneratorPath "GP.Mobile.CodeGenerator.csproj"

Write-Host "GP Mobile Legacy to Modern API Generator" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Cyan
Write-Host ""

# Validate project structure
Write-Host "Validating project structure..." -ForegroundColor Yellow

if (-not (Test-Path $ProjectRoot)) {
    Write-Host "❌ Project root not found: $ProjectRoot" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path (Join-Path $ProjectRoot "Base" "Business"))) {
    Write-Host "❌ Legacy Business folder not found: $ProjectRoot\Base\Business" -ForegroundColor Red
    Write-Host "   Please ensure you're running this from the GP Mobile project root" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $CodeGeneratorProject)) {
    Write-Host "❌ Code generator project not found: $CodeGeneratorProject" -ForegroundColor Red
    exit 1
}

Write-Host "Project structure validated" -ForegroundColor Green
Write-Host ""

# Build the code generator
Write-Host "Building code generator..." -ForegroundColor Yellow

try {
    Push-Location $CodeGeneratorPath
    
    # Clean previous builds
    if (Test-Path "bin") {
        Remove-Item "bin" -Recurse -Force
    }
    if (Test-Path "obj") {
        Remove-Item "obj" -Recurse -Force
    }
    
    # Build the project
    $buildResult = dotnet build --configuration Release --verbosity quiet
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Code generator built successfully" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Build error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
}

Write-Host ""

# Prepare command line arguments
$arguments = @()

switch ($Mode) {
    "single" {
        if ([string]::IsNullOrEmpty($ClassName)) {
            Write-Host "❌ ClassName parameter required for single mode" -ForegroundColor Red
            Write-Host "Usage: .\run-code-generator.ps1 -Mode single -ClassName clsBaoGia" -ForegroundColor Yellow
            exit 1
        }
        $arguments += "--single", $ClassName
    }
    "priority" {
        $arguments += "--priority"
    }
    "all" {
        Write-Host "WARNING: This will generate code for ALL 180+ legacy classes" -ForegroundColor Yellow
        Write-Host "   This process may take 10-30 minutes depending on your system" -ForegroundColor Yellow
        Write-Host ""
        
        $confirm = Read-Host "Are you sure you want to continue? (y/N)"
        if ($confirm.ToLower() -ne "y") {
            Write-Host "❌ Operation cancelled by user" -ForegroundColor Yellow
            exit 0
        }
        
        $arguments += "--all"
    }
    "interactive" {
        # No arguments for interactive mode
    }
}

# Run the code generator
Write-Host "Running code generator in '$Mode' mode..." -ForegroundColor Yellow
Write-Host ""

try {
    Push-Location $ProjectRoot
    
    $exePath = Join-Path $CodeGeneratorPath "bin" "Release" "net8.0" "CodeGenerator.exe"
    
    if (-not (Test-Path $exePath)) {
        Write-Host "❌ Code generator executable not found: $exePath" -ForegroundColor Red
        exit 1
    }
    
    # Run the generator
    if ($arguments.Count -gt 0) {
        & $exePath $arguments
    } else {
        & $exePath
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Code generation failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Runtime error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "Code generation completed successfully!" -ForegroundColor Green
Write-Host ""

# Show next steps
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review generated files in src/API/ folders" -ForegroundColor White
Write-Host "2. Build the API project to check for compilation errors:" -ForegroundColor White
Write-Host "   dotnet build src/API/GP.Mobile.API/GP.Mobile.API.csproj" -ForegroundColor Gray
Write-Host "3. Run the API to test generated endpoints:" -ForegroundColor White
Write-Host "   dotnet run --project src/API/GP.Mobile.API" -ForegroundColor Gray
Write-Host "4. Check Swagger UI at https://localhost:7001/swagger" -ForegroundColor White
Write-Host "5. Update dependency injection in Program.cs if needed" -ForegroundColor White
Write-Host ""

# Show statistics if available
$reportPath = Join-Path $ProjectRoot "docs" "GENERATION_REPORT.md"
if (Test-Path $reportPath) {
    Write-Host "Detailed generation report available at:" -ForegroundColor Cyan
    Write-Host "   $reportPath" -ForegroundColor Gray
    Write-Host ""
}

Write-Host "Happy coding!" -ForegroundColor Green
