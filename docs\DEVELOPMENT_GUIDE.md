# GP Mobile v1 - Development Guide

## 🚨 CRITICAL RULE: NO TOUCH BASE FOLDER
**NEVER modify anything in the `Base\` folder. This is the legacy system and must remain untouched.**

## Project Structure

```
d:\Projects\gp-mobile-v1\
├── Base\                          # ❌ LEGACY - DO NOT MODIFY
│   ├── Business\                  # Original business classes
│   └── Database\                  # SQL Server scripts
├── src\                           # ✅ NEW DEVELOPMENT
│   ├── API\                       # .NET Core Web API
│   │   ├── GP.Mobile.API\         # Main API project
│   │   ├── GP.Mobile.Core\        # Business logic
│   │   ├── GP.Mobile.Data\        # Data access layer
│   │   └── GP.Mobile.Models\      # DTOs and models
│   └── Mobile\                    # React Native Expo app
├── scripts\                       # Build and deployment scripts
└── docs\                          # Documentation
```

## Prerequisites

### For API Development
- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- SQL Server (existing database)

### For Mobile Development
- Node.js 18+
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## Getting Started

### 1. Setup API

```powershell
# Build the API
.\scripts\build-api.ps1

# Run the API
.\scripts\run-api.ps1
```

The API will be available at: `https://localhost:7001`

### 2. Setup Mobile App

```powershell
# Setup mobile app
.\scripts\setup-mobile.ps1

# Navigate to mobile directory
cd src\Mobile

# Start development server
npm start
```

## Database Connection

The new API connects to the same database as the legacy system:
- **Database**: `CARSOFT_GIAPHAT`
- **Connection**: Uses existing tables and stored procedures
- **No Schema Changes**: Database remains unchanged

## Architecture Overview

### API Layer (.NET Core 8)

#### Models (`GP.Mobile.Models`)
- **DTOs**: Data Transfer Objects for API communication
- **Validation**: Input validation attributes
- **Mapping**: Between database entities and API models

#### Data Layer (`GP.Mobile.Data`)
- **Repositories**: Data access using Dapper (raw SQL)
- **DbContext**: Entity Framework for new tables only
- **Legacy Integration**: Direct SQL queries to existing tables

#### Core Layer (`GP.Mobile.Core`)
- **Services**: Business logic implementation
- **Interfaces**: Service contracts
- **Validation**: Business rule validation

#### API Layer (`GP.Mobile.API`)
- **Controllers**: REST API endpoints
- **Middleware**: Error handling, logging
- **Configuration**: Dependency injection, CORS

### Mobile Layer (React Native Expo)

#### Services
- **API Service**: HTTP client for API communication
- **Storage**: AsyncStorage for local data
- **Authentication**: JWT token management

#### Screens
- **Home**: Dashboard with statistics
- **Customers**: List, detail, create/edit
- **Opportunities**: Sales opportunity management
- **Settings**: App configuration

#### Navigation
- **Tab Navigation**: Bottom tabs for main sections
- **Stack Navigation**: Screen navigation within sections

## Development Workflow

### 1. API Development

1. **Add new DTOs** in `GP.Mobile.Models`
2. **Create repositories** in `GP.Mobile.Data`
3. **Implement services** in `GP.Mobile.Core`
4. **Add controllers** in `GP.Mobile.API`
5. **Test endpoints** using Swagger UI

### 2. Mobile Development

1. **Update API service** with new endpoints
2. **Create/update screens** for new features
3. **Add navigation** if needed
4. **Test on device/simulator**

### 3. Database Integration

- **Use existing tables**: Query directly with Dapper
- **No schema changes**: Keep database unchanged
- **Stored procedures**: Call existing SPs when available
- **New tables**: Use Entity Framework if absolutely necessary

## Testing

### API Testing
```powershell
cd src\API
dotnet test
```

### Mobile Testing
```bash
cd src/Mobile
npm test
```

## Deployment

### API Deployment
- Build Release configuration
- Deploy to IIS or Azure App Service
- Update connection strings for production

### Mobile Deployment
- Build APK/IPA using Expo
- Deploy to app stores or internal distribution

## Commit Guidelines

Before committing:
1. **Test thoroughly** - Ensure all changes work
2. **No Base folder changes** - Verify no legacy code modified
3. **Clear commit messages** - Describe what was changed
4. **Small commits** - One feature per commit

## Troubleshooting

### API Issues
- Check connection string in `appsettings.json`
- Verify database connectivity
- Check logs in `logs/` directory

### Mobile Issues
- Clear Expo cache: `expo start -c`
- Restart Metro bundler
- Check API URL in `app.json`

### Database Issues
- Verify SQL Server is running
- Check database permissions
- Test connection with SQL Server Management Studio

## Next Steps

1. **Complete Customer CRUD** - Finish all customer operations
2. **Add Opportunity management** - Sales opportunity features
3. **Implement authentication** - User login/logout
4. **Add offline support** - Local data caching
5. **Performance optimization** - Query optimization, caching
