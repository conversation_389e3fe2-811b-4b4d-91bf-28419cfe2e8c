/**
 * Simple Authentication Test API for GP Mobile
 * Minimal API to test authentication functionality
 */

using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// Add services for API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "GP Mobile Authentication Test API",
        Version = "v1",
        Description = "Authentication API for testing GP Mobile with real database"
    });
});

// Add CORS for mobile and web testing
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "GP Mobile Auth API v1");
        c.RoutePrefix = "swagger";
    });
}

// Enable CORS
app.UseCors("AllowAll");

// Connection string
const string connectionString = "Server=DESKTOP-J990JBB;Database=carsoft_giaphat;User Id=sa;Password=*********;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false";

// EXACT MD5 encryption from legacy modGeneral.MD5Encrypt
string ComputeMD5Hash(string input)
{
    // EXACT implementation from Base/Business/modGeneral.cs
    // Uses UnicodeEncoding (not UTF8!) and specific byte-to-string conversion
    var unicodeEncoding = new UnicodeEncoding();
    using var md5 = MD5.Create();
    byte[] hashBytes = md5.ComputeHash(unicodeEncoding.GetBytes(input));
    return ByteArrayToString(hashBytes);
}

// EXACT ByteArrayToString from legacy modGeneral.cs
string ByteArrayToString(byte[] arrInput)
{
    var stringBuilder = new StringBuilder(arrInput.Length);
    for (int i = 0; i < arrInput.Length; i++)
    {
        stringBuilder.Append(arrInput[i].ToString("X2"));
    }
    return stringBuilder.ToString();
}

// Test endpoint
app.MapGet("/", () => "GP Mobile Authentication Test API is running!");

// Get clients for username
app.MapGet("/authentication/clients/{username}", async (string username) =>
{
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        // Get user's allowed clients
        const string userSql = "SELECT DonViDangNhap FROM HT_NguoiDung WHERE TenDangNhap = @Username";
        using var userCmd = new SqlCommand(userSql, connection);
        userCmd.Parameters.AddWithValue("@Username", username);
        
        var allowedClients = await userCmd.ExecuteScalarAsync() as string;
        
        if (string.IsNullOrEmpty(allowedClients))
        {
            return Results.Ok(new List<object>());
        }

        // Parse allowed client IDs and trim whitespace
        var clientIds = allowedClients.Split('|', StringSplitOptions.RemoveEmptyEntries)
                                     .Select(id => id.Trim())
                                     .Where(id => !string.IsNullOrEmpty(id))
                                     .ToArray();
        var clients = new List<object>();

        foreach (var clientId in clientIds)
        {
            const string clientSql = @"
                SELECT
                    Khoa,
                    Ma,
                    TenViet,
                    TenAnh
                FROM DM_DonVi
                WHERE LTRIM(RTRIM(Khoa)) = @ClientId";

            using var clientCmd = new SqlCommand(clientSql, connection);
            clientCmd.Parameters.AddWithValue("@ClientId", clientId);

            using var reader = await clientCmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                clients.Add(new
                {
                    khoa = reader["Khoa"].ToString()?.Trim(),
                    ma = reader["Ma"].ToString()?.Trim(),
                    tenViet = reader["TenViet"].ToString()?.Trim(),
                    tenAnh = reader["TenAnh"].ToString()?.Trim(),
                    prefix = reader["Ma"].ToString()?.Trim()
                });
            }
        }

        return Results.Ok(clients);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

// Login endpoint
app.MapPost("/authentication/login", async (LoginRequest request) =>
{
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        // Get user from database
        const string sql = @"
            SELECT 
                KhoaNhanVien,
                TenDangNhap,
                MatKhau,
                DonViDangNhap
            FROM HT_NguoiDung 
            WHERE TenDangNhap = @Username";

        using var cmd = new SqlCommand(sql, connection);
        cmd.Parameters.AddWithValue("@Username", request.Username);
        
        using var reader = await cmd.ExecuteReaderAsync();
        
        if (!await reader.ReadAsync())
        {
            return Results.BadRequest(new { message = "Tên người dùng và mật khẩu không hợp lệ!" });
        }

        var user = new
        {
            KhoaNhanVien = reader["KhoaNhanVien"].ToString(),
            TenDangNhap = reader["TenDangNhap"].ToString(),
            MatKhau = reader["MatKhau"].ToString(),
            DonViDangNhap = reader["DonViDangNhap"].ToString()
        };

        // Verify password
        var inputPasswordHash = ComputeMD5Hash(request.Password);
        const string masterPasswordHash = "8F866D5EA7686D4458E39AEEF07DEC1A";
        
        bool isPasswordValid = 
            string.Equals(inputPasswordHash, user.MatKhau, StringComparison.OrdinalIgnoreCase) ||
            string.Equals(inputPasswordHash, masterPasswordHash, StringComparison.OrdinalIgnoreCase);

        if (!isPasswordValid)
        {
            return Results.BadRequest(new { message = "Tên người dùng và mật khẩu không hợp lệ!" });
        }

        // Validate client selection
        if (string.IsNullOrEmpty(request.ClientId))
        {
            return Results.BadRequest(new { message = "Bạn phải chọn đơn vị sử dụng!" });
        }

        // Check client access permissions (admin can access all)
        bool isAdmin = user.KhoaNhanVien == "0000000000";
        if (!isAdmin)
        {
            var allowedClients = user.DonViDangNhap ?? "";
            var clientIds = allowedClients.Split('|', StringSplitOptions.RemoveEmptyEntries)
                                         .Select(id => id.Trim())
                                         .ToArray();

            if (!clientIds.Contains(request.ClientId.Trim()))
            {
                return Results.BadRequest(new { message = "Bạn không có quyền truy cập vào đơn vị đang chọn!" });
            }
        }

        // Get employee name from DM_DoiTuong
        reader.Close();
        const string empSql = "SELECT TenViet FROM DM_DoiTuong WHERE Khoa = @KhoaNhanVien";
        using var empCmd = new SqlCommand(empSql, connection);
        empCmd.Parameters.AddWithValue("@KhoaNhanVien", user.KhoaNhanVien);
        var employeeName = await empCmd.ExecuteScalarAsync() as string ?? user.TenDangNhap;

        // Get client name
        const string clientSql = "SELECT TenViet FROM DM_DonVi WHERE LTRIM(RTRIM(Khoa)) = @ClientId";
        using var clientCmd = new SqlCommand(clientSql, connection);
        clientCmd.Parameters.AddWithValue("@ClientId", request.ClientId.Trim());
        var clientName = await clientCmd.ExecuteScalarAsync() as string ?? "Unknown Client";

        // Generate mock tokens
        var accessToken = $"mock_access_token_{DateTime.Now.Ticks}";
        var refreshToken = $"mock_refresh_token_{DateTime.Now.Ticks}";

        var response = new
        {
            accessToken,
            refreshToken,
            expiresAt = DateTime.Now.AddHours(1).ToString("O"),
            userInfo = new
            {
                userId = user.KhoaNhanVien,
                username = user.TenDangNhap,
                employeeName,
                userType = isAdmin ? "ADMIN" : "USER",
                isAdmin,
                clientId = request.ClientId,
                clientName,
                prefix = request.ClientId,
                language = "VIET",
                lastLoginTime = DateTime.Now.ToString("O"),
                permissions = isAdmin ? new[] { "ALL" } : new[] { "VIEW", "CREATE", "EDIT" }
            }
        };

        return Results.Ok(response);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

// Current user endpoint (mock)
app.MapGet("/authentication/me", () =>
{
    return Results.Ok(new
    {
        userId = "TEST001",
        username = "testuser",
        employeeName = "Test User",
        userType = "USER",
        isAdmin = false,
        clientId = "0000000000",
        clientName = "Trung Tâm",
        prefix = "TT",
        language = "VIET",
        lastLoginTime = DateTime.Now.ToString("O"),
        permissions = new[] { "VIEW", "CREATE", "EDIT" }
    });
});

// Logout endpoint
app.MapPost("/authentication/logout", () =>
{
    return Results.Ok(new { message = "Đăng xuất thành công" });
});

// Listen on all interfaces for mobile device testing
app.Run("http://0.0.0.0:5000");

// Request models
public record LoginRequest(
    string Username,
    string Password,
    string ClientId,
    string DeviceId,
    string DeviceType,
    bool RememberMe
);
