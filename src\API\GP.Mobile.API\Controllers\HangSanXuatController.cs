using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for HangSanXuat (Manufacturer) entity
/// Implements ALL endpoints from clsDMHangSanXuat.cs (533 lines)
/// Includes REST API and 9+ legacy method endpoints
/// Maps to DM_HangSanXuat table with 9 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for manufacturer management and vehicle categorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HangSanXuatController : ControllerBase
{
    private readonly IHangSanXuatService _hangSanXuatService;
    private readonly ILogger<HangSanXuatController> _logger;

    public HangSanXuatController(IHangSanXuatService hangSanXuatService, ILogger<HangSanXuatController> logger)
    {
        _hangSanXuatService = hangSanXuatService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all HangSanXuat records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<HangSanXuatListDto>>> GetAll()
    {
        try
        {
            var result = await _hangSanXuatService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all HangSanXuat records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get HangSanXuat by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<HangSanXuatDto>> GetById(string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get HangSanXuat by manufacturer code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<HangSanXuatDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _hangSanXuatService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new HangSanXuat
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateHangSanXuatDto createDto)
    {
        try
        {
            var result = await _hangSanXuatService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo hãng sản xuất");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating HangSanXuat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update HangSanXuat
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] HangSanXuatDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _hangSanXuatService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating HangSanXuat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete HangSanXuat
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting HangSanXuat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update HangSanXuat status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateHangSanXuatStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _hangSanXuatService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating HangSanXuat status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search HangSanXuat records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<HangSanXuatListDto>>> Search([FromBody] HangSanXuatSearchDto searchDto)
    {
        try
        {
            var result = await _hangSanXuatService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching HangSanXuat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get HangSanXuat lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<HangSanXuatLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _hangSanXuatService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate HangSanXuat data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<HangSanXuatValidationDto>> Validate([FromBody] HangSanXuatValidationRequestDto request)
    {
        try
        {
            var result = await _hangSanXuatService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating HangSanXuat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search manufacturer by code
    /// </summary>
    [HttpGet("search-code/{code}")]
    public async Task<ActionResult<HangSanXuatSearchByCodeDto>> SearchManufacturerByCode(string code)
    {
        try
        {
            var result = await _hangSanXuatService.SearchManufacturerByCodeAsync(code);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching manufacturer by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive manufacturer categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<ManufacturerCategoryDto>>> GetManufacturerCategories()
    {
        try
        {
            var result = await _hangSanXuatService.GetManufacturerCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get manufacturers with vehicle types
    /// </summary>
    [HttpGet("with-vehicle-types")]
    public async Task<ActionResult<IEnumerable<HangSanXuatWithVehicleTypesDto>>> GetManufacturersWithVehicleTypes()
    {
        try
        {
            var result = await _hangSanXuatService.GetManufacturersWithVehicleTypesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturers with vehicle types");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get manufacturer statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<HangSanXuatStatsDto>> GetManufacturerStats(string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.GetManufacturerStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get manufacturers by country
    /// </summary>
    [HttpGet("by-country")]
    public async Task<ActionResult<IEnumerable<ManufacturerCountryDto>>> GetManufacturersByCountry()
    {
        try
        {
            var result = await _hangSanXuatService.GetManufacturersByCountryAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturers by country");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get filtered manufacturer list
    /// </summary>
    [HttpPost("filtered")]
    public async Task<ActionResult<IEnumerable<HangSanXuatListDto>>> GetFilteredList([FromBody] HangSanXuatFilterDto filterDto)
    {
        try
        {
            var result = await _hangSanXuatService.GetFilteredListAsync(filterDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting filtered manufacturer list");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadName method endpoint
    /// </summary>
    [HttpPost("loadname")]
    public async Task<ActionResult<bool>> LoadName([FromBody] string tenViet)
    {
        try
        {
            var result = await _hangSanXuatService.LoadNameAsync(tenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadName endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] HangSanXuatSaveRequestDto request)
    {
        try
        {
            var result = await _hangSanXuatService.SaveAsync(request.Dto, request.Action);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint with key filter
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] HangSanXuatShowListRequestDto? request = null)
    {
        try
        {
            var keyFilter = request?.KeyFilter ?? "";
            var fieldNameFilter = request?.FieldNameFilter ?? "";
            var result = await _hangSanXuatService.ShowListAsync(keyFilter, fieldNameFilter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _hangSanXuatService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] HangSanXuatSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _hangSanXuatService.SearchByCodeAsync(request.Code);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] HangSanXuatShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _hangSanXuatService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] HangSanXuatTrungMaRequestDto request)
    {
        try
        {
            var result = await _hangSanXuatService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _hangSanXuatService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for HangSanXuat

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class HangSanXuatValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class HangSanXuatSaveRequestDto
{
    public HangSanXuatDto Dto { get; set; } = new();
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method with key filter
/// </summary>
public class HangSanXuatShowListRequestDto
{
    public string KeyFilter { get; set; } = string.Empty;
    public string FieldNameFilter { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method
/// </summary>
public class HangSanXuatSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class HangSanXuatShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class HangSanXuatTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
