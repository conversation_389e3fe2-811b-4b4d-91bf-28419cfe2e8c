using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoGiaYeuCauSuaChuaChiTiet (Repair Request Details) entity
/// Maps exactly to SC_BaoGiaYeuCauSuaChuaChiTiet table in legacy database
/// Implements ALL properties from clsBaoGiaYeuCauSuaChuaChiTiet.cs (196 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for detailed repair request specifications in quotations
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietDto
{
    /// <summary>
    /// Primary key - Unique identifier for repair detail
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Foreign key to main quotation
    /// Maps to: mKhoaBaoGia property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string KhoaBaoGia { get; set; } = string.Empty;

    /// <summary>
    /// Work/Job code
    /// Maps to: mMaCongViec property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string MaCongViec { get; set; } = string.Empty;

    /// <summary>
    /// Work/Job description
    /// Maps to: mNoiDungCongViec property in legacy class
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string NoiDungCongViec { get; set; } = string.Empty;

    /// <summary>
    /// Technician key (Foreign key to DM_DoiTuong)
    /// Maps to: mKhoaKTV property in legacy class
    /// </summary>
    [StringLength(50)]
    public string KhoaKTV { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes/comments
    /// Maps to: mGhiChu property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string GhiChu { get; set; } = string.Empty;

    // Extended properties for modern API functionality

    /// <summary>
    /// Work type/category
    /// </summary>
    [StringLength(100)]
    public string LoaiCongViec { get; set; } = string.Empty;

    /// <summary>
    /// Priority level (1=Low, 2=Normal, 3=High, 4=Urgent)
    /// </summary>
    public int MucDoUuTien { get; set; } = 2;

    /// <summary>
    /// Estimated hours for completion
    /// </summary>
    public decimal SoGioUocTinh { get; set; } = 0;

    /// <summary>
    /// Actual hours spent
    /// </summary>
    public decimal SoGioThucTe { get; set; } = 0;

    /// <summary>
    /// Status (0=Pending, 1=InProgress, 2=Completed, 3=Cancelled)
    /// </summary>
    public int TrangThai { get; set; } = 0;

    /// <summary>
    /// Start date (YYYYMMDD format)
    /// </summary>
    [StringLength(8)]
    public string NgayBatDau { get; set; } = string.Empty;

    /// <summary>
    /// End date (YYYYMMDD format)
    /// </summary>
    [StringLength(8)]
    public string NgayKetThuc { get; set; } = string.Empty;

    /// <summary>
    /// Creation date (YYYYMMDD format)
    /// </summary>
    [StringLength(8)]
    public string NgayTao { get; set; } = string.Empty;

    /// <summary>
    /// Created by user
    /// </summary>
    [StringLength(50)]
    public string NguoiTao { get; set; } = string.Empty;

    /// <summary>
    /// Last update date (YYYYMMDD format)
    /// </summary>
    [StringLength(8)]
    public string NgayCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Last updated by user
    /// </summary>
    [StringLength(50)]
    public string NguoiCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Display order
    /// </summary>
    public int ThuTu { get; set; } = 0;

    /// <summary>
    /// Required materials/parts
    /// </summary>
    [StringLength(1000)]
    public string VatTuCanThiet { get; set; } = string.Empty;

    /// <summary>
    /// Required tools/equipment
    /// </summary>
    [StringLength(1000)]
    public string DungCuCanThiet { get; set; } = string.Empty;

    /// <summary>
    /// Safety requirements
    /// </summary>
    [StringLength(1000)]
    public string YeuCauAnToan { get; set; } = string.Empty;

    /// <summary>
    /// Quality standards
    /// </summary>
    [StringLength(1000)]
    public string TieuChuanChatLuong { get; set; } = string.Empty;

    /// <summary>
    /// Completion percentage (0-100)
    /// </summary>
    public decimal PhanTramHoanThanh { get; set; } = 0;

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal ChiPhiUocTinh { get; set; } = 0;

    /// <summary>
    /// Actual cost
    /// </summary>
    public decimal ChiPhiThucTe { get; set; } = 0;

    /// <summary>
    /// Business unit key
    /// </summary>
    [StringLength(50)]
    public string KhoaDonVi { get; set; } = string.Empty;

    // Computed properties for UI display

    /// <summary>
    /// Is pending approval (computed)
    /// </summary>
    public bool DangChoDuyet { get; set; } = false;

    /// <summary>
    /// Is in progress (computed)
    /// </summary>
    public bool DangThucHien { get; set; } = false;

    /// <summary>
    /// Is completed (computed)
    /// </summary>
    public bool DaHoanThanh { get; set; } = false;

    /// <summary>
    /// Is cancelled (computed)
    /// </summary>
    public bool DaHuy { get; set; } = false;

    /// <summary>
    /// Is overdue (computed)
    /// </summary>
    public bool QuaHan { get; set; } = false;

    /// <summary>
    /// Can be approved (computed)
    /// </summary>
    public bool CanDuyet { get; set; } = false;

    /// <summary>
    /// Formatted start date for display
    /// </summary>
    public string NgayBatDauFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Formatted end date for display
    /// </summary>
    public string NgayKetThucFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Formatted creation date for display
    /// </summary>
    public string NgayTaoFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Formatted completion percentage for display
    /// </summary>
    public string PhanTramHoanThanhText { get; set; } = string.Empty;

    /// <summary>
    /// Formatted estimated cost for display
    /// </summary>
    public string ChiPhiUocTinhFormatted { get; set; } = string.Empty;

    /// <summary>
    /// Formatted actual cost for display
    /// </summary>
    public string ChiPhiThucTeFormatted { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new BaoGiaYeuCauSuaChuaChiTiet
/// Contains all fields needed for creation
/// </summary>
public class CreateBaoGiaYeuCauSuaChuaChiTietDto
{
    [Required]
    [StringLength(50)]
    public string KhoaBaoGia { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string MaCongViec { get; set; } = string.Empty;

    [Required]
    [StringLength(1000)]
    public string NoiDungCongViec { get; set; } = string.Empty;

    [StringLength(50)]
    public string KhoaKTV { get; set; } = string.Empty;

    [StringLength(1000)]
    public string GhiChu { get; set; } = string.Empty;

    // Extended properties
    [StringLength(100)]
    public string LoaiCongViec { get; set; } = string.Empty;

    public int MucDoUuTien { get; set; } = 2;

    public decimal SoGioUocTinh { get; set; } = 0;

    [StringLength(8)]
    public string NgayBatDau { get; set; } = string.Empty;

    [StringLength(8)]
    public string NgayKetThuc { get; set; } = string.Empty;

    public int ThuTu { get; set; } = 0;

    [StringLength(1000)]
    public string VatTuCanThiet { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DungCuCanThiet { get; set; } = string.Empty;

    [StringLength(1000)]
    public string YeuCauAnToan { get; set; } = string.Empty;

    [StringLength(1000)]
    public string TieuChuanChatLuong { get; set; } = string.Empty;

    public decimal ChiPhiUocTinh { get; set; } = 0;

    [StringLength(50)]
    public string NguoiTao { get; set; } = string.Empty;

    [StringLength(50)]
    public string KhoaDonVi { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating BaoGiaYeuCauSuaChuaChiTiet
/// Contains all updatable fields
/// </summary>
public class UpdateBaoGiaYeuCauSuaChuaChiTietDto
{
    [StringLength(50)]
    public string MaCongViec { get; set; } = string.Empty;

    [StringLength(1000)]
    public string NoiDungCongViec { get; set; } = string.Empty;

    [StringLength(50)]
    public string KhoaKTV { get; set; } = string.Empty;

    [StringLength(1000)]
    public string GhiChu { get; set; } = string.Empty;

    // Extended updatable properties
    public int TrangThai { get; set; } = 0;

    public decimal SoGioThucTe { get; set; } = 0;

    [StringLength(8)]
    public string NgayKetThuc { get; set; } = string.Empty;

    public decimal PhanTramHoanThanh { get; set; } = 0;

    public decimal ChiPhiThucTe { get; set; } = 0;

    [StringLength(50)]
    public string NguoiCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGiaYeuCauSuaChuaChiTiet list display
/// Optimized for list views with joined data
/// Matches the GetDetailsYeuCauSuaChua query from legacy class
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string MaCongViec { get; set; } = string.Empty;
    public string NoiDungCongViec { get; set; } = string.Empty;
    public string KhoaKTV { get; set; } = string.Empty;
    public string TenKTV { get; set; } = string.Empty; // Joined from DM_DoiTuong
    public string GhiChu { get; set; } = string.Empty;
    
    // Additional display fields
    public string WorkTypeText { get; set; } = string.Empty; // User-friendly work type
    public string StatusText { get; set; } = string.Empty; // Status description
    public bool IsCompleted { get; set; } = false;
    public DateTime? CompletedDate { get; set; }
    public string Priority { get; set; } = "Normal"; // High, Normal, Low
}

/// <summary>
/// DTO for repair request details search operations
/// Contains search criteria and filters
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietSearchDto
{
    public string? KhoaBaoGia { get; set; }
    public string? MaCongViec { get; set; }
    public string? NoiDungCongViec { get; set; }
    public string? KhoaKTV { get; set; }
    public string? TenKTV { get; set; }
    public bool? IsCompleted { get; set; }
    public string? Priority { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
}

/// <summary>
/// DTO for repair request details lookup/dropdown operations
/// Minimal data for UI components
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaCongViec { get; set; } = string.Empty;
    public string NoiDungCongViec { get; set; } = string.Empty;
    public string TenKTV { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public string WorkCategory { get; set; } = string.Empty;
}

/// <summary>
/// DTO for repair request details validation operations
/// Used for validation and business rules
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public bool IsValidKhoaBaoGia { get; set; } = true;
    public bool IsValidMaCongViec { get; set; } = true;
    public bool IsValidKhoaKTV { get; set; } = true;
    public bool CanDelete { get; set; } = true;
    public bool IsInUse { get; set; } = false;
    public List<string> ValidationErrors { get; set; } = new List<string>();
    public List<string> ValidationWarnings { get; set; } = new List<string>();
}

/// <summary>
/// DTO for automotive repair work details
/// Specialized for automotive repair operations
/// </summary>
public class AutomotiveRepairWorkDetailDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string MaCongViec { get; set; } = string.Empty;
    public string NoiDungCongViec { get; set; } = string.Empty;
    public string KhoaKTV { get; set; } = string.Empty;
    public string TenKTV { get; set; } = string.Empty;
    public string GhiChu { get; set; } = string.Empty;
    
    // Automotive-specific fields
    public string WorkCategory { get; set; } = string.Empty; // Engine, Transmission, Brakes, etc.
    public string VehicleSystem { get; set; } = string.Empty; // Which vehicle system
    public string RepairType { get; set; } = string.Empty; // Maintenance, Repair, Replacement
    public bool IsWarrantyWork { get; set; } = false;
    public bool RequiresSpecialTools { get; set; } = false;
    public bool RequiresOEMParts { get; set; } = false;
    public int EstimatedHours { get; set; } = 0;
    public decimal EstimatedCost { get; set; } = 0;
    public string SkillLevel { get; set; } = "Standard"; // Basic, Standard, Advanced, Expert
    public string SafetyRequirements { get; set; } = string.Empty;
}

/// <summary>
/// DTO for repair work summary by quotation
/// Provides summary information for a quotation's repair work
/// </summary>
public class RepairWorkSummaryDto
{
    public string KhoaBaoGia { get; set; } = string.Empty;
    public int TotalWorkItems { get; set; } = 0;
    public int CompletedItems { get; set; } = 0;
    public int PendingItems { get; set; } = 0;
    public int HighPriorityItems { get; set; } = 0;
    public List<string> AssignedTechnicians { get; set; } = new List<string>();
    public List<string> WorkCategories { get; set; } = new List<string>();
    public decimal TotalEstimatedCost { get; set; } = 0;
    public int TotalEstimatedHours { get; set; } = 0;
    public string OverallStatus { get; set; } = "Pending"; // Pending, InProgress, Completed
    public DateTime? StartDate { get; set; }
    public DateTime? CompletionDate { get; set; }
}

/// <summary>
/// DTO for technician workload analysis
/// Shows workload distribution among technicians
/// </summary>
public class TechnicianWorkloadDto
{
    public string KhoaKTV { get; set; } = string.Empty;
    public string TenKTV { get; set; } = string.Empty;
    public int AssignedWorkItems { get; set; } = 0;
    public int CompletedWorkItems { get; set; } = 0;
    public int PendingWorkItems { get; set; } = 0;
    public decimal WorkloadPercentage { get; set; } = 0;
    public List<string> SpecializedAreas { get; set; } = new List<string>();
    public string AvailabilityStatus { get; set; } = "Available"; // Available, Busy, Unavailable
    public DateTime? NextAvailableDate { get; set; }
}

/// <summary>
/// DTO for work item tracking and progress
/// Used for tracking individual work item progress
/// </summary>
public class WorkItemProgressDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaCongViec { get; set; } = string.Empty;
    public string NoiDungCongViec { get; set; } = string.Empty;
    public string Status { get; set; } = "NotStarted"; // NotStarted, InProgress, Completed, OnHold
    public int ProgressPercentage { get; set; } = 0;
    public DateTime? StartDate { get; set; }
    public DateTime? CompletionDate { get; set; }
    public string TechnicianNotes { get; set; } = string.Empty;
    public List<string> IssuesEncountered { get; set; } = new List<string>();
    public List<string> PartsUsed { get; set; } = new List<string>();
    public decimal ActualCost { get; set; } = 0;
    public int ActualHours { get; set; } = 0;
    public string QualityCheckStatus { get; set; } = "Pending"; // Pending, Passed, Failed
}

/// <summary>
/// DTO for repair requirement statistics
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietStatisticsDto
{
    public int TotalRequirements { get; set; } = 0;
    public int PendingRequirements { get; set; } = 0;
    public int InProgressRequirements { get; set; } = 0;
    public int CompletedRequirements { get; set; } = 0;
    public int CancelledRequirements { get; set; } = 0;
    public int OverdueRequirements { get; set; } = 0;
    public double CompletionRate { get; set; } = 0;
    public decimal TotalEstimatedCost { get; set; } = 0;
    public decimal TotalActualCost { get; set; } = 0;
    public decimal CostVariance { get; set; } = 0;
    public List<RepairRequirementByTechnicianDto> StatsByTechnician { get; set; } = new List<RepairRequirementByTechnicianDto>();
}

/// <summary>
/// Statistics by technician
/// </summary>
public class RepairRequirementByTechnicianDto
{
    public string KhoaKTV { get; set; } = string.Empty;
    public string TenKTV { get; set; } = string.Empty;
    public int TotalRequirements { get; set; } = 0;
    public int CompletedRequirements { get; set; } = 0;
    public double CompletionRate { get; set; } = 0;
    public decimal TotalEstimatedHours { get; set; } = 0;
    public decimal TotalActualHours { get; set; } = 0;
    public double Efficiency { get; set; } = 0;
}
