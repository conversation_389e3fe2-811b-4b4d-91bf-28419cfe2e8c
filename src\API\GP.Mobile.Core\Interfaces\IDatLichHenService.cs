using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Service interface for Appointment Scheduling (Đặt Lịch Hẹn) business logic
    /// Provides high-level business operations for appointment management
    /// </summary>
    public interface IDatLichHenService
    {
        /// <summary>
        /// Get appointment by ID
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <returns>Appointment data or null if not found</returns>
        Task<DatLichHenDto?> GetByIdAsync(string khoa);

        /// <summary>
        /// Get appointment by related quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Appointment data or null if not found</returns>
        Task<DatLichHenDto?> GetByBaoGiaIdAsync(string khoaBaoGia);

        /// <summary>
        /// Create new appointment
        /// </summary>
        /// <param name="appointment">Appointment data to create</param>
        /// <returns>Created appointment ID or empty string if failed</returns>
        Task<string> CreateAsync(DatLichHenDto appointment);

        /// <summary>
        /// Update existing appointment
        /// </summary>
        /// <param name="appointment">Appointment data to update</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateAsync(DatLichHenDto appointment);

        /// <summary>
        /// Delete appointment
        /// </summary>
        /// <param name="khoa">Appointment ID to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(string khoa);

        /// <summary>
        /// Get today's appointments for dashboard
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of today's appointments</returns>
        Task<List<DatLichHenDto>> GetTodayAppointmentsAsync(string donViId);

        /// <summary>
        /// Get upcoming appointments (next 7 days)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of upcoming appointments</returns>
        Task<List<DatLichHenDto>> GetUpcomingAppointmentsAsync(string donViId);

        /// <summary>
        /// Get appointments by date range
        /// </summary>
        /// <param name="fromDate">Start date (YYYY-MM-DD format)</param>
        /// <param name="toDate">End date (YYYY-MM-DD format)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of appointments in date range</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByDateRangeAsync(string fromDate, string toDate, string donViId);

        /// <summary>
        /// Get appointments by vehicle
        /// </summary>
        /// <param name="khoaXe">Vehicle ID</param>
        /// <returns>List of appointments for the vehicle</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByVehicleAsync(string khoaXe);

        /// <summary>
        /// Get appointments by customer
        /// </summary>
        /// <param name="khoaKhachHang">Customer ID</param>
        /// <returns>List of appointments for the customer</returns>
        Task<List<DatLichHenDto>> GetAppointmentsByCustomerAsync(string khoaKhachHang);

        /// <summary>
        /// Get appointments requiring reminder calls
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>DataTable with reminder appointments</returns>
        Task<DataTable> GetReminderAppointmentsAsync(string donViId);

        /// <summary>
        /// Get appointments requiring reminder calls (1 day before)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>DataTable with appointments needing reminders</returns>
        Task<DataTable> GetOneDayReminderAppointmentsAsync(string donViId);

        /// <summary>
        /// Update reminder call status
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="noiDungGoiNhacHen">Reminder call content</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateReminderCallStatusAsync(string khoa, string noiDungGoiNhacHen);

        /// <summary>
        /// Check for appointment time conflicts
        /// </summary>
        /// <param name="ngayDatHen">Appointment date (YYYY-MM-DD format)</param>
        /// <param name="gioDatHen">Appointment time (HH:mm format)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="excludeKhoa">Exclude this appointment ID from conflict check</param>
        /// <returns>True if conflict exists, false otherwise</returns>
        Task<bool> CheckAppointmentConflictAsync(string ngayDatHen, string gioDatHen, string donViId, string excludeKhoa = "");

        /// <summary>
        /// Validate appointment data before saving
        /// </summary>
        /// <param name="appointment">Appointment data to validate</param>
        /// <returns>Validation result with error messages</returns>
        Task<ValidationResult> ValidateAppointmentAsync(DatLichHenDto appointment);

        /// <summary>
        /// Generate appointment document number
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="ngayChungTu">Document date</param>
        /// <returns>Generated document number</returns>
        Task<string> GenerateDocumentNumberAsync(string donViId, string ngayChungTu);

        /// <summary>
        /// Convert appointment to quotation
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="userId">User ID performing the conversion</param>
        /// <returns>Created quotation ID or empty string if failed</returns>
        Task<string> ConvertToQuotationAsync(string khoa, string userId);

        /// <summary>
        /// Get appointment statistics for dashboard
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date for statistics</param>
        /// <param name="toDate">End date for statistics</param>
        /// <returns>Appointment statistics</returns>
        Task<AppointmentStatisticsDto> GetAppointmentStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Send reminder notifications (SMS/Email)
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="reminderType">Type of reminder (SMS, Email, Both)</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SendReminderNotificationAsync(string khoa, ReminderType reminderType);

        /// <summary>
        /// Get available time slots for appointment scheduling
        /// </summary>
        /// <param name="ngayDatHen">Appointment date</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of available time slots</returns>
        Task<List<TimeSlotDto>> GetAvailableTimeSlotsAsync(string ngayDatHen, string donViId);

        /// <summary>
        /// Reschedule appointment to new date/time
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="newNgayDatHen">New appointment date</param>
        /// <param name="newGioDatHen">New appointment time</param>
        /// <param name="reason">Reason for rescheduling</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> RescheduleAppointmentAsync(string khoa, string newNgayDatHen, string newGioDatHen, string reason);

        /// <summary>
        /// Cancel appointment
        /// </summary>
        /// <param name="khoa">Appointment ID</param>
        /// <param name="reason">Cancellation reason</param>
        /// <param name="userId">User ID performing the cancellation</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> CancelAppointmentAsync(string khoa, string reason, string userId);
    }

    /// <summary>
    /// Validation result for appointment data
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }

    /// <summary>
    /// Appointment statistics for dashboard
    /// </summary>
    public class AppointmentStatisticsDto
    {
        public int TotalAppointments { get; set; }
        public int TodayAppointments { get; set; }
        public int UpcomingAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int PendingReminders { get; set; }
        public double AverageAppointmentsPerDay { get; set; }
        public List<AppointmentByServiceTypeDto> AppointmentsByServiceType { get; set; } = new List<AppointmentByServiceTypeDto>();
    }

    /// <summary>
    /// Appointment count by service type
    /// </summary>
    public class AppointmentByServiceTypeDto
    {
        public string ServiceTypeName { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// Available time slot for appointment scheduling
    /// </summary>
    public class TimeSlotDto
    {
        public string Time { get; set; } = string.Empty;
        public bool IsAvailable { get; set; }
        public int BookedCount { get; set; }
        public int MaxCapacity { get; set; }
    }

    /// <summary>
    /// Reminder notification types
    /// </summary>
    public enum ReminderType
    {
        SMS = 1,
        Email = 2,
        Both = 3
    }
}
