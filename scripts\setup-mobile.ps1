# Setup script for GP Mobile React Native app
Write-Host "Setting up GP Mobile React Native app..." -ForegroundColor Green

# Check if Node.js is installed
if (!(Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Host "Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    exit 1
}

# Check if Expo CLI is installed
if (!(Get-Command "expo" -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Expo CLI..." -ForegroundColor Yellow
    npm install -g @expo/cli
}

# Navigate to Mobile directory
Set-Location "src\Mobile"

# Install dependencies
Write-Host "Installing npm dependencies..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "Mobile app setup completed successfully!" -ForegroundColor Green
    Write-Host "To start the development server, run: npm start" -ForegroundColor Cyan
} else {
    Write-Host "Mobile app setup failed!" -ForegroundColor Red
    exit 1
}

# Return to root directory
Set-Location "..\..\"
