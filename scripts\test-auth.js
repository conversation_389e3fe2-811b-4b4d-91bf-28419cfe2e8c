/**
 * Test Authentication Database Connection
 * Verifies that the database tables exist and authentication logic works
 */

const sql = require('mssql');
const crypto = require('crypto');

// Database configuration (user's actual connection)
const config = {
    server: 'DESKTOP-J990JBB',
    database: 'carsoft_giaphat',
    user: 'sa',
    password: 'cxM123654',
    options: {
        enableArithAbort: true,
        trustServerCertificate: true,
        encrypt: false
    }
};

// MD5 hash function (same as legacy)
function computeMD5Hash(input) {
    return crypto.createHash('md5').update(input).digest('hex').toUpperCase();
}

async function testDatabaseConnection() {
    try {
        console.log('🔌 Testing database connection...');
        const pool = await sql.connect(config);
        console.log('✅ Database connection successful!');
        
        // Test if HT_NguoiDung table exists
        console.log('\n📋 Checking HT_NguoiDung table...');
        const userTableResult = await pool.request().query(`
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'HT_NguoiDung'
        `);
        
        if (userTableResult.recordset[0].count > 0) {
            console.log('✅ HT_NguoiDung table exists');
            
            // Get sample users
            const usersResult = await pool.request().query(`
                SELECT TOP 5 
                    KhoaNhanVien, 
                    TenDangNhap, 
                    DonViDangNhap,
                    CASE WHEN MatKhau IS NOT NULL THEN 'Has Password' ELSE 'No Password' END as PasswordStatus
                FROM HT_NguoiDung
            `);
            
            console.log('\n👥 Sample users in HT_NguoiDung:');
            usersResult.recordset.forEach(user => {
                console.log(`  - ${user.TenDangNhap} (${user.KhoaNhanVien}) - ${user.PasswordStatus}`);
                if (user.DonViDangNhap) {
                    console.log(`    Allowed clients: ${user.DonViDangNhap}`);
                }
            });
        } else {
            console.log('❌ HT_NguoiDung table does not exist');
        }
        
        // Test if DM_DonVi table exists
        console.log('\n📋 Checking DM_DonVi table...');
        const clientTableResult = await pool.request().query(`
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'DM_DonVi'
        `);
        
        if (clientTableResult.recordset[0].count > 0) {
            console.log('✅ DM_DonVi table exists');
            
            // Get sample clients (without Active column check)
            const clientsResult = await pool.request().query(`
                SELECT TOP 5
                    Khoa,
                    Ma,
                    TenViet
                FROM DM_DonVi
            `);
            
            console.log('\n🏢 Sample clients in DM_DonVi:');
            clientsResult.recordset.forEach(client => {
                console.log(`  - ${client.TenViet} (${client.Ma}) - Khoa: ${client.Khoa}`);
            });
        } else {
            console.log('❌ DM_DonVi table does not exist');
        }
        
        // Test if DM_DoiTuong table exists
        console.log('\n📋 Checking DM_DoiTuong table...');
        const employeeTableResult = await pool.request().query(`
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'DM_DoiTuong'
        `);
        
        if (employeeTableResult.recordset[0].count > 0) {
            console.log('✅ DM_DoiTuong table exists');
        } else {
            console.log('❌ DM_DoiTuong table does not exist');
        }
        
        await pool.close();
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
    
    return true;
}

async function testAuthenticationLogic() {
    try {
        console.log('\n🔐 Testing authentication logic...');
        
        // Test MD5 hash function
        const testPassword = 'test123';
        const md5Hash = computeMD5Hash(testPassword);
        console.log(`📝 MD5 hash of '${testPassword}': ${md5Hash}`);
        
        // Test master password
        const masterPasswordHash = '8F866D5EA7686D4458E39AEEF07DEC1A';
        console.log(`🔑 Master password hash: ${masterPasswordHash}`);
        
        // Test if we can find what the master password is
        const commonPasswords = [
            'admin', 'password', '123456', 'admin123', 'carsoft', 'giaphat',
            'gp', 'gp123', 'gp2024', 'gp2023', 'gp2022', 'gp2021',
            'carsoft123', 'giaphat123', 'sa', 'sa123', 'root', 'root123',
            'manager', 'user', 'test', 'demo', 'default', 'system',
            'adv', 'hieu', 'huethanh', 'ngan', 'tinh', // User names as passwords
            'adv123', 'hieu123', 'huethanh123', 'ngan123', 'tinh123',
            '1', '12', '123', '1234', '12345', '123456789',
            'a', 'aa', 'aaa', 'aaaa', 'aaaaa'
        ];
        console.log('\n🔍 Testing common passwords against master hash:');

        let masterPasswordFound = false;
        for (const pwd of commonPasswords) {
            const hash = computeMD5Hash(pwd);
            if (hash === masterPasswordHash) {
                console.log(`✅ Master password found: '${pwd}' -> ${hash}`);
                masterPasswordFound = true;
                break;
            }
        }

        if (!masterPasswordFound) {
            console.log('❌ Master password not found in common passwords list');
            console.log('🔍 Showing first few attempts:');
            for (let i = 0; i < Math.min(5, commonPasswords.length); i++) {
                const pwd = commonPasswords[i];
                const hash = computeMD5Hash(pwd);
                console.log(`   '${pwd}' -> ${hash}`);
            }
        }
        
    } catch (error) {
        console.error('❌ Authentication logic test failed:', error.message);
    }
}

async function testUserAuthentication() {
    try {
        console.log('\n👤 Testing user authentication...');
        const pool = await sql.connect(config);

        // Get all users for password analysis
        const usersResult = await pool.request().query(`
            SELECT
                KhoaNhanVien,
                TenDangNhap,
                MatKhau,
                DonViDangNhap
            FROM HT_NguoiDung
            WHERE TenDangNhap IS NOT NULL AND TenDangNhap != ''
            ORDER BY TenDangNhap
        `);

        if (usersResult.recordset.length > 0) {
            console.log(`📋 Found ${usersResult.recordset.length} users for password testing:`);

            const masterHash = '8F866D5EA7686D4458E39AEEF07DEC1A';

            for (const user of usersResult.recordset) {
                console.log(`\n👤 User: ${user.TenDangNhap} (${user.KhoaNhanVien})`);
                console.log(`🔑 Hash: ${user.MatKhau}`);
                console.log(`🏢 Clients: ${user.DonViDangNhap || 'None'}`);

                // Test if user has master password
                if (user.MatKhau === masterHash) {
                    console.log('✅ This user has the MASTER PASSWORD hash!');
                    continue;
                }

                // Test common passwords for this user
                const testPasswords = [
                    user.TenDangNhap, // Username as password
                    user.TenDangNhap + '123',
                    user.TenDangNhap + '1',
                    user.TenDangNhap + '2024',
                    '123456', 'password', 'admin', '123',
                    'gp', 'gp123', 'carsoft', 'giaphat'
                ];

                let passwordFound = false;
                for (const testPwd of testPasswords) {
                    const testHash = computeMD5Hash(testPwd);
                    if (testHash === user.MatKhau) {
                        console.log(`✅ PASSWORD FOUND: '${testPwd}'`);
                        passwordFound = true;
                        break;
                    }
                }

                if (!passwordFound) {
                    console.log('❌ Password not found in common patterns');
                }
            }
        } else {
            console.log('❌ No users found in HT_NguoiDung table');
        }

        await pool.close();

    } catch (error) {
        console.error('❌ User authentication test failed:', error.message);
    }
}

async function main() {
    console.log('🚀 GP Mobile Authentication Test');
    console.log('================================\n');
    
    const dbConnected = await testDatabaseConnection();
    
    if (dbConnected) {
        await testAuthenticationLogic();
        await testUserAuthentication();
    }
    
    console.log('\n✅ Test completed!');
}

// Run the test
main().catch(console.error);
