using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for Kho (Warehouse/Storage) entity
/// Implements ALL endpoints from clsDMKho.cs (482 lines)
/// Includes REST API and 9+ legacy method endpoints
/// Maps to DM_Kho table with 10 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and warehouse operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class KhoController : ControllerBase
{
    private readonly IKhoService _khoService;
    private readonly ILogger<KhoController> _logger;

    public KhoController(IKhoService khoService, ILogger<KhoController> logger)
    {
        _khoService = khoService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all warehouses
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<KhoListDto>>> GetAll()
    {
        try
        {
            var result = await _khoService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all warehouses");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouse by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<KhoDto>> GetById(string khoa)
    {
        try
        {
            var result = await _khoService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouse by code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<KhoDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _khoService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new warehouse
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateKhoDto createDto)
    {
        try
        {
            var result = await _khoService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo kho");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating warehouse");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update warehouse
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] KhoDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _khoService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating warehouse");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete warehouse
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _khoService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting warehouse");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update warehouse status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateKhoStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _khoService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating warehouse status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search warehouses
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<KhoListDto>>> Search([FromBody] KhoSearchDto searchDto)
    {
        try
        {
            var result = await _khoService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching warehouses");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouse lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<KhoLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _khoService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate warehouse data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<KhoValidationDto>> Validate([FromBody] KhoValidationRequestDto request)
    {
        try
        {
            var result = await _khoService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating warehouse");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search warehouse by code (modern)
    /// </summary>
    [HttpPost("search-by-code")]
    public async Task<ActionResult<KhoSearchByCodeDto>> SearchByCodeModern([FromBody] KhoSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _khoService.SearchByCodeModernAsync(request.Code, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching warehouse by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive warehouse categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<WarehouseCategoryDto>>> GetWarehouseCategories()
    {
        try
        {
            var result = await _khoService.GetWarehouseCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouses with inventory summary
    /// </summary>
    [HttpGet("with-inventory")]
    public async Task<ActionResult<IEnumerable<KhoWithInventoryDto>>> GetWarehousesWithInventory()
    {
        try
        {
            var result = await _khoService.GetWarehousesWithInventoryAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouses with inventory");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouse statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<KhoStatsDto>> GetWarehouseStats(string khoa)
    {
        try
        {
            var result = await _khoService.GetWarehouseStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warehouse capacity information
    /// </summary>
    [HttpGet("capacity")]
    public async Task<ActionResult<IEnumerable<WarehouseCapacityDto>>> GetWarehouseCapacity()
    {
        try
        {
            var result = await _khoService.GetWarehouseCapacityAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse capacity");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive warehouses with specialization
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveWarehouseDto>>> GetAutomotiveWarehouses()
    {
        try
        {
            var result = await _khoService.GetAutomotiveWarehousesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive warehouses");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _khoService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] KhoSaveRequestDto request)
    {
        try
        {
            var result = await _khoService.SaveAsync(request.Dto, request.Task);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _khoService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] KhoShowListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _khoService.ShowListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _khoService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] KhoShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _khoService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] KhoSearchByCodeLegacyRequestDto request)
    {
        try
        {
            var result = await _khoService.SearchByCodeAsync(request.Code, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] KhoTrungMaRequestDto request)
    {
        try
        {
            var result = await _khoService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _khoService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for Kho

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class KhoValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for search by code (modern) endpoint
/// </summary>
public class KhoSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class KhoSaveRequestDto
{
    public KhoDto Dto { get; set; } = new();
    public string Task { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class KhoShowListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class KhoShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method (legacy)
/// </summary>
public class KhoSearchByCodeLegacyRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class KhoTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
