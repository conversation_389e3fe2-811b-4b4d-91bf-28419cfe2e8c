'use client'

import { useState } from 'react'
import { Plus, Search, Edit, Trash2, Eye, Car } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { withAuth } from '@/contexts/AuthContext'

// Mock vehicle data
const mockVehicles = [
  {
    id: 'XE001',
    licensePlate: '30A-12345',
    brand: 'Toyota',
    model: 'Camry',
    year: 2020,
    color: 'Trắng',
    engineNumber: 'EN123456',
    chassisNumber: 'CH789012',
    customerName: '<PERSON><PERSON>ễn Văn An',
    customerId: 'KH001',
    status: 'Hoạt động',
    lastService: '2024-05-15',
    totalServices: 8,
    mileage: 45000
  },
  {
    id: 'XE002',
    licensePlate: '51G-67890',
    brand: 'Honda',
    model: 'City',
    year: 2019,
    color: 'Đen',
    engineNumber: 'EN654321',
    chassisNumber: 'CH345678',
    customerName: 'Trần Thị Bình',
    customerId: 'KH003',
    status: 'Hoạt động',
    lastService: '2024-06-01',
    totalServices: 12,
    mileage: 38000
  },
  {
    id: 'XE003',
    licensePlate: '29B-11111',
    brand: 'Mazda',
    model: '3',
    year: 2021,
    color: 'Đỏ',
    engineNumber: 'EN987654',
    chassisNumber: 'CH111222',
    customerName: 'Công ty TNHH XYZ',
    customerId: 'KH002',
    status: 'Bảo trì',
    lastService: '2024-06-10',
    totalServices: 5,
    mileage: 25000
  }
]

function VehiclesPage() {
  const [vehicles, setVehicles] = useState(mockVehicles)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBrand, setFilterBrand] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)

  // Get unique brands for filter
  const brands = Array.from(new Set(vehicles.map(v => v.brand)))

  // Filter vehicles
  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = vehicle.licensePlate.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.customerName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesBrand = filterBrand === 'all' || vehicle.brand === filterBrand
    const matchesStatus = filterStatus === 'all' || vehicle.status === filterStatus
    
    return matchesSearch && matchesBrand && matchesStatus
  })

  const handleViewVehicle = (vehicle: any) => {
    setSelectedVehicle(vehicle)
    setIsDetailDialogOpen(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Hoạt động':
        return <Badge variant="default" className="bg-green-100 text-green-800">{status}</Badge>
      case 'Bảo trì':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">{status}</Badge>
      case 'Ngưng hoạt động':
        return <Badge variant="destructive">{status}</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản lý phương tiện</h1>
          <p className="text-muted-foreground">
            Quản lý thông tin xe và lịch sử bảo dưỡng
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm phương tiện
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng số xe</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{vehicles.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đang hoạt động</CardTitle>
            <Car className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vehicles.filter(v => v.status === 'Hoạt động').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đang bảo trì</CardTitle>
            <Car className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vehicles.filter(v => v.status === 'Bảo trì').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng dịch vụ</CardTitle>
            <Car className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vehicles.reduce((sum, v) => sum + v.totalServices, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo biển số, hãng xe, chủ xe..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterBrand} onValueChange={setFilterBrand}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Hãng xe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả hãng</SelectItem>
                {brands.map(brand => (
                  <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="Hoạt động">Hoạt động</SelectItem>
                <SelectItem value="Bảo trì">Bảo trì</SelectItem>
                <SelectItem value="Ngưng hoạt động">Ngưng hoạt động</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách phương tiện</CardTitle>
          <CardDescription>
            Tổng cộng {filteredVehicles.length} phương tiện
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Biển số</TableHead>
                  <TableHead>Xe</TableHead>
                  <TableHead>Chủ xe</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Km đã chạy</TableHead>
                  <TableHead>Lần bảo dưỡng cuối</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id}>
                    <TableCell className="font-medium">{vehicle.licensePlate}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{vehicle.brand} {vehicle.model}</div>
                        <div className="text-sm text-muted-foreground">
                          {vehicle.year} • {vehicle.color}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{vehicle.customerName}</TableCell>
                    <TableCell>{getStatusBadge(vehicle.status)}</TableCell>
                    <TableCell>{vehicle.mileage.toLocaleString()} km</TableCell>
                    <TableCell>{vehicle.lastService}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewVehicle(vehicle)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Chi tiết phương tiện</DialogTitle>
            <DialogDescription>
              Thông tin chi tiết của xe {selectedVehicle?.licensePlate}
            </DialogDescription>
          </DialogHeader>
          {selectedVehicle && (
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">Thông tin cơ bản</TabsTrigger>
                <TabsTrigger value="technical">Thông số kỹ thuật</TabsTrigger>
                <TabsTrigger value="history">Lịch sử bảo dưỡng</TabsTrigger>
              </TabsList>
              
              <TabsContent value="info" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Biển số xe</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.licensePlate}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Hãng xe</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.brand}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Dòng xe</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.model}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Năm sản xuất</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.year}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Màu sắc</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.color}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Chủ xe</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.customerName}</p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="technical" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Số máy</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.engineNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Số khung</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.chassisNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Số km đã chạy</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.mileage.toLocaleString()} km</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Trạng thái</label>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.status}</p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="history" className="space-y-4">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Lịch sử bảo dưỡng chi tiết sẽ được hiển thị tại đây
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Tổng số lần bảo dưỡng: {selectedVehicle.totalServices}
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default withAuth(VehiclesPage)
