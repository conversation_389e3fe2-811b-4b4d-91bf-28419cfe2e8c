'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, Edit, Eye, Save, X, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { withAuth } from '@/contexts/AuthContext'
import { baoGiaSuaChuaApi, baoGiaSuaChuaChiTietApi, type BaoGiaSuaChuaDto, type BaoGiaSuaChuaChiTietDto } from '@/services/api'

function QuotationDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const quotationId = params.id as string

  // State
  const [quotation, setQuotation] = useState<BaoGiaSuaChuaDto | null>(null)
  const [quotationDetails, setQuotationDetails] = useState<BaoGiaSuaChuaChiTietDto[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load quotation data
  useEffect(() => {
    if (quotationId) {
      loadQuotationData()
    }
  }, [quotationId])

  const loadQuotationData = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔍 Loading quotation with ID:', quotationId)

      // Load real quotation data from API
      const quotationData = await baoGiaSuaChuaApi.getById(quotationId)
      console.log('✅ Loaded quotation data:', quotationData)
      setQuotation(quotationData)

      // Load quotation details
      console.log('📊 Loading quotation details for:', quotationId)
      await loadQuotationDetails(quotationId)

    } catch (error) {
      console.error('❌ Failed to load quotation:', error)
      setError(`Không thể tải thông tin báo giá: ${error instanceof Error ? error.message : 'Lỗi không xác định'}`)
    } finally {
      setLoading(false)
    }
  }

  const loadQuotationDetails = async (khoaBaoGia: string) => {
    try {
      setLoadingDetails(true)
      console.log('📊 Calling baoGiaSuaChuaChiTietApi.getByQuotation with:', khoaBaoGia)

      const details = await baoGiaSuaChuaChiTietApi.getByQuotation(khoaBaoGia)
      console.log('✅ Loaded quotation details:', details.length, 'items')
      console.log('📊 Details data:', details)

      setQuotationDetails(details)
    } catch (error) {
      console.warn('⚠️ Failed to load quotation details:', error)
      console.warn('⚠️ Error details:', error instanceof Error ? error.message : 'Unknown error', error instanceof Error ? error.stack : '')
      setQuotationDetails([])
    } finally {
      setLoadingDetails(false)
    }
  }

  const handleBack = () => {
    router.push('/quotations')
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving quotation changes...')
  }

  const getStatusBadge = (tinhTrang: number) => {
    switch (tinhTrang) {
      case 0:
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Báo giá tạm</Badge>
      case 1:
        return <Badge variant="default" className="bg-green-100 text-green-800">Báo giá thực hiện</Badge>
      case 2:
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Báo giá hẹn</Badge>
      case 3:
        return <Badge variant="destructive">Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Chưa có ngày'
    // Convert YYYYMMDD to DD/MM/YYYY
    if (dateString.length === 8) {
      return dateString.replace(/(\d{4})(\d{2})(\d{2})/, '$3/$2/$1')
    }
    return dateString
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Đang tải...</h1>
            <p className="text-muted-foreground">Đang tải thông tin báo giá</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Đang tải chi tiết báo giá...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !quotation) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Lỗi</h1>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <X className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Không thể tải báo giá</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại danh sách
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Edit className="h-8 w-8 text-blue-600" />
              Chỉnh sửa báo giá
            </h1>
            <p className="text-muted-foreground">
              Báo giá số {quotation.soChungTu} • {quotation.khachHang}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <X className="mr-2 h-4 w-4" />
            Hủy
          </Button>
          <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
            <Save className="mr-2 h-4 w-4" />
            Lưu thay đổi
          </Button>
        </div>
      </div>

      {/* Thông tin chung - Separate from tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Thông tin chung</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Số báo giá</label>
              <p className="text-lg font-semibold">{quotation.soChungTu}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Ngày báo giá</label>
              <p className="font-medium">{formatDate(quotation.ngayChungTu)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Khách hàng</label>
              <p className="font-medium">{quotation.khachHang || 'Chưa có thông tin'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Biển số xe</label>
              <p className="font-medium">{quotation.bienSoXe || 'Chưa có biển số'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Loại xe</label>
              <p className="font-medium">{quotation.loaiXe || 'Chưa có thông tin xe'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Loại hình sửa chữa</label>
              <p className="font-medium">
                {quotation.boPhanSuaChua === 0 ? 'Đồng sơn' : 'Gầm máy'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Trạng thái</label>
              <div className="mt-1">{getStatusBadge(quotation.tinhTrangBaoGia)}</div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Tổng thanh toán</label>
              <p className="text-lg font-bold text-green-600">
                {formatCurrency(
                  (quotation.tongTienHang1 + quotation.tongTienHang2 + quotation.tongTienHang3) +
                  (quotation.tienThue1 + quotation.tienThue2 + quotation.tienThue3) -
                  (quotation.tienChietKhau1 + quotation.tienChietKhau2 + quotation.tienChietKhau3)
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comprehensive 6-Tab Interface */}
      <Card>
        <CardContent className="p-6">
          <Tabs defaultValue="items" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="items">Chi tiết hạng mục</TabsTrigger>
              <TabsTrigger value="info">Thông tin chung</TabsTrigger>
              <TabsTrigger value="documents">Hồ sơ</TabsTrigger>
              <TabsTrigger value="terms">Điều khoản</TabsTrigger>
              <TabsTrigger value="payments">Thu chi</TabsTrigger>
              <TabsTrigger value="vehicles">Loại xe</TabsTrigger>
            </TabsList>

            {/* Tab Contents will be added in the next step */}
            <TabsContent value="items" className="space-y-4 mt-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Chi tiết hạng mục sửa chữa</h3>
                <Badge variant="outline">Tương tự VSListHangMuc</Badge>
              </div>
              
              {loadingDetails ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">Đang tải chi tiết hạng mục...</p>
                </div>
              ) : quotationDetails.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Không có chi tiết hạng mục nào</p>
                </div>
              ) : (
                <div className="rounded-md border overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16 text-center">STT</TableHead>
                        <TableHead className="w-40">Phần sửa chữa</TableHead>
                        <TableHead className="min-w-[250px]">Nội dung</TableHead>
                        <TableHead className="w-24 text-center">ĐVT</TableHead>
                        <TableHead className="text-right w-24">Số lượng</TableHead>
                        <TableHead className="text-right w-32">Đơn giá</TableHead>
                        <TableHead className="text-right w-32">Thành tiền</TableHead>
                        <TableHead className="text-right w-36">Chiết khấu</TableHead>
                        <TableHead className="text-right w-36">Thuế</TableHead>
                        <TableHead className="text-right w-36">Tổng tiền</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {quotationDetails.map((detail, index) => (
                        <TableRow key={detail.khoa}>
                          <TableCell className="text-center font-medium">{index + 1}</TableCell>
                          <TableCell className="font-medium text-blue-600">
                            <div>
                              <div className="font-medium">{detail.hangMuc}</div>
                              <div className="text-xs text-muted-foreground">
                                {detail.isService ? 'Dịch vụ' : 'Phụ tùng'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{detail.noiDung}</div>
                              {detail.dienGiai && (
                                <div className="text-sm text-muted-foreground">{detail.dienGiai}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center font-medium">
                            {detail.dvt || 'Cái'}
                          </TableCell>
                          <TableCell className="text-right font-medium">{detail.soLuong1.toFixed(2)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(detail.donGia1)}</TableCell>
                          <TableCell className="text-right font-medium">{formatCurrency(detail.thanhTien1)}</TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm">
                              <div>{formatCurrency(detail.tienChietKhau1)}</div>
                              <div className="text-muted-foreground">({detail.tyLeChietKhau1}%)</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm">
                              <div>0</div>
                              <div className="text-muted-foreground">(0%)</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right font-bold text-green-600">
                            {formatCurrency(detail.thanhTienSauChietKhau)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <div className="p-6 border-t bg-gradient-to-r from-blue-50 to-green-50">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Tổng hạng mục</div>
                        <div className="font-bold text-lg">{quotationDetails.length}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Tổng tiền hàng</div>
                        <div className="font-bold text-lg text-blue-600">
                          {formatCurrency(quotationDetails.reduce((sum, detail) => sum + detail.thanhTien1, 0))}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Tổng chiết khấu</div>
                        <div className="font-bold text-lg text-red-600">
                          -{formatCurrency(quotationDetails.reduce((sum, detail) => sum + detail.tienChietKhau1, 0))}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Tổng cộng</div>
                        <div className="font-bold text-xl text-green-600">
                          {formatCurrency(quotationDetails.reduce((sum, detail) => sum + detail.thanhTienSauChietKhau, 0))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="info" className="space-y-4 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Thông tin chung</h3>
                <Badge variant="outline">Thông tin cơ bản báo giá</Badge>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Số báo giá</label>
                    <p className="text-lg font-semibold">{quotation.soChungTu}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Khách hàng</label>
                    <p className="font-medium">{quotation.khachHang || 'Chưa có thông tin'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Xe</label>
                    <p className="font-medium">
                      {quotation.bienSoXe || 'Chưa có biển số'} - {quotation.loaiXe || 'Chưa có thông tin xe'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Loại hình sửa chữa</label>
                    <p className="font-medium">
                      {quotation.boPhanSuaChua === 0 ? 'Đồng sơn' : 'Gầm máy'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Trạng thái</label>
                    <div className="mt-1">{getStatusBadge(quotation.tinhTrangBaoGia)}</div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tổng tiền hàng</label>
                    <p className="text-lg font-bold text-blue-600">
                      {formatCurrency(quotation.tongTienHang1 + quotation.tongTienHang2 + quotation.tongTienHang3)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tiền thuế</label>
                    <p className="font-medium">{formatCurrency(quotation.tienThue1 + quotation.tienThue2 + quotation.tienThue3)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tiền chiết khấu</label>
                    <p className="font-medium text-red-600">-{formatCurrency(quotation.tienChietKhau1 + quotation.tienChietKhau2 + quotation.tienChietKhau3)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tổng thanh toán</label>
                    <p className="text-xl font-bold text-green-600">
                      {formatCurrency(
                        (quotation.tongTienHang1 + quotation.tongTienHang2 + quotation.tongTienHang3) +
                        (quotation.tienThue1 + quotation.tienThue2 + quotation.tienThue3) -
                        (quotation.tienChietKhau1 + quotation.tienChietKhau2 + quotation.tienChietKhau3)
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Ngày tạo</label>
                    <p className="font-medium">
                      {quotation.ngayChungTu.replace(/(\d{4})(\d{2})(\d{2})/, '$3/$2/$1')}
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Hồ sơ đính kèm</h3>
                <Badge variant="outline">Tương tự VsListHoSo</Badge>
              </div>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">STT</TableHead>
                      <TableHead className="w-20">Chọn</TableHead>
                      <TableHead className="w-32">Mã hồ sơ</TableHead>
                      <TableHead>Tên hồ sơ</TableHead>
                      <TableHead className="w-32">Trạng thái</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="text-center">1</TableCell>
                      <TableCell className="text-center">
                        <input type="checkbox" className="rounded" />
                      </TableCell>
                      <TableCell>HS001</TableCell>
                      <TableCell>Giấy tờ xe</TableCell>
                      <TableCell>
                        <Badge variant="secondary">Chưa có</Badge>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="text-center">2</TableCell>
                      <TableCell className="text-center">
                        <input type="checkbox" className="rounded" />
                      </TableCell>
                      <TableCell>HS002</TableCell>
                      <TableCell>Bảo hiểm xe</TableCell>
                      <TableCell>
                        <Badge variant="secondary">Chưa có</Badge>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="terms" className="space-y-6 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Điều khoản</h3>
                <Badge variant="outline">VSListDKBG, DKLSC, DKQT</Badge>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Điều khoản báo giá</h4>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16">STT</TableHead>
                        <TableHead>Nội dung điều khoản</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="text-center">1</TableCell>
                        <TableCell>Báo giá có hiệu lực trong 30 ngày kể từ ngày lập</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="text-center">2</TableCell>
                        <TableCell>Giá đã bao gồm VAT 10%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Điều khoản lệnh sửa chữa</h4>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16">STT</TableHead>
                        <TableHead>Nội dung điều khoản</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="text-center">1</TableCell>
                        <TableCell>Khách hàng phải thanh toán 100% trước khi nhận xe</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="payments" className="space-y-4 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Lịch sử thu chi</h3>
                <Badge variant="outline">Tương tự VSList_ThuChi</Badge>
              </div>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">TT</TableHead>
                      <TableHead className="w-32">Số chứng từ</TableHead>
                      <TableHead className="w-32">Ngày chứng từ</TableHead>
                      <TableHead className="text-right w-32">Số tiền</TableHead>
                      <TableHead>Diễn giải</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="text-center">1</TableCell>
                      <TableCell>TC001</TableCell>
                      <TableCell>15/12/2024</TableCell>
                      <TableCell className="text-right">{formatCurrency(5000000)}</TableCell>
                      <TableCell>Tạm ứng sửa chữa</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="vehicles" className="space-y-4 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Thông tin loại xe</h3>
                <Badge variant="outline">Tương tự VslistLoaiXe</Badge>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Biển số xe</label>
                    <p className="text-xl font-mono font-bold">{quotation.bienSoXe || 'Chưa có biển số'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Loại xe</label>
                    <p className="font-medium">{quotation.loaiXe || 'Chưa có thông tin xe'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Hãng sản xuất</label>
                    <p className="font-medium">Toyota</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Số khung</label>
                    <p className="font-mono">JTDBT4K3XD1234567</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Số máy</label>
                    <p className="font-mono">2TR-FE123456</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Màu sắc</label>
                    <p className="font-medium">Trắng</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export default withAuth(QuotationDetailsPage)
