using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Core.Services
{
    /// <summary>
    /// Service implementation for Insurance Image Management (Báo Giá H<PERSON> Ảnh <PERSON>)
    /// Implements business logic for managing insurance approval images
    /// Exact functionality from clsBaoGiaHinhAnhBH.cs legacy class
    /// </summary>
    public class BaoGiaHinhAnhBHService : IBaoGiaHinhAnhBHService
    {
        private readonly IBaoGiaHinhAnhBHRepository _repository;
        private readonly ILogger<BaoGiaHinhAnhBHService> _logger;

        // Image validation constants
        private const long MaxImageSize = 10 * 1024 * 1024; // 10MB
        private const int MaxThumbnailSize = 200; // 200px
        private readonly string[] AllowedImageTypes = { "image/jpeg", "image/jpg", "image/png", "image/bmp" };

        public BaoGiaHinhAnhBHService(IBaoGiaHinhAnhBHRepository repository, ILogger<BaoGiaHinhAnhBHService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Load insurance image by quotation ID
        /// </summary>
        public async Task<BaoGiaHinhAnhBHDto?> GetInsuranceImageAsync(string khoaBaoGia)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    _logger.LogWarning("GetInsuranceImageAsync called with empty khoaBaoGia");
                    return null;
                }

                return await _repository.LoadAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Upload insurance image for quotation
        /// </summary>
        public async Task<ServiceResult<bool>> UploadInsuranceImageAsync(UploadInsuranceImageDto uploadDto)
        {
            try
            {
                // Validate input
                var validationResult = await ValidateImageFileAsync(uploadDto.HinhAnh, uploadDto.TenFile);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult(validationResult.Message, validationResult.Errors);
                }

                // Check if quotation already has an image
                var existingImage = await _repository.LoadAsync(uploadDto.KhoaBaoGia);
                if (existingImage != null)
                {
                    return ServiceResult<bool>.ErrorResult("Báo giá này đã có hình ảnh bảo hiểm. Vui lòng xóa hình cũ trước khi tải lên hình mới.");
                }

                // Process image for mobile optimization
                var processedImage = await ProcessImageForMobileAsync(uploadDto.HinhAnh);
                if (!processedImage.Success)
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi xử lý hình ảnh: " + processedImage.Message);
                }

                // Update upload DTO with processed image
                uploadDto.HinhAnh = processedImage.Data?.OriginalImage ?? uploadDto.HinhAnh;

                // Upload image
                var uploadResult = await _repository.UploadImageAsync(uploadDto);
                if (uploadResult)
                {
                    _logger.LogInformation("Successfully uploaded insurance image for quotation {KhoaBaoGia}", uploadDto.KhoaBaoGia);
                    return ServiceResult<bool>.SuccessResult(true, "Tải lên hình ảnh bảo hiểm thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi tải lên hình ảnh bảo hiểm");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading insurance image for quotation {KhoaBaoGia}", uploadDto.KhoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi tải lên hình ảnh");
            }
        }

        /// <summary>
        /// Update insurance approval status
        /// </summary>
        public async Task<ServiceResult<bool>> UpdateApprovalStatusAsync(InsuranceApprovalDto approvalDto)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(approvalDto.KhoaBaoGia))
                {
                    return ServiceResult<bool>.ErrorResult("Mã báo giá không được để trống");
                }

                if (approvalDto.TrangThaiDuyet < 0 || approvalDto.TrangThaiDuyet > 2)
                {
                    return ServiceResult<bool>.ErrorResult("Trạng thái duyệt không hợp lệ");
                }

                // Check if image exists
                var existingImage = await _repository.LoadAsync(approvalDto.KhoaBaoGia);
                if (existingImage == null)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy hình ảnh bảo hiểm cho báo giá này");
                }

                // Validate approval data
                if (approvalDto.TrangThaiDuyet == 1) // Approved
                {
                    if (string.IsNullOrWhiteSpace(approvalDto.TenGiamDinh))
                    {
                        return ServiceResult<bool>.ErrorResult("Tên giám định viên không được để trống khi duyệt");
                    }

                    if (approvalDto.SoTienDuyet <= 0)
                    {
                        return ServiceResult<bool>.ErrorResult("Số tiền duyệt phải lớn hơn 0 khi duyệt");
                    }
                }
                else if (approvalDto.TrangThaiDuyet == 2) // Rejected
                {
                    if (string.IsNullOrWhiteSpace(approvalDto.NhanXetGiamDinh))
                    {
                        return ServiceResult<bool>.ErrorResult("Nhận xét giám định không được để trống khi từ chối");
                    }
                }

                // Update approval status
                var updateResult = await _repository.UpdateApprovalStatusAsync(approvalDto);
                if (updateResult)
                {
                    // Send notification
                    await SendApprovalNotificationAsync(approvalDto.KhoaBaoGia, approvalDto.TrangThaiDuyet);

                    var statusText = approvalDto.TrangThaiDuyet switch
                    {
                        1 => "duyệt",
                        2 => "từ chối",
                        _ => "cập nhật"
                    };

                    _logger.LogInformation("Successfully updated approval status to {Status} for quotation {KhoaBaoGia}", 
                        statusText, approvalDto.KhoaBaoGia);
                    
                    return ServiceResult<bool>.SuccessResult(true, $"Cập nhật trạng thái {statusText} thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi cập nhật trạng thái duyệt");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating approval status for quotation {KhoaBaoGia}", approvalDto.KhoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi cập nhật trạng thái duyệt");
            }
        }

        /// <summary>
        /// Delete insurance image
        /// </summary>
        public async Task<ServiceResult<bool>> DeleteInsuranceImageAsync(string khoaBaoGia)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoaBaoGia))
                {
                    return ServiceResult<bool>.ErrorResult("Mã báo giá không được để trống");
                }

                // Check if image exists
                var existingImage = await _repository.LoadAsync(khoaBaoGia);
                if (existingImage == null)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy hình ảnh bảo hiểm cho báo giá này");
                }

                // Check if image is already approved
                if (existingImage.TrangThaiDuyet == 1)
                {
                    return ServiceResult<bool>.ErrorResult("Không thể xóa hình ảnh đã được duyệt");
                }

                var deleteResult = await _repository.DeleteAsync(khoaBaoGia);
                if (deleteResult)
                {
                    _logger.LogInformation("Successfully deleted insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                    return ServiceResult<bool>.SuccessResult(true, "Xóa hình ảnh bảo hiểm thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi xóa hình ảnh bảo hiểm");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi xóa hình ảnh");
            }
        }

        /// <summary>
        /// Get insurance images requiring approval
        /// </summary>
        public async Task<ServiceResult<List<InsuranceImageListDto>>> GetPendingApprovalsAsync(string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<List<InsuranceImageListDto>>.ErrorResult("Mã đơn vị không được để trống");
                }

                var pendingImages = await _repository.GetPendingApprovalsAsync(donViId);
                return ServiceResult<List<InsuranceImageListDto>>.SuccessResult(pendingImages, 
                    $"Tìm thấy {pendingImages.Count} hình ảnh chờ duyệt");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return ServiceResult<List<InsuranceImageListDto>>.ErrorResult("Lỗi hệ thống khi lấy danh sách chờ duyệt");
            }
        }

        /// <summary>
        /// Validate image file before upload
        /// </summary>
        public async Task<ServiceResult<bool>> ValidateImageFileAsync(byte[] imageData, string fileName)
        {
            try
            {
                var errors = new List<string>();

                // Check if image data is provided
                if (imageData == null || imageData.Length == 0)
                {
                    errors.Add("Dữ liệu hình ảnh không được để trống");
                }

                // Check file size
                if (imageData != null && imageData.Length > MaxImageSize)
                {
                    errors.Add($"Kích thước file không được vượt quá {MaxImageSize / (1024 * 1024)}MB");
                }

                // Check file extension
                if (!string.IsNullOrWhiteSpace(fileName))
                {
                    var extension = Path.GetExtension(fileName).ToLowerInvariant();
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp" };
                    
                    if (!allowedExtensions.Contains(extension))
                    {
                        errors.Add("Chỉ hỗ trợ các định dạng file: JPG, JPEG, PNG, BMP");
                    }
                }

                // Check image format by reading header bytes
                if (imageData != null && imageData.Length > 10)
                {
                    var isValidImage = IsValidImageFormat(imageData);
                    if (!isValidImage)
                    {
                        errors.Add("Định dạng file không hợp lệ hoặc file bị hỏng");
                    }
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.ErrorResult("Validation failed", errors);
                }

                return ServiceResult<bool>.SuccessResult(true, "File hợp lệ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating image file");
                return ServiceResult<bool>.ErrorResult("Lỗi khi kiểm tra file");
            }
        }

        /// <summary>
        /// Check if quotation has insurance image
        /// </summary>
        public async Task<bool> HasInsuranceImageAsync(string khoaBaoGia)
        {
            try
            {
                return await _repository.HasInsuranceImageAsync(khoaBaoGia);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if quotation has insurance image {KhoaBaoGia}", khoaBaoGia);
                return false;
            }
        }

        /// <summary>
        /// Get image thumbnail for mobile display
        /// </summary>
        public async Task<string?> GetImageThumbnailBase64Async(string khoaBaoGia)
        {
            try
            {
                var thumbnailData = await _repository.GetImageThumbnailAsync(khoaBaoGia);
                return thumbnailData != null ? Convert.ToBase64String(thumbnailData) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting thumbnail for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Get full image for mobile display
        /// </summary>
        public async Task<string?> GetFullImageBase64Async(string khoaBaoGia)
        {
            try
            {
                var imageData = await _repository.GetFullImageAsync(khoaBaoGia);
                return imageData != null ? Convert.ToBase64String(imageData) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting full image for quotation {KhoaBaoGia}", khoaBaoGia);
                return null;
            }
        }

        /// <summary>
        /// Helper method to check if image format is valid
        /// </summary>
        private static bool IsValidImageFormat(byte[] imageData)
        {
            if (imageData.Length < 10) return false;

            // Check for common image format signatures
            // JPEG: FF D8 FF
            if (imageData[0] == 0xFF && imageData[1] == 0xD8 && imageData[2] == 0xFF)
                return true;

            // PNG: 89 50 4E 47 0D 0A 1A 0A
            if (imageData[0] == 0x89 && imageData[1] == 0x50 && imageData[2] == 0x4E && imageData[3] == 0x47)
                return true;

            // BMP: 42 4D
            if (imageData[0] == 0x42 && imageData[1] == 0x4D)
                return true;

            return false;
        }

        // Placeholder implementations for remaining interface methods
        public async Task<ServiceResult<List<InsuranceImageListDto>>> GetApprovedImagesAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<List<InsuranceImageListDto>>.SuccessResult(new List<InsuranceImageListDto>());
        }

        public async Task<ServiceResult<List<InsuranceImageListDto>>> GetRejectedImagesAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<List<InsuranceImageListDto>>.SuccessResult(new List<InsuranceImageListDto>());
        }

        public async Task<ServiceResult<List<InsuranceImageListDto>>> GetImagesByInsuranceCompanyAsync(string khoaBaoHiem, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<List<InsuranceImageListDto>>.SuccessResult(new List<InsuranceImageListDto>());
        }

        public async Task<ServiceResult<InsuranceImageStatisticsDto>> GetImageStatisticsAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<InsuranceImageStatisticsDto>.SuccessResult(new InsuranceImageStatisticsDto());
        }

        public async Task<ServiceResult<bool>> UpdateImageMetadataAsync(string khoaBaoGia, string moTa, string tenFile)
        {
            try
            {
                var result = await _repository.UpdateImageMetadataAsync(khoaBaoGia, moTa, tenFile);
                return result ? 
                    ServiceResult<bool>.SuccessResult(true, "Cập nhật thông tin hình ảnh thành công") :
                    ServiceResult<bool>.ErrorResult("Lỗi khi cập nhật thông tin hình ảnh");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating image metadata for quotation {KhoaBaoGia}", khoaBaoGia);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi cập nhật thông tin");
            }
        }

        public async Task<ServiceResult<List<InsuranceImageListDto>>> GetImagesByStatusAsync(int trangThaiDuyet, string donViId)
        {
            // Implementation will be added in next part
            return ServiceResult<List<InsuranceImageListDto>>.SuccessResult(new List<InsuranceImageListDto>());
        }

        public async Task<ServiceResult<int>> BulkApproveImagesAsync(List<string> khoaBaoGiaList, InsuranceApprovalDto approvalDto)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<PaginatedResult<InsuranceImageListDto>>> GetImagesForMobileAsync(string donViId, int pageSize, int pageNumber)
        {
            // Implementation will be added in next part
            return ServiceResult<PaginatedResult<InsuranceImageListDto>>.SuccessResult(new PaginatedResult<InsuranceImageListDto>());
        }

        public async Task<ServiceResult<ProcessedImageDto>> ProcessImageForMobileAsync(byte[] imageData)
        {
            // Implementation will be added in next part
            return ServiceResult<ProcessedImageDto>.SuccessResult(new ProcessedImageDto { OriginalImage = imageData });
        }

        public async Task<ServiceResult<InsuranceWorkflowStatusDto>> GetApprovalWorkflowStatusAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return ServiceResult<InsuranceWorkflowStatusDto>.SuccessResult(new InsuranceWorkflowStatusDto());
        }

        public async Task<ServiceResult<bool>> SendApprovalNotificationAsync(string khoaBaoGia, int approvalStatus)
        {
            // Implementation will be added in next part
            return ServiceResult<bool>.SuccessResult(true);
        }

        public async Task<ServiceResult<byte[]>> ExportInsuranceImagesToPdfAsync(List<string> khoaBaoGiaList)
        {
            // Implementation will be added in next part
            return ServiceResult<byte[]>.SuccessResult(Array.Empty<byte>());
        }

        public async Task<ServiceResult<List<InsuranceImageAuditDto>>> GetImageAuditTrailAsync(string khoaBaoGia)
        {
            // Implementation will be added in next part
            return ServiceResult<List<InsuranceImageAuditDto>>.SuccessResult(new List<InsuranceImageAuditDto>());
        }
    }
}
