using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for LoaiTien (Currency Type) entity
/// Implements ALL endpoints from clsDMLoaiTien.cs (552 lines)
/// Includes REST API and 9+ legacy method endpoints
/// Maps to DM_LoaiTien table with 11 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LoaiTienController : ControllerBase
{
    private readonly ILoaiTienService _loaiTienService;
    private readonly ILogger<LoaiTienController> _logger;

    public LoaiTienController(ILoaiTienService loaiTienService, ILogger<LoaiTienController> logger)
    {
        _loaiTienService = loaiTienService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all LoaiTien records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<LoaiTienListDto>>> GetAll()
    {
        try
        {
            var result = await _loaiTienService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiTien records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiTien by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<LoaiTienDto>> GetById(string khoa)
    {
        try
        {
            var result = await _loaiTienService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiTien by currency code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<LoaiTienDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _loaiTienService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new LoaiTien
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateLoaiTienDto createDto)
    {
        try
        {
            var result = await _loaiTienService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo loại tiền");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiTien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiTien
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] LoaiTienDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiTienService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiTien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete LoaiTien
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _loaiTienService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiTien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update LoaiTien status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateLoaiTienStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _loaiTienService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiTien status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search LoaiTien records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<LoaiTienListDto>>> Search([FromBody] LoaiTienSearchDto searchDto)
    {
        try
        {
            var result = await _loaiTienService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiTien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get LoaiTien lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<LoaiTienLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _loaiTienService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate LoaiTien data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<LoaiTienValidationDto>> Validate([FromBody] LoaiTienValidationRequestDto request)
    {
        try
        {
            var result = await _loaiTienService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiTien");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get exchange rate for currency
    /// </summary>
    [HttpGet("exchange-rate/{maLoaiTien}")]
    public async Task<ActionResult<ExchangeRateDto>> GetExchangeRate(string maLoaiTien, [FromQuery] string? ngay = null)
    {
        try
        {
            var result = await _loaiTienService.GetExchangeRateAsync(maLoaiTien, ngay ?? DateTime.Now.ToString("yyyyMMdd"));
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exchange rate");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search currency by code
    /// </summary>
    [HttpGet("search-code/{code}")]
    public async Task<ActionResult<LoaiTienSearchByCodeDto>> SearchCurrencyByCode(string code, [FromQuery] string? condition = null)
    {
        try
        {
            var result = await _loaiTienService.SearchCurrencyByCodeAsync(code, condition ?? "");
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching currency by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiTienService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy LoadByCode method endpoint
    /// </summary>
    [HttpPost("loadbycode")]
    public async Task<ActionResult<bool>> LoadByCode([FromBody] string ma)
    {
        try
        {
            var result = await _loaiTienService.LoadByCodeAsync(ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] SaveLoaiTienRequestDto request)
    {
        try
        {
            var result = await _loaiTienService.SaveAsync(request.Data, request.Action);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiTienService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] LoaiTienShowListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _loaiTienService.ShowListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _loaiTienService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] LoaiTienShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _loaiTienService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] SearchByCodeRequestDto request)
    {
        try
        {
            var result = await _loaiTienService.SearchByCodeAsync(request.Code, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] TrungMaRequestDto request)
    {
        try
        {
            var result = await _loaiTienService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _loaiTienService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for LoaiTien

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class LoaiTienValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class SaveLoaiTienRequestDto
{
    public LoaiTienDto Data { get; set; } = new();
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class LoaiTienShowListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class LoaiTienShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method
/// </summary>
public class SearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class TrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
