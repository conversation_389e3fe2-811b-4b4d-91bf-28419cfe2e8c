using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Controller for BaoGiaYeuCauSuaChuaChiTiet (Repair Request Details) operations
/// Implements ALL endpoints from clsBaoGiaYeuCauSuaChuaChiTiet.cs (196 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for detailed repair request specifications in quotations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class BaoGiaYeuCauSuaChuaChiTietController : ControllerBase
{
    private readonly IBaoGiaYeuCauSuaChuaChiTietService _baoGiaYeuCauSuaChuaChiTietService;
    private readonly ILogger<BaoGiaYeuCauSuaChuaChiTietController> _logger;

    public BaoGiaYeuCauSuaChuaChiTietController(
        IBaoGiaYeuCauSuaChuaChiTietService baoGiaYeuCauSuaChuaChiTietService,
        ILogger<BaoGiaYeuCauSuaChuaChiTietController> logger)
    {
        _baoGiaYeuCauSuaChuaChiTietService = baoGiaYeuCauSuaChuaChiTietService;
        _logger = logger;
    }

    #region Legacy API Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpGet("load/{khoa}")]
    public async Task<ActionResult<bool>> Load(string khoa)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save()
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.SaveAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetDetailsYeuCauSuaChua method endpoint
    /// </summary>
    [HttpGet("getdetailsyeucausuachua/{khoaBaoGia}")]
    public async Task<ActionResult<DataTable>> GetDetailsYeuCauSuaChua(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetDetailsYeuCauSuaChuaAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDetailsYeuCauSuaChua endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Modern API Endpoints

    /// <summary>
    /// Get all repair request details
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetAll()
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all repair request details");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair request detail by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<BaoGiaYeuCauSuaChuaChiTietDto>> GetById(string khoa)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request detail by ID");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair request details by quotation
    /// </summary>
    [HttpGet("quotation/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>>> GetByQuotation(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetByQuotationAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request details by quotation");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Create new repair request detail
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateBaoGiaYeuCauSuaChuaChiTietDto createDto)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating repair request detail");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update repair request detail
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.UpdateAsync(khoa, updateDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair request detail");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Delete repair request detail
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair request detail");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Search repair request details
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>>> Search([FromBody] BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching repair request details");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair request details for lookup/dropdown
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<BaoGiaYeuCauSuaChuaChiTietLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request detail lookup");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Validate repair request detail
    /// </summary>
    [HttpGet("validate/{khoa}")]
    public async Task<ActionResult<BaoGiaYeuCauSuaChuaChiTietValidationDto>> Validate(string khoa)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.ValidateAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating repair request detail");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get automotive repair work details
    /// </summary>
    [HttpGet("automotive/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<AutomotiveRepairWorkDetailDto>>> GetAutomotiveRepairWorkDetails(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetAutomotiveRepairWorkDetailsAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive repair work details");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get repair work summary for a quotation
    /// </summary>
    [HttpGet("summary/{khoaBaoGia}")]
    public async Task<ActionResult<RepairWorkSummaryDto>> GetRepairWorkSummary(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetRepairWorkSummaryAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair work summary");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get technician workload analysis
    /// </summary>
    [HttpGet("technician-workload")]
    public async Task<ActionResult<IEnumerable<TechnicianWorkloadDto>>> GetTechnicianWorkload()
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetTechnicianWorkloadAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting technician workload");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get work item progress
    /// </summary>
    [HttpGet("progress/{khoa}")]
    public async Task<ActionResult<WorkItemProgressDto>> GetWorkItemProgress(string khoa)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.GetWorkItemProgressAsync(khoa);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting work item progress");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update work item progress
    /// </summary>
    [HttpPut("progress/{khoa}")]
    public async Task<ActionResult<bool>> UpdateWorkItemProgress(string khoa, [FromBody] WorkItemProgressDto progressDto)
    {
        try
        {
            var result = await _baoGiaYeuCauSuaChuaChiTietService.UpdateWorkItemProgressAsync(khoa, progressDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating work item progress");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}
