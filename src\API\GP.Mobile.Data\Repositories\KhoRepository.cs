using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for Kho (Warehouse/Storage) repository
/// Defines ALL methods from clsDMKho.cs (482 lines)
/// Includes both legacy methods and modern API methods
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and warehouse operations
/// </summary>
public interface IKhoRepository
{
    #region Legacy Methods (Exact mapping from clsDMKho.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(KhoDto dto, string task);
    Task<bool> DelDataAsync(string khoa);
    
    // List and data retrieval methods
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");
    
    // Business logic methods
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<KhoListDto>> GetAllAsync();
    Task<KhoDto?> GetByIdAsync(string khoa);
    Task<KhoDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateKhoDto createDto);
    Task<bool> UpdateAsync(KhoDto dto);
    Task<bool> UpdateStatusAsync(UpdateKhoStatusDto statusDto);
    Task<IEnumerable<KhoListDto>> SearchAsync(KhoSearchDto searchDto);
    Task<IEnumerable<KhoLookupDto>> GetLookupAsync();
    Task<KhoValidationDto> ValidateAsync(string khoa, string ma);
    Task<KhoSearchByCodeDto> SearchByCodeModernAsync(string code, string condition = "");
    Task<IEnumerable<WarehouseCategoryDto>> GetWarehouseCategoriesAsync();
    Task<IEnumerable<KhoWithInventoryDto>> GetWarehousesWithInventoryAsync();
    Task<KhoStatsDto?> GetWarehouseStatsAsync(string khoa);
    Task<IEnumerable<WarehouseCapacityDto>> GetWarehouseCapacityAsync();
    Task<IEnumerable<AutomotiveWarehouseDto>> GetAutomotiveWarehousesAsync();
    
    #endregion
}

/// <summary>
/// Complete Repository for Kho entity
/// Implements ALL methods from clsDMKho.cs (482 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and warehouse operations
/// </summary>
public class KhoRepository : IKhoRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<KhoRepository> _logger;

    public KhoRepository(IDbConnection connection, ILogger<KhoRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 184)
            string commandText = "SELECT * FROM DM_Kho WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<KhoDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Kho: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> SaveAsync(KhoDto dto, string task)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 230)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@Ma", dto.Ma);
            parameters.Add("@TenViet", dto.TenViet);
            parameters.Add("@TenAnh", dto.TenAnh);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@KhoaDonVi", dto.KhoaDonVi);
            parameters.Add("@TuNgay", dto.TuNgay);
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@Active", dto.Active);
            parameters.Add("@Send", dto.Send);
            parameters.Add("@pAction", task);
            parameters.Add("@pError", dbType: DbType.Double, direction: ParameterDirection.Output);

            await _connection.ExecuteAsync("sp_DM_Kho", parameters, commandType: CommandType.StoredProcedure);
            
            var errorCode = parameters.Get<double>("@pError");
            return errorCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Kho: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy DelData method (line 394)
            string commandText = "DELETE FROM DM_Kho WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Kho: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Exact SQL from legacy ShowList method (line 254)
            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                whereClause = " AND " + condition;
            }

            string commandText = $@"
                SELECT Khoa, Rtrim(Ma) as Ma, Rtrim(TenViet) as Ten  
                FROM DM_Kho 
                WHERE Active = 1 {whereClause}
                ORDER BY 3";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Kho list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            // Exact SQL from legacy ShowAllList method (line 279)
            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string commandText = @"
                SELECT Khoa, Rtrim(Ma) as Ma, Rtrim(TenViet) as Ten  
                FROM DM_Kho 
                ORDER BY Ma";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all Kho list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        try
        {
            // Exact SQL from legacy ShowListByField method (line 430)
            string whereClause = "";
            string orderClause = "";
            
            strFieldList = strFieldList.Replace("|", ",");
            
            if (!string.IsNullOrWhiteSpace(strConditions))
            {
                whereClause = " WHERE " + strConditions;
            }
            
            if (!string.IsNullOrWhiteSpace(strOrder))
            {
                orderClause = " ORDER BY " + strOrder;
            }
            
            string commandText = $" SELECT {strFieldList}  FROM DM_KHO{whereClause}{orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Kho list by field");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            // Exact SQL from legacy SearchByCode method (line 311)
            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string codeFilter = "";
            string conditionFilter = "";
            
            if (!string.IsNullOrWhiteSpace(code))
            {
                codeFilter = " AND RTRIM(Ma) = @Code";
            }
            
            if (!string.IsNullOrWhiteSpace(condition))
            {
                conditionFilter = " AND " + condition;
            }
            
            string commandText = $@"
                SELECT Khoa, Ma, TenViet as Ten  
                FROM DM_Kho 
                WHERE Active = 1 {codeFilter}{conditionFilter}";
            
            var result = await _connection.QueryFirstOrDefaultAsync(commandText, new { Code = code.Trim() });
            
            if (result != null)
            {
                return $"{result.Khoa}|{result.Ma}|{result.Ten}";
            }
            
            return "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching Kho by code");
            return "";
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            // Exact SQL from legacy TrungMa method (line 343)
            string commandText = @"
                SELECT * FROM DM_Kho 
                WHERE RTRIM(Ma) = @Ma AND RTRIM(Khoa) <> @Khoa";
            
            var result = await _connection.QueryAsync(commandText, new { Ma = ma.Trim(), Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy WasUsed method (line 372)
            string commandText = "SELECT top 1 * FROM ST_TonKhoDauKy WHERE RTRIM(KhoaKho) = @Khoa";
            var result = await _connection.QueryAsync(commandText, new { Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if Kho was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<KhoListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT K.Khoa, K.Ma, K.TenViet, K.TenAnh, K.DienGiai, K.KhoaDonVi,
                       DV.TenViet as DonVi, dbo.char2date(K.TuNgay) as TuNgay,
                       K.KhoaNhanVienCapNhat, NV.TenViet as NhanVienCapNhat, K.Active, K.Send,
                       CASE WHEN UPPER(K.TenViet) LIKE '%CHÍNH%' OR UPPER(K.TenViet) LIKE '%MAIN%' THEN 1 ELSE 0 END as IsMainWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%PHỤ TÙNG%' OR UPPER(K.TenViet) LIKE '%PARTS%' THEN 1 ELSE 0 END as IsPartsWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DỊCH VỤ%' OR UPPER(K.TenViet) LIKE '%SERVICE%' THEN 1 ELSE 0 END as IsServiceWarehouse
                FROM DM_Kho K
                LEFT JOIN DM_DonVi DV on K.KhoaDonVi = DV.Khoa
                LEFT JOIN HT_NguoiDung ND on K.KhoaNhanVienCapNhat = ND.KhoaNhanVien
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien
                ORDER BY K.Ma";

            return await _connection.QueryAsync<KhoListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all warehouses");
            return new List<KhoListDto>();
        }
    }

    public async Task<KhoDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_Kho WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<KhoDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<KhoDto?> GetByCodeAsync(string ma)
    {
        try
        {
            string commandText = "SELECT * FROM DM_Kho WHERE RTRIM(Ma) = @Ma";
            return await _connection.QueryFirstOrDefaultAsync<KhoDto>(commandText, new { Ma = ma.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse by code: {Ma}", ma);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateKhoDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new KhoDto
            {
                Khoa = khoa,
                Ma = createDto.Ma,
                TenViet = createDto.TenViet,
                TenAnh = createDto.TenAnh,
                DienGiai = createDto.DienGiai,
                KhoaDonVi = createDto.KhoaDonVi,
                TuNgay = createDto.TuNgay,
                Active = 1,
                Send = 0
            };

            var success = await SaveAsync(dto, "INSERT");
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating warehouse");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(KhoDto dto)
    {
        try
        {
            return await SaveAsync(dto, "UPDATE");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating warehouse: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateKhoStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE DM_Kho
                SET Active = @Active, Send = @Send, KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, statusDto);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating warehouse status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<KhoListDto>> SearchAsync(KhoSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.Ma))
            {
                conditions.Add("K.Ma LIKE @Ma");
                parameters.Add("@Ma", $"%{searchDto.Ma}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenViet))
            {
                conditions.Add("K.TenViet LIKE @TenViet");
                parameters.Add("@TenViet", $"%{searchDto.TenViet}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenAnh))
            {
                conditions.Add("K.TenAnh LIKE @TenAnh");
                parameters.Add("@TenAnh", $"%{searchDto.TenAnh}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DienGiai))
            {
                conditions.Add("K.DienGiai LIKE @DienGiai");
                parameters.Add("@DienGiai", $"%{searchDto.DienGiai}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaDonVi))
            {
                conditions.Add("K.KhoaDonVi = @KhoaDonVi");
                parameters.Add("@KhoaDonVi", searchDto.KhoaDonVi);
            }

            if (searchDto.Active.HasValue)
            {
                conditions.Add("K.Active = @Active");
                parameters.Add("@Active", searchDto.Active.Value);
            }

            if (searchDto.Send.HasValue)
            {
                conditions.Add("K.Send = @Send");
                parameters.Add("@Send", searchDto.Send.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayFrom))
            {
                conditions.Add("K.TuNgay >= @TuNgayFrom");
                parameters.Add("@TuNgayFrom", searchDto.TuNgayFrom);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayTo))
            {
                conditions.Add("K.TuNgay <= @TuNgayTo");
                parameters.Add("@TuNgayTo", searchDto.TuNgayTo);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT K.Khoa, K.Ma, K.TenViet, K.TenAnh, K.DienGiai, K.KhoaDonVi,
                       DV.TenViet as DonVi, dbo.char2date(K.TuNgay) as TuNgay,
                       K.KhoaNhanVienCapNhat, NV.TenViet as NhanVienCapNhat, K.Active, K.Send,
                       CASE WHEN UPPER(K.TenViet) LIKE '%CHÍNH%' OR UPPER(K.TenViet) LIKE '%MAIN%' THEN 1 ELSE 0 END as IsMainWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%PHỤ TÙNG%' OR UPPER(K.TenViet) LIKE '%PARTS%' THEN 1 ELSE 0 END as IsPartsWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DỊCH VỤ%' OR UPPER(K.TenViet) LIKE '%SERVICE%' THEN 1 ELSE 0 END as IsServiceWarehouse
                FROM DM_Kho K
                LEFT JOIN DM_DonVi DV on K.KhoaDonVi = DV.Khoa
                LEFT JOIN HT_NguoiDung ND on K.KhoaNhanVienCapNhat = ND.KhoaNhanVien
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien
                {whereClause}
                ORDER BY K.Ma";

            return await _connection.QueryAsync<KhoListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching warehouses");
            return new List<KhoListDto>();
        }
    }

    public async Task<IEnumerable<KhoLookupDto>> GetLookupAsync()
    {
        try
        {
            string commandText = @"
                SELECT K.Khoa, K.Ma, K.TenViet, K.TenAnh, DV.TenViet as DonVi,
                       CASE WHEN K.Active = 1 THEN 1 ELSE 0 END as IsActive
                FROM DM_Kho K
                LEFT JOIN DM_DonVi DV on K.KhoaDonVi = DV.Khoa
                WHERE K.Active = 1
                ORDER BY K.Ma";

            return await _connection.QueryAsync<KhoLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse lookup");
            return new List<KhoLookupDto>();
        }
    }

    public async Task<KhoValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            var result = new KhoValidationDto
            {
                Khoa = khoa,
                Ma = ma
            };

            // Check for duplicate code
            result.IsDuplicateCode = await TrungMaAsync(ma, khoa);

            // Check if used in inventory
            if (!string.IsNullOrEmpty(khoa))
            {
                result.IsUsedInInventory = await WasUsedAsync(khoa);
            }

            result.CanDelete = !result.IsUsedInInventory;

            // Check if active
            if (!string.IsNullOrEmpty(khoa))
            {
                var warehouse = await GetByIdAsync(khoa);
                result.IsActive = warehouse?.Active == 1;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating warehouse");
            return new KhoValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<KhoSearchByCodeDto> SearchByCodeModernAsync(string code, string condition = "")
    {
        try
        {
            var searchResult = await SearchByCodeAsync(code, condition);
            var result = new KhoSearchByCodeDto();

            if (!string.IsNullOrEmpty(searchResult))
            {
                var parts = searchResult.Split('|');
                if (parts.Length >= 3)
                {
                    result.Khoa = parts[0];
                    result.Ma = parts[1];
                    result.Ten = parts[2];
                    result.Found = true;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching warehouse by code (modern)");
            return new KhoSearchByCodeDto();
        }
    }

    // Placeholder implementations for remaining complex methods
    public async Task<IEnumerable<WarehouseCategoryDto>> GetWarehouseCategoriesAsync()
    {
        try
        {
            string commandText = @"
                SELECT K.Khoa, K.Ma, K.TenViet, DV.TenViet as DonVi,
                       CASE WHEN UPPER(K.TenViet) LIKE '%CHÍNH%' OR UPPER(K.TenViet) LIKE '%MAIN%' THEN 1 ELSE 0 END as IsMainWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%PHỤ TÙNG%' OR UPPER(K.TenViet) LIKE '%PARTS%' THEN 1 ELSE 0 END as IsPartsWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DỊCH VỤ%' OR UPPER(K.TenViet) LIKE '%SERVICE%' THEN 1 ELSE 0 END as IsServiceWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%THÂN VỎ%' OR UPPER(K.TenViet) LIKE '%BODY%' THEN 1 ELSE 0 END as IsBodyShopWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DẦU%' OR UPPER(K.TenViet) LIKE '%OIL%' THEN 1 ELSE 0 END as IsOilWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%LỐP%' OR UPPER(K.TenViet) LIKE '%TIRE%' THEN 1 ELSE 0 END as IsTireWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%PHỤ KIỆN%' OR UPPER(K.TenViet) LIKE '%ACCESSORY%' THEN 1 ELSE 0 END as IsAccessoryWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%TRẢ%' OR UPPER(K.TenViet) LIKE '%RETURN%' THEN 1 ELSE 0 END as IsReturnWarehouse,
                       CASE WHEN UPPER(K.TenViet) LIKE '%CÁCH LY%' OR UPPER(K.TenViet) LIKE '%QUARANTINE%' THEN 1 ELSE 0 END as IsQuarantineWarehouse,
                       0 as TotalItems, 0 as TotalValue
                FROM DM_Kho K
                LEFT JOIN DM_DonVi DV on K.KhoaDonVi = DV.Khoa
                WHERE K.Active = 1
                ORDER BY K.Ma";

            return await _connection.QueryAsync<WarehouseCategoryDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warehouse categories");
            return new List<WarehouseCategoryDto>();
        }
    }

    public async Task<IEnumerable<KhoWithInventoryDto>> GetWarehousesWithInventoryAsync()
    {
        // TODO: Implement based on business requirements and inventory tables
        return new List<KhoWithInventoryDto>();
    }

    public async Task<KhoStatsDto?> GetWarehouseStatsAsync(string khoa)
    {
        // TODO: Implement based on business requirements and transaction tables
        return null;
    }

    public async Task<IEnumerable<WarehouseCapacityDto>> GetWarehouseCapacityAsync()
    {
        // TODO: Implement based on business requirements and capacity management
        return new List<WarehouseCapacityDto>();
    }

    public async Task<IEnumerable<AutomotiveWarehouseDto>> GetAutomotiveWarehousesAsync()
    {
        try
        {
            string commandText = @"
                SELECT K.Khoa, K.Ma, K.TenViet, DV.TenViet as DonVi,
                       CASE WHEN UPPER(K.TenViet) LIKE '%ĐỘNG CƠ%' OR UPPER(K.TenViet) LIKE '%ENGINE%' THEN 1 ELSE 0 END as HandlesEngineParts,
                       CASE WHEN UPPER(K.TenViet) LIKE '%THÂN VỎ%' OR UPPER(K.TenViet) LIKE '%BODY%' THEN 1 ELSE 0 END as HandlesBodyParts,
                       CASE WHEN UPPER(K.TenViet) LIKE '%ĐIỆN%' OR UPPER(K.TenViet) LIKE '%ELECTRIC%' THEN 1 ELSE 0 END as HandlesElectricalParts,
                       CASE WHEN UPPER(K.TenViet) LIKE '%LỐP%' OR UPPER(K.TenViet) LIKE '%TIRE%' THEN 1 ELSE 0 END as HandlesTires,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DẦU%' OR UPPER(K.TenViet) LIKE '%OIL%' THEN 1 ELSE 0 END as HandlesOils,
                       CASE WHEN UPPER(K.TenViet) LIKE '%PHỤ KIỆN%' OR UPPER(K.TenViet) LIKE '%ACCESSORY%' THEN 1 ELSE 0 END as HandlesAccessories,
                       CASE WHEN UPPER(K.TenViet) LIKE '%DỤNG CỤ%' OR UPPER(K.TenViet) LIKE '%TOOL%' THEN 1 ELSE 0 END as HandlesTools,
                       CASE WHEN UPPER(K.TenViet) LIKE '%LẠNH%' OR UPPER(K.DienGiai) LIKE '%TEMPERATURE%' THEN 1 ELSE 0 END as RequiresTemperatureControl,
                       CASE WHEN UPPER(K.DienGiai) LIKE '%HAZMAT%' OR UPPER(K.DienGiai) LIKE '%NGUY HIỂM%' THEN 1 ELSE 0 END as RequiresHazmatHandling,
                       CASE WHEN UPPER(K.DienGiai) LIKE '%JIT%' OR UPPER(K.DienGiai) LIKE '%JUST IN TIME%' THEN 1 ELSE 0 END as SupportsJustInTime,
                       '' as PreferredSuppliers, K.DienGiai as SpecialRequirements
                FROM DM_Kho K
                LEFT JOIN DM_DonVi DV on K.KhoaDonVi = DV.Khoa
                WHERE K.Active = 1
                ORDER BY K.Ma";

            return await _connection.QueryAsync<AutomotiveWarehouseDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive warehouses");
            return new List<AutomotiveWarehouseDto>();
        }
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    #endregion
}
