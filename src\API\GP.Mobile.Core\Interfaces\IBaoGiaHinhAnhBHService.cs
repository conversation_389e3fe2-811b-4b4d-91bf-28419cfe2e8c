using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Core.Interfaces
{
    /// <summary>
    /// Service interface for Insurance Image Management (Báo G<PERSON>á H<PERSON>nh Ảnh Bả<PERSON> Hiể<PERSON>)
    /// Provides business logic for managing insurance approval images
    /// Implements exact functionality from clsBaoGiaHinhAnhBH.cs legacy class
    /// </summary>
    public interface IBaoGiaHinhAnhBHService
    {
        /// <summary>
        /// Load insurance image by quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Insurance image data or null if not found</returns>
        Task<BaoGiaHinhAnhBHDto?> GetInsuranceImageAsync(string khoaBaoGia);

        /// <summary>
        /// Upload insurance image for quotation
        /// </summary>
        /// <param name="uploadDto">Upload data including image, metadata, and location info</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UploadInsuranceImageAsync(UploadInsuranceImageDto uploadDto);

        /// <summary>
        /// Update insurance approval status
        /// </summary>
        /// <param name="approvalDto">Approval data including status, adjuster info, and amount</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateApprovalStatusAsync(InsuranceApprovalDto approvalDto);

        /// <summary>
        /// Delete insurance image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> DeleteInsuranceImageAsync(string khoaBaoGia);

        /// <summary>
        /// Get insurance images requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of images pending approval</returns>
        Task<ServiceResult<List<InsuranceImageListDto>>> GetPendingApprovalsAsync(string donViId);

        /// <summary>
        /// Get approved insurance images
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of approved images</returns>
        Task<ServiceResult<List<InsuranceImageListDto>>> GetApprovedImagesAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get rejected insurance images
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of rejected images</returns>
        Task<ServiceResult<List<InsuranceImageListDto>>> GetRejectedImagesAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get insurance images by insurance company
        /// </summary>
        /// <param name="khoaBaoHiem">Insurance company ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of images for the insurance company</returns>
        Task<ServiceResult<List<InsuranceImageListDto>>> GetImagesByInsuranceCompanyAsync(string khoaBaoHiem, string fromDate, string toDate);

        /// <summary>
        /// Get insurance image statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>Insurance image statistics</returns>
        Task<ServiceResult<InsuranceImageStatisticsDto>> GetImageStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Check if quotation has insurance image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if image exists, false otherwise</returns>
        Task<bool> HasInsuranceImageAsync(string khoaBaoGia);

        /// <summary>
        /// Get image thumbnail for mobile display
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Base64 encoded thumbnail or null</returns>
        Task<string?> GetImageThumbnailBase64Async(string khoaBaoGia);

        /// <summary>
        /// Get full image for mobile display
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Base64 encoded full image or null</returns>
        Task<string?> GetFullImageBase64Async(string khoaBaoGia);

        /// <summary>
        /// Update image metadata (description and filename)
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="moTa">Description</param>
        /// <param name="tenFile">File name</param>
        /// <returns>Success result with message</returns>
        Task<ServiceResult<bool>> UpdateImageMetadataAsync(string khoaBaoGia, string moTa, string tenFile);

        /// <summary>
        /// Get images by approval status
        /// </summary>
        /// <param name="trangThaiDuyet">Approval status (0=Pending, 1=Approved, 2=Rejected)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of images with specified status</returns>
        Task<ServiceResult<List<InsuranceImageListDto>>> GetImagesByStatusAsync(int trangThaiDuyet, string donViId);

        /// <summary>
        /// Bulk approve insurance images
        /// </summary>
        /// <param name="khoaBaoGiaList">List of quotation IDs to approve</param>
        /// <param name="approvalDto">Approval data</param>
        /// <returns>Number of successfully approved images</returns>
        Task<ServiceResult<int>> BulkApproveImagesAsync(List<string> khoaBaoGiaList, InsuranceApprovalDto approvalDto);

        /// <summary>
        /// Get insurance images for mobile app with pagination
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <returns>Paginated list of insurance images</returns>
        Task<ServiceResult<PaginatedResult<InsuranceImageListDto>>> GetImagesForMobileAsync(string donViId, int pageSize, int pageNumber);

        /// <summary>
        /// Validate image file before upload
        /// </summary>
        /// <param name="imageData">Image data</param>
        /// <param name="fileName">File name</param>
        /// <returns>Validation result</returns>
        Task<ServiceResult<bool>> ValidateImageFileAsync(byte[] imageData, string fileName);

        /// <summary>
        /// Process image for mobile optimization
        /// </summary>
        /// <param name="imageData">Original image data</param>
        /// <returns>Processed image data with thumbnail</returns>
        Task<ServiceResult<ProcessedImageDto>> ProcessImageForMobileAsync(byte[] imageData);

        /// <summary>
        /// Get insurance approval workflow status
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Workflow status information</returns>
        Task<ServiceResult<InsuranceWorkflowStatusDto>> GetApprovalWorkflowStatusAsync(string khoaBaoGia);

        /// <summary>
        /// Send notification for insurance approval
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="approvalStatus">Approval status</param>
        /// <returns>Success result</returns>
        Task<ServiceResult<bool>> SendApprovalNotificationAsync(string khoaBaoGia, int approvalStatus);

        /// <summary>
        /// Export insurance images to PDF report
        /// </summary>
        /// <param name="khoaBaoGiaList">List of quotation IDs</param>
        /// <returns>PDF report data</returns>
        Task<ServiceResult<byte[]>> ExportInsuranceImagesToPdfAsync(List<string> khoaBaoGiaList);

        /// <summary>
        /// Get insurance image audit trail
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>List of audit trail entries</returns>
        Task<ServiceResult<List<InsuranceImageAuditDto>>> GetImageAuditTrailAsync(string khoaBaoGia);
    }



    /// <summary>
    /// Processed image data
    /// </summary>
    public class ProcessedImageDto
    {
        public byte[] OriginalImage { get; set; } = Array.Empty<byte>();
        public byte[] ThumbnailImage { get; set; } = Array.Empty<byte>();
        public long OriginalSize { get; set; }
        public long ThumbnailSize { get; set; }
        public string ImageFormat { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
        public int ThumbnailWidth { get; set; }
        public int ThumbnailHeight { get; set; }
    }

    /// <summary>
    /// Insurance workflow status
    /// </summary>
    public class InsuranceWorkflowStatusDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public int TrangThaiDuyet { get; set; }
        public string TrangThaiText { get; set; } = string.Empty;
        public string TenGiamDinh { get; set; } = string.Empty;
        public string NgayTao { get; set; } = string.Empty;
        public string NgayDuyet { get; set; } = string.Empty;
        public decimal SoTienDuyet { get; set; }
        public string NhanXetGiamDinh { get; set; } = string.Empty;
        public bool CanApprove { get; set; }
        public bool CanReject { get; set; }
        public bool CanEdit { get; set; }
        public List<string> NextActions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Insurance image audit trail
    /// </summary>
    public class InsuranceImageAuditDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public string HanhDong { get; set; } = string.Empty;
        public string NguoiThucHien { get; set; } = string.Empty;
        public string ThoiGian { get; set; } = string.Empty;
        public string NoiDung { get; set; } = string.Empty;
        public string TrangThaiCu { get; set; } = string.Empty;
        public string TrangThaiMoi { get; set; } = string.Empty;
        public string GhiChu { get; set; } = string.Empty;
    }
}
