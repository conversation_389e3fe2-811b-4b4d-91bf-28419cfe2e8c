# GP Mobile Development Startup Script
# This script starts both the .NET API backend and Next.js frontend

Write-Host "🚀 Starting GP Mobile Development Environment..." -ForegroundColor Green
Write-Host ""

# Function to kill processes on specific ports
function Stop-ProcessOnPort {
    param([int]$Port)
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
        foreach ($processId in $processes) {
            if ($processId -and $processId -ne 0) {
                Write-Host "🔄 Stopping process on port $Port (PID: $processId)..." -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 2
            }
        }
    }
    catch {
        Write-Host "⚠️  Could not stop process on port $Port" -ForegroundColor Yellow
    }
}

# Check and stop existing processes
Write-Host "🔍 Checking for existing processes..." -ForegroundColor Cyan

# Stop processes on common ports
Stop-ProcessOnPort 51552
Stop-ProcessOnPort 51551
Stop-ProcessOnPort 3000
Stop-ProcessOnPort 3001

Write-Host ""
Write-Host "🏗️  Starting services..." -ForegroundColor Green

# Start API Backend
Write-Host "🔧 Starting .NET API Backend..." -ForegroundColor Cyan
$apiJob = Start-Job -ScriptBlock {
    Set-Location "d:\Projects\gp-mobile-v1\src\API"
    dotnet run --project GP.Mobile.API
}

# Wait a moment for API to start
Start-Sleep -Seconds 3

# Start Frontend
Write-Host "🎨 Starting Next.js Frontend..." -ForegroundColor Cyan
$frontendJob = Start-Job -ScriptBlock {
    Set-Location "d:\Projects\gp-mobile-v1\gp-web-frontend"
    npm run dev
}

# Wait for services to start
Write-Host ""
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

Write-Host ""
Write-Host "✅ Services should be starting..." -ForegroundColor Green
Write-Host "   🔧 API Backend: http://localhost:51552" -ForegroundColor Gray
Write-Host "   📊 Swagger UI: https://localhost:51551/swagger" -ForegroundColor Gray
Write-Host "   🎨 Frontend: http://localhost:3000 or http://localhost:3001" -ForegroundColor Gray
Write-Host "   🔐 Login page: http://localhost:3001/login" -ForegroundColor Gray

Write-Host ""
Write-Host "🎯 Development Environment Ready!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Available Commands:" -ForegroundColor Cyan
Write-Host "   • Press Ctrl+C to stop this script" -ForegroundColor Gray
Write-Host "   • Run 'stop-dev.ps1' to stop all services" -ForegroundColor Gray
Write-Host ""
Write-Host "🔑 Test Credentials:" -ForegroundColor Cyan
Write-Host "   • Username: ngan, adv, hieu (or any user from database)" -ForegroundColor Gray
Write-Host "   • Master Password: admin (works for all users)" -ForegroundColor Gray
Write-Host "   • Unit: TT (Trung Tâm)" -ForegroundColor Gray
Write-Host ""

Write-Host ""
Write-Host "🔑 Test Credentials:" -ForegroundColor Cyan
Write-Host "   • Username: ngan, adv, hieu (or any user from database)" -ForegroundColor Gray
Write-Host "   • Master Password: admin (works for all users)" -ForegroundColor Gray
Write-Host "   • Unit: TT (Trung Tâm)" -ForegroundColor Gray
Write-Host ""
Write-Host "📝 To stop services, run: .\stop-dev.ps1" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Development Environment Started!" -ForegroundColor Green

# Keep jobs running
Write-Host "🔄 Services are running in background..." -ForegroundColor Yellow
Write-Host "   Press any key to stop all services and exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop jobs when user presses a key
Write-Host ""
Write-Host "🛑 Stopping services..." -ForegroundColor Yellow

if ($apiJob) {
    Stop-Job $apiJob -ErrorAction SilentlyContinue
    Remove-Job $apiJob -ErrorAction SilentlyContinue
}

if ($frontendJob) {
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob -ErrorAction SilentlyContinue
}

Stop-ProcessOnPort 51552
Stop-ProcessOnPort 51551
Stop-ProcessOnPort 3000
Stop-ProcessOnPort 3001

Write-Host "✅ All services stopped" -ForegroundColor Green
