using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Controller for Log (Audit Trail) operations
/// Implements ALL endpoints from clsLog.cs (201 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUDIT SYSTEM COMPONENT - Essential for tracking user actions and system changes
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LogController : ControllerBase
{
    private readonly ILogService _logService;
    private readonly ILogger<LogController> _logger;

    public LogController(
        ILogService logService,
        ILogger<LogController> logger)
    {
        _logService = logService;
        _logger = logger;
    }

    #region Legacy API Endpoints

    /// <summary>
    /// Legacy Insert method endpoint
    /// </summary>
    [HttpPost("insert")]
    public async Task<ActionResult<bool>> Insert([FromBody] LogDto dto)
    {
        try
        {
            var result = await _logService.InsertAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Insert endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetList method endpoint
    /// </summary>
    [HttpGet("getlist")]
    public async Task<ActionResult<DataTable>> GetList([FromQuery] string conditions = "")
    {
        try
        {
            var result = await _logService.GetListAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetList endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetGuidId method endpoint
    /// </summary>
    [HttpGet("getguidid/{id}")]
    public async Task<ActionResult<string>> GetGuidId(string id)
    {
        try
        {
            var result = await _logService.GetGuidIdAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetGuidId endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Modern API Endpoints

    /// <summary>
    /// Get all audit logs with pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<LogListDto>>> GetAll([FromQuery] int pageSize = 50, [FromQuery] int pageNumber = 1)
    {
        try
        {
            var result = await _logService.GetAllAsync(pageSize, pageNumber);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all logs");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get audit log by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<LogDto>> GetById(int id)
    {
        try
        {
            var result = await _logService.GetByIdAsync(id);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting log by ID");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Create new audit log entry
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<int>> Create([FromBody] CreateLogDto createDto)
    {
        try
        {
            var result = await _logService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = result }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating log entry");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Search audit logs
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<LogListDto>>> Search([FromBody] LogSearchDto searchDto)
    {
        try
        {
            var result = await _logService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching logs");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get audit logs for a specific document
    /// </summary>
    [HttpGet("document/{loaiChungTu}/{khoaChungTu}")]
    public async Task<ActionResult<IEnumerable<LogListDto>>> GetByDocument(string loaiChungTu, string khoaChungTu)
    {
        try
        {
            var result = await _logService.GetByDocumentAsync(loaiChungTu, khoaChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logs by document");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get audit logs for a specific user
    /// </summary>
    [HttpGet("user/{nguoiDung}")]
    public async Task<ActionResult<IEnumerable<LogListDto>>> GetByUser(string nguoiDung, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _logService.GetByUserAsync(nguoiDung, fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logs by user");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get audit trail summary statistics
    /// </summary>
    [HttpGet("summary")]
    public async Task<ActionResult<AuditTrailSummaryDto>> GetAuditSummary([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _logService.GetAuditSummaryAsync(fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit summary");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get automotive-specific audit logs
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveAuditLogDto>>> GetAutomotiveAuditLogs([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _logService.GetAutomotiveAuditLogsAsync(fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive audit logs");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Validate log entry data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<LogValidationDto>> Validate([FromBody] CreateLogDto dto)
    {
        try
        {
            var result = await _logService.ValidateAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating log entry");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get real-time system activity
    /// </summary>
    [HttpGet("activity")]
    public async Task<ActionResult<SystemActivityDto>> GetSystemActivity()
    {
        try
        {
            var result = await _logService.GetSystemActivityAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system activity");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Export audit logs
    /// </summary>
    [HttpPost("export")]
    public async Task<ActionResult<LogExportDto>> ExportLogs([FromBody] LogExportRequestDto request)
    {
        try
        {
            var result = await _logService.ExportLogsAsync(request.SearchCriteria, request.Format);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting logs");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Cleanup old audit logs
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<ActionResult<bool>> CleanupOldLogs([FromBody] CleanupLogsRequestDto request)
    {
        try
        {
            var result = await _logService.CleanupOldLogsAsync(request.RetentionDays);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old logs");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get log by GUID (for temporary tracking)
    /// </summary>
    [HttpGet("guid/{tempId}")]
    public async Task<ActionResult<LogGuidTrackingDto>> GetByGuid(string tempId)
    {
        try
        {
            var result = await _logService.GetByGuidAsync(tempId);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting log by GUID");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Save GUID tracking information
    /// </summary>
    [HttpPost("guid")]
    public async Task<ActionResult<bool>> SaveGuidTracking([FromBody] LogGuidTrackingDto dto)
    {
        try
        {
            var result = await _logService.SaveGuidTrackingAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving GUID tracking");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Convenience Endpoints

    /// <summary>
    /// Log user action (convenience method)
    /// </summary>
    [HttpPost("action")]
    public async Task<ActionResult<bool>> LogUserAction([FromBody] LogUserActionRequestDto request)
    {
        try
        {
            var result = await _logService.LogUserActionAsync(
                request.LoaiChungTu, 
                request.KhoaChungTu, 
                request.HanhDong, 
                request.NguoiDung, 
                request.ChiTiet, 
                request.DiaChi);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging user action");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Log system event (convenience method)
    /// </summary>
    [HttpPost("system")]
    public async Task<ActionResult<bool>> LogSystemEvent([FromBody] LogSystemEventRequestDto request)
    {
        try
        {
            var result = await _logService.LogSystemEventAsync(request.EventName, request.Details, request.User);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging system event");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Log automotive action (convenience method)
    /// </summary>
    [HttpPost("automotive")]
    public async Task<ActionResult<bool>> LogAutomotiveAction([FromBody] LogAutomotiveActionRequestDto request)
    {
        try
        {
            var result = await _logService.LogAutomotiveActionAsync(
                request.DocumentType, 
                request.DocumentKey, 
                request.Action, 
                request.User, 
                request.VehicleInfo, 
                request.CustomerInfo);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging automotive action");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}

#region Request DTOs for Log

/// <summary>
/// Request DTO for log export
/// </summary>
public class LogExportRequestDto
{
    public LogSearchDto SearchCriteria { get; set; } = new LogSearchDto();
    public string Format { get; set; } = "CSV"; // CSV, Excel, PDF
}

/// <summary>
/// Request DTO for cleanup old logs
/// </summary>
public class CleanupLogsRequestDto
{
    public int RetentionDays { get; set; } = 365;
}

/// <summary>
/// Request DTO for logging user action
/// </summary>
public class LogUserActionRequestDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string KhoaChungTu { get; set; } = string.Empty;
    public string HanhDong { get; set; } = string.Empty;
    public string NguoiDung { get; set; } = string.Empty;
    public string ChiTiet { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for logging system event
/// </summary>
public class LogSystemEventRequestDto
{
    public string EventName { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string User { get; set; } = "SYSTEM";
}

/// <summary>
/// Request DTO for logging automotive action
/// </summary>
public class LogAutomotiveActionRequestDto
{
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentKey { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public string VehicleInfo { get; set; } = string.Empty;
    public string CustomerInfo { get; set; } = string.Empty;
}

#endregion
