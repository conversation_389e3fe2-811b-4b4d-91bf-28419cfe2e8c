using System.Data;
using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Core.Repositories;

/// <summary>
/// Complete Repository for KhoanMucSuaChua (Repair Categories) entity
/// Maps exactly to DM_KhoanMucSuaChua table in legacy database
/// Implements ALL methods from clsDMKhoanMucSuaChua.cs (646 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for repair category management in automotive service quotations
/// </summary>
public class KhoanMucSuaChuaRepository
{
    private readonly string _connectionString;
    private readonly ILogger<KhoanMucSuaChuaRepository> _logger;

    public KhoanMucSuaChuaRepository(IConfiguration configuration, ILogger<KhoanMucSuaChuaRepository> logger)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Legacy Methods (Exact SQL Preserved from clsDMKhoanMucSuaChua.cs)

    /// <summary>
    /// Legacy Load method - Exact implementation from clsDMKhoanMucSuaChua.Load()
    /// SQL: SELECT * FROM DM_KhoanMucSuaChua WHERE Khoa = @pKhoa
    /// </summary>
    public async Task<KhoanMucSuaChuaDto?> LoadAsync(string pKhoa)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM DM_KhoanMucSuaChua WHERE Khoa = @pKhoa";
            
            var result = await connection.QueryFirstOrDefaultAsync<KhoanMucSuaChuaDto>(sql, new { pKhoa });
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadAsync with Khoa: {Khoa}", pKhoa);
            throw;
        }
    }

    /// <summary>
    /// Legacy LoadByCode method - Exact implementation from clsDMKhoanMucSuaChua.LoadByCode()
    /// SQL: SELECT * FROM DM_KhoanMucSuaChua WHERE RTRIM(Ma) = N'@pMa'
    /// </summary>
    public async Task<KhoanMucSuaChuaDto?> LoadByCodeAsync(string pMa)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM DM_KhoanMucSuaChua WHERE RTRIM(Ma) = N@pMa";
            
            var result = await connection.QueryFirstOrDefaultAsync<KhoanMucSuaChuaDto>(sql, new { pMa = pMa.Trim() });
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadByCodeAsync with Ma: {Ma}", pMa);
            throw;
        }
    }

    /// <summary>
    /// Legacy Save method - Exact implementation from clsDMKhoanMucSuaChua.Save()
    /// Uses stored procedure: sp_DM_KhoanMucSuaChua
    /// </summary>
    public async Task<bool> SaveAsync(KhoanMucSuaChuaDto dto, string pTask)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@KhoaLoaiSuaChua", dto.KhoaLoaiSuaChua);
            parameters.Add("@Ma", dto.Ma);
            parameters.Add("@TenViet", dto.TenViet);
            parameters.Add("@TenAnh", dto.TenAnh);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@TuNgay", dto.TuNgay);
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@Active", dto.Active);
            parameters.Add("@Send", dto.Send);
            parameters.Add("@IsCoVan", dto.IsCoVan);
            parameters.Add("@IsChinhSuaNoiDung", dto.IsChinhSuaNoiDung);
            parameters.Add("@pAction", pTask);
            parameters.Add("@pError", dbType: DbType.Int32, direction: ParameterDirection.Output);

            var result = await connection.ExecuteAsync("sp_DM_KhoanMucSuaChua", parameters, commandType: CommandType.StoredProcedure);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveAsync with Khoa: {Khoa}, Task: {Task}", dto.Khoa, pTask);
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowList method - Exact implementation from clsDMKhoanMucSuaChua.ShowList()
    /// SQL: SELECT rtrim(Khoa) as Khoa, Rtrim(Ma) as Ma, Rtrim(Ten{Language}) as Ten FROM DM_KhoanMucSuaChua WHERE Active = 1
    /// </summary>
    public async Task<IEnumerable<KhoanMucSuaChuaListDto>> ShowListAsync(string strKeyFilter = "", string strFiledNameFilter = "")
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var whereClause = "";
            
            if (!string.IsNullOrWhiteSpace(strKeyFilter) && !string.IsNullOrWhiteSpace(strFiledNameFilter))
            {
                var keys = strKeyFilter.Split('|');
                var inClause = string.Join(",", keys.Select(k => $"'{k}'"));
                whereClause = $" AND {strFiledNameFilter} IN ({inClause})";
            }

            var sql = $@"
                SELECT rtrim(Khoa) as Khoa, Rtrim(Ma) as Ma, Rtrim(TenViet) as TenViet  
                FROM DM_KhoanMucSuaChua 
                WHERE Active = 1 {whereClause}
                ORDER BY Ma";
            
            var result = await connection.QueryAsync<KhoanMucSuaChuaListDto>(sql);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListAsync with KeyFilter: {KeyFilter}, FieldFilter: {FieldFilter}", strKeyFilter, strFiledNameFilter);
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowAllList method - Exact implementation from clsDMKhoanMucSuaChua.ShowAllList()
    /// SQL: SELECT Khoa, Rtrim(Ma) as Ma, Rtrim(Ten{Language}) as Ten FROM DM_KhoanMucSuaChua ORDER BY Ma
    /// </summary>
    public async Task<IEnumerable<KhoanMucSuaChuaListDto>> ShowAllListAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = @"
                SELECT Khoa, Rtrim(Ma) as Ma, Rtrim(TenViet) as TenViet  
                FROM DM_KhoanMucSuaChua 
                ORDER BY Ma";
            
            var result = await connection.QueryAsync<KhoanMucSuaChuaListDto>(sql);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllListAsync");
            throw;
        }
    }

    /// <summary>
    /// Legacy SearchByCode method - Exact implementation from clsDMKhoanMucSuaChua.SearchByCode()
    /// Returns pipe-delimited string: Khoa|Ma|Ten
    /// </summary>
    public async Task<string> SearchByCodeAsync(string strCode = "", string strKeyFilter = "", string strFiledNameFilter = "")
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var whereClause = "";
            
            if (!string.IsNullOrWhiteSpace(strCode))
            {
                whereClause += $" AND RTRIM(Ma) = '{strCode.Trim()}'";
            }
            
            if (!string.IsNullOrWhiteSpace(strKeyFilter) && !string.IsNullOrWhiteSpace(strFiledNameFilter))
            {
                var keys = strKeyFilter.Split('|');
                var inClause = string.Join(",", keys.Select(k => $"'{k}'"));
                whereClause += $" AND {strFiledNameFilter} IN ({inClause})";
            }

            var sql = $@"
                SELECT Khoa, Ma, TenViet as Ten  
                FROM DM_KhoanMucSuaChua 
                WHERE Active = 1 {whereClause}";
            
            var result = await connection.QueryFirstOrDefaultAsync(sql);
            if (result != null)
            {
                return $"{result.Khoa}|{result.Ma}|{result.Ten}";
            }
            return "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCodeAsync with Code: {Code}", strCode);
            throw;
        }
    }

    /// <summary>
    /// Legacy ShowListByField method - Exact implementation from clsDMKhoanMucSuaChua.ShowListByField()
    /// Dynamic SQL with custom field list and conditions
    /// </summary>
    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            
            // Replace pipe separators with commas for field list
            strFieldList = strFieldList.Replace("|", ",");
            
            var sql = $"SELECT {strFieldList} FROM DM_KhoanMucSuaChua";
            
            if (!string.IsNullOrWhiteSpace(strConditions))
            {
                sql += $" WHERE {strConditions}";
            }
            
            if (!string.IsNullOrWhiteSpace(strOrder))
            {
                sql += $" ORDER BY {strOrder}";
            }

            var adapter = new SqlDataAdapter(sql, connection);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            return dataTable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByFieldAsync with Fields: {Fields}, Conditions: {Conditions}", strFieldList, strConditions);
            throw;
        }
    }

    #endregion

    #region Modern API Methods (Additional functionality for React Native app)

    /// <summary>
    /// Get all KhoanMucSuaChua records
    /// Modern API method for React Native app
    /// </summary>
    public async Task<IEnumerable<KhoanMucSuaChuaDto>> GetAllAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var sql = "SELECT * FROM DM_KhoanMucSuaChua ORDER BY Ma";
            
            var result = await connection.QueryAsync<KhoanMucSuaChuaDto>(sql);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetAllAsync");
            throw;
        }
    }

    /// <summary>
    /// Get KhoanMucSuaChua by ID
    /// Modern API method for React Native app
    /// </summary>
    public async Task<KhoanMucSuaChuaDto?> GetByIdAsync(string khoa)
    {
        return await LoadAsync(khoa);
    }

    /// <summary>
    /// Get KhoanMucSuaChua by Code
    /// Modern API method for React Native app
    /// </summary>
    public async Task<KhoanMucSuaChuaDto?> GetByCodeAsync(string ma)
    {
        return await LoadByCodeAsync(ma);
    }

    /// <summary>
    /// Create new KhoanMucSuaChua
    /// Modern API method for React Native app
    /// </summary>
    public async Task<string> CreateAsync(CreateKhoanMucSuaChuaDto createDto)
    {
        try
        {
            var dto = new KhoanMucSuaChuaDto
            {
                Khoa = Guid.NewGuid().ToString(),
                Ma = createDto.Ma,
                TenViet = createDto.TenViet,
                TenAnh = createDto.TenAnh,
                DienGiai = createDto.DienGiai,
                KhoaLoaiSuaChua = createDto.KhoaLoaiSuaChua,
                TuNgay = createDto.TuNgay,
                Active = createDto.Active,
                IsCoVan = createDto.IsCoVan,
                IsChinhSuaNoiDung = createDto.IsChinhSuaNoiDung,
                KhoaNhanVienCapNhat = "", // Set by service layer
                Send = 0
            };

            var success = await SaveAsync(dto, "I");
            if (!success)
            {
                throw new InvalidOperationException("Failed to create KhoanMucSuaChua");
            }

            return dto.Khoa;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateAsync with Ma: {Ma}", createDto.Ma);
            throw;
        }
    }

    /// <summary>
    /// Update existing KhoanMucSuaChua
    /// Modern API method for React Native app
    /// </summary>
    public async Task<bool> UpdateAsync(string khoa, UpdateKhoanMucSuaChuaDto updateDto)
    {
        try
        {
            var existing = await LoadAsync(khoa);
            if (existing == null)
            {
                throw new KeyNotFoundException($"KhoanMucSuaChua with Khoa {khoa} not found");
            }

            existing.Ma = updateDto.Ma;
            existing.TenViet = updateDto.TenViet;
            existing.TenAnh = updateDto.TenAnh;
            existing.DienGiai = updateDto.DienGiai;
            existing.KhoaLoaiSuaChua = updateDto.KhoaLoaiSuaChua;
            existing.TuNgay = updateDto.TuNgay;
            existing.Active = updateDto.Active;
            existing.IsCoVan = updateDto.IsCoVan;
            existing.IsChinhSuaNoiDung = updateDto.IsChinhSuaNoiDung;

            var success = await SaveAsync(existing, "U");
            if (!success)
            {
                throw new InvalidOperationException("Failed to update KhoanMucSuaChua");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateAsync with Khoa: {Khoa}", khoa);
            throw;
        }
    }

    /// <summary>
    /// Delete KhoanMucSuaChua
    /// Modern API method for React Native app
    /// </summary>
    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            var existing = await LoadAsync(khoa);
            if (existing == null)
            {
                return false;
            }

            return await SaveAsync(existing, "D");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteAsync with Khoa: {Khoa}", khoa);
            throw;
        }
    }

    #endregion
}
