'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Car, Eye, EyeOff } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useAuth } from '@/contexts/AuthContext'

const loginSchema = z.object({
  username: z.string().min(1, 'Tên đăng nhập không được để trống'),
  password: z.string().min(1, '<PERSON><PERSON><PERSON> kh<PERSON><PERSON> không được để trống'),
  clientId: z.string().min(1, '<PERSON>ui lòng chọn đơn vị')
})

type LoginFormValues = z.infer<typeof loginSchema>

interface Client {
  khoa: string
  ma: string
  tenViet: string
  tenAnh: string
  prefix: string
}

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [clients, setClients] = useState<Client[]>([])
  const [loadingClients, setLoadingClients] = useState(false)
  const { user, login } = useAuth()
  const router = useRouter()

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      clientId: ''
    }
  })

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push('/dashboard')
    }
  }, [user, router])

  // Fetch all available units/departments for login
  const fetchClients = async () => {
    setLoadingClients(true)
    try {
      const response = await fetch(`http://localhost:51552/api/DonVi/select`)
      if (response.ok) {
        const clientData = await response.json()
        setClients(clientData)
      } else {
        setClients([])
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
      setClients([])
    } finally {
      setLoadingClients(false)
    }
  }

  // Load available units/departments on component mount
  useEffect(() => {
    fetchClients()
  }, [])

  const onSubmit = async (values: LoginFormValues) => {
    setIsLoading(true)
    setError('')

    try {
      const success = await login(values.username, values.password, values.clientId)
      
      if (!success) {
        setError('Tên đăng nhập, mật khẩu hoặc đơn vị không hợp lệ!')
      }
    } catch (error) {
      setError('Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <div className="flex items-center space-x-2">
              <Car className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">CARSOFT GP</span>
            </div>
          </div>
          <CardTitle className="text-2xl">Đăng nhập hệ thống</CardTitle>
          <CardDescription>
            Nhập thông tin đăng nhập để truy cập hệ thống quản lý sửa chữa ô tô
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đăng nhập</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nhập tên đăng nhập"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Nhập mật khẩu"
                          {...field}
                          disabled={isLoading}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                          disabled={isLoading}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Đơn vị</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading || loadingClients}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={
                            loadingClients ? "Đang tải..." :
                            clients.length === 0 ? "Không có đơn vị nào" :
                            "Chọn đơn vị"
                          } />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {clients.map((client) => (
                          <SelectItem key={client.khoa} value={client.khoa}>
                            {client.tenViet}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="text-sm text-destructive text-center p-2 bg-destructive/10 rounded">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
              </Button>
            </form>
          </Form>

          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>Hệ thống quản lý sửa chữa ô tô</p>
            <p>© 2024 CARSOFT GIAPHAT</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
