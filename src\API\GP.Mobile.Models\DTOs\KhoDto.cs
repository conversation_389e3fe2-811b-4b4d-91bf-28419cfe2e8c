using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for Kho (Warehouse/Storage) entity
/// Maps exactly to DM_Kho table in legacy database
/// Implements ALL properties from clsDMKho.cs (482 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and warehouse operations
/// </summary>
public class KhoDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse code
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// From date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    [StringLength(8)]
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Business unit key - Foreign key to DM_DonVi
    /// Maps to: mKhoaDonVi property in legacy class
    /// </summary>
    public string KhoaDonVi { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Send status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for Kho list display with joined data
/// Optimized for automotive warehouse lists with business unit info
/// Used by ShowList and ShowAllList methods
/// </summary>
public class KhoListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaDonVi { get; set; } = string.Empty;
    public string DonVi { get; set; } = string.Empty; // From DM_DonVi.TenViet
    public DateTime? TuNgay { get; set; } // Converted from char2date
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    public string NhanVienCapNhat { get; set; } = string.Empty; // From employee lookup
    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;
    public bool IsMainWarehouse { get; set; } = false;
    public bool IsPartsWarehouse { get; set; } = false;
    public bool IsServiceWarehouse { get; set; } = false;
}

/// <summary>
/// DTO for creating new Kho
/// Contains only required fields for creation
/// </summary>
public class CreateKhoDto
{
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaDonVi { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating Kho status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateKhoStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Kho search operations
/// Used for advanced search and filtering
/// </summary>
public class KhoSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public string? KhoaDonVi { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public int? Active { get; set; }
    public int? Send { get; set; }
    public bool? IsMainWarehouse { get; set; }
    public bool? IsPartsWarehouse { get; set; }
    public bool? IsServiceWarehouse { get; set; }
}

/// <summary>
/// DTO for Kho dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class KhoLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DonVi { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for Kho validation operations
/// Used for duplicate checking and validation
/// </summary>
public class KhoValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsUsedInInventory { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for warehouse search by code
/// Used by SearchByCode method
/// </summary>
public class KhoSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public bool Found { get; set; } = false;
}

/// <summary>
/// DTO for automotive warehouse categories
/// Specialized for automotive warehouse classification
/// </summary>
public class WarehouseCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string DonVi { get; set; } = string.Empty;
    public bool IsMainWarehouse { get; set; } = false;
    public bool IsPartsWarehouse { get; set; } = false;
    public bool IsServiceWarehouse { get; set; } = false;
    public bool IsBodyShopWarehouse { get; set; } = false;
    public bool IsOilWarehouse { get; set; } = false;
    public bool IsTireWarehouse { get; set; } = false;
    public bool IsAccessoryWarehouse { get; set; } = false;
    public bool IsReturnWarehouse { get; set; } = false;
    public bool IsQuarantineWarehouse { get; set; } = false;
    public int TotalItems { get; set; } = 0;
    public decimal TotalValue { get; set; } = 0;
}

/// <summary>
/// DTO for warehouse with inventory summary
/// Used for comprehensive warehouse display with stock information
/// </summary>
public class KhoWithInventoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string DonVi { get; set; } = string.Empty;
    public DateTime? TuNgay { get; set; }
    public int Active { get; set; } = 1;
    public int TotalProducts { get; set; } = 0;
    public int TotalCategories { get; set; } = 0;
    public decimal TotalInventoryValue { get; set; } = 0;
    public decimal TotalIncomingValue { get; set; } = 0;
    public decimal TotalOutgoingValue { get; set; } = 0;
    public DateTime? LastTransactionDate { get; set; }
    public bool HasLowStockItems { get; set; } = false;
    public bool HasExpiredItems { get; set; } = false;
}

/// <summary>
/// DTO for warehouse statistics
/// Used for reporting and analytics
/// </summary>
public class KhoStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public int TotalTransactions { get; set; } = 0;
    public int TotalIncomingTransactions { get; set; } = 0;
    public int TotalOutgoingTransactions { get; set; } = 0;
    public decimal TotalIncomingValue { get; set; } = 0;
    public decimal TotalOutgoingValue { get; set; } = 0;
    public decimal CurrentInventoryValue { get; set; } = 0;
    public decimal AverageTransactionValue { get; set; } = 0;
    public DateTime? FirstTransactionDate { get; set; }
    public DateTime? LastTransactionDate { get; set; }
    public int DaysSinceLastTransaction { get; set; } = 0;
    public string ActivityLevel { get; set; } = string.Empty; // High, Medium, Low
    public bool IsHighVolumeWarehouse { get; set; } = false;
}

/// <summary>
/// DTO for warehouse field-based queries
/// Used by ShowListByField method
/// </summary>
public class KhoFieldQueryDto
{
    public string FieldList { get; set; } = string.Empty; // Pipe-separated field list
    public string Conditions { get; set; } = string.Empty; // WHERE conditions
    public string OrderBy { get; set; } = string.Empty; // ORDER BY clause
}

/// <summary>
/// DTO for warehouse capacity and utilization
/// Used for warehouse management and planning
/// </summary>
public class WarehouseCapacityDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public decimal MaxCapacity { get; set; } = 0; // Maximum storage capacity
    public decimal CurrentUtilization { get; set; } = 0; // Current utilization percentage
    public decimal AvailableCapacity { get; set; } = 0; // Available capacity
    public bool IsNearCapacity { get; set; } = false; // > 90% utilization
    public bool IsOverCapacity { get; set; } = false; // > 100% utilization
    public string CapacityStatus { get; set; } = string.Empty; // Low, Medium, High, Critical
    public DateTime LastCapacityCheck { get; set; }
}

/// <summary>
/// DTO for automotive parts warehouse specialization
/// Used for automotive-specific warehouse operations
/// </summary>
public class AutomotiveWarehouseDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string DonVi { get; set; } = string.Empty;
    public bool HandlesEngineParts { get; set; } = false;
    public bool HandlesBodyParts { get; set; } = false;
    public bool HandlesElectricalParts { get; set; } = false;
    public bool HandlesTires { get; set; } = false;
    public bool HandlesOils { get; set; } = false;
    public bool HandlesAccessories { get; set; } = false;
    public bool HandlesTools { get; set; } = false;
    public bool RequiresTemperatureControl { get; set; } = false;
    public bool RequiresHazmatHandling { get; set; } = false;
    public bool SupportsJustInTime { get; set; } = false;
    public string PreferredSuppliers { get; set; } = string.Empty;
    public string SpecialRequirements { get; set; } = string.Empty;
}
