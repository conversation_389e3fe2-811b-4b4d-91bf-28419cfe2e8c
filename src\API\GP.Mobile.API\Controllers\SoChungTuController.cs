using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Controller for SoChungTu (Document Numbering) operations
/// Implements ALL endpoints from clsSoChungTu.cs (366 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// CRITICAL SYSTEM COMPONENT - Essential for document numbering across all transactions
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SoChungTuController : ControllerBase
{
    private readonly ISoChungTuService _soChungTuService;
    private readonly ILogger<SoChungTuController> _logger;

    public SoChungTuController(
        ISoChungTuService soChungTuService,
        ILogger<SoChungTuController> logger)
    {
        _soChungTuService = soChungTuService;
        _logger = logger;
    }

    #region Legacy API Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpGet("load/{loaiChungTu}")]
    public async Task<ActionResult<bool>> Load(string loaiChungTu)
    {
        try
        {
            var result = await _soChungTuService.LoadAsync(loaiChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UndoVoucher method endpoint
    /// </summary>
    [HttpPost("undovoucher")]
    public async Task<ActionResult<bool>> UndoVoucher([FromBody] UndoVoucherLegacyRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.UndoVoucherAsync(request.NamThang, request.LoaiChungTu, request.SoChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UndoVoucher endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy TinhHauTo method endpoint
    /// </summary>
    [HttpGet("tinhhautho")]
    public async Task<ActionResult<string>> TinhHauTo([FromQuery] string loai, [FromQuery] string namThang)
    {
        try
        {
            var result = await _soChungTuService.TinhHauToAsync(loai, namThang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TinhHauTo endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy CreateVoucher method endpoint
    /// </summary>
    [HttpPost("createvoucher")]
    public async Task<ActionResult<string>> CreateVoucher([FromBody] CreateVoucherLegacyRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.CreateVoucherAsync(request.LoaiChungTu, request.NamThang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateVoucher endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy LoadChungTu method endpoint
    /// </summary>
    [HttpGet("loadchungtu")]
    public async Task<ActionResult<DataTable>> LoadChungTu([FromQuery] string loai, [FromQuery] string namThang, [FromQuery] string condition = "")
    {
        try
        {
            var result = await _soChungTuService.LoadChungTuAsync(loai, namThang, condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadChungTu endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy CreateVoucherBG method endpoint
    /// </summary>
    [HttpPost("createvoucherbg")]
    public async Task<ActionResult<string>> CreateVoucherBG([FromBody] CreateVoucherBGRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.CreateVoucherBGAsync(request.LoaiChungTu, request.NamThang, request.CoVan, request.KhoaCoVan);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateVoucherBG endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy CreateVoucherBaoGiaThucHien method endpoint
    /// </summary>
    [HttpPost("createvoucherbaogiathuchien")]
    public async Task<ActionResult<string>> CreateVoucherBaoGiaThucHien([FromBody] CreateVoucherBaoGiaThucHienRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.CreateVoucherBaoGiaThucHienAsync(request.LoaiChungTu, request.NamThang, request.MaDaiLy);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateVoucherBaoGiaThucHien endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion

    #region Modern API Endpoints

    /// <summary>
    /// Get all document types
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<SoChungTuListDto>>> GetAll()
    {
        try
        {
            var result = await _soChungTuService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all document types");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get document type by ID
    /// </summary>
    [HttpGet("{loaiChungTu}")]
    public async Task<ActionResult<SoChungTuDto>> GetById(string loaiChungTu)
    {
        try
        {
            var result = await _soChungTuService.GetByIdAsync(loaiChungTu);
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document type by ID");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Create new document type
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateSoChungTuDto createDto)
    {
        try
        {
            var result = await _soChungTuService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { loaiChungTu = result }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating document type");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update document type
    /// </summary>
    [HttpPut("{loaiChungTu}")]
    public async Task<ActionResult<bool>> Update(string loaiChungTu, [FromBody] UpdateSoChungTuDto updateDto)
    {
        try
        {
            var result = await _soChungTuService.UpdateAsync(loaiChungTu, updateDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document type");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Delete document type
    /// </summary>
    [HttpDelete("{loaiChungTu}")]
    public async Task<ActionResult<bool>> Delete(string loaiChungTu)
    {
        try
        {
            var result = await _soChungTuService.DeleteAsync(loaiChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document type");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Search document types
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<SoChungTuListDto>>> Search([FromBody] SoChungTuSearchDto searchDto)
    {
        try
        {
            var result = await _soChungTuService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching document types");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get document types for lookup/dropdown
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<SoChungTuLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _soChungTuService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document type lookup");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Validate document type
    /// </summary>
    [HttpGet("validate/{loaiChungTu}")]
    public async Task<ActionResult<SoChungTuValidationDto>> Validate(string loaiChungTu)
    {
        try
        {
            var result = await _soChungTuService.ValidateAsync(loaiChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating document type");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Generate new document number
    /// </summary>
    [HttpPost("generate")]
    public async Task<ActionResult<CreateVoucherResponseDto>> GenerateDocumentNumber([FromBody] CreateVoucherRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.GenerateDocumentNumberAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating document number");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Undo document number
    /// </summary>
    [HttpPost("undo")]
    public async Task<ActionResult<bool>> UndoDocumentNumber([FromBody] UndoVoucherRequestDto request)
    {
        try
        {
            var result = await _soChungTuService.UndoDocumentNumberAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error undoing document number");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get automotive-specific document types
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveDocumentNumberingDto>>> GetAutomotiveDocumentTypes()
    {
        try
        {
            var result = await _soChungTuService.GetAutomotiveDocumentTypesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive document types");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get document numbering statistics
    /// </summary>
    [HttpGet("stats/{loaiChungTu}")]
    public async Task<ActionResult<DocumentNumberingStatsDto>> GetStats(string loaiChungTu)
    {
        try
        {
            var result = await _soChungTuService.GetStatsAsync(loaiChungTu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document numbering stats");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}

#region Request DTOs for SoChungTu

/// <summary>
/// Request DTO for legacy UndoVoucher method
/// </summary>
public class UndoVoucherLegacyRequestDto
{
    public string NamThang { get; set; } = string.Empty;
    public string LoaiChungTu { get; set; } = string.Empty;
    public double SoChungTu { get; set; }
}

/// <summary>
/// Request DTO for legacy CreateVoucher method
/// </summary>
public class CreateVoucherLegacyRequestDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string NamThang { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for CreateVoucherBG method
/// </summary>
public class CreateVoucherBGRequestDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string NamThang { get; set; } = string.Empty;
    public string CoVan { get; set; } = string.Empty;
    public string KhoaCoVan { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for CreateVoucherBaoGiaThucHien method
/// </summary>
public class CreateVoucherBaoGiaThucHienRequestDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string NamThang { get; set; } = string.Empty;
    public string MaDaiLy { get; set; } = string.Empty;
}

#endregion
