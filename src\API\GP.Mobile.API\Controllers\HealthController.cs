using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Health check controller for testing
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly ILogger<HealthController> _logger;

        public HealthController(IConfiguration configuration, ILogger<HealthController> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? "";
            _logger = logger;
        }

        /// <summary>
        /// Basic health check
        /// </summary>
        [HttpGet]
        public IActionResult Get()
        {
            return Ok(new { 
                status = "healthy", 
                timestamp = DateTime.Now,
                version = "1.0.0",
                environment = "development"
            });
        }

        /// <summary>
        /// Database connection test
        /// </summary>
        [HttpGet("database")]
        public async Task<IActionResult> TestDatabase()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                
                var command = new SqlCommand("SELECT DB_NAME() as DatabaseName, SYSTEM_USER as CurrentUser, GETDATE() as CurrentTime", connection);
                var reader = await command.ExecuteReaderAsync();
                
                var result = new Dictionary<string, object>();
                if (await reader.ReadAsync())
                {
                    result["DatabaseName"] = reader["DatabaseName"];
                    result["CurrentUser"] = reader["CurrentUser"];
                    result["CurrentTime"] = reader["CurrentTime"];
                    result["Status"] = "Connected Successfully";
                    result["ConnectionString"] = _connectionString.Replace("Password=", "Password=***");
                }
                
                _logger.LogInformation("Database connection test successful for {DatabaseName}", result["DatabaseName"]);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed");
                return BadRequest(new { 
                    Status = "Connection Failed", 
                    Error = ex.Message,
                    ConnectionString = _connectionString.Replace("Password=", "Password=***")
                });
            }
        }
    }
}
