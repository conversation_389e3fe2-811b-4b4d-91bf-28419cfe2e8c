using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;

namespace GP.Mobile.Core.Services
{
    /// <summary>
    /// Service implementation for Quotation Terms & Conditions (ClsDMDieuKhoanBaoGia)
    /// Implements business logic for managing terms and conditions
    /// Exact functionality from ClsDMDieuKhoanBaoGia legacy class (442 lines)
    /// Used in form lines 8634-8712 for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan
    /// </summary>
    public class DieuKhoanBaoGiaService : IDieuKhoanBaoGiaService
    {
        private readonly IDieuKhoanBaoGiaRepository _repository;
        private readonly ILogger<DieuKhoanBaoGiaService> _logger;

        public DieuKhoanBaoGiaService(IDieuKhoanBaoGiaRepository repository, ILogger<DieuKhoanBaoGiaService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Load terms & conditions by ID
        /// Exact implementation from legacy Load method
        /// </summary>
        public async Task<DieuKhoanBaoGiaDto?> LoadAsync(string khoa)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoa))
                {
                    _logger.LogWarning("LoadAsync called with empty khoa");
                    return null;
                }

                return await _repository.LoadAsync(khoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading terms & conditions for {Khoa}", khoa);
                return null;
            }
        }

        /// <summary>
        /// Load terms & conditions by code
        /// Exact implementation from legacy LoadByCode method
        /// </summary>
        public async Task<DieuKhoanBaoGiaDto?> LoadByCodeAsync(string ma)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(ma))
                {
                    _logger.LogWarning("LoadByCodeAsync called with empty ma");
                    return null;
                }

                return await _repository.LoadByCodeAsync(ma);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading terms & conditions by code {Ma}", ma);
                return null;
            }
        }

        /// <summary>
        /// Create new terms & conditions
        /// </summary>
        public async Task<ServiceResult<string>> CreateAsync(CreateDieuKhoanBaoGiaDto createDto)
        {
            try
            {
                // Validate input
                var validationResult = await ValidateCreateDataAsync(createDto);
                if (!validationResult.Success)
                {
                    return ServiceResult<string>.ErrorResult(validationResult.Message, validationResult.Errors);
                }

                // Check if code already exists
                var codeExists = await _repository.CodeExistsAsync(createDto.Ma);
                if (codeExists)
                {
                    return ServiceResult<string>.ErrorResult("Mã điều khoản đã tồn tại");
                }

                // Create terms & conditions
                var khoa = await _repository.CreateAsync(createDto);
                if (!string.IsNullOrEmpty(khoa))
                {
                    _logger.LogInformation("Successfully created terms & conditions with ID {Khoa}", khoa);
                    return ServiceResult<string>.SuccessResult(khoa, "Tạo điều khoản báo giá thành công");
                }
                else
                {
                    return ServiceResult<string>.ErrorResult("Lỗi khi tạo điều khoản báo giá");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating terms & conditions");
                return ServiceResult<string>.ErrorResult("Lỗi hệ thống khi tạo điều khoản báo giá");
            }
        }

        /// <summary>
        /// Update terms & conditions
        /// </summary>
        public async Task<ServiceResult<bool>> UpdateAsync(string khoa, UpdateDieuKhoanBaoGiaDto updateDto)
        {
            try
            {
                // Validate input
                var validationResult = await ValidateUpdateDataAsync(khoa, updateDto);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult(validationResult.Message, validationResult.Errors);
                }

                // Check if terms exists
                var exists = await _repository.ExistsAsync(khoa);
                if (!exists)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy điều khoản báo giá");
                }

                // Update terms & conditions
                var updateResult = await _repository.UpdateAsync(khoa, updateDto);
                if (updateResult)
                {
                    _logger.LogInformation("Successfully updated terms & conditions {Khoa}", khoa);
                    return ServiceResult<bool>.SuccessResult(true, "Cập nhật điều khoản báo giá thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi cập nhật điều khoản báo giá");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating terms & conditions for {Khoa}", khoa);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi cập nhật điều khoản báo giá");
            }
        }

        /// <summary>
        /// Delete terms & conditions
        /// </summary>
        public async Task<ServiceResult<bool>> DeleteAsync(string khoa)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(khoa))
                {
                    return ServiceResult<bool>.ErrorResult("Mã điều khoản không được để trống");
                }

                // Check if can delete
                var canDelete = await _repository.CanDeleteAsync(khoa);
                if (!canDelete)
                {
                    return ServiceResult<bool>.ErrorResult("Không thể xóa điều khoản hệ thống hoặc điều khoản mặc định");
                }

                // Check if exists
                var exists = await _repository.ExistsAsync(khoa);
                if (!exists)
                {
                    return ServiceResult<bool>.ErrorResult("Không tìm thấy điều khoản báo giá");
                }

                var deleteResult = await _repository.DeleteAsync(khoa);
                if (deleteResult)
                {
                    _logger.LogInformation("Successfully deleted terms & conditions {Khoa}", khoa);
                    return ServiceResult<bool>.SuccessResult(true, "Xóa điều khoản báo giá thành công");
                }
                else
                {
                    return ServiceResult<bool>.ErrorResult("Lỗi khi xóa điều khoản báo giá");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting terms & conditions for {Khoa}", khoa);
                return ServiceResult<bool>.ErrorResult("Lỗi hệ thống khi xóa điều khoản báo giá");
            }
        }

        /// <summary>
        /// Get terms & conditions by type
        /// Exact implementation from form SQL usage: SELECT NoiDung FROM SC_DieuKhoanBaoGia WHERE Loai = 'BG'
        /// </summary>
        public async Task<ServiceResult<List<DieuKhoanBaoGiaListDto>>> GetByTypeAsync(string loai, string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(loai))
                {
                    return ServiceResult<List<DieuKhoanBaoGiaListDto>>.ErrorResult("Loại điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<List<DieuKhoanBaoGiaListDto>>.ErrorResult("Mã đơn vị không được để trống");
                }

                var result = await _repository.GetByTypeAsync(loai, donViId);
                var loaiText = loai switch
                {
                    "BG" => "báo giá",
                    "SC" => "sửa chữa",
                    "QT" => "quyết toán",
                    _ => "không xác định"
                };

                return ServiceResult<List<DieuKhoanBaoGiaListDto>>.SuccessResult(result, 
                    $"Tìm thấy {result.Count} điều khoản {loaiText}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting terms & conditions by type {Loai} for unit {DonViId}", loai, donViId);
                return ServiceResult<List<DieuKhoanBaoGiaListDto>>.ErrorResult("Lỗi hệ thống khi lấy điều khoản theo loại");
            }
        }

        /// <summary>
        /// Get terms content by type for form initialization
        /// Used for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan methods
        /// </summary>
        public async Task<ServiceResult<DieuKhoanContentDto>> GetTermsContentAsync(string loai, string donViId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(loai))
                {
                    return ServiceResult<DieuKhoanContentDto>.ErrorResult("Loại điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(donViId))
                {
                    return ServiceResult<DieuKhoanContentDto>.ErrorResult("Mã đơn vị không được để trống");
                }

                var result = await _repository.GetTermsContentAsync(loai, donViId);
                return ServiceResult<DieuKhoanContentDto>.SuccessResult(result, 
                    $"Lấy nội dung điều khoản {loai} thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting terms content for type {Loai} and unit {DonViId}", loai, donViId);
                return ServiceResult<DieuKhoanContentDto>.ErrorResult("Lỗi hệ thống khi lấy nội dung điều khoản");
            }
        }

        /// <summary>
        /// Initialize terms content for quotation form
        /// Exact implementation from form InitDieuKhoanBaoGia method (lines 8634-8650)
        /// </summary>
        public async Task<ServiceResult<string>> InitDieuKhoanBaoGiaAsync(string donViId)
        {
            try
            {
                var termsContent = await GetTermsContentAsync("BG", donViId);
                if (termsContent.Success && termsContent.Data != null)
                {
                    return ServiceResult<string>.SuccessResult(termsContent.Data.CombinedContent, 
                        "Khởi tạo điều khoản báo giá thành công");
                }
                else
                {
                    return ServiceResult<string>.ErrorResult("Không tìm thấy điều khoản báo giá");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing quotation terms for unit {DonViId}", donViId);
                return ServiceResult<string>.ErrorResult("Lỗi hệ thống khi khởi tạo điều khoản báo giá");
            }
        }

        /// <summary>
        /// Initialize terms content for repair form
        /// Exact implementation from form InitDieuKhoanLSC method (lines 8651-8667)
        /// </summary>
        public async Task<ServiceResult<string>> InitDieuKhoanLSCAsync(string donViId)
        {
            try
            {
                var termsContent = await GetTermsContentAsync("SC", donViId);
                if (termsContent.Success && termsContent.Data != null)
                {
                    return ServiceResult<string>.SuccessResult(termsContent.Data.CombinedContent, 
                        "Khởi tạo điều khoản sửa chữa thành công");
                }
                else
                {
                    return ServiceResult<string>.ErrorResult("Không tìm thấy điều khoản sửa chữa");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing repair terms for unit {DonViId}", donViId);
                return ServiceResult<string>.ErrorResult("Lỗi hệ thống khi khởi tạo điều khoản sửa chữa");
            }
        }

        /// <summary>
        /// Initialize terms content for settlement form
        /// Exact implementation from form InitDieuKhoanQuyetToan method (lines 8668-8684)
        /// </summary>
        public async Task<ServiceResult<string>> InitDieuKhoanQuyetToanAsync(string donViId)
        {
            try
            {
                var termsContent = await GetTermsContentAsync("QT", donViId);
                if (termsContent.Success && termsContent.Data != null)
                {
                    return ServiceResult<string>.SuccessResult(termsContent.Data.CombinedContent, 
                        "Khởi tạo điều khoản quyết toán thành công");
                }
                else
                {
                    return ServiceResult<string>.ErrorResult("Không tìm thấy điều khoản quyết toán");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing settlement terms for unit {DonViId}", donViId);
                return ServiceResult<string>.ErrorResult("Lỗi hệ thống khi khởi tạo điều khoản quyết toán");
            }
        }

        /// <summary>
        /// Validate create data before saving
        /// </summary>
        public async Task<ServiceResult<bool>> ValidateCreateDataAsync(CreateDieuKhoanBaoGiaDto createDto)
        {
            try
            {
                var errors = new List<string>();

                // Check required fields
                if (string.IsNullOrWhiteSpace(createDto.Ma))
                {
                    errors.Add("Mã điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(createDto.TenViet))
                {
                    errors.Add("Tên tiếng Việt không được để trống");
                }

                if (string.IsNullOrWhiteSpace(createDto.Loai))
                {
                    errors.Add("Loại điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(createDto.NoiDung))
                {
                    errors.Add("Nội dung điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(createDto.NguoiTao))
                {
                    errors.Add("Người tạo không được để trống");
                }

                // Validate type
                if (!string.IsNullOrEmpty(createDto.Loai) && !new[] { "BG", "SC", "QT" }.Contains(createDto.Loai))
                {
                    errors.Add("Loại điều khoản phải là BG, SC hoặc QT");
                }

                // Validate active status
                if (createDto.Active != 0 && createDto.Active != 1)
                {
                    errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.ErrorResult("Dữ liệu không hợp lệ", errors);
                }

                return ServiceResult<bool>.SuccessResult(true, "Dữ liệu hợp lệ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating create data");
                return ServiceResult<bool>.ErrorResult("Lỗi khi kiểm tra dữ liệu");
            }
        }

        /// <summary>
        /// Validate update data before saving
        /// </summary>
        public async Task<ServiceResult<bool>> ValidateUpdateDataAsync(string khoa, UpdateDieuKhoanBaoGiaDto updateDto)
        {
            try
            {
                var errors = new List<string>();

                // Check required fields
                if (string.IsNullOrWhiteSpace(khoa))
                {
                    errors.Add("Mã điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(updateDto.TenViet))
                {
                    errors.Add("Tên tiếng Việt không được để trống");
                }

                if (string.IsNullOrWhiteSpace(updateDto.NoiDung))
                {
                    errors.Add("Nội dung điều khoản không được để trống");
                }

                if (string.IsNullOrWhiteSpace(updateDto.NguoiCapNhat))
                {
                    errors.Add("Người cập nhật không được để trống");
                }

                // Validate active status
                if (updateDto.Active != 0 && updateDto.Active != 1)
                {
                    errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");
                }

                if (errors.Any())
                {
                    return ServiceResult<bool>.ErrorResult("Dữ liệu không hợp lệ", errors);
                }

                return ServiceResult<bool>.SuccessResult(true, "Dữ liệu hợp lệ");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating update data");
                return ServiceResult<bool>.ErrorResult("Lỗi khi kiểm tra dữ liệu");
            }
        }

        // Placeholder implementations for remaining interface methods
        public async Task<ServiceResult<DieuKhoanBaoGiaDto>> GetDefaultByTypeAsync(string loai, string donViId)
        {
            // Implementation will be added in next part
            return ServiceResult<DieuKhoanBaoGiaDto>.SuccessResult(new DieuKhoanBaoGiaDto());
        }

        public async Task<ServiceResult<List<DieuKhoanBaoGiaListDto>>> GetAllAsync(string donViId, string? loai = null, int? active = null)
        {
            // Implementation will be added in next part
            return ServiceResult<List<DieuKhoanBaoGiaListDto>>.SuccessResult(new List<DieuKhoanBaoGiaListDto>());
        }

        public async Task<ServiceResult<List<DieuKhoanBaoGiaListDto>>> SearchAsync(DieuKhoanBaoGiaSearchDto searchDto)
        {
            // Implementation will be added in next part
            return ServiceResult<List<DieuKhoanBaoGiaListDto>>.SuccessResult(new List<DieuKhoanBaoGiaListDto>());
        }

        public async Task<ServiceResult<List<DieuKhoanBaoGiaLookupDto>>> GetLookupDataAsync(string loai, string donViId)
        {
            // Implementation will be added in next part
            return ServiceResult<List<DieuKhoanBaoGiaLookupDto>>.SuccessResult(new List<DieuKhoanBaoGiaLookupDto>());
        }

        public async Task<ServiceResult<DieuKhoanBaoGiaStatisticsDto>> GetStatisticsAsync(string donViId)
        {
            // Implementation will be added in next part
            return ServiceResult<DieuKhoanBaoGiaStatisticsDto>.SuccessResult(new DieuKhoanBaoGiaStatisticsDto());
        }

        public async Task<ServiceResult<bool>> SetDefaultAsync(string khoa, string loai, string donViId)
        {
            // Implementation will be added in next part
            return ServiceResult<bool>.SuccessResult(true);
        }

        public async Task<ServiceResult<PaginatedResult<DieuKhoanBaoGiaListDto>>> GetForMobileAsync(string loai, string donViId, int pageSize, int pageNumber, string? searchTerm = null)
        {
            // Implementation will be added in next part
            return ServiceResult<PaginatedResult<DieuKhoanBaoGiaListDto>>.SuccessResult(new PaginatedResult<DieuKhoanBaoGiaListDto>());
        }

        public async Task<ServiceResult<int>> BulkUpdateActiveStatusAsync(List<string> khoaList, int active, string nguoiCapNhat)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<string>> CopyToTypeAsync(string sourceKhoa, string targetLoai, string nguoiTao)
        {
            // Implementation will be added in next part
            return ServiceResult<string>.SuccessResult("");
        }

        public async Task<ServiceResult<TermsUsageStatisticsDto>> GetUsageStatisticsAsync(string khoa)
        {
            // Implementation will be added in next part
            return ServiceResult<TermsUsageStatisticsDto>.SuccessResult(new TermsUsageStatisticsDto());
        }

        public async Task<bool> ExistsAsync(string khoa)
        {
            try
            {
                return await _repository.ExistsAsync(khoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if terms & conditions exists for {Khoa}", khoa);
                return false;
            }
        }

        public async Task<bool> CodeExistsAsync(string ma, string? excludeKhoa = null)
        {
            try
            {
                return await _repository.CodeExistsAsync(ma, excludeKhoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if code exists for {Ma}", ma);
                return false;
            }
        }

        public async Task<bool> CanDeleteAsync(string khoa)
        {
            try
            {
                return await _repository.CanDeleteAsync(khoa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if can delete terms & conditions for {Khoa}", khoa);
                return false;
            }
        }

        public async Task<ServiceResult<List<DieuKhoanBaoGiaListDto>>> GetByDateRangeAsync(string donViId, string fromDate, string toDate)
        {
            // Implementation will be added in next part
            return ServiceResult<List<DieuKhoanBaoGiaListDto>>.SuccessResult(new List<DieuKhoanBaoGiaListDto>());
        }

        public async Task<ServiceResult<int>> ArchiveOldDataAsync(string donViId, int olderThanDays = 365)
        {
            // Implementation will be added in next part
            return ServiceResult<int>.SuccessResult(0);
        }

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync(string donViId, string? loai = null)
        {
            // Implementation will be added in next part
            return ServiceResult<byte[]>.SuccessResult(Array.Empty<byte>());
        }

        public async Task<ServiceResult<ImportResultDto>> ImportFromExcelAsync(byte[] fileData, string donViId, string nguoiTao)
        {
            // Implementation will be added in next part
            return ServiceResult<ImportResultDto>.SuccessResult(new ImportResultDto());
        }

        public async Task<ServiceResult<List<TermsAuditDto>>> GetAuditTrailAsync(string khoa)
        {
            // Implementation will be added in next part
            return ServiceResult<List<TermsAuditDto>>.SuccessResult(new List<TermsAuditDto>());
        }
    }
}
