using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using Microsoft.Data.SqlClient;

namespace GP.Mobile.Core.Repositories;

/// <summary>
/// Repository for BaoGiaYeuCauSuaChuaChiTiet (Repair Request Details) operations
/// Implements ALL methods from clsBaoGiaYeuCauSuaChuaChiTiet.cs (196 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for detailed repair request specifications in quotations
/// </summary>
public interface IBaoGiaYeuCauSuaChuaChiTietRepository
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync();
    Task<DataTable> GetDetailsYeuCauSuaChuaAsync(string khoaBaoGia);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetAllAsync();
    Task<BaoGiaYeuCauSuaChuaChiTietDto?> GetByIdAsync(string khoa);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByQuotationAsync(string khoaBaoGia);
    Task<BaoGiaYeuCauSuaChuaChiTietDto> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto);
    Task<BaoGiaYeuCauSuaChuaChiTietDto?> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto);
    Task<bool> DeleteAsync(string khoa);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietLookupDto>> GetLookupAsync();
    Task<BaoGiaYeuCauSuaChuaChiTietValidationDto> ValidateAsync(string khoa);
    Task<IEnumerable<AutomotiveRepairWorkDetailDto>> GetAutomotiveRepairWorkDetailsAsync(string khoaBaoGia);
    Task<RepairWorkSummaryDto> GetRepairWorkSummaryAsync(string khoaBaoGia);
    Task<IEnumerable<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync();
    Task<WorkItemProgressDto?> GetWorkItemProgressAsync(string khoa);
    Task<bool> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto);
    
    #endregion
}

/// <summary>
/// Implementation of BaoGiaYeuCauSuaChuaChiTiet repository
/// Follows exact legacy SQL queries and business logic from clsBaoGiaYeuCauSuaChuaChiTiet.cs
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietRepository : IBaoGiaYeuCauSuaChuaChiTietRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<BaoGiaYeuCauSuaChuaChiTietRepository> _logger;

    // Current instance data - matches legacy class private fields
    private string mKhoa = string.Empty;
    private string mKhoaBaoGia = string.Empty;
    private string mMaCongViec = string.Empty;
    private string mNoiDungCongViec = string.Empty;
    private string mKhoaKTV = string.Empty;
    private string mGhiChu = string.Empty;

    public BaoGiaYeuCauSuaChuaChiTietRepository(IDbConnection connection, ILogger<BaoGiaYeuCauSuaChuaChiTietRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    /// <summary>
    /// Legacy Load method - Exact implementation from clsBaoGiaYeuCauSuaChuaChiTiet.Load()
    /// SQL: SELECT * FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] WHERE Khoa = '{khoa}'
    /// </summary>
    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            var sql = $"SELECT * FROM [SC_BaoGiaYeuCauSuaChuaChiTiet] WHERE Khoa = '{khoa}'";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                mKhoa = reader.GetString(0)?.Trim() ?? string.Empty;
                mKhoaBaoGia = reader.GetString(1)?.Trim() ?? string.Empty;
                mMaCongViec = reader.GetString(2)?.Trim() ?? string.Empty;
                mNoiDungCongViec = reader.GetString(3)?.Trim() ?? string.Empty;
                mKhoaKTV = reader.GetString(4)?.Trim() ?? string.Empty;
                mGhiChu = reader.GetString(5)?.Trim() ?? string.Empty;
                
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LoadAsync for Khoa: {Khoa}", khoa);
            return false;
        }
    }

    /// <summary>
    /// Legacy Save method - Exact implementation from clsBaoGiaYeuCauSuaChuaChiTiet.Save()
    /// Uses stored procedure: SC_sp_BaoGiaYeuCauSuaChuaChiTiet
    /// </summary>
    public async Task<bool> SaveAsync()
    {
        try
        {
            using var command = new SqlCommand("SC_sp_BaoGiaYeuCauSuaChuaChiTiet", (SqlConnection)_connection);
            command.CommandType = CommandType.StoredProcedure;
            
            command.Parameters.AddWithValue("@Khoa", mKhoa);
            command.Parameters.AddWithValue("@KhoaBaoGia", mKhoaBaoGia);
            command.Parameters.AddWithValue("@MaCongViec", mMaCongViec);
            command.Parameters.AddWithValue("@NoiDungCongViec", mNoiDungCongViec);
            command.Parameters.AddWithValue("@KhoaKTV", mKhoaKTV);
            command.Parameters.AddWithValue("@GhiChu", mGhiChu);

            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            await command.ExecuteNonQueryAsync();
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveAsync");
            return false;
        }
    }

    /// <summary>
    /// Legacy GetDetailsYeuCauSuaChua method - Exact implementation from clsBaoGiaYeuCauSuaChuaChiTiet.GetDetailsYeuCauSuaChua()
    /// SQL: Complex join query with DM_DoiTuong for technician names
    /// </summary>
    public async Task<DataTable> GetDetailsYeuCauSuaChuaAsync(string khoaBaoGia)
    {
        try
        {
            var sql = @"
                SELECT CT.Khoa, CT.MaCongViec, CT.NoiDungCongViec, CT.KhoaKTV, KTV.TenViet As TenKTV, CT.GhiChu  
                FROM SC_BaoGiaYeuCauSuaChuaChiTiet CT  
                LEFT JOIN DM_DoiTuong KTV on CT.KhoaKTV = KTV.Khoa  
                WHERE CT.KhoaBaoGia = @KhoaBaoGia 
                ORDER BY CT.Khoa";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            command.Parameters.AddWithValue("@KhoaBaoGia", khoaBaoGia);
            
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var adapter = new SqlDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            
            return dataTable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDetailsYeuCauSuaChuaAsync");
            return new DataTable();
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetAllAsync()
    {
        try
        {
            var sql = @"
                SELECT CT.Khoa, CT.KhoaBaoGia, CT.MaCongViec, CT.NoiDungCongViec, 
                       CT.KhoaKTV, ISNULL(KTV.TenViet, '') As TenKTV, CT.GhiChu
                FROM SC_BaoGiaYeuCauSuaChuaChiTiet CT  
                LEFT JOIN DM_DoiTuong KTV on CT.KhoaKTV = KTV.Khoa  
                ORDER BY CT.KhoaBaoGia, CT.Khoa";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            var results = new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
            
            while (await reader.ReadAsync())
            {
                var item = new BaoGiaYeuCauSuaChuaChiTietListDto
                {
                    Khoa = reader.GetString("Khoa")?.Trim() ?? string.Empty,
                    KhoaBaoGia = reader.GetString("KhoaBaoGia")?.Trim() ?? string.Empty,
                    MaCongViec = reader.GetString("MaCongViec")?.Trim() ?? string.Empty,
                    NoiDungCongViec = reader.GetString("NoiDungCongViec")?.Trim() ?? string.Empty,
                    KhoaKTV = reader.GetString("KhoaKTV")?.Trim() ?? string.Empty,
                    TenKTV = reader.GetString("TenKTV")?.Trim() ?? string.Empty,
                    GhiChu = reader.GetString("GhiChu")?.Trim() ?? string.Empty
                };
                
                // Add calculated fields
                item.WorkTypeText = GetWorkTypeText(item.MaCongViec);
                item.StatusText = "Pending"; // Default status
                item.Priority = "Normal"; // Default priority
                
                results.Add(item);
            }
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetAllAsync");
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }
    }

    public async Task<BaoGiaYeuCauSuaChuaChiTietDto?> GetByIdAsync(string khoa)
    {
        try
        {
            var sql = "SELECT * FROM SC_BaoGiaYeuCauSuaChuaChiTiet WHERE Khoa = @Khoa";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            command.Parameters.AddWithValue("@Khoa", khoa);
            
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new BaoGiaYeuCauSuaChuaChiTietDto
                {
                    Khoa = reader.GetString("Khoa")?.Trim() ?? string.Empty,
                    KhoaBaoGia = reader.GetString("KhoaBaoGia")?.Trim() ?? string.Empty,
                    MaCongViec = reader.GetString("MaCongViec")?.Trim() ?? string.Empty,
                    NoiDungCongViec = reader.GetString("NoiDungCongViec")?.Trim() ?? string.Empty,
                    KhoaKTV = reader.GetString("KhoaKTV")?.Trim() ?? string.Empty,
                    GhiChu = reader.GetString("GhiChu")?.Trim() ?? string.Empty
                };
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetByIdAsync");
            return null;
        }
    }

    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByQuotationAsync(string khoaBaoGia)
    {
        try
        {
            var dataTable = await GetDetailsYeuCauSuaChuaAsync(khoaBaoGia);
            var results = new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
            
            foreach (DataRow row in dataTable.Rows)
            {
                var item = new BaoGiaYeuCauSuaChuaChiTietListDto
                {
                    Khoa = row["Khoa"]?.ToString()?.Trim() ?? string.Empty,
                    KhoaBaoGia = khoaBaoGia,
                    MaCongViec = row["MaCongViec"]?.ToString()?.Trim() ?? string.Empty,
                    NoiDungCongViec = row["NoiDungCongViec"]?.ToString()?.Trim() ?? string.Empty,
                    KhoaKTV = row["KhoaKTV"]?.ToString()?.Trim() ?? string.Empty,
                    TenKTV = row["TenKTV"]?.ToString()?.Trim() ?? string.Empty,
                    GhiChu = row["GhiChu"]?.ToString()?.Trim() ?? string.Empty
                };
                
                // Add calculated fields
                item.WorkTypeText = GetWorkTypeText(item.MaCongViec);
                item.StatusText = "Pending"; // Default status
                item.Priority = "Normal"; // Default priority
                
                results.Add(item);
            }
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetByQuotationAsync");
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }
    }

    public async Task<BaoGiaYeuCauSuaChuaChiTietDto> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto)
    {
        try
        {
            // Set instance data for legacy Save method
            mKhoa = Guid.NewGuid().ToString();
            mKhoaBaoGia = createDto.KhoaBaoGia;
            mMaCongViec = createDto.MaCongViec;
            mNoiDungCongViec = createDto.NoiDungCongViec;
            mKhoaKTV = createDto.KhoaKTV;
            mGhiChu = createDto.GhiChu;
            
            var success = await SaveAsync();
            if (success)
            {
                return new BaoGiaYeuCauSuaChuaChiTietDto
                {
                    Khoa = mKhoa,
                    KhoaBaoGia = mKhoaBaoGia,
                    MaCongViec = mMaCongViec,
                    NoiDungCongViec = mNoiDungCongViec,
                    KhoaKTV = mKhoaKTV,
                    GhiChu = mGhiChu
                };
            }
            
            throw new InvalidOperationException("Failed to create repair request detail");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateAsync");
            throw;
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<BaoGiaYeuCauSuaChuaChiTietDto?> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto) => null;
    public async Task<bool> DeleteAsync(string khoa) => false;
    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto) => new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietLookupDto>> GetLookupAsync() => new List<BaoGiaYeuCauSuaChuaChiTietLookupDto>();
    public async Task<BaoGiaYeuCauSuaChuaChiTietValidationDto> ValidateAsync(string khoa) => new BaoGiaYeuCauSuaChuaChiTietValidationDto();
    public async Task<IEnumerable<AutomotiveRepairWorkDetailDto>> GetAutomotiveRepairWorkDetailsAsync(string khoaBaoGia) => new List<AutomotiveRepairWorkDetailDto>();
    public async Task<RepairWorkSummaryDto> GetRepairWorkSummaryAsync(string khoaBaoGia) => new RepairWorkSummaryDto();
    public async Task<IEnumerable<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync() => new List<TechnicianWorkloadDto>();
    public async Task<WorkItemProgressDto?> GetWorkItemProgressAsync(string khoa) => null;
    public async Task<bool> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto) => false;

    #endregion

    #region Private Helper Methods

    private string GetWorkTypeText(string maCongViec)
    {
        // Map work codes to user-friendly text
        return maCongViec.ToUpper() switch
        {
            var code when code.StartsWith("ENG") => "Engine Work",
            var code when code.StartsWith("TRA") => "Transmission",
            var code when code.StartsWith("BRA") => "Brake System",
            var code when code.StartsWith("SUS") => "Suspension",
            var code when code.StartsWith("ELE") => "Electrical",
            var code when code.StartsWith("AC") => "Air Conditioning",
            var code when code.StartsWith("BOD") => "Body Work",
            var code when code.StartsWith("INT") => "Interior",
            var code when code.StartsWith("EXT") => "Exterior",
            _ => "General Repair"
        };
    }

    #endregion
}
