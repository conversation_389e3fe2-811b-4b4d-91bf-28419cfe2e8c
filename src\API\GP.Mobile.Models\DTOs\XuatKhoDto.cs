using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for XuatKho (Inventory Issuing/Outbound) entity
/// Maps exactly to ST_XuatKho table in legacy database
/// Implements ALL 74+ properties from clsXuatKho.cs (2,500+ lines)
/// This is a core sales and inventory management class - handle with extreme care
/// Manual implementation based on exact legacy class analysis
/// </summary>
public class XuatKhoDto
{
    #region Core Identification Properties
    
    /// <summary>Primary key - Legacy field: mKhoa</summary>
    public string Khoa { get; set; } = string.Empty;
    
    /// <summary>Document number - Legacy field: mSoChungTu</summary>
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;
    
    /// <summary>Document date - Legacy field: mNgayChungTu</summary>
    public string NgayChungTu { get; set; } = string.Empty;
    
    /// <summary>Export type - Legacy field: mLoaiXuat</summary>
    public int LoaiXuat { get; set; } = 0;
    
    #endregion

    #region Export Type and Category
    
    /// <summary>Export type key - Legacy field: mKhoaLoaiXuat</summary>
    public string KhoaLoaiXuat { get; set; } = string.Empty;
    
    /// <summary>Category key - Legacy field: mKhoaKhoanMuc</summary>
    public string KhoaKhoanMuc { get; set; } = string.Empty;
    
    /// <summary>Document type - Legacy field: mLoaiChungTu</summary>
    public string LoaiChungTu { get; set; } = string.Empty;
    
    #endregion

    #region Order Information
    
    /// <summary>Order key - Legacy field: mKhoaDonHang</summary>
    public string KhoaDonHang { get; set; } = string.Empty;
    
    /// <summary>Order number - Legacy field: mSoDonHang</summary>
    public string SoDonHang { get; set; } = string.Empty;
    
    /// <summary>Order date - Legacy field: mNgayDonHang</summary>
    public string NgayDonHang { get; set; } = string.Empty;
    
    #endregion

    #region Customer Information
    
    /// <summary>Customer key - Legacy field: mKhoaDoiTuong</summary>
    public string KhoaDoiTuong { get; set; } = string.Empty;
    
    /// <summary>Delivery person - Legacy field: mNguoiGiao</summary>
    public string NguoiGiao { get; set; } = string.Empty;
    
    /// <summary>Tax code - Legacy field: mMaSoThue</summary>
    public string MaSoThue { get; set; } = string.Empty;
    
    /// <summary>Tax entity name - Legacy field: mTenDoiTuongThue</summary>
    public string TenDoiTuongThue { get; set; } = string.Empty;
    
    /// <summary>Tax address - Legacy field: mDiaChiThue</summary>
    public string DiaChiThue { get; set; } = string.Empty;
    
    #endregion

    #region Sales Team Information
    
    /// <summary>Sales staff key - Legacy field: mKhoaNhanVienBanHang</summary>
    public string KhoaNhanVienBanHang { get; set; } = string.Empty;
    
    /// <summary>Price type key - Legacy field: mKhoaLoaiGia</summary>
    public string KhoaLoaiGia { get; set; } = string.Empty;
    
    /// <summary>Sales type - Legacy field: mLoaiBanHang</summary>
    public string LoaiBanHang { get; set; } = string.Empty;
    
    #endregion

    #region Currency and Exchange
    
    /// <summary>Currency type - Legacy field: mLoaiTien</summary>
    public string LoaiTien { get; set; } = string.Empty;
    
    /// <summary>Exchange rate - Legacy field: mTyGia</summary>
    public double TyGia { get; set; } = 0.0;
    
    #endregion

    #region Invoice Information
    
    /// <summary>Invoice type key - Legacy field: mKhoaLoaiHoaDon</summary>
    public string KhoaLoaiHoaDon { get; set; } = string.Empty;
    
    /// <summary>Tax rate - Legacy field: mThueSuat</summary>
    public int ThueSuat { get; set; } = 0;
    
    /// <summary>Invoice number - Legacy field: mSoHoaDon</summary>
    public string SoHoaDon { get; set; } = string.Empty;
    
    /// <summary>Invoice date - Legacy field: mNgayHoaDon</summary>
    public string NgayHoaDon { get; set; } = string.Empty;
    
    /// <summary>Invoice series - Legacy field: mSoSeri</summary>
    public string SoSeri { get; set; } = string.Empty;
    
    /// <summary>Invoice symbol - Legacy field: mKiHieuHoaDon</summary>
    public string KiHieuHoaDon { get; set; } = string.Empty;
    
    #endregion

    #region Financial Information - Foreign Currency
    
    /// <summary>Goods amount in foreign currency - Legacy field: mTienHangNT</summary>
    public double TienHangNT { get; set; } = 0.0;
    
    /// <summary>Discount amount in foreign currency - Legacy field: mTienChietKhauNT</summary>
    public double TienChietKhauNT { get; set; } = 0.0;
    
    /// <summary>VAT amount in foreign currency - Legacy field: mTienThueVATNT</summary>
    public double TienThueVATNT { get; set; } = 0.0;
    
    /// <summary>Commission amount in foreign currency - Legacy field: mTienHoaHongNT</summary>
    public double TienHoaHongNT { get; set; } = 0.0;
    
    /// <summary>Paid amount in foreign currency - Legacy field: mDaThanhToanNT</summary>
    public double DaThanhToanNT { get; set; } = 0.0;
    
    #endregion

    #region Financial Information - Local Currency
    
    /// <summary>Goods amount in local currency - Legacy field: mTienHang</summary>
    public double TienHang { get; set; } = 0.0;
    
    /// <summary>Discount amount in local currency - Legacy field: mTienChietKhau</summary>
    public double TienChietKhau { get; set; } = 0.0;
    
    /// <summary>VAT amount in local currency - Legacy field: mTienThueVAT</summary>
    public double TienThueVAT { get; set; } = 0.0;
    
    /// <summary>Commission amount in local currency - Legacy field: mTienHoaHong</summary>
    public double TienHoaHong { get; set; } = 0.0;
    
    /// <summary>Paid amount in local currency - Legacy field: mDaThanhToan</summary>
    public double DaThanhToan { get; set; } = 0.0;
    
    #endregion

    #region Discount Configuration
    
    /// <summary>Discount calculation method - Legacy field: mCachTinhChietKhau</summary>
    public string CachTinhChietKhau { get; set; } = string.Empty;
    
    /// <summary>Discount rate - Legacy field: mTyLeChietKhau</summary>
    public int TyLeChietKhau { get; set; } = 0;
    
    #endregion

    #region Account Information
    
    /// <summary>Payment account key - Legacy field: mKhoaTKThanhToan</summary>
    public string KhoaTKThanhToan { get; set; } = string.Empty;
    
    /// <summary>Tax account key - Legacy field: mKhoaTKThue</summary>
    public string KhoaTKThue { get; set; } = string.Empty;
    
    /// <summary>Revenue account key - Legacy field: mKhoaTKDoanhThu</summary>
    public string KhoaTKDoanhThu { get; set; } = string.Empty;
    
    /// <summary>Discount account key - Legacy field: mKhoaTKChietKhau</summary>
    public string KhoaTKChietKhau { get; set; } = string.Empty;
    
    #endregion

    #region Payment Information
    
    /// <summary>Payment deadline - Legacy field: mHanThanhToan</summary>
    public int HanThanhToan { get; set; } = 0;
    
    /// <summary>Payment date - Legacy field: mNgayThanhToan</summary>
    public string NgayThanhToan { get; set; } = string.Empty;
    
    /// <summary>Payment method - Legacy field: mHinhThucThanhToan</summary>
    public string HinhThucThanhToan { get; set; } = string.Empty;
    
    /// <summary>Account number - Legacy field: mSoTaiKhoan</summary>
    public string SoTaiKhoan { get; set; } = string.Empty;
    
    /// <summary>Payment type - Legacy field: mHTTT</summary>
    public string HTTT { get; set; } = string.Empty;
    
    /// <summary>Bank - Legacy field: mNganHang</summary>
    public string NganHang { get; set; } = string.Empty;
    
    #endregion

    #region Delivery Information
    
    /// <summary>Delivery staff key - Legacy field: mKhoaNhanVienGiaoHang</summary>
    public string KhoaNhanVienGiaoHang { get; set; } = string.Empty;
    
    /// <summary>Delivery status - Legacy field: mTinhTrangGiaoHang</summary>
    public int TinhTrangGiaoHang { get; set; } = 0;
    
    /// <summary>Delivery address - Legacy field: mDiaChiGiaoHang</summary>
    public string DiaChiGiaoHang { get; set; } = string.Empty;
    
    /// <summary>Delivery notes - Legacy field: mDienGiaiGiaoHang</summary>
    public string DienGiaiGiaoHang { get; set; } = string.Empty;
    
    #endregion

    #region POS Integration
    
    /// <summary>Is POS transaction - Legacy field: mIsPXPOS</summary>
    public int IsPXPOS { get; set; } = 0;
    
    /// <summary>Customer payment amount - Legacy field: mKhachDua</summary>
    public double KhachDua { get; set; } = 0.0;
    
    /// <summary>Change amount - Legacy field: mThoiLai</summary>
    public double ThoiLai { get; set; } = 0.0;
    
    /// <summary>POS user - Legacy field: mUserPOS</summary>
    public string UserPOS { get; set; } = string.Empty;
    
    #endregion

    #region Organization Information
    
    /// <summary>Unit key - Legacy field: mKhoaDonVi</summary>
    public string KhoaDonVi { get; set; } = string.Empty;
    
    /// <summary>Department key - Legacy field: mKhoaBoPhan</summary>
    public string KhoaBoPhan { get; set; } = string.Empty;
    
    /// <summary>Product key - Legacy field: mKhoaSanPham</summary>
    public string KhoaSanPham { get; set; } = string.Empty;
    
    #endregion

    #region Contract Information
    
    /// <summary>Contract key - Legacy field: mKhoaHopDong</summary>
    public string KhoaHopDong { get; set; } = string.Empty;
    
    /// <summary>Contract number - Legacy field: mSoHopDong</summary>
    public string SoHopDong { get; set; } = string.Empty;
    
    #endregion

    #region Reference Information
    
    /// <summary>Quotation key - Legacy field: mKhoaBaoGia</summary>
    public string KhoaBaoGia { get; set; } = string.Empty;
    
    /// <summary>Quotation number - Legacy field: mSoBaoGia</summary>
    public string SoBaoGia { get; set; } = string.Empty;
    
    /// <summary>Opportunity key - Legacy field: mKhoaCoHoi</summary>
    public string KhoaCoHoi { get; set; } = string.Empty;
    
    /// <summary>Vehicle key - Legacy field: mKhoaXe</summary>
    public string KhoaXe { get; set; } = string.Empty;
    
    /// <summary>Vehicle number - Legacy field: mSoXe</summary>
    public string SoXe { get; set; } = string.Empty;
    
    #endregion

    #region Description and Notes
    
    /// <summary>Description - Legacy field: mDienGiai</summary>
    public string DienGiai { get; set; } = string.Empty;
    
    #endregion

    #region System Information
    
    /// <summary>Creator staff key - Legacy field: mKhoaNhanVienTao</summary>
    public string KhoaNhanVienTao { get; set; } = string.Empty;
    
    /// <summary>Creation date - Legacy field: mNgayTao</summary>
    public string NgayTao { get; set; } = string.Empty;
    
    /// <summary>Updater staff key - Legacy field: mKhoaNhanVienSua</summary>
    public string KhoaNhanVienSua { get; set; } = string.Empty;
    
    /// <summary>Update date - Legacy field: mNgaySua</summary>
    public string NgaySua { get; set; } = string.Empty;
    
    /// <summary>Posted status - Legacy field: mGhiSo</summary>
    public int GhiSo { get; set; } = 0;
    
    /// <summary>Send status - Legacy field: mSend</summary>
    public int Send { get; set; } = 0;
    
    /// <summary>Include in business report - Legacy field: mIsTinhVaoPhieuQLKD</summary>
    public int IsTinhVaoPhieuQLKD { get; set; } = 0;
    
    #endregion
}

/// <summary>
/// List DTO for XuatKho entity - Used for list operations and search results
/// Contains key properties for display in inventory issuing lists
/// </summary>
public class XuatKhoListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public string NgayChungTu { get; set; } = string.Empty;
    public string SoHoaDon { get; set; } = string.Empty;
    public string SoSeri { get; set; } = string.Empty;
    public string SoXe { get; set; } = string.Empty;
    public string KhachHang { get; set; } = string.Empty;
    public double TienHang { get; set; } = 0.0;
    public double TienChietKhau { get; set; } = 0.0;
    public double TienHangCK { get; set; } = 0.0;
    public double TienThue { get; set; } = 0.0;
    public double TongThanhToan { get; set; } = 0.0;
    public string DienGiai { get; set; } = string.Empty;
    public int GhiSo { get; set; } = 0;
    public int LoaiXuat { get; set; } = 0;
}

/// <summary>
/// Create DTO for XuatKho entity - Used for creating new inventory issuing documents
/// Excludes auto-generated fields like Khoa
/// </summary>
public class CreateXuatKhoDto
{
    [Required]
    [StringLength(50)]
    public string SoChungTu { get; set; } = string.Empty;

    public string NgayChungTu { get; set; } = string.Empty;

    public int LoaiXuat { get; set; } = 0;

    public string KhoaLoaiXuat { get; set; } = string.Empty;

    [Required]
    public string KhoaDoiTuong { get; set; } = string.Empty;

    public string NguoiGiao { get; set; } = string.Empty;

    public string SoHoaDon { get; set; } = string.Empty;

    public string NgayHoaDon { get; set; } = string.Empty;

    public string SoSeri { get; set; } = string.Empty;

    public string LoaiBanHang { get; set; } = string.Empty;

    public string DienGiai { get; set; } = string.Empty;

    public string LoaiTien { get; set; } = "VND";

    public double TyGia { get; set; } = 1.0;

    public int ThueSuat { get; set; } = 10;

    public string KhoaNhanVienBanHang { get; set; } = string.Empty;

    public string KhoaBaoGia { get; set; } = string.Empty;

    public string SoBaoGia { get; set; } = string.Empty;
}

/// <summary>
/// Update status DTO for XuatKho entity - Used for posting and status changes
/// </summary>
public class UpdateXuatKhoStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int GhiSo { get; set; } = 0;

    public int Send { get; set; } = 0;

    public int TinhTrangGiaoHang { get; set; } = 0;

    public string NgayThanhToan { get; set; } = string.Empty;

    public double DaThanhToan { get; set; } = 0.0;

    public double DaThanhToanNT { get; set; } = 0.0;
}

/// <summary>
/// POS transaction DTO for XuatKho entity - Used for POS operations
/// </summary>
public class XuatKhoPOSDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int IsPXPOS { get; set; } = 1;

    public double KhachDua { get; set; } = 0.0;

    public double ThoiLai { get; set; } = 0.0;

    public string UserPOS { get; set; } = string.Empty;

    public string HinhThucThanhToan { get; set; } = string.Empty;

    public string SoTaiKhoan { get; set; } = string.Empty;

    public string HTTT { get; set; } = string.Empty;

    public string NganHang { get; set; } = string.Empty;
}
