using System;
using System.Linq;
using System.Text;

namespace GP.Mobile.CodeGenerator
{
    /// <summary>
    /// Generates DTO classes from legacy class analysis
    /// </summary>
    public class DtoGenerator
    {
        public string GenerateDto(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            
            // File header
            sb.AppendLine("using System.ComponentModel.DataAnnotations;");
            sb.AppendLine();
            sb.AppendLine("namespace GP.Mobile.Models.DTOs;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Complete DTO for {analysis.ClassName.Replace("cls", "")} entity");
            sb.AppendLine($"/// Maps exactly to {analysis.TableName} table in legacy database");
            sb.AppendLine($"/// Implements ALL properties from {analysis.ClassName}.cs ({analysis.Properties.Count} properties)");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var dtoClassName = analysis.ClassName.Replace("cls", "") + "Dto";
            sb.AppendLine($"public class {dtoClassName}");
            sb.AppendLine("{");
            
            // Generate properties
            foreach (var prop in analysis.Properties)
            {
                GenerateProperty(sb, prop);
            }
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }

        private void GenerateProperty(StringBuilder sb, LegacyClassAnalyzer.PropertyInfo prop)
        {
            // Add validation attributes
            if (prop.IsRequired)
            {
                sb.AppendLine("    [Required]");
            }
            
            if (prop.MaxLength > 0)
            {
                sb.AppendLine($"    [StringLength({prop.MaxLength})]");
            }
            
            // Add property with default value
            var defaultValue = GetDefaultValue(prop.Type);
            sb.AppendLine($"    public {prop.Type} {prop.Name} {{ get; set; }} = {defaultValue};");
            sb.AppendLine();
        }

        private string GetDefaultValue(string type)
        {
            return type switch
            {
                "string" => "string.Empty",
                "int" => "0",
                "double" => "0.0",
                "bool" => "false",
                "DateTime" => "DateTime.MinValue",
                "DateTime?" => "null",
                _ => "default"
            };
        }

        public string GenerateListDto(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            
            // File header
            sb.AppendLine("namespace GP.Mobile.Models.DTOs;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// List DTO for {analysis.ClassName.Replace("cls", "")} entity");
            sb.AppendLine("/// Used for list operations and search results");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var listDtoClassName = analysis.ClassName.Replace("cls", "") + "ListDto";
            sb.AppendLine($"public class {listDtoClassName}");
            sb.AppendLine("{");
            
            // Generate key properties for list view
            var keyProperties = analysis.Properties.Take(10).ToList(); // First 10 properties typically include key fields
            
            foreach (var prop in keyProperties)
            {
                sb.AppendLine($"    public {prop.Type} {prop.Name} {{ get; set; }} = {GetDefaultValue(prop.Type)};");
            }
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }

        public string GenerateCreateDto(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            
            // File header
            sb.AppendLine("using System.ComponentModel.DataAnnotations;");
            sb.AppendLine();
            sb.AppendLine("namespace GP.Mobile.Models.DTOs;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Create DTO for {analysis.ClassName.Replace("cls", "")} entity");
            sb.AppendLine("/// Used for creating new records");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var createDtoClassName = "Create" + analysis.ClassName.Replace("cls", "") + "Dto";
            sb.AppendLine($"public class {createDtoClassName}");
            sb.AppendLine("{");
            
            // Generate properties excluding Khoa (primary key)
            var createProperties = analysis.Properties.Where(p => p.Name != "Khoa").ToList();
            
            foreach (var prop in createProperties)
            {
                GenerateProperty(sb, prop);
            }
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }

        public string GenerateUpdateStatusDto(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            
            // File header
            sb.AppendLine("using System.ComponentModel.DataAnnotations;");
            sb.AppendLine();
            sb.AppendLine("namespace GP.Mobile.Models.DTOs;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Update status DTO for {analysis.ClassName.Replace("cls", "")} entity");
            sb.AppendLine("/// Used for status updates");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var updateStatusDtoClassName = "Update" + analysis.ClassName.Replace("cls", "") + "StatusDto";
            sb.AppendLine($"public class {updateStatusDtoClassName}");
            sb.AppendLine("{");
            
            // Generate key properties for status updates
            sb.AppendLine("    [Required]");
            sb.AppendLine("    public string Khoa { get; set; } = string.Empty;");
            sb.AppendLine();
            
            // Find status-related properties
            var statusProperties = analysis.Properties.Where(p => 
                p.Name.Contains("TinhTrang") || 
                p.Name.Contains("Status") || 
                p.Name.Contains("Active")).ToList();
            
            foreach (var prop in statusProperties)
            {
                sb.AppendLine($"    public {prop.Type} {prop.Name} {{ get; set; }} = {GetDefaultValue(prop.Type)};");
            }
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }
    }
}
