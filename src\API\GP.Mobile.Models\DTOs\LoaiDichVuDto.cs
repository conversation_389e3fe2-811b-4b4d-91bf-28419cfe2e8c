using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for LoaiDichVu (Service Type) entity
/// Maps exactly to DM_LoaiDichVu table in legacy database
/// Implements ALL properties from clsDMLoaiDichVu.cs (468 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
public class LoaiDichVuDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Service type code (e.g., MAINTENANCE, REPAIR, INSPECTION)
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of service type
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of service type
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes about the service type
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Effective date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Synchronization status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for LoaiDichVu list display
/// Optimized for automotive service type lists and dropdowns
/// </summary>
public class LoaiDichVuListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for creating new LoaiDichVu
/// Contains only required fields for creation
/// </summary>
public class CreateLoaiDichVuDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for updating LoaiDichVu status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateLoaiDichVuStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public int Active { get; set; }

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for LoaiDichVu search operations
/// Used for advanced search and filtering
/// </summary>
public class LoaiDichVuSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public int? Active { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
}

/// <summary>
/// DTO for LoaiDichVu dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class LoaiDichVuLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
}

/// <summary>
/// DTO for LoaiDichVu validation operations
/// Used for duplicate checking and validation
/// </summary>
public class LoaiDichVuValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}

/// <summary>
/// DTO for LoaiDichVu search by code operations
/// Used for quick service type lookup
/// </summary>
public class LoaiDichVuSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
}

/// <summary>
/// DTO for automotive service categories
/// Specialized for automotive service classification
/// </summary>
public class ServiceCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public bool IsMaintenanceService { get; set; } = false;
    public bool IsRepairService { get; set; } = false;
    public bool IsInspectionService { get; set; } = false;
    public bool IsWarrantyService { get; set; } = false;
}

/// <summary>
/// DTO for service type statistics
/// Used for reporting and analytics
/// </summary>
public class LoaiDichVuStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public int TotalServices { get; set; } = 0;
    public int ActiveServices { get; set; } = 0;
    public decimal TotalRevenue { get; set; } = 0;
    public DateTime LastUsed { get; set; }
}
