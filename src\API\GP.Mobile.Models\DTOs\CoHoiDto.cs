using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for CoHoi (Opportunity) entity
/// Maps exactly to BH_CoHoi table in legacy database
/// Implements ALL properties from clsCoHoi.cs (58 properties)
/// </summary>
public class CoHoiDto
{
    // Core identification
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public string NgayChungTu { get; set; } = string.Empty;

    // Payment and customer info
    public int HinhThucThanhToan { get; set; } = 0;
    public string KhoaNhanVienQuanLy { get; set; } = string.Empty;
    public string MoTaCoHoi { get; set; } = string.Empty;
    public int TinhTrangCoHoi { get; set; } = 0;
    public string KhoaKhachHang { get; set; } = string.Empty;

    // Financial amounts
    public double TongGiaCongBo { get; set; } = 0.0;
    public double TongKhuyenMaiTienMat { get; set; } = 0.0;
    public double TongThanhTien { get; set; } = 0.0;
    public double TongTienPhi { get; set; } = 0.0;
    public double TongThanhToanPKMT { get; set; } = 0.0;
    public double TongThanhToanPKKM { get; set; } = 0.0;
    public double TongThanhToan { get; set; } = 0.0;

    // Banking and payment details
    public string KhoaNganHang { get; set; } = string.Empty;
    public double DatCoc { get; set; } = 0.0;
    public double TienMat { get; set; } = 0.0;
    public double TienVay { get; set; } = 0.0;
    public double TyLeVay { get; set; } = 0.0;

    // Contract information
    public string SoHopDong { get; set; } = string.Empty;
    public string NgayHopDong { get; set; } = string.Empty;

    // Additional financial details
    public double TongTienHangPKMT { get; set; } = 0.0;
    public double TongTienCKPKMT { get; set; } = 0.0;
    public double TongTienThuePKMT { get; set; } = 0.0;
    public string KhoaGhiSoHopDong { get; set; } = string.Empty;
    public double DaThanhToan { get; set; } = 0.0;

    // Delivery information
    public string ThoiGianGiaoXeDuKien { get; set; } = string.Empty;
    public string DiaDiemGiaoXe { get; set; } = string.Empty;
    public string ThoiGianGiaoHoSoDuKien { get; set; } = string.Empty;

    // Settlement information
    public bool IsTatToan { get; set; } = false;
    public string NgayTatToan { get; set; } = string.Empty;
    public string SoBienBanBanGiao { get; set; } = string.Empty;
    public string NgayBanGiao { get; set; } = string.Empty;
    public double TongChiPhiDauVao { get; set; } = 0.0;

    // Payment schedule
    public int SoNgayThanhToan { get; set; } = 0;

    // Container information
    public bool IsKemThung { get; set; } = false;
    public string ThongTinThung { get; set; } = string.Empty;
    public double TongTienThung { get; set; } = 0.0;

    // Delivery schedule
    public int SoNgayGiaoXe { get; set; } = 0;
    public string TuNgayGiaoXe { get; set; } = string.Empty;
    public string DenNgayGiaoXe { get; set; } = string.Empty;
    public bool IsTienXeBaoGomThung { get; set; } = false;

    // Agreement information
    public string SoBBTT { get; set; } = string.Empty;
    public string NgayLapBBTT { get; set; } = string.Empty;

    // Multiple payment phases
    public double ThanhToanLan2 { get; set; } = 0.0;
    public double ThanhToanLan3 { get; set; } = 0.0;
    public bool IsDatCoc { get; set; } = false;
    public bool IsThanhToanLan2 { get; set; } = false;
    public bool IsThanhToanLan3 { get; set; } = false;

    // Organization
    public string KhoaChiNhanh { get; set; } = string.Empty;
    public int LoaiHopDong { get; set; } = 0;

    // Commission information (HH = Hoa Hong)
    public string HH_NguoiGioiThieu { get; set; } = string.Empty;
    public string HH_CMND { get; set; } = string.Empty;
    public string HH_DienThoai { get; set; } = string.Empty;
    public string HH_QuanHe { get; set; } = string.Empty;
    public double HH_SoTien { get; set; } = 0.0;
}

/// <summary>
/// Simplified DTO for list views
/// </summary>
public class CoHoiListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string SoChungTu { get; set; } = string.Empty;
    public DateTime NgayTao { get; set; }
    public string TenKhachHang { get; set; } = string.Empty;
    public string TenCoVan { get; set; } = string.Empty;
    public decimal TongTien { get; set; }
    public int TinhTrang { get; set; }
    public string TinhTrangText { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new CoHoi
/// </summary>
public class CreateCoHoiDto
{
    [Required]
    public string KhoaDoiTuong { get; set; } = string.Empty;
    
    [Required]
    public string KhoaCoVan { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;
    
    public decimal TongTien { get; set; }
}

/// <summary>
/// DTO for updating CoHoi status
/// </summary>
public class UpdateCoHoiStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;
    
    [Range(0, 5)]
    public int TinhTrang { get; set; }
    
    [StringLength(500)]
    public string? GhiChu { get; set; }
}
