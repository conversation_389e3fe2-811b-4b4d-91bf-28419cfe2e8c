using GP.Mobile.Core.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Service for BaoGiaYeuCauSuaChuaChiTiet (Repair Request Details) operations
/// Implements ALL methods from clsBaoGiaYeuCauSuaChuaChiTiet.cs (196 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for detailed repair request specifications in quotations
/// </summary>
public interface IBaoGiaYeuCauSuaChuaChiTietService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync();
    Task<DataTable> GetDetailsYeuCauSuaChuaAsync(string khoaBaoGia);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetAllAsync();
    Task<BaoGiaYeuCauSuaChuaChiTietDto?> GetByIdAsync(string khoa);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByQuotationAsync(string khoaBaoGia);
    Task<string> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto);
    Task<bool> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto);
    Task<bool> DeleteAsync(string khoa);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto);
    Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietLookupDto>> GetLookupAsync();
    Task<BaoGiaYeuCauSuaChuaChiTietValidationDto> ValidateAsync(string khoa);
    Task<IEnumerable<AutomotiveRepairWorkDetailDto>> GetAutomotiveRepairWorkDetailsAsync(string khoaBaoGia);
    Task<RepairWorkSummaryDto> GetRepairWorkSummaryAsync(string khoaBaoGia);
    Task<IEnumerable<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync();
    Task<WorkItemProgressDto?> GetWorkItemProgressAsync(string khoa);
    Task<bool> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto);
    
    #endregion
}

/// <summary>
/// Implementation of BaoGiaYeuCauSuaChuaChiTiet service
/// Follows exact legacy business logic from clsBaoGiaYeuCauSuaChuaChiTiet.cs
/// </summary>
public class BaoGiaYeuCauSuaChuaChiTietService : IBaoGiaYeuCauSuaChuaChiTietService
{
    private readonly IBaoGiaYeuCauSuaChuaChiTietRepository _repository;
    private readonly ILogger<BaoGiaYeuCauSuaChuaChiTietService> _logger;

    public BaoGiaYeuCauSuaChuaChiTietService(IBaoGiaYeuCauSuaChuaChiTietRepository repository, ILogger<BaoGiaYeuCauSuaChuaChiTietService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading BaoGiaYeuCauSuaChuaChiTiet");
            throw;
        }
    }

    public async Task<bool> SaveAsync()
    {
        try
        {
            return await _repository.SaveAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving BaoGiaYeuCauSuaChuaChiTiet");
            throw;
        }
    }

    public async Task<DataTable> GetDetailsYeuCauSuaChuaAsync(string khoaBaoGia)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaBaoGia))
            {
                throw new ArgumentException("KhoaBaoGia không được để trống");
            }

            return await _repository.GetDetailsYeuCauSuaChuaAsync(khoaBaoGia);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request details");
            return new DataTable();
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all repair request details");
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }
    }

    public async Task<BaoGiaYeuCauSuaChuaChiTietDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request detail by ID");
            return null;
        }
    }

    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> GetByQuotationAsync(string khoaBaoGia)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaBaoGia))
            {
                throw new ArgumentException("KhoaBaoGia không được để trống");
            }

            return await _repository.GetByQuotationAsync(khoaBaoGia);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair request details by quotation");
            return new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
        }
    }

    public async Task<string> CreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto createDto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(createDto);

            var result = await _repository.CreateAsync(createDto);
            return result.Khoa;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating repair request detail");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto updateDto)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForUpdateAsync(khoa, updateDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            var result = await _repository.UpdateAsync(khoa, updateDto);
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair request detail");
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await CanDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa chi tiết yêu cầu sửa chữa này vì đang được sử dụng");
            }

            return await _repository.DeleteAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair request detail");
            throw;
        }
    }

    public async Task<RepairWorkSummaryDto> GetRepairWorkSummaryAsync(string khoaBaoGia)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaBaoGia))
            {
                throw new ArgumentException("KhoaBaoGia không được để trống");
            }

            return await _repository.GetRepairWorkSummaryAsync(khoaBaoGia);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair work summary");
            return new RepairWorkSummaryDto { KhoaBaoGia = khoaBaoGia };
        }
    }

    public async Task<IEnumerable<AutomotiveRepairWorkDetailDto>> GetAutomotiveRepairWorkDetailsAsync(string khoaBaoGia)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoaBaoGia))
            {
                throw new ArgumentException("KhoaBaoGia không được để trống");
            }

            return await _repository.GetAutomotiveRepairWorkDetailsAsync(khoaBaoGia);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive repair work details");
            return new List<AutomotiveRepairWorkDetailDto>();
        }
    }

    public async Task<WorkItemProgressDto?> GetWorkItemProgressAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetWorkItemProgressAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting work item progress");
            return null;
        }
    }

    public async Task<bool> UpdateWorkItemProgressAsync(string khoa, WorkItemProgressDto progressDto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.UpdateWorkItemProgressAsync(khoa, progressDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating work item progress");
            throw;
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietListDto>> SearchAsync(BaoGiaYeuCauSuaChuaChiTietSearchDto searchDto) => new List<BaoGiaYeuCauSuaChuaChiTietListDto>();
    public async Task<IEnumerable<BaoGiaYeuCauSuaChuaChiTietLookupDto>> GetLookupAsync() => new List<BaoGiaYeuCauSuaChuaChiTietLookupDto>();
    public async Task<BaoGiaYeuCauSuaChuaChiTietValidationDto> ValidateAsync(string khoa) => new BaoGiaYeuCauSuaChuaChiTietValidationDto();
    public async Task<IEnumerable<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync() => new List<TechnicianWorkloadDto>();

    #endregion

    #region Private Helper Methods

    private async Task<ValidationResult> ValidateForCreateAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(dto.KhoaBaoGia))
            result.Errors.Add("KhoaBaoGia không được để trống");

        if (string.IsNullOrWhiteSpace(dto.MaCongViec))
            result.Errors.Add("MaCongViec không được để trống");

        if (string.IsNullOrWhiteSpace(dto.NoiDungCongViec))
            result.Errors.Add("NoiDungCongViec không được để trống");

        if (dto.MaCongViec.Length > 50)
            result.Errors.Add("MaCongViec không được vượt quá 50 ký tự");

        if (dto.NoiDungCongViec.Length > 1000)
            result.Errors.Add("NoiDungCongViec không được vượt quá 1000 ký tự");

        result.IsValid = !result.Errors.Any();
        await Task.CompletedTask;
        return result;
    }

    private async Task<ValidationResult> ValidateForUpdateAsync(string khoa, UpdateBaoGiaYeuCauSuaChuaChiTietDto dto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(khoa))
            result.Errors.Add("Khoa không được để trống");

        // Check if record exists
        var existing = await _repository.GetByIdAsync(khoa);
        if (existing == null)
            result.Errors.Add("Không tìm thấy chi tiết yêu cầu sửa chữa");

        if (dto.MaCongViec.Length > 50)
            result.Errors.Add("MaCongViec không được vượt quá 50 ký tự");

        if (dto.NoiDungCongViec.Length > 1000)
            result.Errors.Add("NoiDungCongViec không được vượt quá 1000 ký tự");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(CreateBaoGiaYeuCauSuaChuaChiTietDto dto)
    {
        // Trim strings
        dto.KhoaBaoGia = dto.KhoaBaoGia?.Trim() ?? string.Empty;
        dto.MaCongViec = dto.MaCongViec?.Trim() ?? string.Empty;
        dto.NoiDungCongViec = dto.NoiDungCongViec?.Trim() ?? string.Empty;
        dto.KhoaKTV = dto.KhoaKTV?.Trim() ?? string.Empty;
        dto.GhiChu = dto.GhiChu?.Trim() ?? string.Empty;

        await Task.CompletedTask;
    }

    private async Task<bool> CanDeleteAsync(string khoa)
    {
        // Check if this repair request detail is being used
        // This would involve checking related tables
        // For now, return true - implement actual business logic as needed
        await Task.CompletedTask;
        return true;
    }

    #endregion
}

/// <summary>
/// Validation result helper class
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> Errors { get; set; } = new List<string>();
}
