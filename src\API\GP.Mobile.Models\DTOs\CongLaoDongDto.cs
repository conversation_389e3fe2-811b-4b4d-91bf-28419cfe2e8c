using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for CongLaoDong (Labor/Service) entity
/// Maps exactly to DM_CongLaoDong table in legacy database
/// Implements ALL properties from clsDMCongLaoDong.cs (649 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for labor/service item management in repair quotations
/// </summary>
public class CongLaoDongDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Service/Labor code - Unique business identifier
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name - Primary display name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name - Secondary display name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Effective date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Last updated by employee key
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1=Active, 0=Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Sync status for data replication
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;

    /// <summary>
    /// Service/Labor unit price
    /// Maps to: mDonGia property in legacy class
    /// </summary>
    public decimal DonGia { get; set; } = 0;

    /// <summary>
    /// Processing time (hours) for this service
    /// Maps to: mThoiGianXuLy property in legacy class
    /// </summary>
    public decimal ThoiGianXuLy { get; set; } = 0;

    /// <summary>
    /// Unit of measure key - Foreign key to DM_DonViTinh
    /// Maps to: mKhoaDonViTinh property in legacy class
    /// </summary>
    public string KhoaDonViTinh { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new CongLaoDong
/// Contains only required fields for creation
/// </summary>
public class CreateCongLaoDongDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public decimal DonGia { get; set; } = 0;
    public decimal ThoiGianXuLy { get; set; } = 0;
    public string KhoaDonViTinh { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating CongLaoDong
/// Contains all updatable fields
/// </summary>
public class UpdateCongLaoDongDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public decimal DonGia { get; set; } = 0;
    public decimal ThoiGianXuLy { get; set; } = 0;
    public string KhoaDonViTinh { get; set; } = string.Empty;
}

/// <summary>
/// DTO for CongLaoDong list display
/// Optimized for list views with calculated fields
/// </summary>
public class CongLaoDongListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public decimal DonGia { get; set; } = 0;
    public decimal ThoiGianXuLy { get; set; } = 0;
    public int Active { get; set; } = 1;
    public string DonViTinh { get; set; } = string.Empty; // Joined from DM_DonViTinh
}

/// <summary>
/// DTO for CongLaoDong search operations
/// Contains search criteria and filters
/// </summary>
public class CongLaoDongSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public decimal? DonGiaFrom { get; set; }
    public decimal? DonGiaTo { get; set; }
    public decimal? ThoiGianXuLyFrom { get; set; }
    public decimal? ThoiGianXuLyTo { get; set; }
    public string? KhoaDonViTinh { get; set; }
    public int? Active { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
    public string? TuNgay { get; set; }
}

/// <summary>
/// DTO for automotive service-specific view
/// Focused on automotive repair services
/// </summary>
public class AutomotiveServiceDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public decimal DonGia { get; set; } = 0;
    public decimal ThoiGianXuLy { get; set; } = 0;
    public string DonViTinh { get; set; } = string.Empty;
    public bool IsQuickService { get; set; } = false; // Based on ThoiGianXuLy < 1 hour
    public string ServiceCategory { get; set; } = string.Empty; // Derived from Ma prefix
}
