using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for HangSanXuat (Manufacturer) service
/// Defines business logic operations for HangSanXuat entity
/// AUTOMOTIVE FOCUSED - Essential for manufacturer management and vehicle categorization
/// </summary>
public interface IHangSanXuatService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadNameAsync(string tenViet);
    Task<bool> SaveAsync(HangSanXuatDto dto, string action);
    Task<bool> DelDataAsync(string khoa);
    Task<DataTable> ShowListAsync(string keyFilter = "", string fieldNameFilter = "");
    Task<DataTable> ShowAllListAsync();
    Task<string> SearchByCodeAsync(string code = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<HangSanXuatListDto>> GetAllAsync();
    Task<HangSanXuatDto?> GetByIdAsync(string khoa);
    Task<HangSanXuatDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateHangSanXuatDto createDto);
    Task<bool> UpdateAsync(HangSanXuatDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateHangSanXuatStatusDto statusDto);
    Task<IEnumerable<HangSanXuatListDto>> SearchAsync(HangSanXuatSearchDto searchDto);
    Task<IEnumerable<HangSanXuatLookupDto>> GetLookupAsync(string language = "vi");
    Task<HangSanXuatValidationDto> ValidateAsync(string khoa, string ma);
    Task<HangSanXuatSearchByCodeDto?> SearchManufacturerByCodeAsync(string code);
    Task<IEnumerable<ManufacturerCategoryDto>> GetManufacturerCategoriesAsync();
    Task<IEnumerable<HangSanXuatWithVehicleTypesDto>> GetManufacturersWithVehicleTypesAsync();
    Task<HangSanXuatStatsDto?> GetManufacturerStatsAsync(string khoa);
    Task<IEnumerable<ManufacturerCountryDto>> GetManufacturersByCountryAsync();
    Task<IEnumerable<HangSanXuatListDto>> GetFilteredListAsync(HangSanXuatFilterDto filterDto);
    
    #endregion
}

/// <summary>
/// Complete Service for HangSanXuat entity
/// Implements ALL business logic from clsDMHangSanXuat.cs (533 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for manufacturer management and vehicle categorization
/// </summary>
public class HangSanXuatService : IHangSanXuatService
{
    private readonly IHangSanXuatRepository _repository;
    private readonly ILogger<HangSanXuatService> _logger;

    public HangSanXuatService(IHangSanXuatRepository repository, ILogger<HangSanXuatService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading HangSanXuat");
            throw;
        }
    }

    public async Task<bool> LoadNameAsync(string tenViet)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(tenViet))
            {
                throw new ArgumentException("Tên hãng sản xuất không được để trống");
            }

            return await _repository.LoadNameAsync(tenViet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading HangSanXuat by name");
            throw;
        }
    }

    public async Task<bool> SaveAsync(HangSanXuatDto dto, string action)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto, action);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto, action);

            return await _repository.SaveAsync(dto, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving HangSanXuat");
            throw;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa hãng sản xuất đã được sử dụng");
            }

            return await _repository.DelDataAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting HangSanXuat");
            throw;
        }
    }

    public async Task<DataTable> ShowListAsync(string keyFilter = "", string fieldNameFilter = "")
    {
        try
        {
            // Apply security filters
            var secureKeyFilter = ApplySecurityFilters(keyFilter);
            var secureFieldNameFilter = ValidateFieldName(fieldNameFilter);
            
            return await _repository.ShowListAsync(secureKeyFilter, secureFieldNameFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing HangSanXuat list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            return await _repository.ShowAllListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all HangSanXuat list");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "")
    {
        try
        {
            return await _repository.SearchByCodeAsync(code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching HangSanXuat by code");
            return "";
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fieldList);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.ShowListByFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing HangSanXuat list by field");
            return new DataTable();
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                return false;
            }

            return await _repository.TrungMaAsync(ma, khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate manufacturer code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                return false;
            }

            return await _repository.WasUsedAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if HangSanXuat was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<HangSanXuatListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all HangSanXuat records");
            return new List<HangSanXuatListDto>();
        }
    }

    public async Task<HangSanXuatDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat by ID");
            return null;
        }
    }

    public async Task<HangSanXuatDto?> GetByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
                return null;

            return await _repository.GetByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat by code");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateHangSanXuatDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating HangSanXuat");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(HangSanXuatDto dto)
    {
        return await SaveAsync(dto, "UPDATE");
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DelDataAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateHangSanXuatStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating HangSanXuat status");
            throw;
        }
    }

    public async Task<IEnumerable<HangSanXuatListDto>> SearchAsync(HangSanXuatSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching HangSanXuat");
            return new List<HangSanXuatListDto>();
        }
    }

    public async Task<IEnumerable<HangSanXuatLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HangSanXuat lookup");
            return new List<HangSanXuatLookupDto>();
        }
    }

    public async Task<HangSanXuatValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating HangSanXuat");
            return new HangSanXuatValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<HangSanXuatSearchByCodeDto?> SearchManufacturerByCodeAsync(string code)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;

            return await _repository.SearchManufacturerByCodeAsync(code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching manufacturer by code");
            return null;
        }
    }

    public async Task<IEnumerable<ManufacturerCategoryDto>> GetManufacturerCategoriesAsync()
    {
        try
        {
            return await _repository.GetManufacturerCategoriesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer categories");
            return new List<ManufacturerCategoryDto>();
        }
    }

    public async Task<IEnumerable<HangSanXuatWithVehicleTypesDto>> GetManufacturersWithVehicleTypesAsync()
    {
        try
        {
            return await _repository.GetManufacturersWithVehicleTypesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturers with vehicle types");
            return new List<HangSanXuatWithVehicleTypesDto>();
        }
    }

    public async Task<HangSanXuatStatsDto?> GetManufacturerStatsAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetManufacturerStatsAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturer stats");
            return null;
        }
    }

    public async Task<IEnumerable<ManufacturerCountryDto>> GetManufacturersByCountryAsync()
    {
        try
        {
            return await _repository.GetManufacturersByCountryAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting manufacturers by country");
            return new List<ManufacturerCountryDto>();
        }
    }

    public async Task<IEnumerable<HangSanXuatListDto>> GetFilteredListAsync(HangSanXuatFilterDto filterDto)
    {
        try
        {
            // Apply security filters
            var secureFilter = new HangSanXuatFilterDto
            {
                KeyFilter = ApplySecurityFilters(filterDto.KeyFilter),
                FieldNameFilter = ValidateFieldName(filterDto.FieldNameFilter),
                Language = filterDto.Language
            };

            return await _repository.GetFilteredListAsync(secureFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting filtered manufacturer list");
            return new List<HangSanXuatListDto>();
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic for Automotive Manufacturers)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(HangSanXuatDto dto, string action)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã hãng sản xuất không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên hãng sản xuất (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate manufacturer codes
        if (!string.IsNullOrEmpty(dto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(dto.Ma, dto.Khoa);
            if (isDuplicate)
            {
                result.Errors.Add("Mã hãng sản xuất đã tồn tại");
            }
        }

        // Length validation
        if (dto.Ma.Length > 20)
            result.Errors.Add("Mã hãng sản xuất không được vượt quá 20 ký tự");

        if (dto.TenViet.Length > 200)
            result.Errors.Add("Tên hãng sản xuất (Tiếng Việt) không được vượt quá 200 ký tự");

        if (dto.TenAnh.Length > 200)
            result.Errors.Add("Tên hãng sản xuất (Tiếng Anh) không được vượt quá 200 ký tự");

        if (dto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Business validation
        if (dto.Active != 0 && dto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Date validation
        if (!string.IsNullOrEmpty(dto.TuNgay) && !IsValidDateFormat(dto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Manufacturer code format validation for automotive manufacturers
        if (!string.IsNullOrEmpty(dto.Ma) && !IsValidManufacturerCode(dto.Ma))
            result.Errors.Add("Mã hãng sản xuất phải là chữ cái và số, không có ký tự đặc biệt");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateHangSanXuatDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.Ma))
            result.Errors.Add("Mã hãng sản xuất không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên hãng sản xuất (Tiếng Việt) không được để trống");

        // Check for duplicate manufacturer codes
        if (!string.IsNullOrEmpty(createDto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(createDto.Ma, "");
            if (isDuplicate)
            {
                result.Errors.Add("Mã hãng sản xuất đã tồn tại");
            }
        }

        // Length validation
        if (createDto.Ma.Length > 20)
            result.Errors.Add("Mã hãng sản xuất không được vượt quá 20 ký tự");

        if (createDto.TenViet.Length > 200)
            result.Errors.Add("Tên hãng sản xuất (Tiếng Việt) không được vượt quá 200 ký tự");

        if (createDto.TenAnh.Length > 200)
            result.Errors.Add("Tên hãng sản xuất (Tiếng Anh) không được vượt quá 200 ký tự");

        if (createDto.DienGiai.Length > 1000)
            result.Errors.Add("Diễn giải không được vượt quá 1000 ký tự");

        // Date validation
        if (!string.IsNullOrEmpty(createDto.TuNgay) && !IsValidDateFormat(createDto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Manufacturer code format validation
        if (!string.IsNullOrEmpty(createDto.Ma) && !IsValidManufacturerCode(createDto.Ma))
            result.Errors.Add("Mã hãng sản xuất phải là chữ cái và số, không có ký tự đặc biệt");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the manufacturer is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateHangSanXuatStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (statusDto.Active != 0 && statusDto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Business rule: Cannot deactivate if being used
        if (statusDto.Active == 0)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động hãng sản xuất đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(HangSanXuatDto dto, string action)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa) && action.ToUpper() == "INSERT")
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim().ToUpper(); // Manufacturer codes should be uppercase
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        dto.Send = 0; // Not synchronized yet

        // Set default effective date if empty
        if (string.IsNullOrEmpty(dto.TuNgay))
        {
            dto.TuNgay = DateTime.Now.ToString("yyyyMMdd");
        }

        // Automotive manufacturer specific business rules
        await ApplyAutomotiveManufacturerRulesAsync(dto);
    }

    private async Task ApplyAutomotiveManufacturerRulesAsync(HangSanXuatDto dto)
    {
        // Automotive manufacturer specific validations and rules
        var manufacturerCode = dto.Ma.ToUpper();

        // Standardize common automotive manufacturer codes and descriptions
        if (manufacturerCode.Contains("TOYOTA"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Toyota - Nhật Bản";
        }
        else if (manufacturerCode.Contains("HONDA"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô và xe máy Honda - Nhật Bản";
        }
        else if (manufacturerCode.Contains("FORD"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Ford - Mỹ";
        }
        else if (manufacturerCode.Contains("BMW"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô BMW - Đức";
        }
        else if (manufacturerCode.Contains("MERCEDES"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Mercedes-Benz - Đức";
        }
        else if (manufacturerCode.Contains("HYUNDAI"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Hyundai - Hàn Quốc";
        }
        else if (manufacturerCode.Contains("KIA"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Kia - Hàn Quốc";
        }
        else if (manufacturerCode.Contains("NISSAN"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Nissan - Nhật Bản";
        }
        else if (manufacturerCode.Contains("MAZDA"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Mazda - Nhật Bản";
        }
        else if (manufacturerCode.Contains("AUDI"))
        {
            if (string.IsNullOrEmpty(dto.DienGiai))
                dto.DienGiai = "Hãng sản xuất ô tô Audi - Đức";
        }
    }

    private string ApplySecurityFilters(string input)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original input
        // In a real system, you might add filters like:
        // - Only show manufacturers user has access to
        // - Filter by user's dealership/branch

        return input;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DienGiai", "TuNgay",
            "KhoaNhanVienCapNhat", "Active", "Send"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    private string ValidateFieldName(string fieldName)
    {
        // Validate field name to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DienGiai", "TuNgay",
            "KhoaNhanVienCapNhat", "Active", "Send"
        };

        if (string.IsNullOrWhiteSpace(fieldName))
            return "";

        return allowedFields.Contains(fieldName.Trim(), StringComparer.OrdinalIgnoreCase)
            ? fieldName.Trim()
            : "";
    }

    private bool IsValidDateFormat(string date)
    {
        // Validate YYYYMMDD format
        if (string.IsNullOrEmpty(date) || date.Length != 8)
            return false;

        return DateTime.TryParseExact(date, "yyyyMMdd", null,
            System.Globalization.DateTimeStyles.None, out _);
    }

    private bool IsValidManufacturerCode(string code)
    {
        // Manufacturer codes should be alphanumeric (letters and numbers only)
        if (string.IsNullOrEmpty(code))
            return false;

        return code.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-');
    }

    #endregion
}
