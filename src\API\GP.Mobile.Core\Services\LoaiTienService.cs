using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for LoaiTien (Currency Type) service
/// Defines business logic operations for LoaiTien entity
/// </summary>
public interface ILoaiTienService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadByCodeAsync(string ma);
    Task<bool> SaveAsync(LoaiTienDto dto, string action);
    Task<bool> DelDataAsync(string khoa);
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LoaiTienListDto>> GetAllAsync();
    Task<LoaiTienDto?> GetByIdAsync(string khoa);
    Task<LoaiTienDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateLoaiTienDto createDto);
    Task<bool> UpdateAsync(LoaiTienDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateLoaiTienStatusDto statusDto);
    Task<IEnumerable<LoaiTienListDto>> SearchAsync(LoaiTienSearchDto searchDto);
    Task<IEnumerable<LoaiTienLookupDto>> GetLookupAsync(string language = "vi");
    Task<LoaiTienValidationDto> ValidateAsync(string khoa, string ma);
    Task<ExchangeRateDto?> GetExchangeRateAsync(string maLoaiTien, string ngay);
    Task<LoaiTienSearchByCodeDto?> SearchCurrencyByCodeAsync(string code, string condition = "");
    
    #endregion
}

/// <summary>
/// Complete Service for LoaiTien entity
/// Implements ALL business logic from clsDMLoaiTien.cs (552 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class LoaiTienService : ILoaiTienService
{
    private readonly ILoaiTienRepository _repository;
    private readonly ILogger<LoaiTienService> _logger;

    public LoaiTienService(ILoaiTienRepository repository, ILogger<LoaiTienService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiTien");
            throw;
        }
    }

    public async Task<bool> LoadByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                throw new ArgumentException("Mã loại tiền không được để trống");
            }

            return await _repository.LoadByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiTien by code");
            throw;
        }
    }

    public async Task<bool> SaveAsync(LoaiTienDto dto, string action)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto, action);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto, action);

            return await _repository.SaveAsync(dto, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving LoaiTien");
            throw;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa loại tiền đã được sử dụng");
            }

            return await _repository.DelDataAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiTien");
            throw;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Apply security filters
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.ShowListAsync(secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiTien list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            return await _repository.ShowAllListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all LoaiTien list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fieldList);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.ShowListByFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiTien list by field");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiTien by code");
            return "";
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
            {
                return false;
            }

            return await _repository.TrungMaAsync(ma, khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate currency code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                return false;
            }

            return await _repository.WasUsedAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if LoaiTien was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LoaiTienListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiTien records");
            return new List<LoaiTienListDto>();
        }
    }

    public async Task<LoaiTienDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by ID");
            return null;
        }
    }

    public async Task<LoaiTienDto?> GetByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
                return null;

            return await _repository.GetByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien by code");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateLoaiTienDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiTien");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(LoaiTienDto dto)
    {
        return await SaveAsync(dto, "UPDATE");
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DelDataAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateLoaiTienStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiTien status");
            throw;
        }
    }

    public async Task<IEnumerable<LoaiTienListDto>> SearchAsync(LoaiTienSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiTien");
            return new List<LoaiTienListDto>();
        }
    }

    public async Task<IEnumerable<LoaiTienLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiTien lookup");
            return new List<LoaiTienLookupDto>();
        }
    }

    public async Task<LoaiTienValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiTien");
            return new LoaiTienValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<ExchangeRateDto?> GetExchangeRateAsync(string maLoaiTien, string ngay)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(maLoaiTien))
                return null;

            if (string.IsNullOrWhiteSpace(ngay))
                ngay = DateTime.Now.ToString("yyyyMMdd");

            return await _repository.GetExchangeRateAsync(maLoaiTien, ngay);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exchange rate");
            return null;
        }
    }

    public async Task<LoaiTienSearchByCodeDto?> SearchCurrencyByCodeAsync(string code, string condition = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;

            var secureCondition = ApplySecurityFilters(condition);
            return await _repository.SearchCurrencyByCodeAsync(code, secureCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching currency by code");
            return null;
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(LoaiTienDto dto, string action)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã loại tiền không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên loại tiền (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate currency codes
        if (!string.IsNullOrEmpty(dto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(dto.Ma, dto.Khoa);
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại tiền đã tồn tại");
            }
        }

        // Length validation
        if (dto.Ma.Length > 10)
            result.Errors.Add("Mã loại tiền không được vượt quá 10 ký tự");

        if (dto.TenViet.Length > 100)
            result.Errors.Add("Tên loại tiền (Tiếng Việt) không được vượt quá 100 ký tự");

        if (dto.TenAnh.Length > 100)
            result.Errors.Add("Tên loại tiền (Tiếng Anh) không được vượt quá 100 ký tự");

        if (dto.DienGiai.Length > 500)
            result.Errors.Add("Diễn giải không được vượt quá 500 ký tự");

        // Business validation
        if (dto.TyGia <= 0)
            result.Errors.Add("Tỷ giá phải lớn hơn 0");

        if (dto.SoLe < 0 || dto.SoLe > 10)
            result.Errors.Add("Số lẻ phải từ 0 đến 10");

        if (dto.TienViet != 0 && dto.TienViet != 1)
            result.Errors.Add("Tiền Việt phải là 0 hoặc 1");

        if (dto.Active != 0 && dto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Date validation
        if (!string.IsNullOrEmpty(dto.TuNgay) && !IsValidDateFormat(dto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Currency code format validation
        if (!string.IsNullOrEmpty(dto.Ma) && !IsValidCurrencyCode(dto.Ma))
            result.Errors.Add("Mã loại tiền phải là chữ cái viết hoa (VD: VND, USD, EUR)");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateLoaiTienDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.Ma))
            result.Errors.Add("Mã loại tiền không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên loại tiền (Tiếng Việt) không được để trống");

        // Check for duplicate currency codes
        if (!string.IsNullOrEmpty(createDto.Ma))
        {
            var isDuplicate = await _repository.TrungMaAsync(createDto.Ma, "");
            if (isDuplicate)
            {
                result.Errors.Add("Mã loại tiền đã tồn tại");
            }
        }

        // Length validation
        if (createDto.Ma.Length > 10)
            result.Errors.Add("Mã loại tiền không được vượt quá 10 ký tự");

        if (createDto.TenViet.Length > 100)
            result.Errors.Add("Tên loại tiền (Tiếng Việt) không được vượt quá 100 ký tự");

        if (createDto.TenAnh.Length > 100)
            result.Errors.Add("Tên loại tiền (Tiếng Anh) không được vượt quá 100 ký tự");

        if (createDto.DienGiai.Length > 500)
            result.Errors.Add("Diễn giải không được vượt quá 500 ký tự");

        // Business validation
        if (createDto.TyGia <= 0)
            result.Errors.Add("Tỷ giá phải lớn hơn 0");

        if (createDto.SoLe < 0 || createDto.SoLe > 10)
            result.Errors.Add("Số lẻ phải từ 0 đến 10");

        if (createDto.TienViet != 0 && createDto.TienViet != 1)
            result.Errors.Add("Tiền Việt phải là 0 hoặc 1");

        // Date validation
        if (!string.IsNullOrEmpty(createDto.TuNgay) && !IsValidDateFormat(createDto.TuNgay))
            result.Errors.Add("Từ ngày phải có định dạng YYYYMMDD");

        // Currency code format validation
        if (!string.IsNullOrEmpty(createDto.Ma) && !IsValidCurrencyCode(createDto.Ma))
            result.Errors.Add("Mã loại tiền phải là chữ cái viết hoa (VD: VND, USD, EUR)");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the currency is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateLoaiTienStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        if (statusDto.Active != 0 && statusDto.Active != 1)
            result.Errors.Add("Trạng thái hoạt động phải là 0 hoặc 1");

        // Business rule: Cannot deactivate if being used
        if (statusDto.Active == 0)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động loại tiền đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(LoaiTienDto dto, string action)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa) && action.ToUpper() == "INSERT")
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim().ToUpper(); // Currency codes should be uppercase
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DienGiai = dto.DienGiai.Trim();

        // Set default values
        if (action.ToUpper() == "INSERT")
        {
            dto.Send = 0; // Not synchronized yet

            // Set default effective date if empty
            if (string.IsNullOrEmpty(dto.TuNgay))
            {
                dto.TuNgay = DateTime.Now.ToString("yyyyMMdd");
            }
        }

        // Business rule: VND should always have TienViet = 1
        if (dto.Ma.ToUpper() == "VND")
        {
            dto.TienViet = 1;
            dto.TyGia = 1.0; // VND to VND rate is always 1
        }
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show active currencies for non-admin users
        // - Filter by user's allowed currencies

        return conditions;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DienGiai", "TienViet",
            "TyGia", "TuNgay", "SoLe", "KhoaNhanVienCapNhat", "Active", "Send"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    private bool IsValidDateFormat(string date)
    {
        // Validate YYYYMMDD format
        if (string.IsNullOrEmpty(date) || date.Length != 8)
            return false;

        return DateTime.TryParseExact(date, "yyyyMMdd", null,
            System.Globalization.DateTimeStyles.None, out _);
    }

    private bool IsValidCurrencyCode(string code)
    {
        // Currency codes should be 3 uppercase letters (ISO 4217 standard)
        if (string.IsNullOrEmpty(code) || code.Length != 3)
            return false;

        return code.All(char.IsLetter) && code.All(char.IsUpper);
    }

    #endregion
}
