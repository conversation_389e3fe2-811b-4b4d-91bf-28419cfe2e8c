using System;
using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Appointment Scheduling (Đặt Lịch Hẹn)
    /// Maps to SC_DatLichHen table in legacy database
    /// </summary>
    public class DatLichHenDto
    {
        /// <summary>
        /// Primary key - Appointment ID
        /// </summary>
        public string Khoa { get; set; } = string.Empty;

        /// <summary>
        /// Document date (NgayChungTu)
        /// </summary>
        public string NgayChungTu { get; set; } = string.Empty;

        /// <summary>
        /// Document number (SoChungTu)
        /// </summary>
        public string SoChungTu { get; set; } = string.Empty;

        /// <summary>
        /// Vehicle ID reference (KhoaXe)
        /// </summary>
        public string KhoaXe { get; set; } = string.Empty;

        /// <summary>
        /// Customer ID reference (KhoaKhachHang)
        /// </summary>
        public string KhoaKhachHang { get; set; } = string.Empty;

        /// <summary>
        /// Vehicle type ID reference (KhoaLoaiXe)
        /// </summary>
        public string KhoaLoaiXe { get; set; } = string.Empty;

        /// <summary>
        /// Appointment time (GioDatHen)
        /// </summary>
        public string GioDatHen { get; set; } = string.Empty;

        /// <summary>
        /// Appointment date (NgayDatHen)
        /// </summary>
        public string NgayDatHen { get; set; } = string.Empty;

        /// <summary>
        /// Actual arrival date (NgayVaoThucTe)
        /// </summary>
        public string NgayVaoThucTe { get; set; } = string.Empty;

        /// <summary>
        /// Customer requirements (YeuCauKhachHang)
        /// </summary>
        public string YeuCauKhachHang { get; set; } = string.Empty;

        /// <summary>
        /// Service type ID reference (KhoaLoaiDichVu)
        /// </summary>
        public string KhoaLoaiDichVu { get; set; } = string.Empty;

        /// <summary>
        /// Flag for callback reminder (IsGoiLaiNhacNho)
        /// </summary>
        public bool IsGoiLaiNhacNho { get; set; } = false;

        /// <summary>
        /// Callback date (NgayGoiLai)
        /// </summary>
        public string NgayGoiLai { get; set; } = string.Empty;

        /// <summary>
        /// Created by employee ID (KhoaNhanVienTao)
        /// </summary>
        public string KhoaNhanVienTao { get; set; } = string.Empty;

        /// <summary>
        /// Creation date (NgayTao)
        /// </summary>
        public string NgayTao { get; set; } = string.Empty;

        /// <summary>
        /// Updated by employee ID (KhoaNhanVienCapNhat)
        /// </summary>
        public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// Update date (NgayCapNhat)
        /// </summary>
        public string NgayCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// Related quotation ID (KhoaBaoGia)
        /// </summary>
        public string KhoaBaoGia { get; set; } = string.Empty;

        /// <summary>
        /// Insurance company ID (KhoaHangBaoHiem)
        /// </summary>
        public string KhoaHangBaoHiem { get; set; } = string.Empty;

        /// <summary>
        /// Branch/Unit ID (KhoaDonVi)
        /// </summary>
        public string KhoaDonVi { get; set; } = string.Empty;

        /// <summary>
        /// Flag indicating if reminder call was made (IsDaGoiNhacHen)
        /// </summary>
        public bool IsDaGoiNhacHen { get; set; } = false;

        /// <summary>
        /// Reminder call content (NoiDungGoiNhacHen)
        /// </summary>
        public string NoiDungGoiNhacHen { get; set; } = string.Empty;

        /// <summary>
        /// Service type category (LoaiDichVu)
        /// 0 = Maintenance, 1 = Repair, 2 = Insurance
        /// </summary>
        public int LoaiDichVu { get; set; } = 0;

        /// <summary>
        /// Service advisor ID (KhoaCoVan)
        /// </summary>
        public string KhoaCoVan { get; set; } = string.Empty;

        // Navigation properties for display purposes
        /// <summary>
        /// Vehicle license plate number (for display)
        /// </summary>
        public string? SoXe { get; set; }

        /// <summary>
        /// Customer name (for display)
        /// </summary>
        public string? TenKhachHang { get; set; }

        /// <summary>
        /// Vehicle type name (for display)
        /// </summary>
        public string? TenLoaiXe { get; set; }

        /// <summary>
        /// Service type name (for display)
        /// </summary>
        public string? TenLoaiDichVu { get; set; }

        /// <summary>
        /// Insurance company name (for display)
        /// </summary>
        public string? TenBaoHiem { get; set; }

        /// <summary>
        /// Service advisor name (for display)
        /// </summary>
        public string? TenCoVan { get; set; }

        /// <summary>
        /// Formatted appointment date and time (for display)
        /// </summary>
        public string? NgayGioDatHen { get; set; }

        /// <summary>
        /// Formatted actual arrival date and time (for display)
        /// </summary>
        public string? NgayGioVaoThucTe { get; set; }

        /// <summary>
        /// Customer phone number (for display)
        /// </summary>
        public string? DienThoai { get; set; }

        /// <summary>
        /// Vehicle manufacturer name (for display)
        /// </summary>
        public string? TenHangXe { get; set; }

        /// <summary>
        /// Vehicle chassis number (for display)
        /// </summary>
        public string? SoSuon { get; set; }

        /// <summary>
        /// Vehicle engine number (for display)
        /// </summary>
        public string? SoMay { get; set; }

        /// <summary>
        /// Vehicle color (for display)
        /// </summary>
        public string? MauSon { get; set; }
    }
}
