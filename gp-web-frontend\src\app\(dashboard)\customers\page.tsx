'use client'

import { useState } from 'react'
import { Plus, Search, Edit, Trash2, Eye } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { withAuth } from '@/contexts/AuthContext'

// Mock customer data - in real app, this would come from API
const mockCustomers = [
  {
    id: 'KH001',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    phone: '0901234567',
    email: 'ng<PERSON><PERSON><PERSON><PERSON>@email.com',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    customerType: '<PERSON><PERSON> nhân',
    status: 'Hoạt động',
    registrationDate: '2024-01-15',
    totalVehicles: 2,
    totalServices: 15
  },
  {
    id: 'KH002',
    name: 'Công ty TNHH XYZ',
    phone: '0287654321',
    email: '<EMAIL>',
    address: '456 Đường DEF, Quận 3, TP.HCM',
    customerType: 'Doanh nghiệp',
    status: 'Hoạt động',
    registrationDate: '2024-02-20',
    totalVehicles: 8,
    totalServices: 45
  },
  {
    id: 'KH003',
    name: 'Trần Thị Bình',
    phone: '0912345678',
    email: '<EMAIL>',
    address: '789 Đường GHI, Quận 7, TP.HCM',
    customerType: 'Cá nhân',
    status: 'Tạm ngưng',
    registrationDate: '2024-03-10',
    totalVehicles: 1,
    totalServices: 3
  }
]

function CustomersPage() {
  const [customers, setCustomers] = useState(mockCustomers)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)

  // Filter customers based on search and filters
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || customer.customerType === filterType
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const handleViewCustomer = (customer: any) => {
    setSelectedCustomer(customer)
    setIsDetailDialogOpen(true)
  }

  const getStatusBadge = (status: string) => {
    return status === 'Hoạt động' ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        {status}
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
        {status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản lý khách hàng</h1>
          <p className="text-muted-foreground">
            Quản lý thông tin khách hàng và lịch sử dịch vụ
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm khách hàng
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo tên, số điện thoại, email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Loại khách hàng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="Cá nhân">Cá nhân</SelectItem>
                <SelectItem value="Doanh nghiệp">Doanh nghiệp</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="Hoạt động">Hoạt động</SelectItem>
                <SelectItem value="Tạm ngưng">Tạm ngưng</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách khách hàng</CardTitle>
          <CardDescription>
            Tổng cộng {filteredCustomers.length} khách hàng
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mã KH</TableHead>
                  <TableHead>Tên khách hàng</TableHead>
                  <TableHead>Số điện thoại</TableHead>
                  <TableHead>Loại KH</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Số xe</TableHead>
                  <TableHead>Số lần dịch vụ</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{customer.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-muted-foreground">{customer.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{customer.phone}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {customer.customerType}
                      </Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(customer.status)}</TableCell>
                    <TableCell className="text-center">{customer.totalVehicles}</TableCell>
                    <TableCell className="text-center">{customer.totalServices}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewCustomer(customer)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Customer Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Chi tiết khách hàng</DialogTitle>
            <DialogDescription>
              Thông tin chi tiết của khách hàng {selectedCustomer?.name}
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Mã khách hàng</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Tên khách hàng</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Số điện thoại</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.email}</p>
                </div>
                <div className="col-span-2">
                  <label className="text-sm font-medium">Địa chỉ</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.address}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Loại khách hàng</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.customerType}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Trạng thái</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.status}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Ngày đăng ký</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.registrationDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Tổng số xe</label>
                  <p className="text-sm text-muted-foreground">{selectedCustomer.totalVehicles}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default withAuth(CustomersPage)
