using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// API Controller for Appointment Scheduling (Đặt Lịch Hẹn)
    /// Provides REST endpoints for appointment management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DatLichHenController : ControllerBase
    {
        private readonly IDatLichHenService _service;
        private readonly ILogger<DatLichHenController> _logger;

        public DatLichHenController(IDatLichHenService service, ILogger<DatLichHenController> logger)
        {
            _service = service;
            _logger = logger;
        }

        /// <summary>
        /// Get appointment by ID
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Appointment data</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<DatLichHenDto>> GetById(string id)
        {
            try
            {
                var appointment = await _service.GetByIdAsync(id);
                if (appointment == null)
                {
                    return NotFound($"Appointment with ID {id} not found");
                }

                return Ok(appointment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment by ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointment by quotation ID
        /// </summary>
        /// <param name="baoGiaId">Quotation ID</param>
        /// <returns>Appointment data</returns>
        [HttpGet("by-baogia/{baoGiaId}")]
        public async Task<ActionResult<DatLichHenDto>> GetByBaoGiaId(string baoGiaId)
        {
            try
            {
                var appointment = await _service.GetByBaoGiaIdAsync(baoGiaId);
                if (appointment == null)
                {
                    return NotFound($"Appointment for quotation {baoGiaId} not found");
                }

                return Ok(appointment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment by BaoGia ID: {BaoGiaId}", baoGiaId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create new appointment
        /// </summary>
        /// <param name="appointment">Appointment data</param>
        /// <returns>Created appointment ID</returns>
        [HttpPost]
        public async Task<ActionResult<string>> Create([FromBody] DatLichHenDto appointment)
        {
            try
            {
                if (appointment == null)
                {
                    return BadRequest("Appointment data is required");
                }

                var appointmentId = await _service.CreateAsync(appointment);
                if (string.IsNullOrEmpty(appointmentId))
                {
                    return BadRequest("Failed to create appointment");
                }

                return CreatedAtAction(nameof(GetById), new { id = appointmentId }, appointmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating appointment");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update existing appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="appointment">Updated appointment data</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult> Update(string id, [FromBody] DatLichHenDto appointment)
        {
            try
            {
                if (appointment == null)
                {
                    return BadRequest("Appointment data is required");
                }

                if (id != appointment.Khoa)
                {
                    return BadRequest("ID mismatch");
                }

                var success = await _service.UpdateAsync(appointment);
                if (!success)
                {
                    return BadRequest("Failed to update appointment");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating appointment: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(string id)
        {
            try
            {
                var success = await _service.DeleteAsync(id);
                if (!success)
                {
                    return NotFound($"Appointment with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting appointment: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get today's appointments
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of today's appointments</returns>
        [HttpGet("today/{donViId}")]
        public async Task<ActionResult<List<DatLichHenDto>>> GetTodayAppointments(string donViId)
        {
            try
            {
                var appointments = await _service.GetTodayAppointmentsAsync(donViId);
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's appointments for donViId: {DonViId}", donViId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get upcoming appointments (next 7 days)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of upcoming appointments</returns>
        [HttpGet("upcoming/{donViId}")]
        public async Task<ActionResult<List<DatLichHenDto>>> GetUpcomingAppointments(string donViId)
        {
            try
            {
                var appointments = await _service.GetUpcomingAppointmentsAsync(donViId);
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming appointments for donViId: {DonViId}", donViId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointments by date range
        /// </summary>
        /// <param name="fromDate">Start date (YYYY-MM-DD)</param>
        /// <param name="toDate">End date (YYYY-MM-DD)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of appointments in date range</returns>
        [HttpGet("date-range")]
        public async Task<ActionResult<List<DatLichHenDto>>> GetAppointmentsByDateRange(
            [FromQuery] string fromDate, 
            [FromQuery] string toDate, 
            [FromQuery] string donViId)
        {
            try
            {
                if (string.IsNullOrEmpty(fromDate) || string.IsNullOrEmpty(toDate) || string.IsNullOrEmpty(donViId))
                {
                    return BadRequest("fromDate, toDate, and donViId are required");
                }

                var appointments = await _service.GetAppointmentsByDateRangeAsync(fromDate, toDate, donViId);
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by date range: {FromDate} to {ToDate}", fromDate, toDate);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointments by vehicle
        /// </summary>
        /// <param name="vehicleId">Vehicle ID</param>
        /// <returns>List of appointments for the vehicle</returns>
        [HttpGet("by-vehicle/{vehicleId}")]
        public async Task<ActionResult<List<DatLichHenDto>>> GetAppointmentsByVehicle(string vehicleId)
        {
            try
            {
                var appointments = await _service.GetAppointmentsByVehicleAsync(vehicleId);
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by vehicle: {VehicleId}", vehicleId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointments by customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>List of appointments for the customer</returns>
        [HttpGet("by-customer/{customerId}")]
        public async Task<ActionResult<List<DatLichHenDto>>> GetAppointmentsByCustomer(string customerId)
        {
            try
            {
                var appointments = await _service.GetAppointmentsByCustomerAsync(customerId);
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments by customer: {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointments requiring reminders
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>DataTable with reminder appointments</returns>
        [HttpGet("reminders/{donViId}")]
        public async Task<ActionResult<DataTable>> GetReminderAppointments(string donViId)
        {
            try
            {
                var reminders = await _service.GetReminderAppointmentsAsync(donViId);
                return Ok(reminders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder appointments for donViId: {DonViId}", donViId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointments requiring reminders (1 day before)
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>DataTable with appointments needing reminders</returns>
        [HttpGet("reminders-tomorrow/{donViId}")]
        public async Task<ActionResult<DataTable>> GetOneDayReminderAppointments(string donViId)
        {
            try
            {
                var reminders = await _service.GetOneDayReminderAppointmentsAsync(donViId);
                return Ok(reminders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting one-day reminder appointments for donViId: {DonViId}", donViId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update reminder call status
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="reminderContent">Reminder call content</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}/reminder")]
        public async Task<ActionResult> UpdateReminderCallStatus(string id, [FromBody] string reminderContent)
        {
            try
            {
                var success = await _service.UpdateReminderCallStatusAsync(id, reminderContent);
                if (!success)
                {
                    return BadRequest("Failed to update reminder call status");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating reminder call status: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check for appointment time conflicts
        /// </summary>
        /// <param name="ngayDatHen">Appointment date (YYYY-MM-DD)</param>
        /// <param name="gioDatHen">Appointment time (HH:mm)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="excludeKhoa">Exclude this appointment ID from conflict check</param>
        /// <returns>True if conflict exists</returns>
        [HttpGet("check-conflict")]
        public async Task<ActionResult<bool>> CheckAppointmentConflict(
            [FromQuery] string ngayDatHen,
            [FromQuery] string gioDatHen,
            [FromQuery] string donViId,
            [FromQuery] string? excludeKhoa = null)
        {
            try
            {
                if (string.IsNullOrEmpty(ngayDatHen) || string.IsNullOrEmpty(gioDatHen) || string.IsNullOrEmpty(donViId))
                {
                    return BadRequest("ngayDatHen, gioDatHen, and donViId are required");
                }

                var hasConflict = await _service.CheckAppointmentConflictAsync(ngayDatHen, gioDatHen, donViId, excludeKhoa ?? "");
                return Ok(hasConflict);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking appointment conflict");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get appointment statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYY-MM-DD)</param>
        /// <param name="toDate">End date (YYYY-MM-DD)</param>
        /// <returns>Appointment statistics</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<AppointmentStatisticsDto>> GetAppointmentStatistics(
            [FromQuery] string donViId,
            [FromQuery] string fromDate,
            [FromQuery] string toDate)
        {
            try
            {
                if (string.IsNullOrEmpty(donViId) || string.IsNullOrEmpty(fromDate) || string.IsNullOrEmpty(toDate))
                {
                    return BadRequest("donViId, fromDate, and toDate are required");
                }

                var statistics = await _service.GetAppointmentStatisticsAsync(donViId, fromDate, toDate);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get available time slots for appointment scheduling
        /// </summary>
        /// <param name="ngayDatHen">Appointment date (YYYY-MM-DD)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of available time slots</returns>
        [HttpGet("available-slots")]
        public async Task<ActionResult<List<TimeSlotDto>>> GetAvailableTimeSlots(
            [FromQuery] string ngayDatHen,
            [FromQuery] string donViId)
        {
            try
            {
                if (string.IsNullOrEmpty(ngayDatHen) || string.IsNullOrEmpty(donViId))
                {
                    return BadRequest("ngayDatHen and donViId are required");
                }

                var timeSlots = await _service.GetAvailableTimeSlotsAsync(ngayDatHen, donViId);
                return Ok(timeSlots);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available time slots");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Reschedule appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="rescheduleRequest">Reschedule request data</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}/reschedule")]
        public async Task<ActionResult> RescheduleAppointment(string id, [FromBody] RescheduleAppointmentRequest rescheduleRequest)
        {
            try
            {
                if (rescheduleRequest == null)
                {
                    return BadRequest("Reschedule request data is required");
                }

                var success = await _service.RescheduleAppointmentAsync(id, rescheduleRequest.NewNgayDatHen, rescheduleRequest.NewGioDatHen, rescheduleRequest.Reason);
                if (!success)
                {
                    return BadRequest("Failed to reschedule appointment");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rescheduling appointment: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Cancel appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="cancelRequest">Cancellation request data</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}/cancel")]
        public async Task<ActionResult> CancelAppointment(string id, [FromBody] CancelAppointmentRequest cancelRequest)
        {
            try
            {
                if (cancelRequest == null)
                {
                    return BadRequest("Cancel request data is required");
                }

                var success = await _service.CancelAppointmentAsync(id, cancelRequest.Reason, cancelRequest.UserId);
                if (!success)
                {
                    return BadRequest("Failed to cancel appointment");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling appointment: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Convert appointment to quotation
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="convertRequest">Conversion request data</param>
        /// <returns>Created quotation ID</returns>
        [HttpPost("{id}/convert-to-quotation")]
        public async Task<ActionResult<string>> ConvertToQuotation(string id, [FromBody] ConvertToQuotationRequest convertRequest)
        {
            try
            {
                if (convertRequest == null)
                {
                    return BadRequest("Convert request data is required");
                }

                var quotationId = await _service.ConvertToQuotationAsync(id, convertRequest.UserId);
                if (string.IsNullOrEmpty(quotationId))
                {
                    return BadRequest("Failed to convert appointment to quotation");
                }

                return Ok(quotationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting appointment to quotation: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Send reminder notification
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="reminderRequest">Reminder request data</param>
        /// <returns>Success status</returns>
        [HttpPost("{id}/send-reminder")]
        public async Task<ActionResult> SendReminderNotification(string id, [FromBody] SendReminderRequest reminderRequest)
        {
            try
            {
                if (reminderRequest == null)
                {
                    return BadRequest("Reminder request data is required");
                }

                var success = await _service.SendReminderNotificationAsync(id, reminderRequest.ReminderType);
                if (!success)
                {
                    return BadRequest("Failed to send reminder notification");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending reminder notification: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }

    #region Request DTOs

    /// <summary>
    /// Request DTO for rescheduling appointment
    /// </summary>
    public class RescheduleAppointmentRequest
    {
        public string NewNgayDatHen { get; set; } = string.Empty;
        public string NewGioDatHen { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for cancelling appointment
    /// </summary>
    public class CancelAppointmentRequest
    {
        public string Reason { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for converting appointment to quotation
    /// </summary>
    public class ConvertToQuotationRequest
    {
        public string UserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for sending reminder notification
    /// </summary>
    public class SendReminderRequest
    {
        public ReminderType ReminderType { get; set; }
    }

    #endregion
}
