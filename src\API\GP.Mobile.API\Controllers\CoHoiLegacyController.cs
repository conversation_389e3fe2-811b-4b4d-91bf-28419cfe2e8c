using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Legacy API Controller exposing ALL methods from clsCoHoi.cs
/// Maintains exact functionality and SQL queries from legacy system
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CoHoiLegacyController : ControllerBase
{
    private readonly ICoHoiService _coHoiService;
    private readonly ILogger<CoHoiLegacyController> _logger;

    public CoHoiLegacyController(ICoHoiService coHoiService, ILogger<CoHoiLegacyController> logger)
    {
        _coHoiService = coHoiService;
        _logger = logger;
    }

    #region Core Operations - Exact Legacy API

    /// <summary>
    /// Load by Khoa - exact legacy Load(string pKhoa)
    /// </summary>
    [HttpGet("load/{khoa}")]
    public async Task<ActionResult<bool>> Load(string khoa)
    {
        try
        {
            var result = await _coHoiService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading CoHoi by Khoa: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// LoadSoHopDong - exact legacy LoadSoHopDong(string pKhoaSoHopDong)
    /// </summary>
    [HttpGet("load-so-hop-dong/{soHopDong}")]
    public async Task<ActionResult<bool>> LoadSoHopDong(string soHopDong)
    {
        try
        {
            var result = await _coHoiService.LoadSoHopDongAsync(soHopDong);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading CoHoi by SoHopDong: {SoHopDong}", soHopDong);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Save - exact legacy Save()
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] CoHoiDto dto)
    {
        try
        {
            var result = await _coHoiService.SaveAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving CoHoi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete - exact legacy DelData(string pKhoa)
    /// </summary>
    [HttpDelete("del-data/{khoa}")]
    public async Task<ActionResult<bool>> DelData(string khoa)
    {
        try
        {
            var result = await _coHoiService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CoHoi: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region List Operations - Exact Legacy SQL

    /// <summary>
    /// ShowList - exact legacy ShowList(string strConditions = "")
    /// </summary>
    [HttpGet("show-list")]
    public async Task<ActionResult<DataTable>> ShowList([FromQuery] string strConditions = "")
    {
        try
        {
            var result = await _coHoiService.ShowListAsync(strConditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// ShowAllList - exact legacy ShowAllList(string pStrLoai = "")
    /// </summary>
    [HttpGet("show-all-list")]
    public async Task<ActionResult<DataTable>> ShowAllList([FromQuery] string pStrLoai = "")
    {
        try
        {
            var result = await _coHoiService.ShowAllListAsync(pStrLoai);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// ShowListByField - exact legacy ShowListByField method
    /// </summary>
    [HttpGet("show-list-by-field")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromQuery] string strFieldList, [FromQuery] string strConditions = "", [FromQuery] string strOrder = "")
    {
        try
        {
            var result = await _coHoiService.ShowListByFieldAsync(strFieldList, strConditions, strOrder);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Validation Methods - Exact Legacy Logic

    /// <summary>
    /// TrungSoChungTu - exact legacy TrungSoChungTu(string pSoChungTu, string pKhoa)
    /// </summary>
    [HttpGet("trung-so-chung-tu")]
    public async Task<ActionResult<bool>> TrungSoChungTu([FromQuery] string pSoChungTu, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _coHoiService.TrungSoChungTuAsync(pSoChungTu, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungSoChungTu");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungSoBienBanBanGiao - exact legacy TrungSoBienBanBanGiao(string pSoBienBanBanGiao, string pKhoa)
    /// </summary>
    [HttpGet("trung-so-bien-ban-ban-giao")]
    public async Task<ActionResult<bool>> TrungSoBienBanBanGiao([FromQuery] string pSoBienBanBanGiao, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _coHoiService.TrungSoBienBanBanGiaoAsync(pSoBienBanBanGiao, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungSoBienBanBanGiao");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungSoBienBanThoaThuan - exact legacy TrungSoBienBanThoaThuan(string pSoBienBanThoaThuan, string pKhoa)
    /// </summary>
    [HttpGet("trung-so-bien-ban-thoa-thuan")]
    public async Task<ActionResult<bool>> TrungSoBienBanThoaThuan([FromQuery] string pSoBienBanThoaThuan, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _coHoiService.TrungSoBienBanThoaThuanAsync(pSoBienBanThoaThuan, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungSoBienBanThoaThuan");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// TrungSoHopDong - exact legacy TrungSoHopDong(string pSoHopDong, string pKhoa)
    /// </summary>
    [HttpGet("trung-so-hop-dong")]
    public async Task<ActionResult<bool>> TrungSoHopDong([FromQuery] string pSoHopDong, [FromQuery] string pKhoa)
    {
        try
        {
            var result = await _coHoiService.TrungSoHopDongAsync(pSoHopDong, pKhoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungSoHopDong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Utility Operations

    /// <summary>
    /// ClearTemp - exact legacy ClearTemp(string pKeyTable)
    /// </summary>
    [HttpPost("clear-temp/{keyTable}")]
    public async Task<ActionResult> ClearTemp(string keyTable)
    {
        try
        {
            await _coHoiService.ClearTempAsync(keyTable);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Reporting Operations

    /// <summary>
    /// GetChiTieuChiTietTheoHopDong - exact legacy GetChiTieuChiTietTheoHopDong
    /// </summary>
    [HttpGet("get-chi-tieu-chi-tiet-theo-hop-dong")]
    public async Task<ActionResult<DataTable>> GetChiTieuChiTietTheoHopDong([FromQuery] int Nam, [FromQuery] int Quy, [FromQuery] string KhoaNhanVienQuanLy)
    {
        try
        {
            var result = await _coHoiService.GetChiTieuChiTietTheoHopDongAsync(Nam, Quy, KhoaNhanVienQuanLy);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetChiTieuChiTietTheoHopDong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// GetBaoCaoLaiLoTheoHopDongHoanTat - exact legacy GetBaoCaoLaiLoTheoHopDongHoanTat
    /// </summary>
    [HttpGet("get-bao-cao-lai-lo-theo-hop-dong-hoan-tat")]
    public async Task<ActionResult<DataTable>> GetBaoCaoLaiLoTheoHopDongHoanTat([FromQuery] string TuNgay, [FromQuery] string DenNgay, [FromQuery] string KhoaNhanVienQuanLy, [FromQuery] int IsTatToan)
    {
        try
        {
            var result = await _coHoiService.GetBaoCaoLaiLoTheoHopDongHoanTatAsync(TuNgay, DenNgay, KhoaNhanVienQuanLy, IsTatToan);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetBaoCaoLaiLoTheoHopDongHoanTat");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// GetHopDongKyMoi - exact legacy GetHopDongKyMoi
    /// </summary>
    [HttpGet("get-hop-dong-ky-moi")]
    public async Task<ActionResult<DataTable>> GetHopDongKyMoi([FromQuery] string strTuNgay, [FromQuery] string strDenNgay, [FromQuery] string strKhoaNVKD = "", [FromQuery] string strKhoaChiNhanh = "")
    {
        try
        {
            var result = await _coHoiService.GetHopDongKyMoiAsync(strTuNgay, strDenNgay, strKhoaNVKD, strKhoaChiNhanh);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetHopDongKyMoi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}
