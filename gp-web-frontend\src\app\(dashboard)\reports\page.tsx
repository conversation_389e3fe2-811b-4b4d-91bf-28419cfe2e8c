'use client'

import { useState } from 'react'
import { BarChart3, FileText, Download, Calendar, TrendingUp, Users, Car, Package } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { withAuth } from '@/contexts/AuthContext'

// Mock report data
const reportTypes = [
  {
    id: 'revenue',
    title: 'Báo cáo doanh thu',
    description: 'Thống kê doanh thu theo thời gian',
    icon: TrendingUp,
    color: 'text-green-600'
  },
  {
    id: 'services',
    title: 'Báo cáo dịch vụ',
    description: 'Thống kê các dịch vụ đã thực hiện',
    icon: Car,
    color: 'text-blue-600'
  },
  {
    id: 'customers',
    title: '<PERSON><PERSON><PERSON> cáo khách hàng',
    description: '<PERSON>ân tích khách hàng và xu hướng',
    icon: Users,
    color: 'text-purple-600'
  },
  {
    id: 'inventory',
    title: 'Báo cáo tồn kho',
    description: 'Tình hình xuất nhập tồn kho',
    icon: Package,
    color: 'text-orange-600'
  }
]

const quickReports = [
  {
    title: 'Doanh thu hôm nay',
    value: '15,500,000 VND',
    change: '+12%',
    changeType: 'positive'
  },
  {
    title: 'Số xe đã sửa',
    value: '23 xe',
    change: '+5%',
    changeType: 'positive'
  },
  {
    title: 'Khách hàng mới',
    value: '8 khách hàng',
    change: '+15%',
    changeType: 'positive'
  },
  {
    title: 'Tỷ lệ hoàn thành',
    value: '94%',
    change: '-2%',
    changeType: 'negative'
  }
]

function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('today')
  const [selectedReportType, setSelectedReportType] = useState('revenue')

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Báo cáo và thống kê</h1>
          <p className="text-muted-foreground">
            Phân tích dữ liệu kinh doanh và hiệu suất hoạt động
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Lên lịch báo cáo
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Xuất báo cáo
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        {quickReports.map((report, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{report.title}</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{report.value}</div>
              <p className={`text-xs ${report.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                {report.change} so với hôm qua
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Report Types */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {reportTypes.map((report) => (
          <Card key={report.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto mb-2">
                <report.icon className={`h-12 w-12 ${report.color}`} />
              </div>
              <CardTitle className="text-lg">{report.title}</CardTitle>
              <CardDescription>{report.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Xem báo cáo
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Reports */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="revenue">Doanh thu</TabsTrigger>
            <TabsTrigger value="services">Dịch vụ</TabsTrigger>
            <TabsTrigger value="customers">Khách hàng</TabsTrigger>
            <TabsTrigger value="inventory">Tồn kho</TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Chọn thời gian" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Hôm nay</SelectItem>
                <SelectItem value="week">Tuần này</SelectItem>
                <SelectItem value="month">Tháng này</SelectItem>
                <SelectItem value="quarter">Quý này</SelectItem>
                <SelectItem value="year">Năm này</SelectItem>
                <SelectItem value="custom">Tùy chọn</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Biểu đồ doanh thu</CardTitle>
                <CardDescription>Doanh thu theo thời gian</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Biểu đồ doanh thu sẽ được hiển thị tại đây</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Phân tích doanh thu</CardTitle>
                <CardDescription>Chi tiết theo loại dịch vụ</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Sửa chữa</span>
                    <span className="font-bold">65%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Bảo dưỡng</span>
                    <span className="font-bold">25%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '25%' }}></div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Phụ tùng</span>
                    <span className="font-bold">10%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '10%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Bảng doanh thu chi tiết</CardTitle>
              <CardDescription>Doanh thu theo ngày trong tuần</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'].map((day, index) => (
                  <div key={day} className="flex justify-between items-center p-2 border rounded">
                    <span>{day}</span>
                    <span className="font-bold">{formatCurrency((index + 1) * 2500000)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Thống kê dịch vụ</CardTitle>
              <CardDescription>Các dịch vụ được thực hiện nhiều nhất</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Car className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Báo cáo dịch vụ sẽ được hiển thị tại đây</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân tích khách hàng</CardTitle>
              <CardDescription>Thống kê và xu hướng khách hàng</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Báo cáo khách hàng sẽ được hiển thị tại đây</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Báo cáo tồn kho</CardTitle>
              <CardDescription>Tình hình xuất nhập tồn kho</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Báo cáo tồn kho sẽ được hiển thị tại đây</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default withAuth(ReportsPage)
