# Simple GP Mobile Development Startup Script
Write-Host "Starting GP Mobile Development Environment..." -ForegroundColor Green
Write-Host ""

# Kill existing processes on ports
Write-Host "Stopping existing processes..." -ForegroundColor Yellow
try {
    Get-Process | Where-Object {$_.ProcessName -eq "dotnet"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force -ErrorAction SilentlyContinue
} catch {}

Start-Sleep -Seconds 2

# Start API in background
Write-Host "Starting API Backend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'd:\Projects\gp-mobile-v1\src\API'; dotnet run --project GP.Mobile.API"

# Wait a moment
Start-Sleep -Seconds 3

# Start Frontend in background  
Write-Host "Starting Frontend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'd:\Projects\gp-mobile-v1\gp-web-frontend'; npm run dev"

Write-Host ""
Write-Host "Services are starting..." -ForegroundColor Green
Write-Host ""
Write-Host "API Backend: http://localhost:51552" -ForegroundColor Gray
Write-Host "Frontend: http://localhost:3000 or http://localhost:3001" -ForegroundColor Gray
Write-Host "Login Page: http://localhost:3001/login" -ForegroundColor Gray
Write-Host ""
Write-Host "Test Credentials:" -ForegroundColor Cyan
Write-Host "  Username: ngan, adv, hieu" -ForegroundColor Gray
Write-Host "  Password: admin (master password)" -ForegroundColor Gray
Write-Host "  Unit: TT" -ForegroundColor Gray
Write-Host ""
Write-Host "To stop services, run: .\stop-dev.ps1" -ForegroundColor Yellow
Write-Host ""
Write-Host "Development environment started!" -ForegroundColor Green
