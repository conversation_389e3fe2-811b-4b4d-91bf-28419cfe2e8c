using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for DonVi (Business Unit) entity
/// Implements ALL endpoints from clsDMDonVi.cs (445 lines)
/// Includes REST API and 5+ legacy method endpoints
/// Maps to DM_DonVi table with 14 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DonViController : ControllerBase
{
    private readonly IDonViService _donViService;
    private readonly ILogger<DonViController> _logger;

    public DonViController(IDonViService donViService, ILogger<DonViController> logger)
    {
        _donViService = donViService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all DonVi records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DonViListDto>>> GetAll()
    {
        try
        {
            var result = await _donViService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all DonVi records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonVi by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<DonViDto>> GetById(string khoa)
    {
        try
        {
            var result = await _donViService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonVi by business unit code
    /// </summary>
    [HttpGet("code/{ma}")]
    public async Task<ActionResult<DonViDto>> GetByCode(string ma)
    {
        try
        {
            var result = await _donViService.GetByCodeAsync(ma);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new DonVi
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateDonViDto createDto)
    {
        try
        {
            var result = await _donViService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo đơn vị");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DonVi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update DonVi
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] DonViDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _donViService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonVi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete DonVi
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _donViService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DonVi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update DonVi status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateDonViStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _donViService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonVi status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search DonVi records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<DonViListDto>>> Search([FromBody] DonViSearchDto searchDto)
    {
        try
        {
            var result = await _donViService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonVi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonVi lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<DonViLookupDto>>> GetLookup([FromQuery] string language = "vi")
    {
        try
        {
            var result = await _donViService.GetLookupAsync(language);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonVi select list
    /// </summary>
    [HttpGet("select")]
    public async Task<ActionResult<IEnumerable<DonViSelectDto>>> GetSelectList()
    {
        try
        {
            var result = await _donViService.GetSelectListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi select list");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate DonVi data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<DonViValidationDto>> Validate([FromBody] DonViValidationRequestDto request)
    {
        try
        {
            var result = await _donViService.ValidateAsync(request.Khoa, request.Ma);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating DonVi");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get DonVi contact information
    /// </summary>
    [HttpGet("{khoa}/contact")]
    public async Task<ActionResult<DonViContactDto>> GetContactInfo(string khoa)
    {
        try
        {
            var result = await _donViService.GetContactInfoAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi contact info");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _donViService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] SaveDonViRequestDto request)
    {
        try
        {
            var result = await _donViService.SaveAsync(request.Data, request.Action);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetListForSelect method endpoint
    /// </summary>
    [HttpPost("getlistforselect")]
    public async Task<ActionResult<DataTable>> GetListForSelect()
    {
        try
        {
            var result = await _donViService.GetListForSelectAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListForSelect endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetList method endpoint
    /// </summary>
    [HttpPost("getlist")]
    public async Task<ActionResult<DataTable>> GetList()
    {
        try
        {
            var result = await _donViService.GetListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList()
    {
        try
        {
            var result = await _donViService.ShowListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListField method endpoint
    /// </summary>
    [HttpPost("getlistfield")]
    public async Task<ActionResult<DataTable>> GetListField([FromBody] DonViGetListFieldRequestDto request)
    {
        try
        {
            var result = await _donViService.GetListFieldAsync(request.Fields, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for DonVi

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class DonViValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for Save method
/// </summary>
public class SaveDonViRequestDto
{
    public DonViDto Data { get; set; } = new();
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetListField method
/// </summary>
public class DonViGetListFieldRequestDto
{
    public string Fields { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

#endregion
