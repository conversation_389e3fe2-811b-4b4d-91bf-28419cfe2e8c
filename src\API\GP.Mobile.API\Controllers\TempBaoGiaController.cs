using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Controller for Temporary Quotation Data (clsTempBaoGia)
    /// Provides REST API endpoints for managing temporary quotation data
    /// Implements exact functionality from clsTempBaoGia legacy class usage
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TempBaoGiaController : ControllerBase
    {
        private readonly ITempBaoGiaService _service;
        private readonly ILogger<TempBaoGiaController> _logger;

        public TempBaoGiaController(ITempBaoGiaService service, ILogger<TempBaoGiaController> logger)
        {
            _service = service;
            _logger = logger;
        }

        /// <summary>
        /// Load temporary quotation data by quotation ID
        /// Exact implementation from legacy Load method (line 10929)
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary quotation data</returns>
        [HttpGet("{khoaBaoGia}")]
        public async Task<ActionResult<TempBaoGiaDto>> Load(string khoaBaoGia)
        {
            try
            {
                var result = await _service.LoadAsync(khoaBaoGia);
                if (result == null)
                {
                    return NotFound($"Không tìm thấy dữ liệu tạm thời cho báo giá {khoaBaoGia}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Save temporary quotation data
        /// Exact implementation from legacy Save method (line 9964)
        /// </summary>
        /// <param name="saveDto">Save data</param>
        /// <returns>Save result</returns>
        [HttpPost("save")]
        public async Task<ActionResult> Save([FromBody] SaveTempBaoGiaDto saveDto)
        {
            try
            {
                var result = await _service.SaveAsync(saveDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving temporary quotation data for {Khoa}", saveDto.Khoa);
                return StatusCode(500, "Lỗi hệ thống khi lưu dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Save request for cancellation approval
        /// Exact implementation from legacy SaveRequestDuyetHuy method (line 11320)
        /// </summary>
        /// <param name="requestDto">Request cancellation approval data</param>
        /// <returns>Save result</returns>
        [HttpPost("save-request-duyet-huy")]
        public async Task<ActionResult> SaveRequestDuyetHuy([FromBody] RequestDuyetHuyDto requestDto)
        {
            try
            {
                var result = await _service.SaveRequestDuyetHuyAsync(requestDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cancellation request for quotation {KhoaBaoGia}", requestDto.KhoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi gửi yêu cầu duyệt hủy");
            }
        }

        /// <summary>
        /// Delete temporary quotation data
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Delete result</returns>
        [HttpDelete("{khoaBaoGia}")]
        public async Task<ActionResult> Delete(string khoaBaoGia)
        {
            try
            {
                var result = await _service.DeleteAsync(khoaBaoGia);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting temporary quotation data for {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi xóa dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Check if temporary quotation data exists
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if exists</returns>
        [HttpGet("exists/{khoaBaoGia}")]
        public async Task<ActionResult<bool>> Exists(string khoaBaoGia)
        {
            try
            {
                var exists = await _service.ExistsAsync(khoaBaoGia);
                return Ok(new { exists = exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if temporary quotation data exists for {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Get temporary quotations requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of temporary quotations pending approval</returns>
        [HttpGet("pending-approvals/{donViId}")]
        public async Task<ActionResult<List<TempBaoGiaListDto>>> GetPendingApprovals(string donViId)
        {
            try
            {
                var result = await _service.GetPendingApprovalsAsync(donViId);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return StatusCode(500, "Lỗi hệ thống khi lấy danh sách chờ duyệt");
            }
        }

        /// <summary>
        /// Update cancellation approval status
        /// </summary>
        /// <param name="approvalDto">Approval data</param>
        /// <returns>Update result</returns>
        [HttpPut("cancellation-approval")]
        public async Task<ActionResult> UpdateCancellationApproval([FromBody] CancellationApprovalDto approvalDto)
        {
            try
            {
                var result = await _service.UpdateCancellationApprovalAsync(
                    approvalDto.KhoaBaoGia, 
                    approvalDto.IsDuyetHuy, 
                    approvalDto.NguoiDuyet, 
                    approvalDto.LyDo);

                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating cancellation approval for quotation {KhoaBaoGia}", approvalDto.KhoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi cập nhật trạng thái duyệt");
            }
        }

        /// <summary>
        /// Get temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Temporary data content</returns>
        [HttpGet("temp-data/{khoaBaoGia}")]
        public async Task<ActionResult<string>> GetTempData(string khoaBaoGia)
        {
            try
            {
                var tempData = await _service.GetTempDataAsync(khoaBaoGia);
                if (tempData == null)
                {
                    return NotFound("Không tìm thấy dữ liệu tạm thời cho báo giá này");
                }

                return Ok(new { tempData = tempData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temp data for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Update temporary data content
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Update result</returns>
        [HttpPut("temp-data/{khoaBaoGia}")]
        public async Task<ActionResult> UpdateTempData(string khoaBaoGia, [FromBody] UpdateTempDataDto updateDto)
        {
            try
            {
                var result = await _service.UpdateTempDataAsync(khoaBaoGia, updateDto.TempData, updateDto.NguoiCapNhat);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating temp data for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi cập nhật dữ liệu tạm thời");
            }
        }

        /// <summary>
        /// Get cancellation request details
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Cancellation request data</returns>
        [HttpGet("cancellation-request/{khoaBaoGia}")]
        public async Task<ActionResult<RequestDuyetHuyDto>> GetCancellationRequest(string khoaBaoGia)
        {
            try
            {
                var request = await _service.GetCancellationRequestAsync(khoaBaoGia);
                if (request == null)
                {
                    return NotFound("Không tìm thấy yêu cầu duyệt hủy cho báo giá này");
                }

                return Ok(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cancellation request for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy yêu cầu duyệt hủy");
            }
        }

        /// <summary>
        /// Check if quotation has pending cancellation requests
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if has pending requests</returns>
        [HttpGet("has-pending-cancellation/{khoaBaoGia}")]
        public async Task<ActionResult<bool>> HasPendingCancellationRequest(string khoaBaoGia)
        {
            try
            {
                var hasPending = await _service.HasPendingCancellationRequestAsync(khoaBaoGia);
                return Ok(new { hasPendingRequest = hasPending });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking pending cancellation request for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra yêu cầu duyệt hủy");
            }
        }

        /// <summary>
        /// Validate temporary quotation data before save
        /// </summary>
        /// <param name="saveDto">Save data to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate")]
        public async Task<ActionResult> ValidateForSave([FromBody] SaveTempBaoGiaDto saveDto)
        {
            try
            {
                var result = await _service.ValidateForSaveAsync(saveDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating save data");
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra dữ liệu");
            }
        }

        /// <summary>
        /// Validate cancellation request before save
        /// </summary>
        /// <param name="requestDto">Request data to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-cancellation-request")]
        public async Task<ActionResult> ValidateCancellationRequest([FromBody] RequestDuyetHuyDto requestDto)
        {
            try
            {
                var result = await _service.ValidateCancellationRequestAsync(requestDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating cancellation request");
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra yêu cầu");
            }
        }
    }

    /// <summary>
    /// DTO for cancellation approval
    /// </summary>
    public class CancellationApprovalDto
    {
        public string KhoaBaoGia { get; set; } = string.Empty;
        public bool IsDuyetHuy { get; set; }
        public string NguoiDuyet { get; set; } = string.Empty;
        public string LyDo { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for updating temporary data
    /// </summary>
    public class UpdateTempDataDto
    {
        public string TempData { get; set; } = string.Empty;
        public string NguoiCapNhat { get; set; } = string.Empty;
    }
}
