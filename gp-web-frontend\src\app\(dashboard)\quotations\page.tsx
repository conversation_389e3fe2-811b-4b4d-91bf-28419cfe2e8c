'use client'

import { useState, useEffect } from 'react'
import { Plus, Search, Edit, Trash2, Eye, FileText, Clock, CheckCircle, XCircle, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { withAuth } from '@/contexts/AuthContext'
import QuotationForm from '@/components/quotation/QuotationForm'
import { baoGiaSuaChuaApi, mockQuotations, type BaoGiaSuaChuaListDto } from '@/services/api'

function QuotationsPage() {
  const [quotations, setQuotations] = useState<BaoGiaSuaChuaListDto[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterServiceType, setFilterServiceType] = useState('all')
  const [selectedQuotation, setSelectedQuotation] = useState<BaoGiaSuaChuaListDto | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingQuotation, setEditingQuotation] = useState<string | undefined>(undefined)

  // Load real data from API
  useEffect(() => {
    loadQuotations()
  }, [])

  const loadQuotations = async () => {
    try {
      setLoading(true)
      setError(null)

      // Try to load from API first
      const data = await baoGiaSuaChuaApi.getAll()
      setQuotations(data)
      console.log('✅ Loaded real quotations from API:', data.length)
    } catch (apiError) {
      console.warn('⚠️ API not available, using mock data:', apiError)
      // Fallback to mock data if API is not available
      setQuotations(mockQuotations)
      setError('Đang sử dụng dữ liệu mẫu - API chưa sẵn sàng')
    } finally {
      setLoading(false)
    }
  }

  // Helper function to convert status number to text
  const getStatusText = (tinhTrang: number): string => {
    switch (tinhTrang) {
      case 0: return 'Chờ duyệt'
      case 1: return 'Đã duyệt'
      case 2: return 'Báo giá hẹn'
      case 3: return 'Đã hủy'
      default: return 'Không xác định'
    }
  }

  // Filter quotations
  const filteredQuotations = quotations.filter(quotation => {
    const matchesSearch = quotation.soChungTu.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.khachHang.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.bienSoXe.toLowerCase().includes(searchTerm.toLowerCase())

    // Convert tinhTrangBaoGia to status text for filtering
    const statusText = getStatusText(quotation.tinhTrangBaoGia)
    const matchesStatus = filterStatus === 'all' || statusText === filterStatus

    // Convert boPhanSuaChua to service type for filtering
    const serviceTypeText = quotation.boPhanSuaChua === 0 ? 'Thân vỏ + Sơn' : 'Máy móc'
    const matchesServiceType = filterServiceType === 'all' || serviceTypeText === filterServiceType

    return matchesSearch && matchesStatus && matchesServiceType
  })

  const handleViewQuotation = (quotation: any) => {
    setSelectedQuotation(quotation)
    setIsDetailDialogOpen(true)
  }

  const handleCreateQuotation = () => {
    setEditingQuotation(undefined)
    setShowForm(true)
  }

  const handleEditQuotation = (quotationId: string) => {
    setEditingQuotation(quotationId)
    setShowForm(true)
  }

  const handleSaveQuotation = (quotationData: any) => {
    console.log('Saving quotation:', quotationData)
    // Here you would call the API to save the quotation
    // For now, just close the form
    setShowForm(false)
    setEditingQuotation(undefined)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingQuotation(undefined)
  }

  const getStatusBadge = (tinhTrang: number) => {
    switch (tinhTrang) {
      case 0:
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Chờ duyệt</Badge>
      case 1:
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Đã duyệt</Badge>
      case 2:
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Báo giá hẹn</Badge>
      case 3:
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  // Show form if creating or editing
  if (showForm) {
    return (
      <div className="space-y-6">
        {/* Form Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleCancelForm}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {editingQuotation ? 'Chỉnh sửa báo giá' : 'Tạo báo giá mới'}
            </h1>
            <p className="text-muted-foreground">
              {editingQuotation ? 'Cập nhật thông tin báo giá' : 'Tạo báo giá sửa chữa mới theo logic legacy Frm_BaoGiaSuaChua.cs'}
            </p>
          </div>
        </div>

        {/* Quotation Form */}
        <QuotationForm
          quotationId={editingQuotation}
          onSave={handleSaveQuotation}
          onCancel={handleCancelForm}
        />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quản lý báo giá</h1>
            <p className="text-muted-foreground">Đang tải dữ liệu từ cơ sở dữ liệu...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Đang tải báo giá...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản lý báo giá</h1>
          <p className="text-muted-foreground">
            Quản lý báo giá sửa chữa và bảo dưỡng xe
            {error && <span className="text-orange-600 ml-2">({error})</span>}
          </p>
        </div>
        <Button onClick={handleCreateQuotation}>
          <Plus className="mr-2 h-4 w-4" />
          Tạo báo giá mới
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng báo giá</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotations.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chờ duyệt</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quotations.filter(q => q.tinhTrangBaoGia === 0).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đã duyệt</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quotations.filter(q => q.tinhTrangBaoGia === 1).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng giá trị</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(quotations.reduce((sum, q) => sum + q.tongThanhToan, 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo số báo giá, khách hàng, biển số..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="Chờ duyệt">Chờ duyệt</SelectItem>
                <SelectItem value="Đã duyệt">Đã duyệt</SelectItem>
                <SelectItem value="Báo giá hẹn">Báo giá hẹn</SelectItem>
                <SelectItem value="Đã hủy">Đã hủy</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterServiceType} onValueChange={setFilterServiceType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Bộ phận sửa chữa" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả bộ phận</SelectItem>
                <SelectItem value="Thân vỏ + Sơn">Thân vỏ + Sơn</SelectItem>
                <SelectItem value="Máy móc">Máy móc</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quotation List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách báo giá</CardTitle>
          <CardDescription>
            Tổng cộng {filteredQuotations.length} báo giá
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Số báo giá</TableHead>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Xe</TableHead>
                  <TableHead>Loại dịch vụ</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Tổng tiền</TableHead>
                  <TableHead>Ngày tạo</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuotations.map((quotation) => (
                  <TableRow key={quotation.khoa}>
                    <TableCell className="font-medium">{quotation.soChungTu}</TableCell>
                    <TableCell>{quotation.khachHang}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{quotation.bienSoXe}</div>
                        <div className="text-sm text-muted-foreground">{quotation.loaiXe}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {quotation.boPhanSuaChua === 0 ? 'Thân vỏ + Sơn' : 'Máy móc'}
                      </Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(quotation.tinhTrangBaoGia)}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(quotation.tongThanhToan)}</TableCell>
                    <TableCell>
                      {quotation.ngayChungTu.replace(/(\d{4})(\d{2})(\d{2})/, '$3/$2/$1')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewQuotation(quotation)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditQuotation(quotation.khoa)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Quotation Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Chi tiết báo giá</DialogTitle>
            <DialogDescription>
              Báo giá số {selectedQuotation?.soChungTu}
            </DialogDescription>
          </DialogHeader>
          {selectedQuotation && (
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">Thông tin chung</TabsTrigger>
                <TabsTrigger value="items">Chi tiết hạng mục</TabsTrigger>
                <TabsTrigger value="history">Lịch sử thay đổi</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Số báo giá</label>
                    <p className="text-sm text-muted-foreground">{selectedQuotation.soChungTu}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Khách hàng</label>
                    <p className="text-sm text-muted-foreground">{selectedQuotation.khachHang}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Xe</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedQuotation.bienSoXe} - {selectedQuotation.loaiXe}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Loại dịch vụ</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedQuotation.boPhanSuaChua === 0 ? 'Thân vỏ + Sơn' : 'Máy móc'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Trạng thái</label>
                    <div className="mt-1">{getStatusBadge(selectedQuotation.tinhTrangBaoGia)}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tổng tiền hàng</label>
                    <p className="text-sm text-muted-foreground font-bold">
                      {formatCurrency(selectedQuotation.tongTienHang)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tiền thuế</label>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(selectedQuotation.tienThue)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tiền chiết khấu</label>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(selectedQuotation.tienChietKhau)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tổng thanh toán</label>
                    <p className="text-sm text-muted-foreground font-bold text-lg">
                      {formatCurrency(selectedQuotation.tongThanhToan)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Ngày tạo</label>
                    <p className="text-sm text-muted-foreground">
                      {selectedQuotation.ngayChungTu.replace(/(\d{4})(\d{2})(\d{2})/, '$3/$2/$1')}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Nhân viên tạo</label>
                    <p className="text-sm text-muted-foreground">{selectedQuotation.nhanVienTao}</p>
                  </div>
                  <div className="col-span-2">
                    <label className="text-sm font-medium">Diễn giải</label>
                    <p className="text-sm text-muted-foreground">{selectedQuotation.dienGiai}</p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="items" className="space-y-4">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Chi tiết hạng mục sẽ được tải từ API BaoGiaSuaChuaChiTiet
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Mã báo giá: {selectedQuotation.khoa}
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => console.log('Load details for:', selectedQuotation.khoa)}
                  >
                    Tải chi tiết hạng mục
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="history" className="space-y-4">
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Lịch sử thay đổi báo giá sẽ được hiển thị tại đây
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Người tạo: {selectedQuotation.nhanVienTao}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Ngày tạo: {selectedQuotation.ngayTao}
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default withAuth(QuotationsPage)
