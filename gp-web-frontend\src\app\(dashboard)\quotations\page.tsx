'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Plus, Search, Edit, Trash2, Eye, FileText, Clock, CheckCircle, XCircle, ArrowLeft, ChevronLeft, ChevronRight } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { Label } from '@/components/ui/label'
import { withAuth } from '@/contexts/AuthContext'
import QuotationForm from '@/components/quotation/QuotationForm'
import { baoGiaSuaChuaApi, mockQuotations, type BaoGiaSuaChuaListDto, type PaginatedResponseDto, type BaoGiaSuaChuaPaginationDto } from '@/services/api'

function QuotationsPage() {
  const router = useRouter()

  // Pagination state
  const [paginatedData, setPaginatedData] = useState<PaginatedResponseDto<BaoGiaSuaChuaListDto> | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filter state
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterServiceType, setFilterServiceType] = useState('all')

  // Set default date range: 1 month before current day to current day
  const getCurrentDate = () => new Date().toISOString().split('T')[0]
  const getOneMonthAgo = () => {
    const date = new Date()
    date.setMonth(date.getMonth() - 1)
    return date.toISOString().split('T')[0]
  }

  const [startDate, setStartDate] = useState(getOneMonthAgo())
  const [endDate, setEndDate] = useState(getCurrentDate())

  // Dialog state
  const [selectedQuotation, setSelectedQuotation] = useState<BaoGiaSuaChuaListDto | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingQuotation, setEditingQuotation] = useState<string | undefined>(undefined)

  // Load paginated data from API
  useEffect(() => {
    loadQuotations()
  }, [currentPage, pageSize, startDate, endDate, filterStatus, filterServiceType])

  const loadQuotations = async () => {
    try {
      setLoading(true)
      setError(null)

      // Build pagination request
      const request: BaoGiaSuaChuaPaginationDto = {
        page: currentPage,
        pageSize: pageSize,
        sortBy: 'NgayChungTu',
        sortDirection: 'desc'
      }

      // Add date range filter
      if (startDate || endDate) {
        request.dateRange = {
          startDate: startDate || undefined,
          endDate: endDate || undefined
        }
      }

      // Add status filter
      if (filterStatus !== 'all') {
        const statusMap: { [key: string]: number } = {
          'Báo giá tạm': 0,
          'Báo giá thực hiện': 1,
          'Báo giá hẹn': 2,
          'Đã hủy': 3
        }
        request.tinhTrangBaoGia = statusMap[filterStatus]
      }

      // Add service type filter
      if (filterServiceType !== 'all') {
        request.boPhanSuaChua = filterServiceType === 'Đồng sơn' ? 0 : 1
      }

      // Try to load from API first
      const data = await baoGiaSuaChuaApi.getPaginated(request)
      setPaginatedData(data)
      console.log('✅ Loaded paginated quotations from API:', data.totalCount, 'total,', data.data.length, 'on page', data.page)
    } catch (apiError) {
      console.warn('⚠️ API not available, using mock data:', apiError)
      // Fallback to mock data if API is not available
      const mockPaginatedData: PaginatedResponseDto<BaoGiaSuaChuaListDto> = {
        page: currentPage,
        pageSize: pageSize,
        totalCount: mockQuotations.length,
        totalPages: Math.ceil(mockQuotations.length / pageSize),
        hasPreviousPage: currentPage > 1,
        hasNextPage: currentPage < Math.ceil(mockQuotations.length / pageSize),
        data: mockQuotations.slice((currentPage - 1) * pageSize, currentPage * pageSize),
        sortBy: 'NgayChungTu',
        sortDirection: 'desc'
      }
      setPaginatedData(mockPaginatedData)
      setError('Đang sử dụng dữ liệu mẫu - API chưa sẵn sàng')
    } finally {
      setLoading(false)
    }
  }

  // Helper function to convert status number to text
  const getStatusText = (tinhTrang: number): string => {
    switch (tinhTrang) {
      case 0: return 'Báo giá tạm'
      case 1: return 'Báo giá thực hiện'
      case 2: return 'Báo giá hẹn'
      case 3: return 'Đã hủy'
      default: return 'Không xác định'
    }
  }

  // Get current quotations from paginated data
  const quotations = paginatedData?.data || []

  // Client-side search filtering (for current page only)
  const filteredQuotations = quotations.filter(quotation => {
    if (!searchTerm) return true

    const matchesSearch = quotation.soChungTu.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.khachHang.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.bienSoXe.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesSearch
  })

  const handleViewQuotation = (quotation: BaoGiaSuaChuaListDto) => {
    // Simple summary view - show basic info in dialog
    setSelectedQuotation(quotation)
    setIsDetailDialogOpen(true)
  }

  const handleEditQuotation = (quotation: BaoGiaSuaChuaListDto) => {
    // Navigate to comprehensive editing page
    router.push(`/quotations/${quotation.khoa}`)
  }

  const handleCreateQuotation = () => {
    setEditingQuotation(undefined)
    setShowForm(true)
  }

  const handleSaveQuotation = (quotationData: any) => {
    console.log('Saving quotation:', quotationData)
    // Here you would call the API to save the quotation
    // For now, just close the form
    setShowForm(false)
    setEditingQuotation(undefined)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingQuotation(undefined)
  }

  const getStatusBadge = (tinhTrang: number) => {
    switch (tinhTrang) {
      case 0:
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Báo giá tạm</Badge>
      case 1:
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Báo giá thực hiện</Badge>
      case 2:
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Báo giá hẹn</Badge>
      case 3:
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Đã hủy</Badge>
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  // Show form if creating or editing
  if (showForm) {
    return (
      <div className="space-y-6">
        {/* Form Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleCancelForm}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {editingQuotation ? 'Chỉnh sửa báo giá' : 'Tạo báo giá mới'}
            </h1>
            <p className="text-muted-foreground">
              {editingQuotation ? 'Cập nhật thông tin báo giá' : 'Tạo báo giá sửa chữa mới theo logic legacy Frm_BaoGiaSuaChua.cs'}
            </p>
          </div>
        </div>

        {/* Quotation Form */}
        <QuotationForm
          quotationId={editingQuotation}
          onSave={handleSaveQuotation}
          onCancel={handleCancelForm}
        />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quản lý báo giá</h1>
            <p className="text-muted-foreground">Đang tải dữ liệu từ cơ sở dữ liệu...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Đang tải báo giá...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản lý báo giá</h1>
          <p className="text-muted-foreground">
            Quản lý báo giá sửa chữa và bảo dưỡng xe
            {error && <span className="text-orange-600 ml-2">({error})</span>}
          </p>
        </div>
        <Button onClick={handleCreateQuotation}>
          <Plus className="mr-2 h-4 w-4" />
          Tạo báo giá mới
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng báo giá</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paginatedData?.totalCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              Trang {paginatedData?.page || 1} / {paginatedData?.totalPages || 1}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chờ duyệt</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quotations.filter(q => q.tinhTrangBaoGia === 0).length}
            </div>
            <p className="text-xs text-muted-foreground">Trang hiện tại</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đã duyệt</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {quotations.filter(q => q.tinhTrangBaoGia === 1).length}
            </div>
            <p className="text-xs text-muted-foreground">Trang hiện tại</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng giá trị</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(quotations.reduce((sum, q) => sum + q.tongThanhToan, 0))}
            </div>
            <p className="text-xs text-muted-foreground">Trang hiện tại</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
          <CardDescription>
            Lọc theo ngày chứng từ, trạng thái và bộ phận sửa chữa
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Date Range Filter */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate" className="text-sm font-medium">Từ ngày</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="endDate" className="text-sm font-medium">Đến ngày</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Search and Status Filters */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm theo số báo giá, khách hàng, biển số..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả trạng thái</SelectItem>
                  <SelectItem value="Báo giá tạm">Báo giá tạm</SelectItem>
                  <SelectItem value="Báo giá thực hiện">Báo giá thực hiện</SelectItem>
                  <SelectItem value="Báo giá hẹn">Báo giá hẹn</SelectItem>
                  <SelectItem value="Đã hủy">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterServiceType} onValueChange={setFilterServiceType}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Loại hình sửa chữa" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Loại hình sửa chữa</SelectItem>
                  <SelectItem value="Đồng sơn">Đồng sơn</SelectItem>
                  <SelectItem value="Gầm máy">Gầm máy</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quotation List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách báo giá</CardTitle>
          <CardDescription>
            Tổng cộng {filteredQuotations.length} báo giá
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Xe</TableHead>
                  <TableHead>Số báo giá</TableHead>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Tổng tiền</TableHead>
                  <TableHead>Ngày tạo</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuotations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="text-muted-foreground">
                        {loading ? 'Đang tải dữ liệu...' : 'Không có báo giá nào'}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredQuotations.map((quotation) => (
                    <TableRow key={quotation.khoa}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{quotation.bienSoXe}</div>
                          <div className="text-sm text-muted-foreground">{quotation.loaiXe}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{quotation.soChungTu}</TableCell>
                      <TableCell>
                        <div className="break-words max-w-xs">
                          {quotation.khachHang}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(quotation.tinhTrangBaoGia)}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(quotation.tongThanhToan)}</TableCell>
                      <TableCell>
                        {quotation.ngayChungTu.replace(/(\d{4})(\d{2})(\d{2})/, '$3/$2/$1')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewQuotation(quotation)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditQuotation(quotation)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          {paginatedData && paginatedData.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <p className="text-sm text-muted-foreground">
                  Hiển thị {((paginatedData.page - 1) * paginatedData.pageSize) + 1} - {Math.min(paginatedData.page * paginatedData.pageSize, paginatedData.totalCount)}
                  trong tổng số {paginatedData.totalCount} báo giá
                </p>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="pageSize" className="text-sm">Hiển thị:</Label>
                  <Select value={pageSize.toString()} onValueChange={(value) => {
                    setPageSize(parseInt(value))
                    setCurrentPage(1)
                  }}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={!paginatedData.hasPreviousPage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {
                      const pageNum = Math.max(1, Math.min(paginatedData.totalPages - 4, currentPage - 2)) + i
                      return (
                        <Button
                          key={pageNum}
                          variant={pageNum === currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      )
                    })}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(paginatedData.totalPages, currentPage + 1))}
                    disabled={!paginatedData.hasNextPage}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quotation Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-y-auto p-0 sm:p-6">
          <div className="p-6 pb-0">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-gray-600" />
                Tóm tắt báo giá
              </DialogTitle>
              <DialogDescription>
                Báo giá số {selectedQuotation?.soChungTu} • Xem nhanh thông tin cơ bản
              </DialogDescription>
            </DialogHeader>
          </div>
          {selectedQuotation && (
            <div className="px-6 pb-6">
              {/* SIMPLE SUMMARY VIEW */}
              <div className="space-y-6">
                <div className="text-center py-4">
                  <Badge variant="outline" className="mb-4">Xem nhanh</Badge>
                  <p className="text-muted-foreground">
                    Nhấn nút <Edit className="inline h-4 w-4 mx-1" /> trong danh sách để mở chế độ chỉnh sửa đầy đủ
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Thông tin báo giá</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Số báo giá</label>
                        <p className="text-lg font-semibold">{selectedQuotation.soChungTu}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Khách hàng</label>
                        <p className="font-medium">{selectedQuotation.khachHang}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Trạng thái</label>
                        <div className="mt-1">{getStatusBadge(selectedQuotation.tinhTrangBaoGia)}</div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Thông tin xe</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Biển số xe</label>
                        <p className="text-lg font-mono font-bold">{selectedQuotation.bienSoXe}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Loại xe</label>
                        <p className="font-medium">{selectedQuotation.loaiXe}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Loại hình sửa chữa</label>
                        <p className="font-medium">
                          {selectedQuotation.boPhanSuaChua === 0 ? 'Đồng sơn' : 'Gầm máy'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="font-semibold text-lg mb-4">Tổng quan tài chính</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-sm text-muted-foreground">Tổng tiền hàng</div>
                      <div className="text-xl font-bold text-blue-600">
                        {formatCurrency(selectedQuotation.tongTienHang)}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-sm text-muted-foreground">Chiết khấu</div>
                      <div className="text-xl font-bold text-red-600">
                        -{formatCurrency(selectedQuotation.tienChietKhau)}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-sm text-muted-foreground">Tổng thanh toán</div>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(selectedQuotation.tongThanhToan)}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center pt-4 border-t">
                  <Button
                    onClick={() => handleEditQuotation(selectedQuotation)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Mở trang chỉnh sửa đầy đủ
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default withAuth(QuotationsPage)
