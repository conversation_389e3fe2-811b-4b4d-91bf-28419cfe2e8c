using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for DonViTinh (Unit of Measure) repository
/// Defines ALL methods from clsDMDonViTinh.cs (504 lines)
/// Includes both legacy methods and modern API methods
/// </summary>
public interface IDonViTinhRepository
{
    #region Legacy Methods (Exact mapping from clsDMDonViTinh.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> LoadNameAsync(string tenViet);
    Task<bool> SaveAsync(DonViTinhDto dto, string action);
    Task<bool> DeleteAsync(string khoa);
    
    // List and search methods
    Task<DataTable> GetListAsync();
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    
    // Utility methods
    Task<bool> IsDuplicateCodeAsync(string khoa, string tenViet);
    Task<bool> WasUsedAsync(string khoa);
    Task<string> SearchByNameAsync(string name);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<DonViTinhListDto>> GetAllAsync();
    Task<DonViTinhDto?> GetByIdAsync(string khoa);
    Task<string> CreateAsync(CreateDonViTinhDto createDto);
    Task<bool> UpdateAsync(DonViTinhDto dto);
    Task<bool> UpdateStatusAsync(UpdateDonViTinhStatusDto statusDto);
    Task<IEnumerable<DonViTinhListDto>> SearchAsync(DonViTinhSearchDto searchDto);
    Task<IEnumerable<DonViTinhLookupDto>> GetLookupAsync(string language = "vi");
    Task<DonViTinhValidationDto> ValidateAsync(string khoa, string tenViet);
    
    #endregion
}

/// <summary>
/// Complete Repository for DonViTinh entity
/// Implements ALL methods from clsDMDonViTinh.cs (504 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class DonViTinhRepository : IDonViTinhRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<DonViTinhRepository> _logger;

    public DonViTinhRepository(IDbConnection connection, ILogger<DonViTinhRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 185)
            string commandText = "SELECT * FROM DM_DonViTinh WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<DonViTinhDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DonViTinh: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> LoadNameAsync(string tenViet)
    {
        try
        {
            // Exact SQL from legacy LoadName method (line 153)
            string commandText = "SELECT * FROM DM_DonViTinh WHERE Lower(RTRIM(TenViet)) = @TenViet";
            var result = await _connection.QueryFirstOrDefaultAsync<DonViTinhDto>(
                commandText, new { TenViet = tenViet.Trim().ToLower() });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DonViTinh by name: {TenViet}", tenViet);
            return false;
        }
    }

    public async Task<bool> SaveAsync(DonViTinhDto dto, string action)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 227)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@TenViet", dto.TenViet);
            parameters.Add("@TenAnh", dto.TenAnh);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@Active", dto.Active);
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@NgayCapNhat", dto.NgayCapNhat);
            parameters.Add("@Send", dto.Send);
            parameters.Add("@pAction", action);
            parameters.Add("@pError", dbType: DbType.Int16, direction: ParameterDirection.Output);

            await _connection.ExecuteAsync("sp_DM_DonViTinh", parameters, commandType: CommandType.StoredProcedure);
            
            var errorCode = parameters.Get<int>("@pError");
            return errorCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving DonViTinh: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Delete method (line 246)
            string commandText = "DELETE FROM DM_DonViTinh WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DonViTinh: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<DataTable> GetListAsync()
    {
        try
        {
            // Exact SQL from legacy GetList method (line 269)
            string commandText = "SELECT Khoa, TenViet, TenAnh, DienGiai FROM DM_DonViTinh ORDER BY TenViet";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Exact SQL from legacy ShowList method (line 295)
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                whereClause = " AND " + condition;
            }

            string commandText = $"SELECT Khoa, RTRIM(TenViet) as Ten FROM DM_DonViTinh WHERE Active = 1 {whereClause} ORDER BY TenViet";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing DonViTinh list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "")
    {
        try
        {
            // Exact SQL from legacy GetListField method (line 334)
            fields = fields.Replace("|", ", ");
            
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(conditions))
            {
                whereClause = " WHERE " + conditions;
            }

            string orderClause = "";
            if (!string.IsNullOrWhiteSpace(order))
            {
                orderClause = " ORDER BY " + order;
            }

            string commandText = $"SELECT {fields} FROM DM_DonViTinh {whereClause} {orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh list by field");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Exact SQL from legacy ShowListByField method (line 444)
            fieldList = fieldList.Replace("|", ",");
            
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(conditions))
            {
                whereClause = " WHERE " + conditions;
            }

            string orderClause = "";
            if (!string.IsNullOrWhiteSpace(order))
            {
                orderClause = " ORDER BY " + order;
            }

            string commandText = $"SELECT {fieldList} FROM DM_DonViTinh {whereClause} {orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing DonViTinh list by field");
            return new DataTable();
        }
    }

    public async Task<bool> IsDuplicateCodeAsync(string khoa, string tenViet)
    {
        try
        {
            // Exact SQL from legacy isDupplicateCode method (line 370)
            string commandText = "SELECT * FROM DM_DonViTinh WHERE RTRIM(TenViet) = @TenViet AND RTRIM(Khoa) <> @Khoa";
            var result = await _connection.QueryAsync(commandText, new { TenViet = tenViet.Trim(), Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy wasUsed method (line 405)
            string commandText = "SELECT * FROM DM_HangHoa WHERE KhoaDonViTinh = @Khoa";
            var result = await _connection.QueryAsync(commandText, new { Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if DonViTinh was used");
            return true; // Return true to be safe
        }
    }

    public async Task<string> SearchByNameAsync(string name)
    {
        try
        {
            // Exact SQL from legacy SearchByName method (line 468)
            string commandText = "SELECT Khoa FROM DM_DonViTinh WHERE UPPER(RTRIM(TenViet)) = @Name";
            var result = await _connection.QueryFirstOrDefaultAsync<string>(
                commandText, new { Name = name.Trim().ToUpper() });
            return result?.Trim() ?? "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonViTinh by name");
            return "";
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<DonViTinhListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT Khoa, TenViet, TenAnh, DienGiai, Active
                FROM DM_DonViTinh
                WHERE Active = 1
                ORDER BY TenViet";

            return await _connection.QueryAsync<DonViTinhListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all DonViTinh records");
            return new List<DonViTinhListDto>();
        }
    }

    public async Task<DonViTinhDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_DonViTinh WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<DonViTinhDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateDonViTinhDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new DonViTinhDto
            {
                Khoa = khoa,
                TenViet = createDto.TenViet,
                TenAnh = createDto.TenAnh,
                DienGiai = createDto.DienGiai,
                Active = createDto.Active,
                NgayCapNhat = DateTime.Now.ToString("yyyyMMdd"),
                Send = 0
            };

            var success = await SaveAsync(dto, "INSERT");
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DonViTinh");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(DonViTinhDto dto)
    {
        try
        {
            dto.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
            return await SaveAsync(dto, "UPDATE");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonViTinh: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateDonViTinhStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE DM_DonViTinh
                SET Active = @Active,
                    KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat,
                    NgayCapNhat = @NgayCapNhat
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                statusDto.Khoa,
                statusDto.Active,
                statusDto.KhoaNhanVienCapNhat,
                NgayCapNhat = DateTime.Now.ToString("yyyyMMdd")
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonViTinh status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<DonViTinhListDto>> SearchAsync(DonViTinhSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.TenViet))
            {
                conditions.Add("TenViet LIKE @TenViet");
                parameters.Add("@TenViet", $"%{searchDto.TenViet}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenAnh))
            {
                conditions.Add("TenAnh LIKE @TenAnh");
                parameters.Add("@TenAnh", $"%{searchDto.TenAnh}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DienGiai))
            {
                conditions.Add("DienGiai LIKE @DienGiai");
                parameters.Add("@DienGiai", $"%{searchDto.DienGiai}%");
            }

            if (searchDto.Active.HasValue)
            {
                conditions.Add("Active = @Active");
                parameters.Add("@Active", searchDto.Active.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaNhanVienCapNhat))
            {
                conditions.Add("KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat");
                parameters.Add("@KhoaNhanVienCapNhat", searchDto.KhoaNhanVienCapNhat);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.NgayCapNhatFrom))
            {
                conditions.Add("NgayCapNhat >= @NgayCapNhatFrom");
                parameters.Add("@NgayCapNhatFrom", searchDto.NgayCapNhatFrom);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.NgayCapNhatTo))
            {
                conditions.Add("NgayCapNhat <= @NgayCapNhatTo");
                parameters.Add("@NgayCapNhatTo", searchDto.NgayCapNhatTo);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT Khoa, TenViet, TenAnh, DienGiai, Active
                FROM DM_DonViTinh
                {whereClause}
                ORDER BY TenViet";

            return await _connection.QueryAsync<DonViTinhListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonViTinh");
            return new List<DonViTinhListDto>();
        }
    }

    public async Task<IEnumerable<DonViTinhLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            string nameField = language.ToLower() == "en" ? "TenAnh" : "TenViet";
            string commandText = $@"
                SELECT Khoa, RTRIM({nameField}) as Ten
                FROM DM_DonViTinh
                WHERE Active = 1
                ORDER BY {nameField}";

            return await _connection.QueryAsync<DonViTinhLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonViTinh lookup");
            return new List<DonViTinhLookupDto>();
        }
    }

    public async Task<DonViTinhValidationDto> ValidateAsync(string khoa, string tenViet)
    {
        try
        {
            var result = new DonViTinhValidationDto
            {
                Khoa = khoa,
                TenViet = tenViet
            };

            // Check if duplicate
            result.IsDuplicate = await IsDuplicateCodeAsync(khoa, tenViet);

            // Check if used (only if not creating new)
            if (!string.IsNullOrEmpty(khoa))
            {
                result.IsUsed = await WasUsedAsync(khoa);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating DonViTinh");
            return new DonViTinhValidationDto { Khoa = khoa, TenViet = tenViet };
        }
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    #endregion
}
