using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for NhanVien (Employees) entity
/// Implements ALL endpoints from clsDMNhanVien.cs (468 lines)
/// Includes REST API and 9+ legacy method endpoints
/// Maps to DM_NhanVien table with 8 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service operations and employee management
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class NhanVienController : ControllerBase
{
    private readonly INhanVienService _nhanVienService;
    private readonly ILogger<NhanVienController> _logger;

    public NhanVienController(INhanVienService nhanVienService, ILogger<NhanVienController> logger)
    {
        _nhanVienService = nhanVienService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all employees
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<NhanVienListDto>>> GetAll()
    {
        try
        {
            var result = await _nhanVienService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all employees");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employee by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<NhanVienDto>> GetById(string khoa)
    {
        try
        {
            var result = await _nhanVienService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employee by code
    /// </summary>
    [HttpGet("code/{maNhanVien}")]
    public async Task<ActionResult<NhanVienDto>> GetByCode(string maNhanVien)
    {
        try
        {
            var result = await _nhanVienService.GetByCodeAsync(maNhanVien);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employee by name
    /// </summary>
    [HttpGet("name/{tenViet}")]
    public async Task<ActionResult<NhanVienDto>> GetByName(string tenViet)
    {
        try
        {
            var result = await _nhanVienService.GetByNameAsync(tenViet);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee by name");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new employee
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateNhanVienDto createDto)
    {
        try
        {
            var result = await _nhanVienService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo nhân viên");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating employee");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update employee
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] NhanVienDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhanVienService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating employee");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete employee
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _nhanVienService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting employee");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update employee status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateNhanVienStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _nhanVienService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating employee status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search employees
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<NhanVienListDto>>> Search([FromBody] NhanVienSearchDto searchDto)
    {
        try
        {
            var result = await _nhanVienService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching employees");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employee lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<NhanVienLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _nhanVienService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate employee data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<NhanVienValidationDto>> Validate([FromBody] NhanVienValidationRequestDto request)
    {
        try
        {
            var result = await _nhanVienService.ValidateAsync(request.Khoa, request.MaNhanVien, request.TenViet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating employee");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search employee by code (modern)
    /// </summary>
    [HttpPost("search-by-code")]
    public async Task<ActionResult<NhanVienSearchByCodeDto>> SearchByCodeModern([FromBody] NhanVienSearchByCodeRequestDto request)
    {
        try
        {
            var result = await _nhanVienService.SearchByCodeModernAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching employee by code");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive technicians
    /// </summary>
    [HttpGet("automotive-technicians")]
    public async Task<ActionResult<IEnumerable<AutomotiveTechnicianDto>>> GetAutomotiveTechnicians()
    {
        try
        {
            var result = await _nhanVienService.GetAutomotiveTechniciansAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive technicians");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get service advisors
    /// </summary>
    [HttpGet("service-advisors")]
    public async Task<ActionResult<IEnumerable<ServiceAdvisorDto>>> GetServiceAdvisors()
    {
        try
        {
            var result = await _nhanVienService.GetServiceAdvisorsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service advisors");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employees with statistics
    /// </summary>
    [HttpGet("with-stats")]
    public async Task<ActionResult<IEnumerable<NhanVienWithStatsDto>>> GetEmployeesWithStats()
    {
        try
        {
            var result = await _nhanVienService.GetEmployeesWithStatsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employees with stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get employee groups
    /// </summary>
    [HttpGet("groups")]
    public async Task<ActionResult<IEnumerable<EmployeeGroupDto>>> GetEmployeeGroups()
    {
        try
        {
            var result = await _nhanVienService.GetEmployeeGroupsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee groups");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _nhanVienService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] NhanVienDto dto)
    {
        try
        {
            var result = await _nhanVienService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _nhanVienService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ShowList method endpoint
    /// </summary>
    [HttpPost("showlist")]
    public async Task<ActionResult<DataTable>> ShowList([FromBody] NhanVienShowListRequestDto? request = null)
    {
        try
        {
            var conditions = request?.Conditions ?? "";
            var result = await _nhanVienService.ShowListAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListAllNhanVien method endpoint
    /// </summary>
    [HttpPost("getlistallnhanvien")]
    public async Task<ActionResult<DataTable>> GetListAllNhanVien([FromBody] NhanVienShowListRequestDto? request = null)
    {
        try
        {
            var conditions = request?.Conditions ?? "";
            var result = await _nhanVienService.GetListAllNhanVienAsync(conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListAllNhanVien endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowAllList method endpoint
    /// </summary>
    [HttpPost("showalllist")]
    public async Task<ActionResult<DataTable>> ShowAllList()
    {
        try
        {
            var result = await _nhanVienService.ShowAllListAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowAllList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] NhanVienShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _nhanVienService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SearchByCode method endpoint
    /// </summary>
    [HttpPost("searchbycode")]
    public async Task<ActionResult<string>> SearchByCode([FromBody] NhanVienSearchByCodeLegacyRequestDto request)
    {
        try
        {
            var result = await _nhanVienService.SearchByCodeAsync(request.Code, request.Conditions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchByCode endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy TrungMa method endpoint
    /// </summary>
    [HttpPost("trungma")]
    public async Task<ActionResult<bool>> TrungMa([FromBody] NhanVienTrungMaRequestDto request)
    {
        try
        {
            var result = await _nhanVienService.TrungMaAsync(request.Ma, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TrungMa endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy WasUsed method endpoint
    /// </summary>
    [HttpPost("wasused")]
    public async Task<ActionResult<bool>> WasUsed([FromBody] string khoa)
    {
        try
        {
            var result = await _nhanVienService.WasUsedAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in WasUsed endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for NhanVien

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class NhanVienValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for search by code (modern) endpoint
/// </summary>
public class NhanVienSearchByCodeRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowList method
/// </summary>
public class NhanVienShowListRequestDto
{
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class NhanVienShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SearchByCode method (legacy)
/// </summary>
public class NhanVienSearchByCodeLegacyRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for TrungMa method
/// </summary>
public class NhanVienTrungMaRequestDto
{
    public string Ma { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

#endregion
