using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for BaoDuong (Maintenance Service) repository
/// Defines ALL methods from clsDMBaoDuong.cs (364 lines)
/// Includes both legacy methods and modern API methods
/// AUTOMOTIVE FOCUSED - Essential for maintenance service templates linking vehicle types with service types
/// </summary>
public interface IBaoDuongRepository
{
    #region Legacy Methods (Exact mapping from clsDMBaoDuong.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(BaoDuongDto dto);
    Task<bool> DelDataAsync(string khoa);
    Task SetBlankAsync();
    
    // Business logic methods
    Task<bool> IsDupllicateAsync(string soChungTu, string khoa);
    Task<bool> CheckExistsAsync(string khoaLoaiXe, string khoaLoaiDichVu);
    Task ClearTempAsync(string khoa);
    
    // List and data retrieval methods
    Task<DataTable> GetListDMBDAsync(string condition = "");
    Task<DataTable> GetDetailsDMBDAsync(string khoaBaoGia);
    Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiDichVu);
    Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiXe, string khoaLoaiDichVu);
    Task<DataTable> GetDataPrintYeuCauAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<BaoDuongListDto>> GetAllAsync();
    Task<BaoDuongDto?> GetByIdAsync(string khoa);
    Task<BaoDuongDto?> GetByVehicleAndServiceTypeAsync(string khoaLoaiXe, string khoaLoaiDichVu);
    Task<string> CreateAsync(CreateBaoDuongDto createDto);
    Task<bool> UpdateAsync(BaoDuongDto dto);
    Task<bool> UpdateStatusAsync(UpdateBaoDuongStatusDto statusDto);
    Task<IEnumerable<BaoDuongListDto>> SearchAsync(BaoDuongSearchDto searchDto);
    Task<IEnumerable<BaoDuongLookupDto>> GetLookupAsync();
    Task<BaoDuongValidationDto> ValidateAsync(string khoa, string khoaLoaiXe, string khoaLoaiDichVu);
    Task<IEnumerable<BaoDuongChiTietDto>> GetMaintenanceDetailsAsync(string khoa);
    Task<IEnumerable<MaintenanceCategoryDto>> GetMaintenanceCategoriesAsync();
    Task<IEnumerable<BaoDuongWithDetailsDto>> GetMaintenanceWithDetailsAsync();
    Task<BaoDuongStatsDto?> GetMaintenanceStatsAsync(string khoa);
    Task<IEnumerable<BaoDuongPrintDto>> GetPrintDataAsync(string khoa);
    
    #endregion
}

/// <summary>
/// Complete Repository for BaoDuong entity
/// Implements ALL methods from clsDMBaoDuong.cs (364 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for maintenance service templates linking vehicle types with service types
/// </summary>
public class BaoDuongRepository : IBaoDuongRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<BaoDuongRepository> _logger;

    public BaoDuongRepository(IDbConnection connection, ILogger<BaoDuongRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 119)
            string commandText = "SELECT * FROM DM_DMBaoDuong WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<BaoDuongDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading BaoDuong: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> SaveAsync(BaoDuongDto dto)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 143)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@KhoaLoaiXe", dto.KhoaLoaiXe);
            parameters.Add("@KhoaLoaiDichVu", dto.KhoaLoaiDichVu);
            parameters.Add("@DienGiaiXacNhanThanhToan", dto.DienGiai); // Note: Legacy uses this parameter name
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@NgayCapNhat", dto.NgayCapNhat);
            parameters.Add("@pError", dbType: DbType.Double, direction: ParameterDirection.Output);

            await _connection.ExecuteAsync("SC_sp_DMBaoDuong", parameters, commandType: CommandType.StoredProcedure);
            
            var errorCode = parameters.Get<double>("@pError");
            return errorCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving BaoDuong: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy DelData method (line 273)
            // First delete details
            string commandText = "DELETE FROM DM_DMBaoDuongChiTiet WHERE KhoaDMBD = @Khoa";
            var detailsDeleted = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
            
            if (detailsDeleted >= 0) // Allow 0 details
            {
                // Then delete master
                commandText = "DELETE FROM DM_DMBaoDuong WHERE Khoa = @Khoa";
                var masterDeleted = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
                return masterDeleted > 0;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting BaoDuong: {Khoa}", khoa);
            return false;
        }
    }

    public async Task SetBlankAsync()
    {
        try
        {
            // Legacy SetBlank method (line 305) - This is a local operation in legacy
            // In modern implementation, this would be handled by creating new DTOs
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SetBlank");
        }
    }

    public async Task<bool> IsDupllicateAsync(string soChungTu, string khoa)
    {
        try
        {
            // Exact SQL from legacy IsDupllicate method (line 168)
            // Note: Legacy method seems to have incorrect logic, but maintaining exact compatibility
            string commandText = "SELECT TOP 1 IsNull(Khoa,'') as Khoa FROM DM_DMBaoDuong Where Khoa <> @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<string>(commandText, new { Khoa = khoa.Trim() });
            return !string.IsNullOrEmpty(result?.Trim());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate BaoDuong");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> CheckExistsAsync(string khoaLoaiXe, string khoaLoaiDichVu)
    {
        try
        {
            // Exact SQL from legacy CheckExists method (line 316)
            string commandText = @"
                SELECT COUNT(*) FROM DM_DMBaoDuong 
                WHERE KhoaLoaiXe = @KhoaLoaiXe AND KhoaLoaiDichVu = @KhoaLoaiDichVu";
            
            var count = await _connection.QuerySingleAsync<int>(commandText, new 
            { 
                KhoaLoaiXe = khoaLoaiXe.Trim(), 
                KhoaLoaiDichVu = khoaLoaiDichVu.Trim() 
            });
            
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if BaoDuong exists");
            return false;
        }
    }

    public async Task ClearTempAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy ClearTemp method (line 176)
            string commandText = "DELETE FROM DM_DMBaoDuongChiTietTmp WHERE KhoaDMBD = @Khoa";
            await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing temp data for BaoDuong: {Khoa}", khoa);
        }
    }

    public async Task<DataTable> GetListDMBDAsync(string condition = "")
    {
        try
        {
            // Exact SQL from legacy GetListDMBD method (line 183)
            string commandText = @"
                SELECT DISTINCT BD.Khoa, BD.KhoaLoaiXe, LX.TenViet as LoaiXe, BD.KhoaLoaiDichVu, 
                       DV.TenViet as LoaiDichVu, BD.DienGiai, BD.KhoaNhanVienCapNhat, 
                       NV.TenViet as NhanVienCapNhat, dbo.char2date(BD.NgayCapNhat) as NgayCapNhat 
                FROM DM_DMBaoDuong BD  
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa  
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa 
                LEFT JOIN HT_NguoiDung ND on BD.KhoaNhanVienCapNhat = ND.KhoaNhanVien 
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien 
                WHERE 1 = 1 " + condition;
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetDetailsDMBDAsync(string khoaBaoGia)
    {
        try
        {
            // Exact SQL from legacy GetDetailsDMBD method (line 200)
            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string commandText = @"
                SELECT CT.Khoa, CT.KhoaDMBD, CT.STT, CT.KhoaLoaiDichVu, CT.KhoaHangMuc, CT.Loai,
                       CT.NoiDung, CT.KhoaHangHoa, CT.NoiDung, CT.KhoaDonViTinh, CT.SoLuong,
                       CT.DonGia, CT.ThanhTien, CT.DienGiai, HH.Ma as MaHangHoa,
                       DV.TenViet As LoaiDichVu, DVT.TenViet As DonViTinh, KM.TenViet As KhoanMucSuaChua
                FROM DM_DMBaoDuongChiTiet CT
                LEFT JOIN DM_HangHoa HH on CT.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu DV on CT.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DonViTinh DVT on CT.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_KhoanMucSuaChua KM on CT.KhoaHangMuc = KM.Khoa
                WHERE CT.KhoaDMBD = @KhoaBaoGia
                ORDER BY CT.STT";

            var result = await _connection.QueryAsync(commandText, new { KhoaBaoGia = khoaBaoGia });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong details");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiDichVu)
    {
        try
        {
            // Exact SQL from legacy GetDMBD_BaoGia method (line 218)
            string commandText = @"
                SELECT CT.Khoa, CT.KhoaDMBD, CT.STT, CT.KhoaLoaiDichVu, CT.KhoaHangMuc, CT.Loai,
                       CT.NoiDung, CT.KhoaHangHoa, CT.NoiDung, CT.KhoaDonViTinh, CT.SoLuong,
                       CT.DonGia, CT.ThanhTien, CT.DienGiai, HH.Ma as MaHangHoa,
                       DV.TenViet As LoaiDichVu, DVT.TenViet As DonViTinh, KM.TenViet As KhoanMucSuaChua
                FROM DM_DMBaoDuongChiTiet CT
                INNER JOIN DM_DMBaoDuong BD on CT.KhoaDMBD = BD.Khoa
                LEFT JOIN DM_HangHoa HH on CT.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu DV on CT.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DonViTinh DVT on CT.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_KhoanMucSuaChua KM on CT.KhoaHangMuc = KM.Khoa
                WHERE BD.KhoaLoaiDichVu = @KhoaLoaiDichVu
                ORDER BY CT.STT";

            var result = await _connection.QueryAsync(commandText, new { KhoaLoaiDichVu = khoaLoaiDichVu });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong for BaoGia by service type");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetDMBD_BaoGiaAsync(string khoaLoaiXe, string khoaLoaiDichVu)
    {
        try
        {
            // Exact SQL from legacy GetDMBD_BaoGia overload method (line 236)
            string commandText = @"
                SELECT CT.Khoa, CT.KhoaDMBD, CT.STT, CT.KhoaLoaiDichVu, CT.KhoaHangMuc, CT.Loai,
                       CT.NoiDung, CT.KhoaHangHoa, CT.NoiDung, CT.KhoaDonViTinh, CT.SoLuong,
                       CT.DonGia, CT.ThanhTien, CT.DienGiai, HH.Ma as MaHangHoa,
                       DV.TenViet As LoaiDichVu, DVT.TenViet As DonViTinh, KM.TenViet As KhoanMucSuaChua
                FROM DM_DMBaoDuongChiTiet CT
                INNER JOIN DM_DMBaoDuong BD on CT.KhoaDMBD = BD.Khoa
                LEFT JOIN DM_HangHoa HH on CT.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu DV on CT.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DonViTinh DVT on CT.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_KhoanMucSuaChua KM on CT.KhoaHangMuc = KM.Khoa
                WHERE BD.KhoaLoaiXe = @KhoaLoaiXe AND BD.KhoaLoaiDichVu = @KhoaLoaiDichVu
                ORDER BY CT.STT";

            var result = await _connection.QueryAsync(commandText, new
            {
                KhoaLoaiXe = khoaLoaiXe,
                KhoaLoaiDichVu = khoaLoaiDichVu
            });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong for BaoGia by vehicle and service type");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetDataPrintYeuCauAsync(string khoa)
    {
        try
        {
            // Load the BaoDuong first (as in legacy method line 258)
            await LoadAsync(khoa);

            // Exact SQL from legacy GetDataPrintYeuCau method (line 256)
            string commandText = @"
                SELECT BG.SoChungTu, dbo.Char2Date(BG.NgayChungTu) AS NgayChungTu, DV.TenViet as DichVu,
                       KH.TenViet as KhachHang, KH.DiaChi as DiaChiKhach, KH.DienThoai as DienThoaiKhach,
                       KH.MaSoThue, KH.Fax, BG.SoXe, LX.TenViet as LoaiXe, X.Model, X.MauSon,
                       X.SoSuon as SoKhung, X.SoMay, X.MaVin, BG.SoKmHienTai as SoKm, X.DoiXe,
                       BG.GioVaoXuong , dbo.Char2Date(BG.NgayVaoXuong) as NgayVaoXuong,
                       BG.GioXuatXuong , dbo.Char2Date(BG.NgayXuatXuong) as NgayXuatXuong,
                       BG.KhachHangYeuCau, BG.GioDuKienHoanThanh ,
                       dbo.Char2Date(BG.NgayDuKienHoanThanh) as NgayDuKienHoanThanh,
                       BG.KhachChoNhanXe, BG.KhachLayPhuTung, BG.LienHeQuaDienThoai, BG.LienHeQuaThu,
                       BG.TenTaiXe, BG.DienThoaiTaiXe, BG.CoVanDichVu1 As CoVanDichVu,
                       P.TenViet as PhanSuaChua, CT.NoiDung, HH.Ma as MaPhuTung,
                       DVT.TenViet as DonViTinh, CT.SoLuong, CT.DonGia, CT.ThanhTien,
                       CT.TyleChietKhau, CT.TienChietKhau, BG.TyLeThue, CT.TienThue,
                       (CASE WHEN CT.LOAI=1 THEN N'  I. CÔNG LAO ĐỘNG' ELSE N'  II. PHỤ TÙNG - GIA CÔNG' END) as DienGiai,
                       CT.Loai
                FROM SC_BaoGia BG
                LEFT JOIN DM_Xe X on BG.KhoaXe = X.Khoa
                LEFT JOIN DM_DoiTuong KH on BG.KhoaKhachHang = KH.Khoa
                LEFT JOIN SC_BaoGiaChiTiet CT on BG.Khoa = CT.KhoaBaogia
                LEFT JOIN DM_LoaiDichVu DV on BG.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_LoaiXe LX on BG.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_KhoanMucSuaChua P on CT.KhoaHangMuc = P.Khoa
                LEFT JOIN DM_HangHoa HH on CT.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_QuocGia QG on HH.KhoaQuocGia = QG.Khoa
                LEFT JOIN DM_DonViTinh DVT on CT.KhoaDonViTinh = DVT.Khoa
                WHERE BG.Khoa = @Khoa AND CT.Huy = 0
                ORDER BY PhanSuaChua, CT.SoThuTu";

            var result = await _connection.QueryAsync(commandText, new { Khoa = khoa.Trim() });
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting print data for BaoDuong");
            return new DataTable();
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<BaoDuongListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT BD.Khoa, BD.KhoaLoaiXe, LX.TenViet as LoaiXe, BD.KhoaLoaiDichVu,
                       DV.TenViet as LoaiDichVu, BD.DienGiai, BD.KhoaNhanVienCapNhat,
                       NV.TenViet as NhanVienCapNhat, dbo.char2date(BD.NgayCapNhat) as NgayCapNhat
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN HT_NguoiDung ND on BD.KhoaNhanVienCapNhat = ND.KhoaNhanVien
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien
                ORDER BY LX.TenViet, DV.TenViet";

            return await _connection.QueryAsync<BaoDuongListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all BaoDuong records");
            return new List<BaoDuongListDto>();
        }
    }

    public async Task<BaoDuongDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_DMBaoDuong WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<BaoDuongDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<BaoDuongDto?> GetByVehicleAndServiceTypeAsync(string khoaLoaiXe, string khoaLoaiDichVu)
    {
        try
        {
            string commandText = @"
                SELECT * FROM DM_DMBaoDuong
                WHERE KhoaLoaiXe = @KhoaLoaiXe AND KhoaLoaiDichVu = @KhoaLoaiDichVu";

            return await _connection.QueryFirstOrDefaultAsync<BaoDuongDto>(commandText, new
            {
                KhoaLoaiXe = khoaLoaiXe,
                KhoaLoaiDichVu = khoaLoaiDichVu
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong by vehicle and service type");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateBaoDuongDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new BaoDuongDto
            {
                Khoa = khoa,
                KhoaLoaiXe = createDto.KhoaLoaiXe,
                KhoaLoaiDichVu = createDto.KhoaLoaiDichVu,
                DienGiai = createDto.DienGiai,
                NgayCapNhat = DateTime.Now.ToString("yyyyMMdd")
            };

            var success = await SaveAsync(dto);
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BaoDuong");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(BaoDuongDto dto)
    {
        try
        {
            dto.NgayCapNhat = DateTime.Now.ToString("yyyyMMdd");
            return await SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating BaoDuong: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateBaoDuongStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE DM_DMBaoDuong
                SET KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat,
                    NgayCapNhat = @NgayCapNhat
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                statusDto.Khoa,
                statusDto.KhoaNhanVienCapNhat,
                NgayCapNhat = DateTime.Now.ToString("yyyyMMdd")
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating BaoDuong status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<BaoDuongListDto>> SearchAsync(BaoDuongSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaLoaiXe))
            {
                conditions.Add("BD.KhoaLoaiXe = @KhoaLoaiXe");
                parameters.Add("@KhoaLoaiXe", searchDto.KhoaLoaiXe);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaLoaiDichVu))
            {
                conditions.Add("BD.KhoaLoaiDichVu = @KhoaLoaiDichVu");
                parameters.Add("@KhoaLoaiDichVu", searchDto.KhoaLoaiDichVu);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DienGiai))
            {
                conditions.Add("BD.DienGiai LIKE @DienGiai");
                parameters.Add("@DienGiai", $"%{searchDto.DienGiai}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaNhanVienCapNhat))
            {
                conditions.Add("BD.KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat");
                parameters.Add("@KhoaNhanVienCapNhat", searchDto.KhoaNhanVienCapNhat);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.NgayCapNhatFrom))
            {
                conditions.Add("BD.NgayCapNhat >= @NgayCapNhatFrom");
                parameters.Add("@NgayCapNhatFrom", searchDto.NgayCapNhatFrom);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.NgayCapNhatTo))
            {
                conditions.Add("BD.NgayCapNhat <= @NgayCapNhatTo");
                parameters.Add("@NgayCapNhatTo", searchDto.NgayCapNhatTo);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT BD.Khoa, BD.KhoaLoaiXe, LX.TenViet as LoaiXe, BD.KhoaLoaiDichVu,
                       DV.TenViet as LoaiDichVu, BD.DienGiai, BD.KhoaNhanVienCapNhat,
                       NV.TenViet as NhanVienCapNhat, dbo.char2date(BD.NgayCapNhat) as NgayCapNhat
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN HT_NguoiDung ND on BD.KhoaNhanVienCapNhat = ND.KhoaNhanVien
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien
                {whereClause}
                ORDER BY LX.TenViet, DV.TenViet";

            return await _connection.QueryAsync<BaoDuongListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching BaoDuong");
            return new List<BaoDuongListDto>();
        }
    }

    public async Task<IEnumerable<BaoDuongLookupDto>> GetLookupAsync()
    {
        try
        {
            string commandText = @"
                SELECT BD.Khoa, LX.TenViet as LoaiXe, DV.TenViet as LoaiDichVu, BD.DienGiai
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                ORDER BY LX.TenViet, DV.TenViet";

            return await _connection.QueryAsync<BaoDuongLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong lookup");
            return new List<BaoDuongLookupDto>();
        }
    }

    public async Task<BaoDuongValidationDto> ValidateAsync(string khoa, string khoaLoaiXe, string khoaLoaiDichVu)
    {
        try
        {
            var result = new BaoDuongValidationDto
            {
                Khoa = khoa,
                KhoaLoaiXe = khoaLoaiXe,
                KhoaLoaiDichVu = khoaLoaiDichVu
            };

            // Check if duplicate
            result.IsDuplicate = await IsDupllicateAsync("", khoa);

            // Check if exists
            result.Exists = await CheckExistsAsync(khoaLoaiXe, khoaLoaiDichVu);

            // Check if used (simplified - would need to check actual usage in quotations)
            result.IsUsed = false; // TODO: Implement actual usage checking

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating BaoDuong");
            return new BaoDuongValidationDto { Khoa = khoa, KhoaLoaiXe = khoaLoaiXe, KhoaLoaiDichVu = khoaLoaiDichVu };
        }
    }

    public async Task<IEnumerable<BaoDuongChiTietDto>> GetMaintenanceDetailsAsync(string khoa)
    {
        try
        {
            string commandText = @"
                SELECT CT.Khoa, CT.KhoaDMBD, CT.STT, CT.KhoaLoaiDichVu, CT.KhoaHangMuc, CT.Loai,
                       CT.NoiDung, CT.KhoaHangHoa, CT.KhoaDonViTinh, CT.SoLuong,
                       CT.DonGia, CT.ThanhTien, CT.DienGiai, HH.Ma as MaHangHoa,
                       DV.TenViet As LoaiDichVu, DVT.TenViet As DonViTinh, KM.TenViet As KhoanMucSuaChua
                FROM DM_DMBaoDuongChiTiet CT
                LEFT JOIN DM_HangHoa HH on CT.KhoaHangHoa = HH.Khoa
                LEFT JOIN DM_LoaiDichVu DV on CT.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DonViTinh DVT on CT.KhoaDonViTinh = DVT.Khoa
                LEFT JOIN DM_KhoanMucSuaChua KM on CT.KhoaHangMuc = KM.Khoa
                WHERE CT.KhoaDMBD = @Khoa
                ORDER BY CT.STT";

            return await _connection.QueryAsync<BaoDuongChiTietDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance details");
            return new List<BaoDuongChiTietDto>();
        }
    }

    public async Task<IEnumerable<MaintenanceCategoryDto>> GetMaintenanceCategoriesAsync()
    {
        try
        {
            string commandText = @"
                SELECT BD.Khoa, BD.KhoaLoaiXe, LX.TenViet as LoaiXe, BD.KhoaLoaiDichVu,
                       DV.TenViet as LoaiDichVu, BD.DienGiai,
                       CASE WHEN UPPER(DV.TenViet) LIKE '%BẢO DƯỠNG%' OR UPPER(DV.TenViet) LIKE '%ĐỊNH KỲ%' THEN 1 ELSE 0 END as IsPreventiveMaintenance,
                       CASE WHEN UPPER(DV.TenViet) LIKE '%SỬA CHỮA%' OR UPPER(DV.TenViet) LIKE '%SỬA%' THEN 1 ELSE 0 END as IsCorrectiveMaintenance,
                       CASE WHEN UPPER(DV.TenViet) LIKE '%ĐỊNH KỲ%' OR UPPER(DV.TenViet) LIKE '%THƯỜNG XUYÊN%' THEN 1 ELSE 0 END as IsPeriodicMaintenance,
                       CASE WHEN UPPER(DV.TenViet) LIKE '%KHẨN CẤP%' OR UPPER(DV.TenViet) LIKE '%GẤP%' THEN 1 ELSE 0 END as IsEmergencyMaintenance,
                       COUNT(CT.Khoa) as TotalItems,
                       ISNULL(SUM(CT.ThanhTien), 0) as TotalCost
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DMBaoDuongChiTiet CT on BD.Khoa = CT.KhoaDMBD
                GROUP BY BD.Khoa, BD.KhoaLoaiXe, LX.TenViet, BD.KhoaLoaiDichVu, DV.TenViet, BD.DienGiai
                ORDER BY LX.TenViet, DV.TenViet";

            return await _connection.QueryAsync<MaintenanceCategoryDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance categories");
            return new List<MaintenanceCategoryDto>();
        }
    }

    public async Task<IEnumerable<BaoDuongWithDetailsDto>> GetMaintenanceWithDetailsAsync()
    {
        try
        {
            string commandText = @"
                SELECT BD.Khoa, BD.KhoaLoaiXe, LX.TenViet as LoaiXe, BD.KhoaLoaiDichVu,
                       DV.TenViet as LoaiDichVu, BD.DienGiai, BD.KhoaNhanVienCapNhat,
                       NV.TenViet as NhanVienCapNhat, dbo.char2date(BD.NgayCapNhat) as NgayCapNhat,
                       COUNT(CT.Khoa) as TotalItems,
                       ISNULL(SUM(CT.ThanhTien), 0) as TotalCost
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN HT_NguoiDung ND on BD.KhoaNhanVienCapNhat = ND.KhoaNhanVien
                LEFT JOIN DM_DoiTuong NV on NV.Khoa = ND.KhoaNhanVien
                LEFT JOIN DM_DMBaoDuongChiTiet CT on BD.Khoa = CT.KhoaDMBD
                GROUP BY BD.Khoa, BD.KhoaLoaiXe, LX.TenViet, BD.KhoaLoaiDichVu, DV.TenViet,
                         BD.DienGiai, BD.KhoaNhanVienCapNhat, NV.TenViet, BD.NgayCapNhat
                ORDER BY LX.TenViet, DV.TenViet";

            var maintenanceList = await _connection.QueryAsync<BaoDuongWithDetailsDto>(commandText);

            // Get details for each maintenance
            foreach (var maintenance in maintenanceList)
            {
                var details = await GetMaintenanceDetailsAsync(maintenance.Khoa);
                maintenance.ChiTiet = details.ToList();
            }

            return maintenanceList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance with details");
            return new List<BaoDuongWithDetailsDto>();
        }
    }

    public async Task<BaoDuongStatsDto?> GetMaintenanceStatsAsync(string khoa)
    {
        try
        {
            string commandText = @"
                SELECT BD.Khoa, LX.TenViet as LoaiXe, DV.TenViet as LoaiDichVu,
                       COUNT(CT.Khoa) as TotalMaintenanceItems,
                       COUNT(CASE WHEN CT.Loai = 1 THEN 1 END) as TotalLaborItems,
                       COUNT(CASE WHEN CT.Loai = 2 THEN 1 END) as TotalPartsItems,
                       ISNULL(SUM(CT.ThanhTien), 0) as TotalMaintenanceCost,
                       ISNULL(SUM(CASE WHEN CT.Loai = 1 THEN CT.ThanhTien ELSE 0 END), 0) as TotalLaborCost,
                       ISNULL(SUM(CASE WHEN CT.Loai = 2 THEN CT.ThanhTien ELSE 0 END), 0) as TotalPartsCost,
                       dbo.char2date(BD.NgayCapNhat) as LastUpdated,
                       0 as UsageCount
                FROM DM_DMBaoDuong BD
                LEFT JOIN DM_LoaiXe LX on BD.KhoaLoaiXe = LX.Khoa
                LEFT JOIN DM_LoaiDichVu DV on BD.KhoaLoaiDichVu = DV.Khoa
                LEFT JOIN DM_DMBaoDuongChiTiet CT on BD.Khoa = CT.KhoaDMBD
                WHERE BD.Khoa = @Khoa
                GROUP BY BD.Khoa, LX.TenViet, DV.TenViet, BD.NgayCapNhat";

            return await _connection.QueryFirstOrDefaultAsync<BaoDuongStatsDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance stats");
            return null;
        }
    }

    public async Task<IEnumerable<BaoDuongPrintDto>> GetPrintDataAsync(string khoa)
    {
        try
        {
            var dataTable = await GetDataPrintYeuCauAsync(khoa);
            var result = new List<BaoDuongPrintDto>();

            foreach (DataRow row in dataTable.Rows)
            {
                result.Add(new BaoDuongPrintDto
                {
                    SoChungTu = row["SoChungTu"]?.ToString() ?? "",
                    NgayChungTu = row["NgayChungTu"] as DateTime?,
                    DichVu = row["DichVu"]?.ToString() ?? "",
                    KhachHang = row["KhachHang"]?.ToString() ?? "",
                    DiaChiKhach = row["DiaChiKhach"]?.ToString() ?? "",
                    DienThoaiKhach = row["DienThoaiKhach"]?.ToString() ?? "",
                    MaSoThue = row["MaSoThue"]?.ToString() ?? "",
                    Fax = row["Fax"]?.ToString() ?? "",
                    SoXe = row["SoXe"]?.ToString() ?? "",
                    LoaiXe = row["LoaiXe"]?.ToString() ?? "",
                    Model = row["Model"]?.ToString() ?? "",
                    MauSon = row["MauSon"]?.ToString() ?? "",
                    SoKhung = row["SoKhung"]?.ToString() ?? "",
                    SoMay = row["SoMay"]?.ToString() ?? "",
                    MaVin = row["MaVin"]?.ToString() ?? "",
                    SoKm = Convert.ToInt32(row["SoKm"] ?? 0),
                    DoiXe = row["DoiXe"]?.ToString() ?? "",
                    GioVaoXuong = row["GioVaoXuong"]?.ToString() ?? "",
                    NgayVaoXuong = row["NgayVaoXuong"] as DateTime?,
                    GioXuatXuong = row["GioXuatXuong"]?.ToString() ?? "",
                    NgayXuatXuong = row["NgayXuatXuong"] as DateTime?,
                    KhachHangYeuCau = row["KhachHangYeuCau"]?.ToString() ?? "",
                    GioDuKienHoanThanh = row["GioDuKienHoanThanh"]?.ToString() ?? "",
                    NgayDuKienHoanThanh = row["NgayDuKienHoanThanh"] as DateTime?,
                    KhachChoNhanXe = Convert.ToBoolean(row["KhachChoNhanXe"] ?? false),
                    KhachLayPhuTung = Convert.ToBoolean(row["KhachLayPhuTung"] ?? false),
                    LienHeQuaDienThoai = Convert.ToBoolean(row["LienHeQuaDienThoai"] ?? false),
                    LienHeQuaThu = Convert.ToBoolean(row["LienHeQuaThu"] ?? false),
                    TenTaiXe = row["TenTaiXe"]?.ToString() ?? "",
                    DienThoaiTaiXe = row["DienThoaiTaiXe"]?.ToString() ?? "",
                    CoVanDichVu = row["CoVanDichVu"]?.ToString() ?? "",
                    PhanSuaChua = row["PhanSuaChua"]?.ToString() ?? "",
                    NoiDung = row["NoiDung"]?.ToString() ?? "",
                    MaPhuTung = row["MaPhuTung"]?.ToString() ?? "",
                    DonViTinh = row["DonViTinh"]?.ToString() ?? "",
                    SoLuong = Convert.ToDecimal(row["SoLuong"] ?? 0),
                    DonGia = Convert.ToDecimal(row["DonGia"] ?? 0),
                    ThanhTien = Convert.ToDecimal(row["ThanhTien"] ?? 0),
                    TyleChietKhau = Convert.ToDecimal(row["TyleChietKhau"] ?? 0),
                    TienChietKhau = Convert.ToDecimal(row["TienChietKhau"] ?? 0),
                    TyLeThue = Convert.ToDecimal(row["TyLeThue"] ?? 0),
                    TienThue = Convert.ToDecimal(row["TienThue"] ?? 0),
                    DienGiai = row["DienGiai"]?.ToString() ?? "",
                    Loai = Convert.ToInt32(row["Loai"] ?? 0)
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting print data");
            return new List<BaoDuongPrintDto>();
        }
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    #endregion
}
