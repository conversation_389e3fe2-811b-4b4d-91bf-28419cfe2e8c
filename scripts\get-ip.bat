@echo off
echo ========================================
echo   FINDING YOUR IP ADDRESS FOR IPHONE
echo ========================================
echo.

echo Your computer's IP addresses:
echo.

for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    echo %%a
)

echo.
echo ========================================
echo   SETUP INSTRUCTIONS FOR IPHONE
echo ========================================
echo.
echo 1. Make sure your iPhone and computer are on the same WiFi network
echo 2. Copy one of the IP addresses above (usually starts with 192.168.x.x)
echo 3. Update the LOCAL_IP in: gp-mobile-quotation/src/config/environment.ts
echo 4. Make sure your backend API is running on http://localhost:5001
echo 5. Install "Expo Go" app on your iPhone from App Store
echo 6. Run: npm start (in gp-mobile-quotation folder)
echo 7. Scan the QR code with your iPhone camera or Expo Go app
echo.
echo Current backend API: http://localhost:5001
echo Backend status: Check if TestAPI is still running
echo.
pause
