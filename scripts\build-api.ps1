# Build script for GP Mobile API
Write-Host "Building GP Mobile API..." -ForegroundColor Green

# Navigate to API directory
Set-Location "src\API"

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore GP.Mobile.sln

# Build solution
Write-Host "Building solution..." -ForegroundColor Yellow
dotnet build GP.Mobile.sln --configuration Release --no-restore

if ($LASTEXITCODE -eq 0) {
    Write-Host "API build completed successfully!" -ForegroundColor Green
} else {
    Write-Host "API build failed!" -ForegroundColor Red
    exit 1
}

# Return to root directory
Set-Location "..\..\"
