using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for HangSanXuat (Manufacturer) entity
/// Maps exactly to DM_HangSanXuat table in legacy database
/// Implements ALL properties from clsDMHangSanXuat.cs (533 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for manufacturer management and vehicle categorization
/// </summary>
public class HangSanXuatDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer code (e.g., TOYOTA, HONDA, FORD, BMW)
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of manufacturer
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of manufacturer
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes about the manufacturer
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Effective date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhat property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Synchronization status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;
}

/// <summary>
/// DTO for HangSanXuat list display
/// Optimized for automotive manufacturer lists and dropdowns
/// </summary>
public class HangSanXuatListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public int TotalVehicleTypes { get; set; } = 0; // Count of vehicle types from this manufacturer
}

/// <summary>
/// DTO for creating new HangSanXuat
/// Contains only required fields for creation
/// </summary>
public class CreateHangSanXuatDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(1000)]
    public string DienGiai { get; set; } = string.Empty;

    public string TuNgay { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
}

/// <summary>
/// DTO for updating HangSanXuat status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateHangSanXuatStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public int Active { get; set; }

    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for HangSanXuat search operations
/// Used for advanced search and filtering
/// </summary>
public class HangSanXuatSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public int? Active { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public string? KhoaNhanVienCapNhat { get; set; }
}

/// <summary>
/// DTO for HangSanXuat dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class HangSanXuatLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
}

/// <summary>
/// DTO for HangSanXuat validation operations
/// Used for duplicate checking and validation
/// </summary>
public class HangSanXuatValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}

/// <summary>
/// DTO for HangSanXuat search by code operations
/// Used for quick manufacturer lookup
/// </summary>
public class HangSanXuatSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
}

/// <summary>
/// DTO for automotive manufacturer categories
/// Specialized for automotive manufacturer classification
/// </summary>
public class ManufacturerCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string QuocGia { get; set; } = string.Empty; // Country of origin
    public bool IsLuxuryBrand { get; set; } = false;
    public bool IsCommercialBrand { get; set; } = false;
    public bool IsMotorcycleBrand { get; set; } = false;
    public bool IsElectricVehicleBrand { get; set; } = false;
}

/// <summary>
/// DTO for manufacturer with vehicle types
/// Used for detailed manufacturer display with vehicle type information
/// </summary>
public class HangSanXuatWithVehicleTypesDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public List<LoaiXeLookupDto> VehicleTypes { get; set; } = new();
    public int TotalVehicleTypes { get; set; } = 0;
    public int ActiveVehicleTypes { get; set; } = 0;
}

/// <summary>
/// DTO for manufacturer statistics
/// Used for reporting and analytics
/// </summary>
public class HangSanXuatStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public int TotalVehicleTypes { get; set; } = 0;
    public int ActiveVehicleTypes { get; set; } = 0;
    public int TotalVehicles { get; set; } = 0;
    public int ActiveVehicles { get; set; } = 0;
    public decimal TotalServiceRevenue { get; set; } = 0;
    public DateTime LastServiceDate { get; set; }
    public string PopularVehicleType { get; set; } = string.Empty;
}

/// <summary>
/// DTO for manufacturer country information
/// Used for geographic categorization
/// </summary>
public class ManufacturerCountryDto
{
    public string QuocGia { get; set; } = string.Empty;
    public string TenQuocGia { get; set; } = string.Empty;
    public int TotalManufacturers { get; set; } = 0;
    public List<HangSanXuatLookupDto> Manufacturers { get; set; } = new();
}

/// <summary>
/// DTO for manufacturer filter operations
/// Used for advanced filtering with key filters
/// </summary>
public class HangSanXuatFilterDto
{
    public string KeyFilter { get; set; } = string.Empty; // Pipe-separated keys
    public string FieldNameFilter { get; set; } = string.Empty; // Field name to filter on
    public string Language { get; set; } = "vi"; // Language for display
}
