using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Interface for DonVi (Business Unit) service
/// Defines business logic operations for DonVi entity
/// </summary>
public interface IDonViService
{
    #region Legacy Methods
    
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(DonViDto dto, string action);
    Task<bool> DeleteAsync(string khoa);
    Task<DataTable> GetListForSelectAsync();
    Task<DataTable> GetListAsync();
    Task<DataTable> ShowListAsync();
    Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "");
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<DonViListDto>> GetAllAsync();
    Task<DonViDto?> GetByIdAsync(string khoa);
    Task<DonViDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateDonViDto createDto);
    Task<bool> UpdateAsync(DonViDto dto);
    Task<bool> DeleteDataAsync(string khoa);
    Task<bool> UpdateStatusAsync(UpdateDonViStatusDto statusDto);
    Task<IEnumerable<DonViListDto>> SearchAsync(DonViSearchDto searchDto);
    Task<IEnumerable<DonViLookupDto>> GetLookupAsync(string language = "vi");
    Task<IEnumerable<DonViSelectDto>> GetSelectListAsync();
    Task<DonViValidationDto> ValidateAsync(string khoa, string ma);
    Task<DonViContactDto?> GetContactInfoAsync(string khoa);
    
    #endregion
}

/// <summary>
/// Complete Service for DonVi entity
/// Implements ALL business logic from clsDMDonVi.cs (445 lines)
/// Includes validation, business rules, and data transformation
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class DonViService : IDonViService
{
    private readonly IDonViRepository _repository;
    private readonly ILogger<DonViService> _logger;

    public DonViService(IDonViRepository repository, ILogger<DonViService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            return await _repository.LoadAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading DonVi");
            throw;
        }
    }

    public async Task<bool> SaveAsync(DonViDto dto, string action)
    {
        try
        {
            // Validate input
            var validationResult = await ValidateForSaveAsync(dto, action);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            // Apply business rules
            await ApplyBusinessRulesAsync(dto, action);

            return await _repository.SaveAsync(dto, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving DonVi");
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
            {
                throw new ArgumentException("Khoa không được để trống");
            }

            // Check if can delete
            var canDelete = await ValidateForDeleteAsync(khoa);
            if (!canDelete)
            {
                throw new InvalidOperationException("Không thể xóa đơn vị đã được sử dụng");
            }

            return await _repository.DeleteAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting DonVi");
            throw;
        }
    }

    public async Task<DataTable> GetListForSelectAsync()
    {
        try
        {
            return await _repository.GetListForSelectAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi list for select");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListAsync()
    {
        try
        {
            return await _repository.GetListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowListAsync()
    {
        try
        {
            return await _repository.ShowListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing DonVi list");
            return new DataTable();
        }
    }

    public async Task<DataTable> GetListFieldAsync(string fields, string conditions = "", string order = "")
    {
        try
        {
            // Validate field list for security
            var secureFields = ValidateFieldList(fields);
            var secureConditions = ApplySecurityFilters(conditions);
            
            return await _repository.GetListFieldAsync(secureFields, secureConditions, order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi list by field");
            return new DataTable();
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<DonViListDto>> GetAllAsync()
    {
        try
        {
            return await _repository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all DonVi records");
            return new List<DonViListDto>();
        }
    }

    public async Task<DonViDto?> GetByIdAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetByIdAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi by ID");
            return null;
        }
    }

    public async Task<DonViDto?> GetByCodeAsync(string ma)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ma))
                return null;

            return await _repository.GetByCodeAsync(ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi by code");
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateDonViDto createDto)
    {
        try
        {
            // Validate creation data
            var validationResult = await ValidateForCreateAsync(createDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.CreateAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating DonVi");
            throw;
        }
    }

    public async Task<bool> UpdateAsync(DonViDto dto)
    {
        return await SaveAsync(dto, "UPDATE");
    }

    public async Task<bool> DeleteDataAsync(string khoa)
    {
        return await DeleteAsync(khoa);
    }

    public async Task<bool> UpdateStatusAsync(UpdateDonViStatusDto statusDto)
    {
        try
        {
            // Validate status update
            var validationResult = await ValidateStatusUpdateAsync(statusDto);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException(string.Join(", ", validationResult.Errors));
            }

            return await _repository.UpdateStatusAsync(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating DonVi status");
            throw;
        }
    }

    public async Task<IEnumerable<DonViListDto>> SearchAsync(DonViSearchDto searchDto)
    {
        try
        {
            return await _repository.SearchAsync(searchDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching DonVi");
            return new List<DonViListDto>();
        }
    }

    public async Task<IEnumerable<DonViLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            return await _repository.GetLookupAsync(language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi lookup");
            return new List<DonViLookupDto>();
        }
    }

    public async Task<IEnumerable<DonViSelectDto>> GetSelectListAsync()
    {
        try
        {
            return await _repository.GetSelectListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi select list");
            return new List<DonViSelectDto>();
        }
    }

    public async Task<DonViValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            return await _repository.ValidateAsync(khoa, ma);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating DonVi");
            return new DonViValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<DonViContactDto?> GetContactInfoAsync(string khoa)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(khoa))
                return null;

            return await _repository.GetContactInfoAsync(khoa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DonVi contact info");
            return null;
        }
    }

    #endregion

    #region Validation Methods (Critical Business Logic)

    private class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
    }

    private async Task<ValidationResult> ValidateForSaveAsync(DonViDto dto, string action)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(dto.Ma))
            result.Errors.Add("Mã đơn vị không được để trống");

        if (string.IsNullOrWhiteSpace(dto.TenViet))
            result.Errors.Add("Tên đơn vị (Tiếng Việt) không được để trống");

        // Business rule: Check for duplicate codes
        if (!string.IsNullOrEmpty(dto.Ma))
        {
            var isDuplicate = await _repository.IsDuplicateCodeAsync(dto.Ma, dto.Khoa);
            if (isDuplicate)
            {
                result.Errors.Add("Mã đơn vị đã tồn tại");
            }
        }

        // Length validation
        if (dto.Ma.Length > 20)
            result.Errors.Add("Mã đơn vị không được vượt quá 20 ký tự");

        if (dto.TenViet.Length > 200)
            result.Errors.Add("Tên đơn vị (Tiếng Việt) không được vượt quá 200 ký tự");

        if (dto.TenAnh.Length > 200)
            result.Errors.Add("Tên đơn vị (Tiếng Anh) không được vượt quá 200 ký tự");

        if (dto.DiaChi.Length > 500)
            result.Errors.Add("Địa chỉ không được vượt quá 500 ký tự");

        if (dto.DienThoai.Length > 50)
            result.Errors.Add("Điện thoại không được vượt quá 50 ký tự");

        if (dto.Fax.Length > 50)
            result.Errors.Add("Fax không được vượt quá 50 ký tự");

        if (dto.Email.Length > 100)
            result.Errors.Add("Email không được vượt quá 100 ký tự");

        if (dto.NguoiDaiDien.Length > 100)
            result.Errors.Add("Người đại diện không được vượt quá 100 ký tự");

        if (dto.TieuDeNguoiDaiDien.Length > 100)
            result.Errors.Add("Tiêu đề người đại diện không được vượt quá 100 ký tự");

        if (dto.MobileNguoiDaiDien.Length > 50)
            result.Errors.Add("Mobile người đại diện không được vượt quá 50 ký tự");

        if (dto.EmailNguoiDaiDien.Length > 100)
            result.Errors.Add("Email người đại diện không được vượt quá 100 ký tự");

        if (dto.GhiChu.Length > 1000)
            result.Errors.Add("Ghi chú không được vượt quá 1000 ký tự");

        if (dto.Prefix.Length > 10)
            result.Errors.Add("Prefix không được vượt quá 10 ký tự");

        // Email format validation
        if (!string.IsNullOrEmpty(dto.Email) && !IsValidEmail(dto.Email))
            result.Errors.Add("Email không đúng định dạng");

        if (!string.IsNullOrEmpty(dto.EmailNguoiDaiDien) && !IsValidEmail(dto.EmailNguoiDaiDien))
            result.Errors.Add("Email người đại diện không đúng định dạng");

        // Phone format validation
        if (!string.IsNullOrEmpty(dto.DienThoai) && !IsValidPhoneNumber(dto.DienThoai))
            result.Errors.Add("Số điện thoại không đúng định dạng");

        if (!string.IsNullOrEmpty(dto.MobileNguoiDaiDien) && !IsValidPhoneNumber(dto.MobileNguoiDaiDien))
            result.Errors.Add("Mobile người đại diện không đúng định dạng");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<ValidationResult> ValidateForCreateAsync(CreateDonViDto createDto)
    {
        var result = new ValidationResult();

        // Required field validation
        if (string.IsNullOrWhiteSpace(createDto.Ma))
            result.Errors.Add("Mã đơn vị không được để trống");

        if (string.IsNullOrWhiteSpace(createDto.TenViet))
            result.Errors.Add("Tên đơn vị (Tiếng Việt) không được để trống");

        // Check for duplicate codes
        if (!string.IsNullOrEmpty(createDto.Ma))
        {
            var isDuplicate = await _repository.IsDuplicateCodeAsync(createDto.Ma, "");
            if (isDuplicate)
            {
                result.Errors.Add("Mã đơn vị đã tồn tại");
            }
        }

        // Length validation
        if (createDto.Ma.Length > 20)
            result.Errors.Add("Mã đơn vị không được vượt quá 20 ký tự");

        if (createDto.TenViet.Length > 200)
            result.Errors.Add("Tên đơn vị (Tiếng Việt) không được vượt quá 200 ký tự");

        if (createDto.TenAnh.Length > 200)
            result.Errors.Add("Tên đơn vị (Tiếng Anh) không được vượt quá 200 ký tự");

        // Email format validation
        if (!string.IsNullOrEmpty(createDto.Email) && !IsValidEmail(createDto.Email))
            result.Errors.Add("Email không đúng định dạng");

        if (!string.IsNullOrEmpty(createDto.EmailNguoiDaiDien) && !IsValidEmail(createDto.EmailNguoiDaiDien))
            result.Errors.Add("Email người đại diện không đúng định dạng");

        // Phone format validation
        if (!string.IsNullOrEmpty(createDto.DienThoai) && !IsValidPhoneNumber(createDto.DienThoai))
            result.Errors.Add("Số điện thoại không đúng định dạng");

        if (!string.IsNullOrEmpty(createDto.MobileNguoiDaiDien) && !IsValidPhoneNumber(createDto.MobileNguoiDaiDien))
            result.Errors.Add("Mobile người đại diện không đúng định dạng");

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task<bool> ValidateForDeleteAsync(string khoa)
    {
        try
        {
            // Check if the business unit is being used
            var wasUsed = await _repository.WasUsedAsync(khoa);
            return !wasUsed;
        }
        catch (Exception)
        {
            return false; // If error checking dependencies, don't allow delete
        }
    }

    private async Task<ValidationResult> ValidateStatusUpdateAsync(UpdateDonViStatusDto statusDto)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(statusDto.Khoa))
            result.Errors.Add("Khoa không được để trống");

        // Business rule: Cannot deactivate if being used
        if (!statusDto.IsActive)
        {
            var wasUsed = await _repository.WasUsedAsync(statusDto.Khoa);
            if (wasUsed)
            {
                result.Errors.Add("Không thể ngừng hoạt động đơn vị đang được sử dụng");
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    private async Task ApplyBusinessRulesAsync(DonViDto dto, string action)
    {
        // Auto-generate Khoa if empty (new record)
        if (string.IsNullOrEmpty(dto.Khoa) && action.ToUpper() == "INSERT")
        {
            dto.Khoa = Guid.NewGuid().ToString();
        }

        // Trim whitespace
        dto.Ma = dto.Ma.Trim().ToUpper(); // Business unit codes should be uppercase
        dto.TenViet = dto.TenViet.Trim();
        dto.TenAnh = dto.TenAnh.Trim();
        dto.DiaChi = dto.DiaChi.Trim();
        dto.DienThoai = dto.DienThoai.Trim();
        dto.Fax = dto.Fax.Trim();
        dto.Email = dto.Email.Trim().ToLower(); // Email should be lowercase
        dto.NguoiDaiDien = dto.NguoiDaiDien.Trim();
        dto.TieuDeNguoiDaiDien = dto.TieuDeNguoiDaiDien.Trim();
        dto.MobileNguoiDaiDien = dto.MobileNguoiDaiDien.Trim();
        dto.EmailNguoiDaiDien = dto.EmailNguoiDaiDien.Trim().ToLower();
        dto.GhiChu = dto.GhiChu.Trim();
        dto.Prefix = dto.Prefix.Trim().ToUpper(); // Prefix should be uppercase

        // Set default values
        if (action.ToUpper() == "INSERT")
        {
            // Set default active status if not specified
            if (!dto.IsActive)
            {
                dto.IsActive = true;
            }
        }
    }

    private string ApplySecurityFilters(string conditions)
    {
        // Apply security filters based on user context
        // TODO: Add user-based security filters if needed

        // For now, just return the original conditions
        // In a real system, you might add filters like:
        // - Only show business units user has access to
        // - Filter by user's department/branch

        return conditions;
    }

    private string ValidateFieldList(string fields)
    {
        // Validate field list to prevent SQL injection
        var allowedFields = new[]
        {
            "Khoa", "Ma", "TenViet", "TenAnh", "DiaChi", "DienThoai", "Fax", "Email",
            "NguoiDaiDien", "TieuDeNguoiDaiDien", "MobileNguoiDaiDien", "EmailNguoiDaiDien",
            "GhiChu", "Prefix", "IsActive"
        };

        var requestedFields = fields.Split(',', '|')
            .Select(f => f.Trim())
            .Where(f => allowedFields.Contains(f, StringComparer.OrdinalIgnoreCase))
            .ToArray();

        return string.Join(", ", requestedFields);
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private bool IsValidPhoneNumber(string phoneNumber)
    {
        // Basic phone number validation - digits, spaces, dashes, parentheses, plus sign
        if (string.IsNullOrEmpty(phoneNumber))
            return true; // Empty is valid (optional field)

        // Remove common formatting characters
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace("+", "");

        // Check if remaining characters are all digits and length is reasonable
        return cleaned.All(char.IsDigit) && cleaned.Length >= 7 && cleaned.Length <= 15;
    }

    #endregion
}
