using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for SoChungTu (Document Numbering) entity
/// Maps exactly to HT_SoChungTu table in legacy database
/// Implements ALL properties from clsSoChungTu.cs (366 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// CRITICAL SYSTEM COMPONENT - Essential for document numbering across all transactions
/// </summary>
public class SoChungTuDto
{
    /// <summary>
    /// Document type code - Primary key
    /// Maps to: mLoaiChungTu property in legacy class
    /// </summary>
    [Required]
    [StringLength(10)]
    public string LoaiChungTu { get; set; } = string.Empty;

    /// <summary>
    /// Document prefix
    /// Maps to: mTienTo property in legacy class
    /// </summary>
    [StringLength(10)]
    public string TienTo { get; set; } = string.Empty;

    /// <summary>
    /// Document suffix
    /// Maps to: mHauTo property in legacy class
    /// </summary>
    [StringLength(10)]
    public string HauTo { get; set; } = string.Empty;

    /// <summary>
    /// Document type (Y=Year, Q=Quarter, M=Month, I=Individual)
    /// Maps to: mLoai property in legacy class
    /// </summary>
    [StringLength(1)]
    public string Loai { get; set; } = string.Empty;

    /// <summary>
    /// Document number length
    /// Maps to: mChieuDai property in legacy class
    /// </summary>
    public int ChieuDai { get; set; } = 10;

    /// <summary>
    /// Current document number
    /// Maps to: mSoChungTu property in legacy class
    /// </summary>
    public double SoChungTu { get; set; } = 0;

    /// <summary>
    /// Period (YYYYMM format)
    /// Maps to: mNamThang property in legacy class
    /// </summary>
    [StringLength(6)]
    public string NamThang { get; set; } = string.Empty;

    /// <summary>
    /// Description of document type
    /// </summary>
    [StringLength(200)]
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new SoChungTu
/// Contains only required fields for creation
/// </summary>
public class CreateSoChungTuDto
{
    [Required]
    [StringLength(10)]
    public string LoaiChungTu { get; set; } = string.Empty;

    [StringLength(10)]
    public string TienTo { get; set; } = string.Empty;

    [StringLength(10)]
    public string HauTo { get; set; } = string.Empty;

    [Required]
    [StringLength(1)]
    public string Loai { get; set; } = "M"; // Default to Monthly

    public int ChieuDai { get; set; } = 10;

    [StringLength(200)]
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating SoChungTu
/// Contains all updatable fields
/// </summary>
public class UpdateSoChungTuDto
{
    [StringLength(10)]
    public string TienTo { get; set; } = string.Empty;

    [StringLength(10)]
    public string HauTo { get; set; } = string.Empty;

    [StringLength(1)]
    public string Loai { get; set; } = string.Empty;

    public int ChieuDai { get; set; } = 10;

    [StringLength(200)]
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for SoChungTu list display
/// Optimized for list views
/// </summary>
public class SoChungTuListDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string TienTo { get; set; } = string.Empty;
    public string HauTo { get; set; } = string.Empty;
    public string Loai { get; set; } = string.Empty;
    public int ChieuDai { get; set; } = 10;
    public double SoChungTu { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
    public string LoaiText { get; set; } = string.Empty; // Friendly name for Loai
    public string NextNumber { get; set; } = string.Empty; // Preview of next number
}

/// <summary>
/// DTO for document number generation request
/// Used for creating new document numbers
/// </summary>
public class CreateVoucherRequestDto
{
    [Required]
    [StringLength(10)]
    public string LoaiChungTu { get; set; } = string.Empty;

    [Required]
    [StringLength(6)]
    public string NamThang { get; set; } = string.Empty;

    [StringLength(10)]
    public string CoVan { get; set; } = string.Empty; // For BaoGia vouchers

    [StringLength(10)]
    public string KhoaCoVan { get; set; } = string.Empty; // For BaoGia vouchers

    [StringLength(10)]
    public string MaDaiLy { get; set; } = string.Empty; // For BaoGia execution vouchers
}

/// <summary>
/// DTO for document number generation response
/// Contains the generated document number
/// </summary>
public class CreateVoucherResponseDto
{
    public string SoChungTu { get; set; } = string.Empty;
    public double SoChungTuNumber { get; set; } = 0;
    public string NamThang { get; set; } = string.Empty;
    public string LoaiChungTu { get; set; } = string.Empty;
    public bool Success { get; set; } = true;
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// DTO for undoing document numbers
/// Used for rollback operations
/// </summary>
public class UndoVoucherRequestDto
{
    [Required]
    [StringLength(10)]
    public string LoaiChungTu { get; set; } = string.Empty;

    [Required]
    [StringLength(6)]
    public string NamThang { get; set; } = string.Empty;

    [Required]
    public double SoChungTu { get; set; }
}

/// <summary>
/// DTO for document numbering lookup/dropdown operations
/// Minimal data for UI components
/// </summary>
public class SoChungTuLookupDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string TienTo { get; set; } = string.Empty;
    public string Loai { get; set; } = string.Empty;
    public string LoaiText { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for document numbering validation operations
/// Used for validation and business rules
/// </summary>
public class SoChungTuValidationDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public bool IsValidLoaiChungTu { get; set; } = true;
    public bool IsValidLoai { get; set; } = true;
    public bool IsValidChieuDai { get; set; } = true;
    public bool CanDelete { get; set; } = true;
    public bool IsInUse { get; set; } = false;
    public List<string> ValidationErrors { get; set; } = new List<string>();
    public List<string> ValidationWarnings { get; set; } = new List<string>();
}

/// <summary>
/// DTO for document numbering search operations
/// Contains search criteria and filters
/// </summary>
public class SoChungTuSearchDto
{
    public string? LoaiChungTu { get; set; }
    public string? TienTo { get; set; }
    public string? Loai { get; set; }
    public string? DienGiai { get; set; }
    public int? ChieuDaiMin { get; set; }
    public int? ChieuDaiMax { get; set; }
}

/// <summary>
/// DTO for automotive document numbering
/// Specialized for automotive document types
/// </summary>
public class AutomotiveDocumentNumberingDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string TienTo { get; set; } = string.Empty;
    public string DocumentCategory { get; set; } = string.Empty; // Service/Sales/Maintenance
    public bool IsServiceDocument { get; set; } = false;
    public bool IsSalesDocument { get; set; } = false;
    public bool IsMaintenanceDocument { get; set; } = false;
    public bool IsWarrantyDocument { get; set; } = false;
    public string NextNumber { get; set; } = string.Empty;
    public string SampleFormat { get; set; } = string.Empty;
}

/// <summary>
/// DTO for document numbering statistics
/// Shows usage statistics for document types
/// </summary>
public class DocumentNumberingStatsDto
{
    public string LoaiChungTu { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public double CurrentNumber { get; set; } = 0;
    public int TotalDocuments { get; set; } = 0;
    public int DocumentsThisMonth { get; set; } = 0;
    public int DocumentsThisYear { get; set; } = 0;
    public string LastUsedDate { get; set; } = string.Empty;
    public double AveragePerMonth { get; set; } = 0;
}
