using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using Microsoft.Data.SqlClient;

namespace GP.Mobile.Core.Repositories;

/// <summary>
/// Repository for Log (Audit Trail) operations
/// Implements ALL methods from clsLog.cs (201 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUDIT SYSTEM COMPONENT - Essential for tracking user actions and system changes
/// </summary>
public interface ILogRepository
{
    #region Legacy Methods
    
    Task<bool> InsertAsync(LogDto dto);
    Task<DataTable> GetListAsync(string conditions = "");
    Task<string> GetGuidIdAsync(string id);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LogListDto>> GetAllAsync(int pageSize = 50, int pageNumber = 1);
    Task<LogDto?> GetByIdAsync(int id);
    Task<int> CreateAsync(CreateLogDto createDto);
    Task<IEnumerable<LogListDto>> SearchAsync(LogSearchDto searchDto);
    Task<IEnumerable<LogListDto>> GetByDocumentAsync(string loaiChungTu, string khoaChungTu);
    Task<IEnumerable<LogListDto>> GetByUserAsync(string nguoiDung, DateTime? fromDate = null, DateTime? toDate = null);
    Task<AuditTrailSummaryDto> GetAuditSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<IEnumerable<AutomotiveAuditLogDto>> GetAutomotiveAuditLogsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<LogValidationDto> ValidateAsync(CreateLogDto dto);
    Task<SystemActivityDto> GetSystemActivityAsync();
    Task<LogExportDto> ExportLogsAsync(LogSearchDto searchDto, string format = "CSV");
    Task<bool> CleanupOldLogsAsync(int retentionDays = 365);
    Task<LogGuidTrackingDto?> GetByGuidAsync(string tempId);
    Task<bool> SaveGuidTrackingAsync(LogGuidTrackingDto dto);
    
    #endregion
}

/// <summary>
/// Implementation of Log repository
/// Follows exact legacy SQL queries and business logic from clsLog.cs
/// </summary>
public class LogRepository : ILogRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<LogRepository> _logger;

    // Current instance data - matches legacy class private fields
    private int mId = 0;
    private string mLoaiChungTu = string.Empty;
    private string mKhoaChungTu = string.Empty;
    private string mHanhDong = string.Empty;
    private DateTime mNgayGio = DateTime.Now;
    private string mNguoiDung = string.Empty;

    public LogRepository(IDbConnection connection, ILogger<LogRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    /// <summary>
    /// Legacy Insert method - Exact implementation from clsLog.Insert()
    /// Uses stored procedure: sp_HT_Log_Insert
    /// </summary>
    public async Task<bool> InsertAsync(LogDto dto)
    {
        try
        {
            using var command = new SqlCommand("sp_HT_Log_Insert", (SqlConnection)_connection);
            command.CommandType = CommandType.StoredProcedure;
            
            command.Parameters.AddWithValue("@LoaiChungTu", dto.LoaiChungTu);
            command.Parameters.AddWithValue("@KhoaChungTu", dto.KhoaChungTu);
            command.Parameters.AddWithValue("@HanhDong", dto.HanhDong);
            command.Parameters.AddWithValue("@NgayGio", dto.NgayGio);
            command.Parameters.AddWithValue("@NguoiDung", dto.NguoiDung);

            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            await command.ExecuteNonQueryAsync();
            
            // Get the inserted ID - exact legacy implementation
            var getListResult = await GetListAsync($"KhoaChungTu = '{dto.KhoaChungTu}'");
            if (getListResult != null && getListResult.Rows.Count > 0)
            {
                mId = Convert.ToInt32(getListResult.Rows[getListResult.Rows.Count - 1]["Id"]);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in InsertAsync");
            return false;
        }
    }

    /// <summary>
    /// Legacy GetList method - Exact implementation from clsLog.GetList()
    /// SQL: SELECT Id, LoaiChungTu, KhoaChungTu, NgayGio, NguoiDung, HanhDong FROM HT_Log WHERE {conditions} ORDER BY NgayGio
    /// </summary>
    public async Task<DataTable> GetListAsync(string conditions = "")
    {
        try
        {
            string whereClause = string.Empty;
            if (!string.IsNullOrWhiteSpace(conditions))
            {
                whereClause = " WHERE " + conditions;
            }
            
            var sql = "SELECT Id, LoaiChungTu, KhoaChungTu, NgayGio, NguoiDung, HanhDong " +
                     "FROM HT_Log " + whereClause + " ORDER BY NgayGio";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var adapter = new SqlDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            
            return dataTable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListAsync");
            return new DataTable();
        }
    }

    /// <summary>
    /// Legacy GetGuidId method - Exact implementation from clsLog.GetGuidId()
    /// SQL: SELECT TempId FROM Temp_Log_SuaChuaChiTiet WHERE LogId = '{id}'
    /// </summary>
    public async Task<string> GetGuidIdAsync(string id)
    {
        try
        {
            var sql = $"SELECT TempId FROM Temp_Log_SuaChuaChiTiet WHERE LogId = '{id}'";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var adapter = new SqlDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            
            if (dataTable.Rows.Count > 0)
            {
                return dataTable.Rows[0]["TempId"]?.ToString() ?? string.Empty;
            }
            
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetGuidIdAsync");
            return string.Empty;
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LogListDto>> GetAllAsync(int pageSize = 50, int pageNumber = 1)
    {
        try
        {
            int offset = (pageNumber - 1) * pageSize;
            
            var sql = @"
                SELECT Id, LoaiChungTu, KhoaChungTu, HanhDong, NgayGio, NguoiDung, 
                       ISNULL(ChiTiet, '') as ChiTiet, ISNULL(DiaChi, '') as DiaChi
                FROM HT_Log 
                ORDER BY NgayGio DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            command.Parameters.AddWithValue("@Offset", offset);
            command.Parameters.AddWithValue("@PageSize", pageSize);
            
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            var results = new List<LogListDto>();
            
            while (await reader.ReadAsync())
            {
                var log = new LogListDto
                {
                    Id = reader.GetInt32("Id"),
                    LoaiChungTu = reader.GetString("LoaiChungTu")?.Trim() ?? string.Empty,
                    KhoaChungTu = reader.GetString("KhoaChungTu")?.Trim() ?? string.Empty,
                    HanhDong = reader.GetString("HanhDong")?.Trim() ?? string.Empty,
                    NgayGio = reader.GetDateTime("NgayGio"),
                    NguoiDung = reader.GetString("NguoiDung")?.Trim() ?? string.Empty,
                    ChiTiet = reader.GetString("ChiTiet")?.Trim() ?? string.Empty,
                    DiaChi = reader.GetString("DiaChi")?.Trim() ?? string.Empty
                };
                
                // Add calculated fields
                log.NgayGioText = log.NgayGio.ToString("dd/MM/yyyy HH:mm:ss");
                log.HanhDongText = GetActionText(log.HanhDong);
                log.LoaiChungTuText = GetDocumentTypeText(log.LoaiChungTu);
                log.TimeAgo = GetTimeAgo(log.NgayGio);
                
                results.Add(log);
            }
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetAllAsync");
            return new List<LogListDto>();
        }
    }

    public async Task<LogDto?> GetByIdAsync(int id)
    {
        try
        {
            var sql = "SELECT * FROM HT_Log WHERE Id = @Id";
            
            using var command = new SqlCommand(sql, (SqlConnection)_connection);
            command.Parameters.AddWithValue("@Id", id);
            
            if (_connection.State != ConnectionState.Open)
                await ((SqlConnection)_connection).OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new LogDto
                {
                    Id = reader.GetInt32("Id"),
                    LoaiChungTu = reader.GetString("LoaiChungTu")?.Trim() ?? string.Empty,
                    KhoaChungTu = reader.GetString("KhoaChungTu")?.Trim() ?? string.Empty,
                    HanhDong = reader.GetString("HanhDong")?.Trim() ?? string.Empty,
                    NgayGio = reader.GetDateTime("NgayGio"),
                    NguoiDung = reader.GetString("NguoiDung")?.Trim() ?? string.Empty,
                    ChiTiet = reader.IsDBNull("ChiTiet") ? string.Empty : reader.GetString("ChiTiet").Trim(),
                    DiaChi = reader.IsDBNull("DiaChi") ? string.Empty : reader.GetString("DiaChi").Trim(),
                    UserAgent = reader.IsDBNull("UserAgent") ? string.Empty : reader.GetString("UserAgent").Trim()
                };
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetByIdAsync");
            return null;
        }
    }

    public async Task<int> CreateAsync(CreateLogDto createDto)
    {
        try
        {
            var dto = new LogDto
            {
                LoaiChungTu = createDto.LoaiChungTu,
                KhoaChungTu = createDto.KhoaChungTu,
                HanhDong = createDto.HanhDong,
                NgayGio = DateTime.Now,
                NguoiDung = createDto.NguoiDung,
                ChiTiet = createDto.ChiTiet,
                DiaChi = createDto.DiaChi,
                UserAgent = createDto.UserAgent
            };
            
            var success = await InsertAsync(dto);
            return success ? mId : 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateAsync");
            return 0;
        }
    }

    // Placeholder implementations for remaining modern API methods
    public async Task<IEnumerable<LogListDto>> SearchAsync(LogSearchDto searchDto) => new List<LogListDto>();
    public async Task<IEnumerable<LogListDto>> GetByDocumentAsync(string loaiChungTu, string khoaChungTu) => new List<LogListDto>();
    public async Task<IEnumerable<LogListDto>> GetByUserAsync(string nguoiDung, DateTime? fromDate = null, DateTime? toDate = null) => new List<LogListDto>();
    public async Task<AuditTrailSummaryDto> GetAuditSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null) => new AuditTrailSummaryDto();
    public async Task<IEnumerable<AutomotiveAuditLogDto>> GetAutomotiveAuditLogsAsync(DateTime? fromDate = null, DateTime? toDate = null) => new List<AutomotiveAuditLogDto>();
    public async Task<LogValidationDto> ValidateAsync(CreateLogDto dto) => new LogValidationDto();
    public async Task<SystemActivityDto> GetSystemActivityAsync() => new SystemActivityDto();
    public async Task<LogExportDto> ExportLogsAsync(LogSearchDto searchDto, string format = "CSV") => new LogExportDto();
    public async Task<bool> CleanupOldLogsAsync(int retentionDays = 365) => false;
    public async Task<LogGuidTrackingDto?> GetByGuidAsync(string tempId) => null;
    public async Task<bool> SaveGuidTrackingAsync(LogGuidTrackingDto dto) => false;

    #endregion

    #region Private Helper Methods

    private string GetActionText(string hanhDong)
    {
        return hanhDong.ToUpper() switch
        {
            "INSERT" => "Thêm mới",
            "UPDATE" => "Cập nhật",
            "DELETE" => "Xóa",
            "VIEW" => "Xem",
            "PRINT" => "In",
            "EXPORT" => "Xuất",
            "LOGIN" => "Đăng nhập",
            "LOGOUT" => "Đăng xuất",
            _ => hanhDong
        };
    }

    private string GetDocumentTypeText(string loaiChungTu)
    {
        return loaiChungTu.ToUpper() switch
        {
            "BG" => "Báo giá",
            "SC" => "Sửa chữa",
            "NK" => "Nhập kho",
            "XK" => "Xuất kho",
            "HD" => "Hóa đơn",
            "TT" => "Thanh toán",
            _ => loaiChungTu
        };
    }

    private string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;
        
        if (timeSpan.TotalMinutes < 1)
            return "Vừa xong";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} phút trước";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} giờ trước";
        if (timeSpan.TotalDays < 30)
            return $"{(int)timeSpan.TotalDays} ngày trước";
        
        return dateTime.ToString("dd/MM/yyyy");
    }

    #endregion
}
