using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Models.Interfaces
{
    /// <summary>
    /// Repository interface for Insurance Image Management (Báo G<PERSON>á <PERSON> Ảnh <PERSON><PERSON><PERSON>)
    /// Provides methods for managing insurance approval images in SC_BaoGiaHinhAnhDuyetGiaBH table
    /// </summary>
    public interface IBaoGiaHinhAnhBHRepository
    {
        /// <summary>
        /// Load insurance image by quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Insurance image data or null if not found</returns>
        Task<BaoGiaHinhAnhBHDto?> LoadAsync(string khoaBaoGia);

        /// <summary>
        /// Save insurance image data (insert or update)
        /// </summary>
        /// <param name="insuranceImage">Insurance image data to save</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveAsync(BaoGiaHinhAnhBHDto insuranceImage);

        /// <summary>
        /// Delete insurance image by quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(string khoaBaoGia);

        /// <summary>
        /// Upload insurance image
        /// </summary>
        /// <param name="uploadDto">Upload data</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UploadImageAsync(UploadInsuranceImageDto uploadDto);

        /// <summary>
        /// Update insurance approval status
        /// </summary>
        /// <param name="approvalDto">Approval data</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateApprovalStatusAsync(InsuranceApprovalDto approvalDto);

        /// <summary>
        /// Get insurance images requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of images pending approval</returns>
        Task<List<InsuranceImageListDto>> GetPendingApprovalsAsync(string donViId);

        /// <summary>
        /// Get approved insurance images
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of approved images</returns>
        Task<List<InsuranceImageListDto>> GetApprovedImagesAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get rejected insurance images
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of rejected images</returns>
        Task<List<InsuranceImageListDto>> GetRejectedImagesAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Get insurance images by insurance company
        /// </summary>
        /// <param name="khoaBaoHiem">Insurance company ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of images for the insurance company</returns>
        Task<List<InsuranceImageListDto>> GetImagesByInsuranceCompanyAsync(string khoaBaoHiem, string fromDate, string toDate);

        /// <summary>
        /// Get insurance image statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>Insurance image statistics</returns>
        Task<InsuranceImageStatisticsDto> GetImageStatisticsAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Check if quotation has insurance image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if image exists, false otherwise</returns>
        Task<bool> HasInsuranceImageAsync(string khoaBaoGia);

        /// <summary>
        /// Get image thumbnail
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Thumbnail image data</returns>
        Task<byte[]?> GetImageThumbnailAsync(string khoaBaoGia);

        /// <summary>
        /// Get full image data
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Full image data</returns>
        Task<byte[]?> GetFullImageAsync(string khoaBaoGia);

        /// <summary>
        /// Update image metadata
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="moTa">Description</param>
        /// <param name="tenFile">File name</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateImageMetadataAsync(string khoaBaoGia, string moTa, string tenFile);

        /// <summary>
        /// Get images by approval status
        /// </summary>
        /// <param name="trangThaiDuyet">Approval status (0=Pending, 1=Approved, 2=Rejected)</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of images with specified status</returns>
        Task<List<InsuranceImageListDto>> GetImagesByStatusAsync(int trangThaiDuyet, string donViId);

        /// <summary>
        /// Bulk approve insurance images
        /// </summary>
        /// <param name="khoaBaoGiaList">List of quotation IDs to approve</param>
        /// <param name="approvalDto">Approval data</param>
        /// <returns>Number of successfully approved images</returns>
        Task<int> BulkApproveImagesAsync(List<string> khoaBaoGiaList, InsuranceApprovalDto approvalDto);

        /// <summary>
        /// Get insurance images for mobile app
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <returns>Paginated list of insurance images</returns>
        Task<PaginatedResult<InsuranceImageListDto>> GetImagesForMobileAsync(string donViId, int pageSize, int pageNumber);
    }


}
