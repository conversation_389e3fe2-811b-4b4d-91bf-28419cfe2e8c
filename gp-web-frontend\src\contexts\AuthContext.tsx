'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  username: string
  fullName: string
  clientId: string
  clientName: string
  permissions: string[]
}

interface AuthContextType {
  user: User | null
  login: (username: string, password: string, clientId: string) => Promise<boolean>
  logout: () => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check for stored authentication on mount
    const checkAuth = async () => {
      try {
        // Only access localStorage on client side
        if (typeof window !== 'undefined') {
          const token = localStorage.getItem('auth_token')
          const userData = localStorage.getItem('user_data')

          if (token && userData) {
            const parsedUser = JSON.parse(userData)
            setUser(parsedUser)
          }
        }
      } catch (error) {
        console.error('Error checking authentication:', error)
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_data')
        }
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (username: string, password: string, clientId: string): Promise<boolean> => {
    try {
      setIsLoading(true)

      // Call the real authentication API
      const response = await fetch('http://localhost:5000/authentication/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          Username: username,
          Password: password,
          ClientId: clientId,
          DeviceId: 'web-browser',
          DeviceType: 'web',
          RememberMe: false
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Login failed:', errorData.message || 'Authentication failed')
        return false
      }

      const data = await response.json()

      // Map API response to our User interface
      const user: User = {
        id: data.userInfo.userId,
        username: data.userInfo.username,
        fullName: data.userInfo.employeeName,
        clientId: data.userInfo.clientId,
        clientName: data.userInfo.clientName,
        permissions: data.userInfo.permissions
      }

      // Store authentication data (client-side only)
      if (typeof window !== 'undefined') {
        localStorage.setItem('auth_token', data.accessToken)
        localStorage.setItem('refresh_token', data.refreshToken)
        localStorage.setItem('user_data', JSON.stringify(user))
      }

      setUser(user)
      router.push('/dashboard')
      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Call logout API if we have a token
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth_token')
        if (token) {
          await fetch('http://localhost:5000/authentication/logout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          })
        }

        // Clear local storage
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_data')
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Still clear local storage even if API call fails
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_data')
      }
    } finally {
      setUser(null)
      router.push('/login')
    }
  }

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { user, isLoading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!isLoading && !user) {
        router.push('/login')
      }
    }, [user, isLoading, router])

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      )
    }

    if (!user) {
      return null
    }

    return <Component {...props} />
  }
}
