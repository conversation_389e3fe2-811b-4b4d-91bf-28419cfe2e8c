'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Car, 
  Users, 
  FileText, 
  Package, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { withAuth } from '@/contexts/AuthContext'

// Mock data - in real app, this would come from API
const stats = {
  totalVehicles: 1247,
  totalCustomers: 892,
  pendingQuotations: 23,
  completedServices: 156,
  monthlyRevenue: 2450000000, // VND
  inventoryItems: 3421
}

const recentActivities = [
  {
    id: 1,
    type: 'quotation',
    title: 'Báo giá sửa chữa xe Toyota Camry',
    customer: 'Nguyễn Văn A',
    time: '2 giờ trước',
    status: 'pending'
  },
  {
    id: 2,
    type: 'service',
    title: '<PERSON><PERSON><PERSON> thành bảo dưỡng xe Honda Civic',
    customer: 'Trần Thị B',
    time: '4 giờ trước',
    status: 'completed'
  },
  {
    id: 3,
    type: 'inventory',
    title: 'Nhập kho phụ tùng phanh',
    customer: 'Kho chính',
    time: '6 giờ trước',
    status: 'completed'
  },
  {
    id: 4,
    type: 'customer',
    title: 'Khách hàng mới đăng ký',
    customer: 'Lê Văn C',
    time: '1 ngày trước',
    status: 'new'
  }
]

const upcomingServices = [
  {
    id: 1,
    vehicle: 'Toyota Vios - 30A-12345',
    customer: 'Nguyễn Văn D',
    service: 'Bảo dưỡng định kỳ',
    date: '2024-06-18',
    time: '09:00'
  },
  {
    id: 2,
    vehicle: 'Honda City - 51G-67890',
    customer: 'Trần Thị E',
    service: 'Thay dầu máy',
    date: '2024-06-18',
    time: '14:00'
  },
  {
    id: 3,
    vehicle: 'Mazda 3 - 29B-11111',
    customer: 'Lê Văn F',
    service: 'Sửa chữa hệ thống phanh',
    date: '2024-06-19',
    time: '10:30'
  }
]

function DashboardPage() {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount)
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'quotation':
        return <FileText className="h-4 w-4" />
      case 'service':
        return <Car className="h-4 w-4" />
      case 'inventory':
        return <Package className="h-4 w-4" />
      case 'customer':
        return <Users className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'new':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Tổng quan hệ thống</h1>
        <p className="text-muted-foreground">
          Chào mừng bạn đến với hệ thống quản lý sửa chữa ô tô CARSOFT GP
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng số xe</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVehicles.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khách hàng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +8% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Báo giá chờ duyệt</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingQuotations}</div>
            <p className="text-xs text-muted-foreground">
              Cần xử lý trong ngày
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Doanh thu tháng</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.monthlyRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              +15% so với tháng trước
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="activities" className="space-y-4">
        <TabsList>
          <TabsTrigger value="activities">Hoạt động gần đây</TabsTrigger>
          <TabsTrigger value="schedule">Lịch hẹn sắp tới</TabsTrigger>
          <TabsTrigger value="reports">Báo cáo nhanh</TabsTrigger>
        </TabsList>

        <TabsContent value="activities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hoạt động gần đây</CardTitle>
              <CardDescription>
                Các hoạt động mới nhất trong hệ thống
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground">
                        {activity.title}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {activity.customer} • {activity.time}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      {getStatusIcon(activity.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lịch hẹn sắp tới</CardTitle>
              <CardDescription>
                Các cuộc hẹn sửa chữa và bảo dưỡng trong những ngày tới
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingServices.map((service) => (
                  <div key={service.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{service.vehicle}</p>
                      <p className="text-sm text-muted-foreground">{service.customer}</p>
                      <p className="text-sm text-muted-foreground">{service.service}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{service.date}</p>
                      <p className="text-sm text-muted-foreground">{service.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Báo cáo doanh thu</CardTitle>
                <CardDescription>Thống kê doanh thu theo thời gian</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Biểu đồ doanh thu sẽ được hiển thị tại đây</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Báo cáo hiệu suất</CardTitle>
                <CardDescription>Thống kê hiệu suất làm việc</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Biểu đồ hiệu suất sẽ được hiển thị tại đây</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default withAuth(DashboardPage)
