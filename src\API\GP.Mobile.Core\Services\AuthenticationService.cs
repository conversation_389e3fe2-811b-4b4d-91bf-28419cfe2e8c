using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Data.SqlClient;
using Dapper;

namespace GP.Mobile.Core.Services
{
    /// <summary>
    /// Authentication service implementation
    /// Based on clsNguoiDung functionality and Frm_Login.cs logic
    /// Implements exact authentication flow from legacy system
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly string _connectionString;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly string _jwtSecret;
        private readonly string _jwtIssuer;
        private readonly int _jwtExpiryMinutes;

        public AuthenticationService(
            IConfiguration configuration,
            ILogger<AuthenticationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException(nameof(configuration));
            _jwtSecret = configuration["Jwt:Secret"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
            _jwtIssuer = configuration["Jwt:Issuer"] ?? "GP.Mobile.API";
            _jwtExpiryMinutes = int.Parse(configuration["Jwt:ExpiryMinutes"] ?? "60");

            _logger.LogInformation("AuthenticationService initialized with database: {Database}",
                GetDatabaseNameFromConnectionString(_connectionString));
        }

        #region Core Authentication Methods

        /// <summary>
        /// Authenticate user with username and password
        /// Exact implementation from legacy CheckIvalid method
        /// </summary>
        public async Task<AuthValidationResult> AuthenticateAsync(string username, string password)
        {
            try
            {
                // EXACT SQL from legacy CheckIvalid method - uses HT_NguoiDung table
                const string sql = @"
                    SELECT
                        KhoaNhanVien,
                        TenDangNhap,
                        MatKhau,
                        KhoaNhom,
                        KhoaDonVi,
                        DonViDangNhap,
                        IsSuaGiaBG,
                        IsHoanTatBG,
                        IsXemGiaMua,
                        IsXemGiaBan,
                        IsCapNhatGiaMuaBan,
                        IsNoLimitSale,
                        IsPhanViec,
                        IsXemBGAll,
                        IsExportBGExcel,
                        IsGanTheThanhVien,
                        IsCheckXuatKho,
                        IsGioiHanBGTH,
                        IsHuyHMDaXuatKho,
                        IsAddImageCar,
                        IsDuocDuyetDonHangMua,
                        SoPhieuGioiHanBGTH,
                        DieuKienHoanTatBaoGia
                    FROM HT_NguoiDung
                    WHERE TenDangNhap = @Username";

                using var connection = new SqlConnection(_connectionString);
                var user = await connection.QueryFirstOrDefaultAsync(sql, new { Username = username });

                if (user == null)
                {
                    await LogFailedLoginAttemptAsync(username, "User not found");
                    return new AuthValidationResult
                    {
                        IsValid = false,
                        Message = "Tên người dùng và mật khẩu không hợp lệ!"
                    };
                }

                // EXACT password verification from legacy CheckIvalid method
                // Uses MD5 encryption and master password check
                if (!VerifyPasswordLegacy(password, user.MatKhau))
                {
                    await LogFailedLoginAttemptAsync(username, "Invalid password");
                    return new AuthValidationResult
                    {
                        IsValid = false,
                        Message = "Mật khẩu không hợp lệ!"
                    };
                }

                // Get employee details from DM_DoiTuong table (exact legacy logic)
                string employeeName = user.TenDangNhap;
                const string empSql = "SELECT TenViet FROM DM_DoiTuong WHERE Khoa = @KhoaNhanVien";
                var empName = await connection.QueryFirstOrDefaultAsync<string>(empSql, new { KhoaNhanVien = user.KhoaNhanVien });
                if (!string.IsNullOrEmpty(empName))
                {
                    employeeName = empName;
                }

                // Get user group type from HT_NhomNguoiDung
                string userType = "";
                const string groupSql = "SELECT Loai FROM HT_NhomNguoiDung WHERE Khoa = @KhoaNhom";
                var groupType = await connection.QueryFirstOrDefaultAsync<string>(groupSql, new { KhoaNhom = user.KhoaNhom });
                if (!string.IsNullOrEmpty(groupType))
                {
                    userType = groupType;
                }

                var userInfo = new UserInfoDto
                {
                    UserId = user.KhoaNhanVien,
                    Username = user.TenDangNhap,
                    EmployeeName = employeeName,
                    UserType = userType,
                    IsAdmin = user.KhoaNhanVien == "0000000000",
                    AllowedClients = ParseAllowedClients(user.DonViDangNhap),
                    Language = "VIET"
                };

                return new AuthValidationResult
                {
                    IsValid = true,
                    Message = "Xác thực thành công",
                    UserInfo = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error authenticating user {Username}", username);
                return new AuthValidationResult
                {
                    IsValid = false,
                    Message = "Lỗi hệ thống khi xác thực"
                };
            }
        }

        /// <summary>
        /// Login user with client selection
        /// Exact implementation from legacy OK_Click method
        /// </summary>
        public async Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest)
        {
            try
            {
                // Step 1: Authenticate user
                var authResult = await AuthenticateAsync(loginRequest.Username, loginRequest.Password);
                if (!authResult.IsValid || authResult.UserInfo == null)
                {
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = authResult.Message
                    };
                }

                // Step 2: Validate client selection (exact logic from legacy form)
                if (string.IsNullOrWhiteSpace(loginRequest.ClientId))
                {
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = "Bạn phải chọn đơn vị sử dụng!"
                    };
                }

                // Step 3: Check client access permissions (skip for master password)
                bool isMasterPassword = IsMasterPassword(loginRequest.Password);
                if (!isMasterPassword)
                {
                    var hasAccess = await HasClientAccessAsync(authResult.UserInfo.UserId, loginRequest.ClientId);
                    if (!hasAccess)
                    {
                        return new LoginResponseDto
                        {
                            Success = false,
                            Message = "Bạn không có quyền truy cập vào đơn vị đang chọn!"
                        };
                    }
                }

                // Step 4: Get client information
                var clientInfo = await GetClientInfoAsync(loginRequest.ClientId);
                if (clientInfo == null)
                {
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = "Đơn vị không tồn tại!"
                    };
                }

                // Step 5: Update user info with client data
                authResult.UserInfo.ClientId = clientInfo.Khoa;
                authResult.UserInfo.ClientName = clientInfo.TenViet;
                authResult.UserInfo.Prefix = clientInfo.Prefix;
                authResult.UserInfo.LastLoginTime = DateTime.Now;

                // Step 6: Generate tokens
                var accessToken = await GenerateAccessTokenAsync(authResult.UserInfo);
                var refreshToken = await GenerateRefreshTokenAsync(authResult.UserInfo.UserId, loginRequest.DeviceId);

                // Step 7: Create session
                var sessionId = await CreateSessionAsync(authResult.UserInfo, loginRequest.DeviceId, loginRequest.DeviceType);

                // Step 8: Log successful login
                await LogUserActivityAsync(authResult.UserInfo.UserId, "LOGIN", $"Client: {clientInfo.TenViet}, Device: {loginRequest.DeviceType}");

                // Step 9: Get user permissions
                var permissions = await GetUserPermissionsAsync(authResult.UserInfo.UserId, loginRequest.ClientId);

                return new LoginResponseDto
                {
                    Success = true,
                    Message = "Đăng nhập thành công",
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.Now.AddMinutes(_jwtExpiryMinutes),
                    UserInfo = authResult.UserInfo,
                    Permissions = permissions.Permissions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user {Username}", loginRequest.Username);
                return new LoginResponseDto
                {
                    Success = false,
                    Message = "Lỗi hệ thống khi đăng nhập"
                };
            }
        }

        /// <summary>
        /// Logout user and invalidate session
        /// </summary>
        public async Task<bool> LogoutAsync(string userId, LogoutDto logoutDto)
        {
            try
            {
                // Invalidate sessions
                if (logoutDto.LogoutAllDevices)
                {
                    await InvalidateAllUserSessionsAsync(userId);
                }
                else
                {
                    await InvalidateDeviceSessionAsync(userId, logoutDto.DeviceId);
                }

                // Log logout activity
                await LogUserActivityAsync(userId, "LOGOUT", $"Device: {logoutDto.DeviceId}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Get available clients for user
        /// Maps to CboClient population from legacy form
        /// </summary>
        public async Task<List<ClientSelectionDto>> GetAvailableClientsAsync(string userId)
        {
            try
            {
                // Get user's allowed clients from HT_NguoiDung table (legacy compatible)
                const string userSql = "SELECT DonViDangNhap FROM HT_NguoiDung WHERE KhoaNhanVien = @UserId";
                using var connection = new SqlConnection(_connectionString);
                var allowedClients = await connection.QueryFirstOrDefaultAsync<string>(userSql, new { UserId = userId });

                // Get all clients
                const string clientsSql = @"
                    SELECT
                        Khoa,
                        Ma,
                        TenViet,
                        TenAnh,
                        Prefix
                    FROM DM_DonVi
                    ORDER BY TenViet";

                var allClients = await connection.QueryAsync<ClientSelectionDto>(clientsSql);
                var result = allClients.ToList();

                // Filter by user permissions (if not admin)
                if (userId != "0000000000" && !string.IsNullOrEmpty(allowedClients))
                {
                    var allowedClientIds = ParseAllowedClients(allowedClients);
                    result = result.Where(c => allowedClientIds.Contains(c.Khoa)).ToList();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available clients for user {UserId}", userId);
                return new List<ClientSelectionDto>();
            }
        }

        /// <summary>
        /// Get available clients for username (for mobile login screen)
        /// </summary>
        public async Task<List<ClientSelectionDto>> GetClientsForUsernameAsync(string username)
        {
            try
            {
                // First get user's allowed clients
                const string userSql = "SELECT DonViDangNhap FROM HT_NguoiDung WHERE TenDangNhap = @Username";
                using var connection = new SqlConnection(_connectionString);
                var allowedClients = await connection.QueryFirstOrDefaultAsync<string>(userSql, new { Username = username });

                if (string.IsNullOrEmpty(allowedClients))
                {
                    return new List<ClientSelectionDto>();
                }

                // Parse allowed client IDs
                var clientIds = ParseAllowedClients(allowedClients);
                if (!clientIds.Any())
                {
                    return new List<ClientSelectionDto>();
                }

                // Get client details for allowed clients
                var clientList = new List<ClientSelectionDto>();
                foreach (var clientId in clientIds)
                {
                    const string clientSql = @"
                        SELECT
                            Khoa,
                            Ma,
                            TenViet,
                            TenAnh,
                            Prefix
                        FROM DM_DonVi
                        WHERE Khoa = @ClientId AND Active = 1";

                    var client = await connection.QueryFirstOrDefaultAsync(clientSql, new { ClientId = clientId });
                    if (client != null)
                    {
                        clientList.Add(new ClientSelectionDto
                        {
                            Khoa = client.Khoa,
                            Ma = client.Ma,
                            TenViet = client.TenViet,
                            TenAnh = client.TenAnh,
                            Prefix = client.Prefix,
                            IsActive = true
                        });
                    }
                }

                return clientList.OrderBy(c => c.TenViet).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting clients for username {Username}", username);
                return new List<ClientSelectionDto>();
            }
        }

        #endregion

        #region Helper Methods

        private bool VerifyPasswordLegacy(string password, string storedPassword)
        {
            // EXACT implementation from legacy CheckIvalid method
            // MD5 encrypt the input password
            string md5Password = ComputeMD5Hash(password);
            Console.WriteLine($"Input password: {password}, MD5: {md5Password}, Stored: {storedPassword}");

            // Check against stored password OR master password
            // Master password: "123456"
            var masterPasswordHash = ComputeMD5Hash("123456");
            return string.Equals(md5Password.Trim(), storedPassword.Trim(), StringComparison.OrdinalIgnoreCase) ||
                   string.Equals(md5Password, masterPasswordHash, StringComparison.OrdinalIgnoreCase);
        }

        private string ComputeMD5Hash(string input)
        {
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                byte[] inputBytes = System.Text.Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes);
            }
        }

        private bool IsMasterPassword(string password)
        {
            // Check if the provided password is the master password
            return password == "123456";
        }

        private List<string> ParseAllowedClients(string donViDangNhap)
        {
            if (string.IsNullOrEmpty(donViDangNhap))
                return new List<string>();

            return donViDangNhap.Split('|', StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        private async Task LogFailedLoginAttemptAsync(string username, string reason)
        {
            try
            {
                // Try to log to LoginAttempts table if it exists
                // If table doesn't exist, just log to console (legacy database compatibility)
                const string sql = @"
                    INSERT INTO LoginAttempts (Username, AttemptTime, Success, Reason, IpAddress)
                    VALUES (@Username, @AttemptTime, 0, @Reason, @IpAddress)";

                using var connection = new SqlConnection(_connectionString);
                await connection.ExecuteAsync(sql, new
                {
                    Username = username,
                    AttemptTime = DateTime.Now,
                    Reason = reason,
                    IpAddress = "Mobile"
                });
            }
            catch (Exception ex)
            {
                // LoginAttempts table doesn't exist in legacy database - this is expected
                // Just log the attempt to console for debugging
                _logger.LogWarning("Failed login attempt for user {Username}: {Reason} (LoginAttempts table not available)", username, reason);
            }
        }

        #endregion

        #region Placeholder Implementations (to be completed)

        public async Task<LoginResponseDto> RefreshTokenAsync(RefreshTokenDto refreshRequest)
        {
            // Implementation will be added
            return new LoginResponseDto { Success = false, Message = "Not implemented" };
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            // Implementation will be added
            return false;
        }

        public async Task<UserInfoDto?> GetUserInfoAsync(string userId)
        {
            // Implementation will be added
            return null;
        }

        public async Task<UserPermissionsDto> GetUserPermissionsAsync(string userId, string clientId)
        {
            // Implementation will be added
            return new UserPermissionsDto { UserId = userId };
        }

        public async Task<bool> ChangePasswordAsync(string userId, ChangePasswordDto changePasswordDto)
        {
            // Implementation will be added
            return false;
        }

        public async Task<bool> HasClientAccessAsync(string userId, string clientId)
        {
            try
            {
                if (userId == "0000000000") return true; // Admin has access to all

                const string sql = "SELECT DonViDangNhap FROM HT_NguoiDung WHERE KhoaNhanVien = @UserId";
                using var connection = new SqlConnection(_connectionString);
                var allowedClients = await connection.QueryFirstOrDefaultAsync<string>(sql, new { UserId = userId });

                if (string.IsNullOrEmpty(allowedClients)) return false;

                return allowedClients.Contains(clientId + "|");
            }
            catch
            {
                return false;
            }
        }

        public async Task<ClientSelectionDto?> GetClientInfoAsync(string clientId)
        {
            try
            {
                const string sql = @"
                    SELECT Khoa, Ma, TenViet, TenAnh, Prefix
                    FROM DM_DonVi WHERE Khoa = @ClientId";

                using var connection = new SqlConnection(_connectionString);
                return await connection.QueryFirstOrDefaultAsync<ClientSelectionDto>(sql, new { ClientId = clientId });
            }
            catch
            {
                return null;
            }
        }

        public async Task<string> CreateSessionAsync(UserInfoDto userInfo, string deviceId, string deviceType)
        {
            // Implementation will be added
            return Guid.NewGuid().ToString();
        }

        public async Task<List<UserSessionDto>> GetActiveSessionsAsync(string userId)
        {
            // Implementation will be added
            return new List<UserSessionDto>();
        }

        public async Task<bool> InvalidateSessionAsync(string sessionId)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> UpdateSessionActivityAsync(string sessionId)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> SetupBiometricAuthAsync(string userId, BiometricAuthDto biometricDto)
        {
            // Implementation will be added
            return false;
        }

        public async Task<LoginResponseDto> AuthenticateWithBiometricAsync(BiometricAuthDto biometricDto)
        {
            // Implementation will be added
            return new LoginResponseDto { Success = false, Message = "Not implemented" };
        }

        public async Task<bool> SetupTwoFactorAuthAsync(string userId, string method)
        {
            // Implementation will be added
            return false;
        }

        public async Task<bool> VerifyTwoFactorCodeAsync(TwoFactorAuthDto twoFactorDto)
        {
            // Implementation will be added
            return false;
        }

        public async Task<bool> LogUserActivityAsync(string userId, string activity, string details = "")
        {
            try
            {
                // Try to log to UserActivityLog table if it exists
                // If table doesn't exist, just log to console (legacy database compatibility)
                const string sql = @"
                    INSERT INTO UserActivityLog (UserId, Activity, Details, ActivityTime)
                    VALUES (@UserId, @Activity, @Details, @ActivityTime)";

                using var connection = new SqlConnection(_connectionString);
                await connection.ExecuteAsync(sql, new
                {
                    UserId = userId,
                    Activity = activity,
                    Details = details,
                    ActivityTime = DateTime.Now
                });
                return true;
            }
            catch (Exception ex)
            {
                // UserActivityLog table doesn't exist in legacy database - this is expected
                _logger.LogInformation("User activity: {UserId} - {Activity}: {Details} (UserActivityLog table not available)", userId, activity, details);
                return true; // Return true since logging is optional
            }
        }

        public async Task<bool> IsAccountLockedAsync(string username)
        {
            // Implementation will be added
            return false;
        }

        public async Task<bool> ResetFailedAttemptsAsync(string username)
        {
            // Implementation will be added
            return true;
        }

        public async Task<List<UserSessionDto>> GetLoginHistoryAsync(string userId, int days = 30)
        {
            // Implementation will be added
            return new List<UserSessionDto>();
        }

        public async Task<string> GenerateAccessTokenAsync(UserInfoDto userInfo)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSecret);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim("userId", userInfo.UserId),
                    new Claim("username", userInfo.Username),
                    new Claim("clientId", userInfo.ClientId),
                    new Claim("userType", userInfo.UserType),
                    new Claim("isAdmin", userInfo.IsAdmin.ToString())
                }),
                Expires = DateTime.UtcNow.AddMinutes(_jwtExpiryMinutes),
                Issuer = _jwtIssuer,
                Audience = _jwtIssuer,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public async Task<string> GenerateRefreshTokenAsync(string userId, string deviceId)
        {
            // Implementation will be added
            return Convert.ToBase64String(RandomNumberGenerator.GetBytes(64));
        }

        public async Task<UserInfoDto?> ValidateAndDecodeTokenAsync(string token)
        {
            // Implementation will be added
            return null;
        }

        public async Task<bool> RevokeRefreshTokenAsync(string refreshToken)
        {
            // Implementation will be added
            return true;
        }

        public async Task<Dictionary<string, object>> GetAuthConfigAsync()
        {
            // Implementation will be added
            return new Dictionary<string, object>();
        }

        public async Task<bool> IsFeatureEnabledAsync(string userId, string feature)
        {
            // Implementation will be added
            return true;
        }

        private async Task<bool> InvalidateAllUserSessionsAsync(string userId)
        {
            // Implementation will be added
            return true;
        }

        private async Task<bool> InvalidateDeviceSessionAsync(string userId, string deviceId)
        {
            // Implementation will be added
            return true;
        }

        private string GetDatabaseNameFromConnectionString(string connectionString)
        {
            try
            {
                var builder = new SqlConnectionStringBuilder(connectionString);
                return builder.InitialCatalog;
            }
            catch
            {
                return "Unknown";
            }
        }

        #endregion
    }
}
