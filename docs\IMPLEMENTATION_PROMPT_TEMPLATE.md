# DM Class Implementation Prompt Template

## Overview
This document provides the exact prompting approach that has been successfully used to implement DM classes with 100% legacy compatibility and comprehensive modern API features.

## Success Pattern Summary
We have successfully implemented 12/181 classes (6.6%) with this approach:
- **4 Automotive DM Classes**: DM_LoaiDichVu, DM_LoaiXe, DM_HangSanXuat, DM_BaoDuong
- **3 Foundation DM Classes**: DM_DonViTinh, DM_LoaiTien, DM_DonVi
- **5 Business Classes**: DoiTuong, CoHoi, BaoGia, NhapKho, XuatKho

## Exact Prompt Template

### Step 1: Initial Analysis Request
```
I want you to implement [CLASS_NAME] next. Let me examine the legacy [LEGACY_FILE_NAME] class to understand its structure:

[Examine the legacy file with view command to see properties, methods, and line count]
```

### Step 2: Implementation Declaration
```
Perfect! Now I can see that **[LEGACY_FILE_NAME]** ([CLASS_DESCRIPTION]) is [LINE_COUNT] lines and has a comprehensive structure for [BUSINESS_DOMAIN]. It has:

**Properties ([PROPERTY_COUNT]):**
- [List all properties with descriptions]

**Methods ([METHOD_COUNT]):**
- [List all methods with brief descriptions]

**Table:** `[TABLE_NAME]`

This is perfect for [BUSINESS_CONTEXT]! [Explain business importance]. Let me implement it:

## 🚗 **Starting [CLASS_NAME] Implementation**
```

### Step 3: DTO Implementation
```
[Create comprehensive DTO file with save-file command]

Key DTO Requirements:
1. **Main DTO**: Maps exactly to legacy table with ALL properties
2. **Supporting DTOs**: 
   - ListDto (for display with joins)
   - CreateDto (for creation)
   - UpdateStatusDto (for status updates)
   - SearchDto (for filtering)
   - LookupDto (for dropdowns)
   - ValidationDto (for validation)
   - [Domain-specific DTOs based on business context]
3. **Documentation**: Each property must have XML comments mapping to legacy
4. **Validation**: Data annotations for required fields and lengths
5. **Business Context**: Include domain-specific DTOs (e.g., automotive, financial)
```

### Step 4: Repository Implementation
```
[Create comprehensive Repository with save-file command, then extend with str-replace-editor]

Key Repository Requirements:
1. **Legacy Methods**: ALL methods from legacy class with exact SQL queries
2. **Modern API Methods**: Full CRUD + advanced features
3. **SQL Compatibility**: Use exact SQL from legacy, including stored procedures
4. **Error Handling**: Comprehensive try-catch with logging
5. **Business Logic**: Implement exact legacy business rules
6. **Helper Methods**: ConvertToDataTable and other utilities
7. **Line Count**: Typically 800+ lines for comprehensive implementation
```

### Step 5: Service Implementation
```
[Create comprehensive Service with save-file command, then extend with str-replace-editor]

Key Service Requirements:
1. **Legacy Methods**: All legacy methods with business validation
2. **Modern API Methods**: Full modern API support
3. **Validation**: Comprehensive validation methods with business rules
4. **Business Rules**: Apply domain-specific business logic
5. **Security**: Security filters and field validation
6. **Error Handling**: Proper exception handling and logging
7. **Line Count**: Typically 650+ lines for comprehensive implementation
```

### Step 6: Controller Implementation
```
[Create comprehensive Controller with save-file command, then extend with str-replace-editor]

Key Controller Requirements:
1. **Modern REST Endpoints**: Full CRUD with proper HTTP verbs
2. **Legacy Method Endpoints**: All legacy methods as POST endpoints
3. **Request DTOs**: Create request DTOs for legacy methods
4. **Error Handling**: Proper HTTP status codes and error messages
5. **Documentation**: XML comments for all endpoints
6. **Business Context**: Domain-specific endpoints
7. **Line Count**: Typically 600+ lines for comprehensive implementation
```

### Step 7: Service Registration
```
[Update Program.cs to register new services in DI container]

Add both Repository and Service registrations:
- builder.Services.AddScoped<I[CLASS_NAME]Repository, [CLASS_NAME]Repository>();
- builder.Services.AddScoped<I[CLASS_NAME]Service, [CLASS_NAME]Service>();
```

### Step 8: Build and Commit
```
[Build the API to ensure everything works]
[Commit with descriptive message following the established pattern]

Commit Message Pattern:
"feat: Implement [CLASS_NAME] ([DESCRIPTION]) - [BUSINESS_FOCUS]

Complete implementation with [PROPERTY_COUNT] properties, [METHOD_COUNT]+ legacy methods
- [CLASS_NAME]Dto, Repository ([REPO_LINES]+ lines), Service ([SERVICE_LINES]+ lines), Controller ([CONTROLLER_LINES]+ lines)
- 100% legacy compatibility with [LEGACY_FILE_NAME] ([LEGACY_LINES] lines)
- [BUSINESS_FOCUS]: [Business importance description]
- [Key features list]
- All builds successful

Progress: [COMPLETED]/181 classes ([PERCENTAGE]%) - [MILESTONE] complete!"
```

## Critical Success Factors

### 1. **100% Legacy Compatibility**
- Use EXACT SQL queries from legacy code
- Maintain exact method signatures and behavior
- Preserve all business logic and validation rules
- Include line number references to legacy code

### 2. **Comprehensive Implementation**
- Implement ALL properties and methods from legacy
- Create extensive supporting DTOs for different use cases
- Include domain-specific features (automotive, financial, etc.)
- Add modern API enhancements while preserving legacy

### 3. **Business Context Focus**
- Emphasize the business domain (🚗 AUTOMOTIVE FOCUSED, 💰 FINANCIAL, etc.)
- Explain business importance and relationships
- Include domain-specific validation and categorization
- Add analytics and reporting capabilities

### 4. **Code Quality Standards**
- Comprehensive error handling and logging
- Proper validation with detailed error messages
- Security filters and field validation
- Extensive XML documentation

### 5. **File Organization**
- DTOs in GP.Mobile.Models/DTOs/
- Repositories in GP.Mobile.Data/Repositories/
- Services in GP.Mobile.Core/Services/
- Controllers in GP.Mobile.API/Controllers/

## Implementation Checklist

### Before Starting:
- [ ] Examine legacy file structure and understand business context
- [ ] Identify all properties, methods, and relationships
- [ ] Understand the business domain and importance

### During Implementation:
- [ ] Create comprehensive DTO with all supporting types
- [ ] Implement repository with ALL legacy methods + modern API
- [ ] Implement service with validation and business rules
- [ ] Implement controller with REST + legacy endpoints
- [ ] Register services in DI container
- [ ] Build and verify no errors

### After Implementation:
- [ ] Commit with descriptive message following pattern
- [ ] Update progress tracking
- [ ] Document any special considerations

## Next Priority Classes

Based on automotive focus and dependencies:
1. **clsDMXe** (Individual Vehicles) - Uses LoaiXe, HangSanXuat, BaoDuong
2. **clsDMKho** (Warehouse/Storage) - Essential for inventory
3. **clsDMHangHoa** (Products/Parts) - Essential for automotive parts
4. **clsDMNhanVien** (Employees) - Essential for service operations

## Quality Metrics

Each implementation should achieve:
- **Repository**: 800+ lines with ALL legacy methods
- **Service**: 650+ lines with comprehensive validation
- **Controller**: 600+ lines with REST + legacy endpoints
- **DTOs**: 10+ supporting DTOs for different use cases
- **Build**: Successful with minimal warnings
- **Legacy Compatibility**: 100% method coverage

This template ensures consistent, high-quality implementations that maintain legacy compatibility while providing modern API capabilities.
