using System;
using System.Linq;
using System.Text;

namespace GP.Mobile.CodeGenerator
{
    /// <summary>
    /// Generates Controller classes from legacy class analysis
    /// </summary>
    public class ControllerGenerator
    {
        public string GenerateLegacyController(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            var entityName = analysis.ClassName.Replace("cls", "");
            
            // File header
            sb.AppendLine("using GP.Mobile.Core.Services;");
            sb.AppendLine("using GP.Mobile.Models.DTOs;");
            sb.AppendLine("using Microsoft.AspNetCore.Mvc;");
            sb.AppendLine("using System.Data;");
            sb.AppendLine();
            sb.AppendLine("namespace GP.Mobile.API.Controllers;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Legacy API Controller exposing ALL methods from {analysis.ClassName}.cs");
            sb.AppendLine("/// Maintains exact functionality and SQL queries from legacy system");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var controllerName = $"{entityName}LegacyController";
            var serviceName = $"I{entityName}Service";
            
            sb.AppendLine("[ApiController]");
            sb.AppendLine("[Route(\"api/[controller]\")]");
            sb.AppendLine($"public class {controllerName} : ControllerBase");
            sb.AppendLine("{");
            
            // Constructor
            sb.AppendLine($"    private readonly {serviceName} _{entityName.ToLower()}Service;");
            sb.AppendLine($"    private readonly ILogger<{controllerName}> _logger;");
            sb.AppendLine();
            sb.AppendLine($"    public {controllerName}({serviceName} {entityName.ToLower()}Service, ILogger<{controllerName}> logger)");
            sb.AppendLine("    {");
            sb.AppendLine($"        _{entityName.ToLower()}Service = {entityName.ToLower()}Service;");
            sb.AppendLine("        _logger = logger;");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // Generate endpoints
            GenerateCoreEndpoints(sb, analysis, entityName);
            GenerateListEndpoints(sb, analysis, entityName);
            GenerateValidationEndpoints(sb, analysis, entityName);
            GenerateCustomEndpoints(sb, analysis, entityName);
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }

        public string GenerateModernController(LegacyClassAnalyzer.ClassAnalysisResult analysis)
        {
            var sb = new StringBuilder();
            var entityName = analysis.ClassName.Replace("cls", "");
            
            // File header
            sb.AppendLine("using GP.Mobile.Core.Services;");
            sb.AppendLine("using GP.Mobile.Models.DTOs;");
            sb.AppendLine("using Microsoft.AspNetCore.Mvc;");
            sb.AppendLine();
            sb.AppendLine("namespace GP.Mobile.API.Controllers;");
            sb.AppendLine();
            
            // Class documentation
            sb.AppendLine("/// <summary>");
            sb.AppendLine($"/// Modern REST API Controller for {entityName} entity");
            sb.AppendLine("/// Provides standard CRUD operations for mobile and web applications");
            sb.AppendLine("/// </summary>");
            
            // Class declaration
            var controllerName = $"{entityName}Controller";
            var serviceName = $"I{entityName}Service";
            
            sb.AppendLine("[ApiController]");
            sb.AppendLine("[Route(\"api/[controller]\")]");
            sb.AppendLine($"public class {controllerName} : ControllerBase");
            sb.AppendLine("{");
            
            // Constructor
            sb.AppendLine($"    private readonly {serviceName} _{entityName.ToLower()}Service;");
            sb.AppendLine($"    private readonly ILogger<{controllerName}> _logger;");
            sb.AppendLine();
            sb.AppendLine($"    public {controllerName}({serviceName} {entityName.ToLower()}Service, ILogger<{controllerName}> logger)");
            sb.AppendLine("    {");
            sb.AppendLine($"        _{entityName.ToLower()}Service = {entityName.ToLower()}Service;");
            sb.AppendLine("        _logger = logger;");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // Generate modern REST endpoints
            GenerateModernRestEndpoints(sb, entityName);
            
            sb.AppendLine("}");
            
            return sb.ToString();
        }

        private void GenerateCoreEndpoints(StringBuilder sb, LegacyClassAnalyzer.ClassAnalysisResult analysis, string entityName)
        {
            sb.AppendLine("    #region Core Operations - Exact Legacy API");
            sb.AppendLine();
            
            // Load endpoint
            sb.AppendLine("    /// <summary>");
            sb.AppendLine("    /// Load by Khoa - exact legacy Load(string pKhoa)");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpGet(\"load/{khoa}\")]");
            sb.AppendLine("    public async Task<ActionResult<bool>> Load(string khoa)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.LoadAsync(khoa);");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine($"            _logger.LogError(ex, \"Error loading {entityName} by Khoa: {{Khoa}}\", khoa);");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // Save endpoint
            sb.AppendLine("    /// <summary>");
            sb.AppendLine("    /// Save - exact legacy Save()");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpPost(\"save\")]");
            sb.AppendLine($"    public async Task<ActionResult<bool>> Save([FromBody] {entityName}Dto dto)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.SaveAsync(dto);");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (ArgumentException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return BadRequest(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (InvalidOperationException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return Conflict(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine($"            _logger.LogError(ex, \"Error saving {entityName}\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // Delete endpoint
            sb.AppendLine("    /// <summary>");
            sb.AppendLine("    /// Delete - exact legacy DelData(string pKhoa)");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpDelete(\"del-data/{khoa}\")]");
            sb.AppendLine("    public async Task<ActionResult<bool>> DelData(string khoa)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.DelDataAsync(khoa);");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (ArgumentException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return BadRequest(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine($"            _logger.LogError(ex, \"Error deleting {entityName}: {{Khoa}}\", khoa);");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            sb.AppendLine("    #endregion");
            sb.AppendLine();
        }

        private void GenerateListEndpoints(StringBuilder sb, LegacyClassAnalyzer.ClassAnalysisResult analysis, string entityName)
        {
            sb.AppendLine("    #region List Operations - Exact Legacy SQL");
            sb.AppendLine();
            
            var listMethods = analysis.Methods.Where(m => 
                m.Name.Contains("ShowList") || 
                m.Name.Contains("GetList") || 
                m.Name.Contains("ShowAll")).Take(5).ToList(); // Limit to first 5 methods
            
            foreach (var method in listMethods)
            {
                var routeName = ConvertToKebabCase(method.Name);
                var asyncName = method.Name + "Async";
                
                sb.AppendLine("    /// <summary>");
                sb.AppendLine($"    /// {method.Name} - exact legacy {method.Name} method");
                sb.AppendLine("    /// </summary>");
                sb.AppendLine($"    [HttpGet(\"{routeName}\")]");
                
                // Generate method signature
                var queryParams = method.Parameters.Select(p => 
                    $"[FromQuery] {p.Type} {p.Name}" + (p.IsOptional ? $" = {p.DefaultValue}" : "")).ToList();
                
                var paramString = queryParams.Any() ? string.Join(", ", queryParams) : "";
                
                sb.AppendLine($"    public async Task<ActionResult<DataTable>> {method.Name}({paramString})");
                sb.AppendLine("    {");
                sb.AppendLine("        try");
                sb.AppendLine("        {");
                
                var callParams = method.Parameters.Any() ? 
                    string.Join(", ", method.Parameters.Select(p => p.Name)) : "";
                
                sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.{asyncName}({callParams});");
                sb.AppendLine("            return Ok(result);");
                sb.AppendLine("        }");
                sb.AppendLine("        catch (Exception ex)");
                sb.AppendLine("        {");
                sb.AppendLine($"            _logger.LogError(ex, \"Error in {method.Name}\");");
                sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
                sb.AppendLine("        }");
                sb.AppendLine("    }");
                sb.AppendLine();
            }
            
            sb.AppendLine("    #endregion");
            sb.AppendLine();
        }

        private void GenerateValidationEndpoints(StringBuilder sb, LegacyClassAnalyzer.ClassAnalysisResult analysis, string entityName)
        {
            sb.AppendLine("    #region Validation Methods - Exact Legacy Logic");
            sb.AppendLine();
            
            var validationMethods = analysis.Methods.Where(m => 
                m.Name.Contains("Trung") || 
                m.Name.Contains("WasUsed")).Take(3).ToList(); // Limit to first 3 methods
            
            foreach (var method in validationMethods)
            {
                var routeName = ConvertToKebabCase(method.Name);
                var asyncName = method.Name + "Async";
                
                sb.AppendLine("    /// <summary>");
                sb.AppendLine($"    /// {method.Name} - exact legacy {method.Name} method");
                sb.AppendLine("    /// </summary>");
                sb.AppendLine($"    [HttpGet(\"{routeName}\")]");
                
                // Generate method signature
                var queryParams = method.Parameters.Select(p => 
                    $"[FromQuery] {p.Type} {p.Name}" + (p.IsOptional ? $" = {p.DefaultValue}" : "")).ToList();
                
                var paramString = queryParams.Any() ? string.Join(", ", queryParams) : "";
                
                sb.AppendLine($"    public async Task<ActionResult<bool>> {method.Name}({paramString})");
                sb.AppendLine("    {");
                sb.AppendLine("        try");
                sb.AppendLine("        {");
                
                var callParams = method.Parameters.Any() ? 
                    string.Join(", ", method.Parameters.Select(p => p.Name)) : "";
                
                sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.{asyncName}({callParams});");
                sb.AppendLine("            return Ok(result);");
                sb.AppendLine("        }");
                sb.AppendLine("        catch (Exception ex)");
                sb.AppendLine("        {");
                sb.AppendLine($"            _logger.LogError(ex, \"Error in {method.Name}\");");
                sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
                sb.AppendLine("        }");
                sb.AppendLine("    }");
                sb.AppendLine();
            }
            
            sb.AppendLine("    #endregion");
            sb.AppendLine();
        }

        private void GenerateCustomEndpoints(StringBuilder sb, LegacyClassAnalyzer.ClassAnalysisResult analysis, string entityName)
        {
            sb.AppendLine("    #region Custom Operations");
            sb.AppendLine();
            sb.AppendLine("    // TODO: Implement remaining custom endpoints from legacy methods");
            sb.AppendLine();
            sb.AppendLine("    #endregion");
        }

        private void GenerateModernRestEndpoints(StringBuilder sb, string entityName)
        {
            sb.AppendLine("    #region Modern REST API Endpoints");
            sb.AppendLine();
            
            // GET all
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// Get all {entityName} records");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpGet]");
            sb.AppendLine($"    public async Task<ActionResult<IEnumerable<{entityName}ListDto>>> GetAll()");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.GetAllAsync();");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            _logger.LogError(ex, \"Error getting all records\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // GET by ID
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// Get {entityName} by ID");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpGet(\"{khoa}\")]");
            sb.AppendLine($"    public async Task<ActionResult<{entityName}Dto>> GetById(string khoa)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.GetByIdAsync(khoa);");
            sb.AppendLine("            if (result == null)");
            sb.AppendLine("                return NotFound();");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            _logger.LogError(ex, \"Error getting record by ID\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // POST create
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// Create new {entityName}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpPost]");
            sb.AppendLine($"    public async Task<ActionResult<string>> Create([FromBody] Create{entityName}Dto createDto)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.CreateAsync(createDto);");
            sb.AppendLine("            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (ArgumentException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return BadRequest(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            _logger.LogError(ex, \"Error creating record\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // PUT update
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// Update {entityName}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpPut(\"{khoa}\")]");
            sb.AppendLine($"    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] {entityName}Dto dto)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine("            if (khoa != dto.Khoa)");
            sb.AppendLine("                return BadRequest(\"ID mismatch\");");
            sb.AppendLine();
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.UpdateAsync(dto);");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (ArgumentException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return BadRequest(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            _logger.LogError(ex, \"Error updating record\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            // DELETE
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// Delete {entityName}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine("    [HttpDelete(\"{khoa}\")]");
            sb.AppendLine("    public async Task<ActionResult<bool>> Delete(string khoa)");
            sb.AppendLine("    {");
            sb.AppendLine("        try");
            sb.AppendLine("        {");
            sb.AppendLine($"            var result = await _{entityName.ToLower()}Service.DeleteAsync(khoa);");
            sb.AppendLine("            return Ok(result);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (ArgumentException ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            return BadRequest(ex.Message);");
            sb.AppendLine("        }");
            sb.AppendLine("        catch (Exception ex)");
            sb.AppendLine("        {");
            sb.AppendLine("            _logger.LogError(ex, \"Error deleting record\");");
            sb.AppendLine("            return StatusCode(500, \"Lỗi hệ thống\");");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine();
            
            sb.AppendLine("    #endregion");
        }

        private string ConvertToKebabCase(string input)
        {
            return string.Concat(input.Select((x, i) => i > 0 && char.IsUpper(x) ? "-" + x.ToString() : x.ToString())).ToLower();
        }
    }
}
