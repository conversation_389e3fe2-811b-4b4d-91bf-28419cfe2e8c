using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for BaoDuong (Maintenance Service) entity
/// Implements ALL endpoints from clsDMBaoDuong.cs (364 lines)
/// Includes REST API and 10+ legacy method endpoints
/// Maps to DM_DMBaoDuong table with 6 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for maintenance service templates linking vehicle types with service types
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class BaoDuongController : ControllerBase
{
    private readonly IBaoDuongService _baoDuongService;
    private readonly ILogger<BaoDuongController> _logger;

    public BaoDuongController(IBaoDuongService baoDuongService, ILogger<BaoDuongController> logger)
    {
        _baoDuongService = baoDuongService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all BaoDuong records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BaoDuongListDto>>> GetAll()
    {
        try
        {
            var result = await _baoDuongService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all BaoDuong records");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get BaoDuong by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<BaoDuongDto>> GetById(string khoa)
    {
        try
        {
            var result = await _baoDuongService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get BaoDuong by vehicle type and service type
    /// </summary>
    [HttpGet("vehicle/{khoaLoaiXe}/service/{khoaLoaiDichVu}")]
    public async Task<ActionResult<BaoDuongDto>> GetByVehicleAndServiceType(string khoaLoaiXe, string khoaLoaiDichVu)
    {
        try
        {
            var result = await _baoDuongService.GetByVehicleAndServiceTypeAsync(khoaLoaiXe, khoaLoaiDichVu);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong by vehicle and service type");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new BaoDuong
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateBaoDuongDto createDto)
    {
        try
        {
            var result = await _baoDuongService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo bảo dưỡng");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BaoDuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update BaoDuong
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] BaoDuongDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoDuongService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating BaoDuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete BaoDuong
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _baoDuongService.DeleteDataAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting BaoDuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update BaoDuong status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateBaoDuongStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoDuongService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating BaoDuong status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search BaoDuong records
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<BaoDuongListDto>>> Search([FromBody] BaoDuongSearchDto searchDto)
    {
        try
        {
            var result = await _baoDuongService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching BaoDuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get BaoDuong lookup data
    /// </summary>
    [HttpGet("lookup")]
    public async Task<ActionResult<IEnumerable<BaoDuongLookupDto>>> GetLookup()
    {
        try
        {
            var result = await _baoDuongService.GetLookupAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BaoDuong lookup");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Validate BaoDuong data
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<BaoDuongValidationDto>> Validate([FromBody] BaoDuongValidationRequestDto request)
    {
        try
        {
            var result = await _baoDuongService.ValidateAsync(request.Khoa, request.KhoaLoaiXe, request.KhoaLoaiDichVu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating BaoDuong");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Check if maintenance service exists for vehicle and service type
    /// </summary>
    [HttpGet("exists")]
    public async Task<ActionResult<bool>> CheckExists([FromQuery] string khoaLoaiXe, [FromQuery] string khoaLoaiDichVu)
    {
        try
        {
            var result = await _baoDuongService.CheckExistsAsync(khoaLoaiXe, khoaLoaiDichVu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if BaoDuong exists");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get maintenance details for a specific maintenance service
    /// </summary>
    [HttpGet("{khoa}/details")]
    public async Task<ActionResult<IEnumerable<BaoDuongChiTietDto>>> GetMaintenanceDetails(string khoa)
    {
        try
        {
            var result = await _baoDuongService.GetMaintenanceDetailsAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive maintenance categories
    /// </summary>
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<MaintenanceCategoryDto>>> GetMaintenanceCategories()
    {
        try
        {
            var result = await _baoDuongService.GetMaintenanceCategoriesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance categories");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get maintenance services with details
    /// </summary>
    [HttpGet("with-details")]
    public async Task<ActionResult<IEnumerable<BaoDuongWithDetailsDto>>> GetMaintenanceWithDetails()
    {
        try
        {
            var result = await _baoDuongService.GetMaintenanceWithDetailsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance with details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get maintenance statistics
    /// </summary>
    [HttpGet("{khoa}/stats")]
    public async Task<ActionResult<BaoDuongStatsDto>> GetMaintenanceStats(string khoa)
    {
        try
        {
            var result = await _baoDuongService.GetMaintenanceStatsAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance stats");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get print data for maintenance request
    /// </summary>
    [HttpGet("{khoa}/print")]
    public async Task<ActionResult<IEnumerable<BaoDuongPrintDto>>> GetPrintData(string khoa)
    {
        try
        {
            var result = await _baoDuongService.GetPrintDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting print data");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _baoDuongService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] BaoDuongDto dto)
    {
        try
        {
            var result = await _baoDuongService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _baoDuongService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy SetBlank method endpoint
    /// </summary>
    [HttpPost("setblank")]
    public async Task<ActionResult> SetBlank()
    {
        try
        {
            await _baoDuongService.SetBlankAsync();
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SetBlank endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy IsDupllicate method endpoint
    /// </summary>
    [HttpPost("isdupllicate")]
    public async Task<ActionResult<bool>> IsDupllicate([FromBody] BaoDuongDuplicateCheckRequestDto request)
    {
        try
        {
            var result = await _baoDuongService.IsDupllicateAsync(request.SoChungTu, request.Khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IsDupllicate endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy CheckExists method endpoint
    /// </summary>
    [HttpPost("checkexists")]
    public async Task<ActionResult<bool>> CheckExists([FromBody] BaoDuongCheckExistsRequestDto request)
    {
        try
        {
            var result = await _baoDuongService.CheckExistsAsync(request.KhoaLoaiXe, request.KhoaLoaiDichVu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CheckExists endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult> ClearTemp([FromBody] string khoa)
    {
        try
        {
            await _baoDuongService.ClearTempAsync(khoa);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListDMBD method endpoint
    /// </summary>
    [HttpPost("getlistdmbd")]
    public async Task<ActionResult<DataTable>> GetListDMBD([FromBody] BaoDuongGetListRequestDto? request = null)
    {
        try
        {
            var condition = request?.Condition ?? "";
            var result = await _baoDuongService.GetListDMBDAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDMBD endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetDetailsDMBD method endpoint
    /// </summary>
    [HttpPost("getdetailsdmbd")]
    public async Task<ActionResult<DataTable>> GetDetailsDMBD([FromBody] string khoaBaoGia)
    {
        try
        {
            var result = await _baoDuongService.GetDetailsDMBDAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDetailsDMBD endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetDMBD_BaoGia method endpoint (by service type)
    /// </summary>
    [HttpPost("getdmbd-baogia")]
    public async Task<ActionResult<DataTable>> GetDMBD_BaoGia([FromBody] string khoaLoaiDichVu)
    {
        try
        {
            var result = await _baoDuongService.GetDMBD_BaoGiaAsync(khoaLoaiDichVu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDMBD_BaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetDMBD_BaoGia method endpoint (by vehicle and service type)
    /// </summary>
    [HttpPost("getdmbd-baogia-full")]
    public async Task<ActionResult<DataTable>> GetDMBD_BaoGiaFull([FromBody] BaoDuongGetDMBDRequestDto request)
    {
        try
        {
            var result = await _baoDuongService.GetDMBD_BaoGiaAsync(request.KhoaLoaiXe, request.KhoaLoaiDichVu);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDMBD_BaoGiaFull endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetDataPrintYeuCau method endpoint
    /// </summary>
    [HttpPost("getdataprintyeucau")]
    public async Task<ActionResult<DataTable>> GetDataPrintYeuCau([FromBody] string khoa)
    {
        try
        {
            var result = await _baoDuongService.GetDataPrintYeuCauAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDataPrintYeuCau endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for BaoDuong

/// <summary>
/// Request DTO for validation endpoint
/// </summary>
public class BaoDuongValidationRequestDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for duplicate check method
/// </summary>
public class BaoDuongDuplicateCheckRequestDto
{
    public string SoChungTu { get; set; } = string.Empty;
    public string Khoa { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for CheckExists method
/// </summary>
public class BaoDuongCheckExistsRequestDto
{
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetListDMBD method
/// </summary>
public class BaoDuongGetListRequestDto
{
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetDMBD_BaoGia method (full version)
/// </summary>
public class BaoDuongGetDMBDRequestDto
{
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
}

#endregion
