using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DoiTuongController : ControllerBase
{
    private readonly IDoiTuongService _doiTuongService;
    private readonly ILogger<DoiTuongController> _logger;

    public DoiTuongController(IDoiTuongService doiTuongService, ILogger<DoiTuongController> logger)
    {
        _doiTuongService = doiTuongService;
        _logger = logger;
    }

    /// <summary>
    /// Get all customers
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DoiTuongListDto>>> GetAll()
    {
        try
        {
            var customers = await _doiTuongService.GetAllCustomersAsync();
            return Ok(customers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all customers");
            return StatusCode(500, "Lỗi hệ thống khi lấy danh sách khách hàng");
        }
    }

    /// <summary>
    /// Get customer by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<DoiTuongDto>> GetById(string khoa)
    {
        try
        {
            var customer = await _doiTuongService.GetCustomerByIdAsync(khoa);
            if (customer == null)
                return NotFound("Không tìm thấy khách hàng");

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer by ID: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống khi lấy thông tin khách hàng");
        }
    }

    /// <summary>
    /// Get customer by code
    /// </summary>
    [HttpGet("by-code/{ma}")]
    public async Task<ActionResult<DoiTuongDto>> GetByCode(string ma)
    {
        try
        {
            var customer = await _doiTuongService.GetCustomerByCodeAsync(ma);
            if (customer == null)
                return NotFound("Không tìm thấy khách hàng");

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer by code: {Ma}", ma);
            return StatusCode(500, "Lỗi hệ thống khi lấy thông tin khách hàng");
        }
    }

    /// <summary>
    /// Search customers
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<DoiTuongListDto>>> Search([FromQuery] string searchTerm)
    {
        try
        {
            var customers = await _doiTuongService.SearchCustomersAsync(searchTerm);
            return Ok(customers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching customers with term: {SearchTerm}", searchTerm);
            return StatusCode(500, "Lỗi hệ thống khi tìm kiếm khách hàng");
        }
    }

    /// <summary>
    /// Create new customer
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateDoiTuongDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var khoa = await _doiTuongService.CreateCustomerAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { khoa }, khoa);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating customer");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Business rule violation creating customer");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer");
            return StatusCode(500, "Lỗi hệ thống khi tạo khách hàng");
        }
    }

    /// <summary>
    /// Update customer
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult> Update(string khoa, [FromBody] DoiTuongDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID không khớp");

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var success = await _doiTuongService.UpdateCustomerAsync(dto);
            if (!success)
                return NotFound("Không tìm thấy khách hàng");

            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating customer");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Business rule violation updating customer");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating customer: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống khi cập nhật khách hàng");
        }
    }

    /// <summary>
    /// Delete customer (soft delete)
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult> Delete(string khoa)
    {
        try
        {
            var success = await _doiTuongService.DeleteCustomerAsync(khoa);
            if (!success)
                return NotFound("Không tìm thấy khách hàng");

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Business rule violation deleting customer");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting customer: {Khoa}", khoa);
            return StatusCode(500, "Lỗi hệ thống khi xóa khách hàng");
        }
    }
}
