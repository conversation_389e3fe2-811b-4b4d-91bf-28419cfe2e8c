# 🚗 **CARSOFT GP - Modern Web Frontend**

## 📋 **Project Overview**

This is the modern Next.js web frontend for the CARSOFT GP automotive service management system. It converts the legacy Windows Forms application from the `Base/Forms` directory into a modern, responsive, and mobile-friendly web application.

## 🛠️ **Tech Stack**

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **State Management**: React Context API
- **Form Handling**: React Hook Form + Zod validation
- **Data Fetching**: TanStack Query (React Query)
- **Icons**: Lucide React
- **Date Handling**: date-fns

## 🏗️ **Project Structure**

```
gp-web-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (dashboard)/        # Protected dashboard routes
│   │   │   ├── customers/      # Customer management
│   │   │   ├── vehicles/       # Vehicle management
│   │   │   ├── quotations/     # Quotation management
│   │   │   ├── inventory/      # Inventory management
│   │   │   ├── reports/        # Reports and analytics
│   │   │   ├── settings/       # System settings
│   │   │   └── dashboard/      # Main dashboard
│   │   ├── login/              # Authentication page
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page (redirects)
│   ├── components/
│   │   ├── layout/             # Layout components
│   │   │   └── MainLayout.tsx  # Main application layout
│   │   └── ui/                 # shadcn/ui components
│   ├── contexts/
│   │   └── AuthContext.tsx     # Authentication context
│   └── lib/
│       └── utils.ts            # Utility functions
├── public/                     # Static assets
├── docs/                       # Documentation
└── package.json
```

## 🚀 **Getting Started**

### Prerequisites
- Node.js 18+
- npm or yarn
- Access to the CARSOFT GP API backend

### Installation

1. **Clone and navigate to the project**:
   ```bash
   cd gp-web-frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Open in browser**:
   ```
   http://localhost:3000
   ```

## 🔐 **Authentication**

The application includes a complete authentication system that replaces the legacy `Frm_Login.cs`:

- **Login Page**: `/login`
- **Multi-client Support**: Users can select their business unit
- **Session Management**: Automatic token handling
- **Protected Routes**: All dashboard routes require authentication
- **Auto-redirect**: Unauthenticated users are redirected to login

### Default Test Credentials
```
Username: admin
Password: admin123
Client: Any available option
```
