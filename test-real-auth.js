/**
 * Test Real Authentication with Legacy Encryption
 * Tests actual users with correct encryption algorithm
 */

const crypto = require('crypto');

// EXACT MD5 encryption from legacy modGeneral.MD5Encrypt
function computeMD5Hash(input) {
    // Uses UnicodeEncoding (not UTF8!) and specific byte-to-string conversion
    const buffer = Buffer.from(input, 'utf16le'); // UTF-16 LE is equivalent to UnicodeEncoding
    const hash = crypto.createHash('md5').update(buffer).digest();
    return byteArrayToString(hash);
}

// EXACT ByteArrayToString from legacy modGeneral.cs
function byteArrayToString(buffer) {
    let result = '';
    for (let i = 0; i < buffer.length; i++) {
        result += buffer[i].toString(16).toUpperCase().padStart(2, '0');
    }
    return result;
}

// Real users from database
const realUsers = [
    { username: 'adv', hash: '5F205532524AF8F242435795677F2A7A', type: 'ADMIN' },
    { username: 'hieu', hash: 'D7F112E46EEE9054DCA7916D4CD7D460', type: 'USER' },
    { username: 'huethanh', hash: 'E78225790C13658FA5067F8341BA5C47', type: 'USER' },
    { username: 'ngan', hash: '5F205532524AF8F242435795677F2A7A', type: 'USER' },
    { username: 'tinh', hash: '5F205532524AF8F242435795677F2A7A', type: 'USER' }
];

// Common password candidates
const passwordCandidates = [
    // Simple passwords
    '123', '1234', '12345', '123456', '1234567', '12345678',
    'admin', 'password', 'user', 'test',
    
    // Vietnamese patterns
    'admin123', 'user123', 'pass123',
    'giaphat', 'carsoft', 'gp', 'GP',
    
    // Company patterns
    'giaphat123', 'carsoft123', 'gp123', 'GP123',
    'gia-phat', 'car-soft', 'gia_phat', 'car_soft',
    
    // Date patterns
    '2024', '2023', '2022', '2021', '2020',
    '01012024', '31122023', '20240617',
    
    // User-specific patterns (based on usernames)
    'adv', 'adv123', 'ADV', 'ADV123',
    'hieu', 'hieu123', 'HIEU', 'HIEU123',
    'ngan', 'ngan123', 'NGAN', 'NGAN123',
    'tinh', 'tinh123', 'TINH', 'TINH123',
    
    // Empty and special
    '', ' ', 'null', 'NULL',
    
    // Vietnamese names
    'minh', 'tran', 'nguyen', 'pham', 'le',
    'MINH', 'TRAN', 'NGUYEN', 'PHAM', 'LE'
];

async function testRealAuthentication() {
    console.log('🔐 Testing Real Authentication with Legacy Encryption');
    console.log('==================================================\n');
    
    console.log('🧪 Testing encryption algorithm...');
    console.log('Master password hash target: 8F866D5EA7686D4458E39AEEF07DEC1A');
    
    // Test master password candidates
    let masterPasswordFound = false;
    for (const password of passwordCandidates) {
        const hash = computeMD5Hash(password);
        if (hash === '8F866D5EA7686D4458E39AEEF07DEC1A') {
            console.log(`🎉 MASTER PASSWORD FOUND: '${password}' -> ${hash}`);
            masterPasswordFound = true;
            break;
        }
    }
    
    if (!masterPasswordFound) {
        console.log('❌ Master password not found in candidate list\n');
    }
    
    console.log('👥 Testing real user passwords...\n');
    
    const foundPasswords = [];
    
    for (const user of realUsers) {
        console.log(`Testing user: ${user.username} (${user.type})`);
        console.log(`Target hash: ${user.hash}`);
        
        let passwordFound = false;
        for (const password of passwordCandidates) {
            const hash = computeMD5Hash(password);
            if (hash === user.hash) {
                console.log(`✅ PASSWORD FOUND: '${password}' -> ${hash}`);
                foundPasswords.push({ username: user.username, password: password, type: user.type });
                passwordFound = true;
                break;
            }
        }
        
        if (!passwordFound) {
            console.log(`❌ Password not found for ${user.username}`);
        }
        console.log('');
    }
    
    if (foundPasswords.length > 0) {
        console.log('🎯 FOUND CREDENTIALS FOR TESTING:');
        console.log('=================================');
        foundPasswords.forEach(cred => {
            console.log(`Username: ${cred.username}`);
            console.log(`Password: ${cred.password}`);
            console.log(`Type: ${cred.type}`);
            console.log('---');
        });
        
        // Test with API
        console.log('\n🚀 Testing with API...');
        const testUser = foundPasswords[0];
        await testAPILogin(testUser.username, testUser.password);
    } else {
        console.log('❌ No passwords found. Try expanding the candidate list.');
    }
}

async function testAPILogin(username, password) {
    try {
        console.log(`Testing API login: ${username} / ${password}`);
        
        // Get clients
        const clientsResponse = await fetch(`http://localhost:5000/authentication/clients/${username}`);
        if (!clientsResponse.ok) {
            throw new Error(`Failed to get clients: ${clientsResponse.status}`);
        }
        
        const clients = await clientsResponse.json();
        if (clients.length === 0) {
            throw new Error('No clients available');
        }
        
        console.log(`Found ${clients.length} clients for ${username}`);
        
        // Test login
        const loginResponse = await fetch('http://localhost:5000/authentication/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                Username: username,
                Password: password,
                ClientId: clients[0].khoa,
                DeviceId: 'test-browser',
                DeviceType: 'web',
                RememberMe: false
            })
        });
        
        if (loginResponse.ok) {
            const loginData = await loginResponse.json();
            console.log('✅ API LOGIN SUCCESSFUL!');
            console.log(`User: ${loginData.userInfo.employeeName}`);
            console.log(`Client: ${loginData.userInfo.clientName}`);
        } else {
            const errorData = await loginResponse.json();
            console.log(`❌ API login failed: ${errorData.message}`);
        }
        
    } catch (error) {
        console.error('❌ API test error:', error.message);
    }
}

// Run the test
testRealAuthentication();
