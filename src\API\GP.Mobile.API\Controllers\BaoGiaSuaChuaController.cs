using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for BaoGiaSuaChua (Service Quotation Repair) entity
/// Implements ALL endpoints from clsBaoGiaSuaChua.cs (1,884 lines)
/// Includes REST API and 20+ legacy method endpoints
/// Maps to SC_BaoGia table with 59 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive repair quotation management and service pricing
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class BaoGiaSuaChuaController : ControllerBase
{
    private readonly IBaoGiaSuaChuaService _baoGiaSuaChuaService;
    private readonly ILogger<BaoGiaSuaChuaController> _logger;

    public BaoGiaSuaChuaController(IBaoGiaSuaChuaService baoGiaSuaChuaService, ILogger<BaoGiaSuaChuaController> logger)
    {
        _baoGiaSuaChuaService = baoGiaSuaChuaService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all repair quotations
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetAll()
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all repair quotations");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotation by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<BaoGiaSuaChuaDto>> GetById(string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotation by document number
    /// </summary>
    [HttpGet("document/{soChungTu}")]
    public async Task<ActionResult<BaoGiaSuaChuaDto>> GetByDocumentNumber(string soChungTu)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetByDocumentNumberAsync(soChungTu);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation by document number");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new repair quotation
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateBaoGiaSuaChuaDto createDto)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo báo giá sửa chữa");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating repair quotation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update repair quotation
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] BaoGiaSuaChuaDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaSuaChuaService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair quotation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete repair quotation
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair quotation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update repair quotation status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateBaoGiaSuaChuaStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaSuaChuaService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair quotation status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search repair quotations
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> Search([FromBody] BaoGiaSuaChuaSearchDto searchDto)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching repair quotations");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive repair quotations
    /// </summary>
    [HttpGet("automotive")]
    public async Task<ActionResult<IEnumerable<AutomotiveRepairQuotationDto>>> GetAutomotiveRepairQuotations()
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetAutomotiveRepairQuotationsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive repair quotations");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get financial summary for repair quotation
    /// </summary>
    [HttpGet("{khoa}/financial")]
    public async Task<ActionResult<RepairQuotationFinancialDto>> GetFinancialSummary(string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetFinancialSummaryAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation financial summary");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotations by customer
    /// </summary>
    [HttpGet("customer/{khoaKhachHang}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetByCustomer(string khoaKhachHang)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetByCustomerAsync(khoaKhachHang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotations by customer");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotations by vehicle
    /// </summary>
    [HttpGet("vehicle/{khoaXe}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetByVehicle(string khoaXe)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetByVehicleAsync(khoaXe);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotations by vehicle");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotations by insurance
    /// </summary>
    [HttpGet("insurance/{khoaBaoHiem}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetByInsurance(string khoaBaoHiem)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetByInsuranceAsync(khoaBaoHiem);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotations by insurance");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get pending approval quotations
    /// </summary>
    [HttpGet("pending-approval")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetPendingApproval()
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetPendingApprovalAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending approval quotations");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get outstanding payments
    /// </summary>
    [HttpGet("outstanding-payments")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaListDto>>> GetOutstandingPayments()
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetOutstandingPaymentsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting outstanding payments");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get total revenue
    /// </summary>
    [HttpGet("revenue")]
    public async Task<ActionResult<decimal>> GetTotalRevenue([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetTotalRevenueAsync(fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total revenue");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get quotation count
    /// </summary>
    [HttpGet("count")]
    public async Task<ActionResult<int>> GetQuotationCount([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetQuotationCountAsync(fromDate, toDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quotation count");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] BaoGiaSuaChuaDto dto)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy CanDel method endpoint
    /// </summary>
    [HttpPost("candel")]
    public async Task<ActionResult<bool>> CanDel([FromBody] string khoaBG)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.CanDelAsync(khoaBG);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CanDel endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy DelData method endpoint
    /// </summary>
    [HttpPost("deldata")]
    public async Task<ActionResult<bool>> DelData([FromBody] string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.DelDataAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DelData endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy GetPDBangKeBaoGia method endpoint
    /// </summary>
    [HttpPost("getpdbangkebaogia")]
    public async Task<ActionResult<DataTable>> GetPDBangKeBaoGia([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetPDBangKeBaoGiaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPDBangKeBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetPDDoanhThu method endpoint
    /// </summary>
    [HttpPost("getpddoanhthu")]
    public async Task<ActionResult<DataTable>> GetPDDoanhThu([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetPDDoanhThuAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPDDoanhThu endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetPDCongNoSuaChua method endpoint
    /// </summary>
    [HttpPost("getpdcongnosuachua")]
    public async Task<ActionResult<DataTable>> GetPDCongNoSuaChua([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetPDCongNoSuaChuaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPDCongNoSuaChua endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetPDCongNoBaoHiem method endpoint
    /// </summary>
    [HttpPost("getpdcongnobaohiem")]
    public async Task<ActionResult<DataTable>> GetPDCongNoBaoHiem([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetPDCongNoBaoHiemAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPDCongNoBaoHiem endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetBaoGiaList method endpoint
    /// </summary>
    [HttpPost("getbaogialist")]
    public async Task<ActionResult<DataTable>> GetBaoGiaList([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetBaoGiaListAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetBaoGiaList endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] BaoGiaSuaChuaShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy CoBaoGia method endpoint
    /// </summary>
    [HttpPost("cobaogia")]
    public async Task<ActionResult<bool>> CoBaoGia([FromBody] BaoGiaSuaChuaCoBaoGiaRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.CoBaoGiaAsync(request.KhoaTN, request.PhanLoai, request.KhoaBG, request.BoPhan);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CoBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy UpdateTinhTrang method endpoint
    /// </summary>
    [HttpPost("updatetinhtrang")]
    public async Task<ActionResult<bool>> UpdateTinhTrang([FromBody] BaoGiaSuaChuaUpdateTinhTrangRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.UpdateTinhTrangAsync(request.TinhTrang, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateTinhTrang endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy DuyetBaoGia method endpoint
    /// </summary>
    [HttpPost("duyetbaogia")]
    public async Task<ActionResult<bool>> DuyetBaoGia([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.DuyetBaoGiaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DuyetBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ExistsBienSo method endpoint
    /// </summary>
    [HttpPost("existsbienso")]
    public async Task<ActionResult<bool>> ExistsBienSo([FromBody] BaoGiaSuaChuaExistsBienSoRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.ExistsBienSoAsync(request.SoXe, request.KhoaBG);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ExistsBienSo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetBaoGiaThanhToan method endpoint
    /// </summary>
    [HttpPost("getbaogiathanhoan")]
    public async Task<ActionResult<DataTable>> GetBaoGiaThanhToan([FromBody] BaoGiaSuaChuaGetBaoGiaThanhToanRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetBaoGiaThanhToanAsync(request.NgayChungTu, request.KhoaDonVi, request.KhoaThanhToan, request.Loai, request.KhoaBaoHiem, request.KhoaKhachHang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetBaoGiaThanhToan endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetBaoGiaCheTai method endpoint
    /// </summary>
    [HttpPost("getbaogiacheai")]
    public async Task<ActionResult<DataTable>> GetBaoGiaCheTai([FromBody] BaoGiaSuaChuaGetBaoGiaCheTaiRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetBaoGiaCheTaiAsync(request.NgayChungTu, request.KhoaDonVi, request.KhoaThanhToan, request.Loai, request.KhoaBaoHiem, request.KhoaKhachHang);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetBaoGiaCheTai endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy UpdateHoaDon method endpoint
    /// </summary>
    [HttpPost("updatehoadon")]
    public async Task<ActionResult<bool>> UpdateHoaDon([FromBody] string khoaBG)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.UpdateHoaDonAsync(khoaBG);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateHoaDon endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy UpdateTienCanTruCongNo method endpoint
    /// </summary>
    [HttpPost("updatetiencantrucongno")]
    public async Task<ActionResult<bool>> UpdateTienCanTruCongNo([FromBody] BaoGiaSuaChuaUpdateTienCanTruCongNoRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.UpdateTienCanTruCongNoAsync(request.SoTien, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateTienCanTruCongNo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetKhoaCongNoPhaiThu method endpoint
    /// </summary>
    [HttpPost("getkhoacongnophaithu")]
    public async Task<ActionResult<string>> GetKhoaCongNoPhaiThu([FromBody] string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetKhoaCongNoPhaiThuAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetKhoaCongNoPhaiThu endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetBaoGiaToXe method endpoint
    /// </summary>
    [HttpPost("getbaogiaxe")]
    public async Task<ActionResult<DataTable>> GetBaoGiaToXe([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetBaoGiaToXeAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetBaoGiaToXe endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetCacLanSuaBaoGia method endpoint
    /// </summary>
    [HttpPost("getcaclansuabaogia")]
    public async Task<ActionResult<DataTable>> GetCacLanSuaBaoGia([FromBody] string khoaBG)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetCacLanSuaBaoGiaAsync(khoaBG);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetCacLanSuaBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetKhoaFromSoBaoGia method endpoint
    /// </summary>
    [HttpPost("getkhoafromsobaogia")]
    public async Task<ActionResult<string>> GetKhoaFromSoBaoGia([FromBody] string soBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaService.GetKhoaFromSoBaoGiaAsync(soBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetKhoaFromSoBaoGia endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for BaoGiaSuaChua

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class BaoGiaSuaChuaShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for CoBaoGia method
/// </summary>
public class BaoGiaSuaChuaCoBaoGiaRequestDto
{
    public string KhoaTN { get; set; } = string.Empty;
    public int PhanLoai { get; set; } = 0;
    public string KhoaBG { get; set; } = string.Empty;
    public int BoPhan { get; set; } = -1;
}

/// <summary>
/// Request DTO for UpdateTinhTrang method
/// </summary>
public class BaoGiaSuaChuaUpdateTinhTrangRequestDto
{
    public int TinhTrang { get; set; } = 0;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ExistsBienSo method
/// </summary>
public class BaoGiaSuaChuaExistsBienSoRequestDto
{
    public string SoXe { get; set; } = string.Empty;
    public string KhoaBG { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetBaoGiaThanhToan method
/// </summary>
public class BaoGiaSuaChuaGetBaoGiaThanhToanRequestDto
{
    public string NgayChungTu { get; set; } = string.Empty;
    public string KhoaDonVi { get; set; } = string.Empty;
    public string KhoaThanhToan { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string KhoaBaoHiem { get; set; } = string.Empty;
    public string KhoaKhachHang { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for GetBaoGiaCheTai method
/// </summary>
public class BaoGiaSuaChuaGetBaoGiaCheTaiRequestDto
{
    public string NgayChungTu { get; set; } = string.Empty;
    public string KhoaDonVi { get; set; } = string.Empty;
    public string KhoaThanhToan { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string KhoaBaoHiem { get; set; } = string.Empty;
    public string KhoaKhachHang { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for UpdateTienCanTruCongNo method
/// </summary>
public class BaoGiaSuaChuaUpdateTienCanTruCongNoRequestDto
{
    public decimal SoTien { get; set; } = 0;
    public string Condition { get; set; } = string.Empty;
}

#endregion
