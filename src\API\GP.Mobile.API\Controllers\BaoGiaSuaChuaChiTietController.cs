using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for BaoGiaSuaChuaChiTiet (Service Quotation Repair Detail) entity
/// Implements ALL endpoints from clsBaoGiaSuaChuaChiTiet.cs (551 lines)
/// Includes REST API and 8+ legacy method endpoints
/// Maps to SC_BaoGiaChiTiet table with 17 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive repair quotation detail line items and service pricing breakdown
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class BaoGiaSuaChuaChiTietController : ControllerBase
{
    private readonly IBaoGiaSuaChuaChiTietService _baoGiaSuaChuaChiTietService;
    private readonly ILogger<BaoGiaSuaChuaChiTietController> _logger;

    public BaoGiaSuaChuaChiTietController(IBaoGiaSuaChuaChiTietService baoGiaSuaChuaChiTietService, ILogger<BaoGiaSuaChuaChiTietController> logger)
    {
        _baoGiaSuaChuaChiTietService = baoGiaSuaChuaChiTietService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all repair quotation details
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetAll()
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all repair quotation details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotation detail by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<BaoGiaSuaChuaChiTietDto>> GetById(string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation detail by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair quotation details by quotation
    /// </summary>
    [HttpGet("quotation/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetByQuotation(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetByQuotationAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation details by quotation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new repair quotation detail
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateBaoGiaSuaChuaChiTietDto createDto)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo chi tiết báo giá sửa chữa");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating repair quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update repair quotation detail
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] BaoGiaSuaChuaChiTietDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaSuaChuaChiTietService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating repair quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete repair quotation detail
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete all details by quotation
    /// </summary>
    [HttpDelete("quotation/{khoaBaoGia}")]
    public async Task<ActionResult<bool>> DeleteByQuotation(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.DeleteByQuotationAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting repair quotation details by quotation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search repair quotation details
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> Search([FromBody] BaoGiaSuaChuaChiTietSearchDto searchDto)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching repair quotation details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive repair details
    /// </summary>
    [HttpGet("automotive/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<AutomotiveRepairDetailDto>>> GetAutomotiveRepairDetails(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetAutomotiveRepairDetailsAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive repair details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get detail summary for quotation
    /// </summary>
    [HttpGet("summary/{khoaBaoGia}")]
    public async Task<ActionResult<RepairQuotationDetailSummaryDto>> GetDetailSummary(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetDetailSummaryAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation detail summary");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get repair content templates
    /// </summary>
    [HttpGet("templates")]
    public async Task<ActionResult<IEnumerable<RepairContentDto>>> GetRepairContentTemplates()
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetRepairContentTemplatesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair content templates");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Bulk operations on repair quotation details
    /// </summary>
    [HttpPost("bulk")]
    public async Task<ActionResult<bool>> BulkOperation([FromBody] BulkBaoGiaSuaChuaChiTietDto bulkDto)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.BulkOperationAsync(bulkDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk operation");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get details by repair category
    /// </summary>
    [HttpGet("category/{khoaHangMuc}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetByRepairCategory(string khoaHangMuc)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetByRepairCategoryAsync(khoaHangMuc);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation details by category");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get details by product
    /// </summary>
    [HttpGet("product/{khoaHangHoa}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetByProduct(string khoaHangHoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetByProductAsync(khoaHangHoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting repair quotation details by product");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get service items for quotation
    /// </summary>
    [HttpGet("services/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetServiceItems(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetServiceItemsAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get parts items for quotation
    /// </summary>
    [HttpGet("parts/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<BaoGiaSuaChuaChiTietListDto>>> GetPartsItems(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetPartsItemsAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting parts items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get total amount for quotation
    /// </summary>
    [HttpGet("total-amount/{khoaBaoGia}")]
    public async Task<ActionResult<decimal>> GetTotalAmount(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetTotalAmountAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total amount");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get total discount for quotation
    /// </summary>
    [HttpGet("total-discount/{khoaBaoGia}")]
    public async Task<ActionResult<decimal>> GetTotalDiscount(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetTotalDiscountAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total discount");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get item count for quotation
    /// </summary>
    [HttpGet("count/{khoaBaoGia}")]
    public async Task<ActionResult<int>> GetItemCount(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetItemCountAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item count");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy SaveTemp method endpoint
    /// </summary>
    [HttpPost("savetemp")]
    public async Task<ActionResult<bool>> SaveTemp([FromBody] BaoGiaSuaChuaChiTietDto dto)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.SaveTempAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveTemp endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy ClearTemp method endpoint
    /// </summary>
    [HttpPost("cleartemp")]
    public async Task<ActionResult<bool>> ClearTemp([FromBody] string khoaBG)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.ClearTempAsync(khoaBG);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ClearTemp endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListDetail method endpoint
    /// </summary>
    [HttpPost("getlistdetail")]
    public async Task<ActionResult<DataTable>> GetListDetail([FromBody] GetListDetailRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetListDetailAsync(request.KhoaBG, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDetail endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetListDetailFromNhatKy method endpoint
    /// </summary>
    [HttpPost("getlistdetailfromnhatky")]
    public async Task<ActionResult<DataTable>> GetListDetailFromNhatKy([FromBody] GetListDetailRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetListDetailFromNhatKyAsync(request.KhoaBG, request.Condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetListDetailFromNhatKy endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy ShowListByField method endpoint
    /// </summary>
    [HttpPost("showlistbyfield")]
    public async Task<ActionResult<DataTable>> ShowListByField([FromBody] BaoGiaSuaChuaChiTietShowListByFieldRequestDto request)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.ShowListByFieldAsync(request.FieldList, request.Conditions, request.Order);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ShowListByField endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetNoiDungSuaChua method endpoint
    /// </summary>
    [HttpPost("getnoidungsuachua")]
    public async Task<ActionResult<DataTable>> GetNoiDungSuaChua([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetNoiDungSuaChuaAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetNoiDungSuaChua endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy GetNoiDungSuaChuaChoGiayNo method endpoint
    /// </summary>
    [HttpPost("getnoidungsuachuachogiayno")]
    public async Task<ActionResult<DataTable>> GetNoiDungSuaChuaChoGiayNo([FromBody] string condition)
    {
        try
        {
            var result = await _baoGiaSuaChuaChiTietService.GetNoiDungSuaChuaChoGiayNoAsync(condition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetNoiDungSuaChuaChoGiayNo endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion
}

#region Request DTOs for BaoGiaSuaChuaChiTiet

/// <summary>
/// Request DTO for GetListDetail methods
/// </summary>
public class GetListDetailRequestDto
{
    public string KhoaBG { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for ShowListByField method
/// </summary>
public class BaoGiaSuaChuaChiTietShowListByFieldRequestDto
{
    public string FieldList { get; set; } = string.Empty;
    public string Conditions { get; set; } = string.Empty;
    public string Order { get; set; } = string.Empty;
}

#endregion
