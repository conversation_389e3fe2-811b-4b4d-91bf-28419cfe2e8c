using GP.Mobile.Models.DTOs;

namespace GP.Mobile.Models.Interfaces
{
    /// <summary>
    /// Repository interface for Quotation Terms & Conditions (ClsDMDieuKhoanBaoGia)
    /// Provides methods for managing terms and conditions in DM_DieuKhoanbaoGia table
    /// Implements exact functionality from ClsDMDieuKhoanBaoGia legacy class (442 lines)
    /// Used in form lines 8634-8712 for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan
    /// </summary>
    public interface IDieuKhoanBaoGiaRepository
    {
        /// <summary>
        /// Load terms & conditions by ID
        /// Exact implementation from legacy Load method
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>Terms & conditions data or null if not found</returns>
        Task<DieuKhoanBaoGiaDto?> LoadAsync(string khoa);

        /// <summary>
        /// Load terms & conditions by code
        /// Exact implementation from legacy LoadByCode method
        /// </summary>
        /// <param name="ma">Terms & conditions code</param>
        /// <returns>Terms & conditions data or null if not found</returns>
        Task<DieuKhoanBaoGiaDto?> LoadByCodeAsync(string ma);

        /// <summary>
        /// Save terms & conditions (insert or update)
        /// Exact implementation from legacy Save method
        /// </summary>
        /// <param name="termsConditions">Terms & conditions data to save</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SaveAsync(DieuKhoanBaoGiaDto termsConditions);

        /// <summary>
        /// Create new terms & conditions
        /// </summary>
        /// <param name="createDto">Create data</param>
        /// <returns>Created terms & conditions ID</returns>
        Task<string?> CreateAsync(CreateDieuKhoanBaoGiaDto createDto);

        /// <summary>
        /// Update terms & conditions
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateAsync(string khoa, UpdateDieuKhoanBaoGiaDto updateDto);

        /// <summary>
        /// Delete terms & conditions by ID
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(string khoa);

        /// <summary>
        /// Get terms & conditions by type
        /// Exact implementation from form SQL usage: SELECT NoiDung FROM SC_DieuKhoanBaoGia WHERE Loai = 'BG'
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of terms & conditions for the specified type</returns>
        Task<List<DieuKhoanBaoGiaListDto>> GetByTypeAsync(string loai, string donViId);

        /// <summary>
        /// Get terms content by type for form initialization
        /// Used for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan methods
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Terms content data for form initialization</returns>
        Task<DieuKhoanContentDto> GetTermsContentAsync(string loai, string donViId);

        /// <summary>
        /// Get default terms & conditions by type
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Default terms & conditions for the specified type</returns>
        Task<DieuKhoanBaoGiaDto?> GetDefaultByTypeAsync(string loai, string donViId);

        /// <summary>
        /// Get all terms & conditions with filtering
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="loai">Terms type filter (optional)</param>
        /// <param name="active">Active status filter (optional)</param>
        /// <returns>List of terms & conditions</returns>
        Task<List<DieuKhoanBaoGiaListDto>> GetAllAsync(string donViId, string? loai = null, int? active = null);

        /// <summary>
        /// Search terms & conditions
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching terms & conditions</returns>
        Task<List<DieuKhoanBaoGiaListDto>> SearchAsync(DieuKhoanBaoGiaSearchDto searchDto);

        /// <summary>
        /// Get terms & conditions lookup data
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of terms & conditions for lookup/dropdown</returns>
        Task<List<DieuKhoanBaoGiaLookupDto>> GetLookupDataAsync(string loai, string donViId);

        /// <summary>
        /// Get terms & conditions statistics
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>Terms & conditions statistics</returns>
        Task<DieuKhoanBaoGiaStatisticsDto> GetStatisticsAsync(string donViId);

        /// <summary>
        /// Set default terms & conditions for type
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <param name="loai">Terms type</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> SetDefaultAsync(string khoa, string loai, string donViId);

        /// <summary>
        /// Get terms & conditions for mobile app with pagination
        /// </summary>
        /// <param name="loai">Terms type ('BG', 'SC', 'QT')</param>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="searchTerm">Search term (optional)</param>
        /// <returns>Paginated list of terms & conditions</returns>
        Task<PaginatedResult<DieuKhoanBaoGiaListDto>> GetForMobileAsync(string loai, string donViId, int pageSize, int pageNumber, string? searchTerm = null);

        /// <summary>
        /// Bulk update active status
        /// </summary>
        /// <param name="khoaList">List of terms & conditions IDs</param>
        /// <param name="active">New active status</param>
        /// <param name="nguoiCapNhat">User updating</param>
        /// <returns>Number of successfully updated records</returns>
        Task<int> BulkUpdateActiveStatusAsync(List<string> khoaList, int active, string nguoiCapNhat);

        /// <summary>
        /// Copy terms & conditions to another type
        /// </summary>
        /// <param name="sourceKhoa">Source terms & conditions ID</param>
        /// <param name="targetLoai">Target terms type</param>
        /// <param name="nguoiTao">User creating</param>
        /// <returns>New terms & conditions ID if successful</returns>
        Task<string?> CopyToTypeAsync(string sourceKhoa, string targetLoai, string nguoiTao);

        /// <summary>
        /// Get terms & conditions usage statistics
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>Usage statistics</returns>
        Task<TermsUsageStatisticsDto> GetUsageStatisticsAsync(string khoa);

        /// <summary>
        /// Check if terms & conditions exists
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(string khoa);

        /// <summary>
        /// Check if terms & conditions code exists
        /// </summary>
        /// <param name="ma">Terms & conditions code</param>
        /// <param name="excludeKhoa">Exclude this ID from check (for updates)</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> CodeExistsAsync(string ma, string? excludeKhoa = null);

        /// <summary>
        /// Check if terms & conditions can be deleted
        /// </summary>
        /// <param name="khoa">Terms & conditions ID</param>
        /// <returns>True if can be deleted, false otherwise</returns>
        Task<bool> CanDeleteAsync(string khoa);

        /// <summary>
        /// Get terms & conditions by date range
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="fromDate">Start date (YYYYMMDD format)</param>
        /// <param name="toDate">End date (YYYYMMDD format)</param>
        /// <returns>List of terms & conditions in date range</returns>
        Task<List<DieuKhoanBaoGiaListDto>> GetByDateRangeAsync(string donViId, string fromDate, string toDate);

        /// <summary>
        /// Archive old terms & conditions
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <param name="olderThanDays">Archive data older than specified days</param>
        /// <returns>Number of records archived</returns>
        Task<int> ArchiveOldDataAsync(string donViId, int olderThanDays = 365);
    }


}
