using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for HangHoa (Products/Parts) entity
/// Maps exactly to DM_HangHoa table in legacy database
/// Implements ALL properties from clsDMHangHoa.cs (2611 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive parts inventory management and product catalog
/// </summary>
public class HangHoaDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Industry/Sector key - Foreign key to DM_NganhHang
    /// Maps to: mKhoaNganh property in legacy class
    /// </summary>
    public string KhoaNganh { get; set; } = string.Empty;

    /// <summary>
    /// Product group key - Foreign key to DM_NhomHangHoa
    /// Maps to: mKhoaNhom property in legacy class
    /// </summary>
    public string KhoaNhom { get; set; } = string.Empty;

    /// <summary>
    /// Product code
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Barcode
    /// Maps to: mMaVach property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaVach { get; set; } = string.Empty;

    /// <summary>
    /// Import product code (supplier code)
    /// Maps to: mMaHangNhapKhau property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaHangNhapKhau { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Description/Notes
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Unit of measure key - Foreign key to DM_DonViTinh
    /// Maps to: mKhoaDonViTinh property in legacy class
    /// </summary>
    public string KhoaDonViTinh { get; set; } = string.Empty;

    /// <summary>
    /// Minimum stock quantity
    /// Maps to: mSoLuongTonToiThieu property in legacy class
    /// </summary>
    public double SoLuongTonToiThieu { get; set; } = 0;

    /// <summary>
    /// Country key - Foreign key to DM_QuocGia
    /// Maps to: mKhoaQuocGia property in legacy class
    /// </summary>
    public string KhoaQuocGia { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer key - Foreign key to DM_HangSanXuat
    /// Maps to: mKhoaHangSanXuat property in legacy class
    /// </summary>
    public string KhoaHangSanXuat { get; set; } = string.Empty;

    /// <summary>
    /// Warranty period (months)
    /// Maps to: mThoiGianBaoHanh property in legacy class
    /// </summary>
    public int ThoiGianBaoHanh { get; set; } = 0;

    /// <summary>
    /// Product account key - Foreign key to DM_TaiKhoan
    /// Maps to: mKhoaTKHangHoa property in legacy class
    /// </summary>
    public string KhoaTKHangHoa { get; set; } = string.Empty;

    /// <summary>
    /// Cost account key - Foreign key to DM_TaiKhoan
    /// Maps to: mKhoaTKGiaVon property in legacy class
    /// </summary>
    public string KhoaTKGiaVon { get; set; } = string.Empty;

    /// <summary>
    /// Tax rate (%)
    /// Maps to: mTyLeThue property in legacy class
    /// </summary>
    public int TyLeThue { get; set; } = 0;

    /// <summary>
    /// Import tax rate (%)
    /// Maps to: mTyLeThueNhapKhau property in legacy class
    /// </summary>
    public int TyLeThueNhapKhau { get; set; } = 0;

    /// <summary>
    /// Raw material flag (1 = Yes, 0 = No)
    /// Maps to: mNguyenLieu property in legacy class
    /// </summary>
    public int NguyenLieu { get; set; } = 0;

    /// <summary>
    /// Finished product flag (1 = Yes, 0 = No)
    /// Maps to: mThanhPham property in legacy class
    /// </summary>
    public int ThanhPham { get; set; } = 0;

    /// <summary>
    /// Menu item flag (1 = Yes, 0 = No)
    /// Maps to: mThucDon property in legacy class
    /// </summary>
    public int ThucDon { get; set; } = 0;

    /// <summary>
    /// From date (YYYYMMDD format)
    /// Maps to: mTuNgay property in legacy class
    /// </summary>
    [StringLength(8)]
    public string TuNgay { get; set; } = string.Empty;

    /// <summary>
    /// Employee who last updated this record
    /// Maps to: mKhoaNhanVienCapNhap property in legacy class
    /// </summary>
    public string KhoaNhanVienCapNhap { get; set; } = string.Empty;

    /// <summary>
    /// Technical specifications
    /// Maps to: mThongSoKythuat property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string ThongSoKythuat { get; set; } = string.Empty;

    /// <summary>
    /// Active status (1 = Active, 0 = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public int Active { get; set; } = 1;

    /// <summary>
    /// Send status (1 = Sent, 0 = Not sent)
    /// Maps to: mSend property in legacy class
    /// </summary>
    public int Send { get; set; } = 0;

    /// <summary>
    /// Weight (kg)
    /// Maps to: mTrongLuong property in legacy class
    /// </summary>
    public double TrongLuong { get; set; } = 0;

    /// <summary>
    /// Dimensions
    /// Maps to: mKichThuoc property in legacy class
    /// </summary>
    [StringLength(100)]
    public string KichThuoc { get; set; } = string.Empty;

    /// <summary>
    /// Warranty tracking flag (1 = Yes, 0 = No)
    /// Maps to: mTheoDoiBaoHanh property in legacy class
    /// </summary>
    public int TheoDoiBaoHanh { get; set; } = 0;

    /// <summary>
    /// Vehicle type key - Foreign key to DM_LoaiXe
    /// Maps to: mKhoaLoaiXe property in legacy class
    /// </summary>
    public string KhoaLoaiXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle generation/model year
    /// Maps to: mDoiXe property in legacy class
    /// </summary>
    [StringLength(50)]
    public string DoiXe { get; set; } = string.Empty;

    /// <summary>
    /// Shelf/Counter location
    /// Maps to: mQuayKe property in legacy class
    /// </summary>
    [StringLength(50)]
    public string QuayKe { get; set; } = string.Empty;

    /// <summary>
    /// Selling price
    /// Maps to: mGiaBan property in legacy class
    /// </summary>
    public double GiaBan { get; set; } = 0;

    /// <summary>
    /// Current stock quantity
    /// Maps to: mSoLuongTonKho property in legacy class
    /// </summary>
    public double SoLuongTonKho { get; set; } = 0;

    /// <summary>
    /// Current stock value
    /// Maps to: mTriGiaTonKho property in legacy class
    /// </summary>
    public double TriGiaTonKho { get; set; } = 0;

    /// <summary>
    /// Applicable vehicle models
    /// Maps to: mApDungDongXe property in legacy class
    /// </summary>
    [StringLength(500)]
    public string ApDungDongXe { get; set; } = string.Empty;

    /// <summary>
    /// Material/Part 01
    /// Maps to: mVatTu01 property in legacy class
    /// </summary>
    [StringLength(100)]
    public string VatTu01 { get; set; } = string.Empty;

    /// <summary>
    /// Material/Part 02
    /// Maps to: mVatTu02 property in legacy class
    /// </summary>
    [StringLength(100)]
    public string VatTu02 { get; set; } = string.Empty;

    /// <summary>
    /// Material/Part 03
    /// Maps to: mVatTu03 property in legacy class
    /// </summary>
    [StringLength(100)]
    public string VatTu03 { get; set; } = string.Empty;

    /// <summary>
    /// Material/Part 04
    /// Maps to: mVatTu04 property in legacy class
    /// </summary>
    [StringLength(100)]
    public string VatTu04 { get; set; } = string.Empty;

    /// <summary>
    /// Price before tax
    /// Maps to: mGiaBanTruocThue property in legacy class
    /// </summary>
    public decimal GiaBanTruocThue { get; set; } = 0;

    /// <summary>
    /// Price after tax
    /// Maps to: mGiaBanSauThue property in legacy class
    /// </summary>
    public decimal GiaBanSauThue { get; set; } = 0;

    /// <summary>
    /// Supplier key - Foreign key to DM_NhaCungCap
    /// Maps to: mKhoaNhaCungCap property in legacy class
    /// </summary>
    public string KhoaNhaCungCap { get; set; } = string.Empty;

    /// <summary>
    /// Contract code
    /// Maps to: mMaHD property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaHD { get; set; } = string.Empty;

    /// <summary>
    /// Supplier code
    /// Maps to: mMaNCC property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaNCC { get; set; } = string.Empty;

    /// <summary>
    /// Fixed asset flag
    /// Maps to: mis_CCDC property in legacy class
    /// </summary>
    public bool is_CCDC { get; set; } = false;

    /// <summary>
    /// Model name
    /// Maps to: mModel property in legacy class
    /// </summary>
    [StringLength(100)]
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// Product image
    /// Maps to: mHinhAnh property in legacy class
    /// </summary>
    public byte[]? HinhAnh { get; set; }

    /// <summary>
    /// Product type (0 = Parts, 1 = Vehicle)
    /// Maps to: mLoaiHangHoa property in legacy class
    /// </summary>
    public int LoaiHangHoa { get; set; } = 0;

    /// <summary>
    /// Number of seats (for vehicles)
    /// Maps to: mSoChoNgoi property in legacy class
    /// </summary>
    [StringLength(10)]
    public string SoChoNgoi { get; set; } = string.Empty;

    /// <summary>
    /// Transmission type key
    /// Maps to: mKhoaLoaiHopSo property in legacy class
    /// </summary>
    public string KhoaLoaiHopSo { get; set; } = string.Empty;

    /// <summary>
    /// Engine type key
    /// Maps to: mKhoaLoaiDongCo property in legacy class
    /// </summary>
    public string KhoaLoaiDongCo { get; set; } = string.Empty;

    /// <summary>
    /// Body type key
    /// Maps to: mKhoaLoaiThanXe property in legacy class
    /// </summary>
    public string KhoaLoaiThanXe { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle style key
    /// Maps to: mKhoaPhongCachXe property in legacy class
    /// </summary>
    public string KhoaPhongCachXe { get; set; } = string.Empty;

    /// <summary>
    /// Product characteristics
    /// Maps to: mDacDiemHangHoa property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DacDiemHangHoa { get; set; } = string.Empty;

    /// <summary>
    /// Origin/Source
    /// Maps to: mNguonGoc property in legacy class
    /// </summary>
    [StringLength(100)]
    public string NguonGoc { get; set; } = string.Empty;

    /// <summary>
    /// Published price
    /// Maps to: mGiaCongBo property in legacy class
    /// </summary>
    public double GiaCongBo { get; set; } = 0;

    /// <summary>
    /// Cash discount
    /// Maps to: mKhuyenMaiTienMat property in legacy class
    /// </summary>
    public double KhuyenMaiTienMat { get; set; } = 0;

    /// <summary>
    /// Vehicle selling price
    /// Maps to: mGiaBanXe property in legacy class
    /// </summary>
    public double GiaBanXe { get; set; } = 0;

    /// <summary>
    /// Gift notes
    /// Maps to: mGhiChuQuaTang property in legacy class
    /// </summary>
    [StringLength(500)]
    public string GhiChuQuaTang { get; set; } = string.Empty;

    /// <summary>
    /// Rolling price
    /// Maps to: mGiaLanBanh property in legacy class
    /// </summary>
    public double GiaLanBanh { get; set; } = 0;

    /// <summary>
    /// Total fees
    /// Maps to: mTongLePhi property in legacy class
    /// </summary>
    public double TongLePhi { get; set; } = 0;

    /// <summary>
    /// Vehicle quality
    /// Maps to: mChatLuongXe property in legacy class
    /// </summary>
    [StringLength(50)]
    public string ChatLuongXe { get; set; } = string.Empty;

    /// <summary>
    /// Air conditioning flag
    /// Maps to: mIsMayLanh property in legacy class
    /// </summary>
    public bool IsMayLanh { get; set; } = false;

    /// <summary>
    /// Vietnamese name 1 (alternative)
    /// Maps to: mTenViet1 property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenViet1 { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name 2 (alternative)
    /// Maps to: mTenViet2 property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenViet2 { get; set; } = string.Empty;
}

/// <summary>
/// DTO for HangHoa list display with joined data
/// Optimized for automotive parts lists with category and manufacturer info
/// Used by ShowList and ShowAllListHangHoa methods
/// </summary>
public class HangHoaListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string MaVach { get; set; } = string.Empty;
    public string MaHangNhapKhau { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string TenViet1 { get; set; } = string.Empty;
    public string TenViet2 { get; set; } = string.Empty;
    public string NhomHangHoa { get; set; } = string.Empty;
    public string NhaSanXuat { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string XuatXu { get; set; } = string.Empty;
    public string QuayKe { get; set; } = string.Empty;
    public double SoLuongTonToiThieu { get; set; } = 0;
    public bool HasImage { get; set; } = false;
    public string ApDungDongXe { get; set; } = string.Empty;
    public string ThongSoKyThuat { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public int LoaiHangHoa { get; set; } = 0;
    public bool IsAutomotivePart { get; set; } = false;
    public bool IsVehicle { get; set; } = false;
    public bool IsFixedAsset { get; set; } = false;
}

/// <summary>
/// DTO for creating new HangHoa
/// Contains only required fields for creation
/// </summary>
public class CreateHangHoaDto
{
    [Required]
    [StringLength(50)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    public string KhoaNhom { get; set; } = string.Empty;
    public string KhoaDonViTinh { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string TuNgay { get; set; } = string.Empty;
    public int LoaiHangHoa { get; set; } = 0;
    public double SoLuongTonToiThieu { get; set; } = 0;
}

/// <summary>
/// DTO for updating HangHoa status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateHangHoaStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;
    public string KhoaNhanVienCapNhap { get; set; } = string.Empty;
}

/// <summary>
/// DTO for HangHoa search operations
/// Used for advanced search and filtering
/// </summary>
public class HangHoaSearchDto
{
    public string? Ma { get; set; }
    public string? MaVach { get; set; }
    public string? MaHangNhapKhau { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DienGiai { get; set; }
    public string? KhoaNhom { get; set; }
    public string? KhoaHangSanXuat { get; set; }
    public string? KhoaLoaiXe { get; set; }
    public string? KhoaDonViTinh { get; set; }
    public string? DoiXe { get; set; }
    public string? ApDungDongXe { get; set; }
    public string? TuNgayFrom { get; set; }
    public string? TuNgayTo { get; set; }
    public int? Active { get; set; }
    public int? Send { get; set; }
    public int? LoaiHangHoa { get; set; }
    public bool? is_CCDC { get; set; }
    public bool? HasImage { get; set; }
    public double? SoLuongTonMin { get; set; }
    public double? SoLuongTonMax { get; set; }
    public double? GiaBanMin { get; set; }
    public double? GiaBanMax { get; set; }
}

/// <summary>
/// DTO for HangHoa dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class HangHoaLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public double GiaBan { get; set; } = 0;
    public double SoLuongTon { get; set; } = 0;
    public bool IsActive { get; set; } = true;
    public bool IsAutomotivePart { get; set; } = false;
}

/// <summary>
/// DTO for HangHoa validation operations
/// Used for duplicate checking and validation
/// </summary>
public class HangHoaValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string MaHangNhapKhau { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsDuplicateSupplierCode { get; set; } = false;
    public bool IsDuplicateName { get; set; } = false;
    public bool IsUsedInTransactions { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for product search by code
/// Used by SearchByCode method
/// </summary>
public class HangHoaSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public bool Found { get; set; } = false;
}

/// <summary>
/// DTO for automotive parts categories
/// Specialized for automotive parts classification
/// </summary>
public class AutomotivePartsCategoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string NhomHangHoa { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public bool IsEnginePart { get; set; } = false;
    public bool IsBodyPart { get; set; } = false;
    public bool IsElectricalPart { get; set; } = false;
    public bool IsBrakePart { get; set; } = false;
    public bool IsSuspensionPart { get; set; } = false;
    public bool IsTransmissionPart { get; set; } = false;
    public bool IsExhaustPart { get; set; } = false;
    public bool IsInteriorPart { get; set; } = false;
    public bool IsExteriorPart { get; set; } = false;
    public bool IsMaintenancePart { get; set; } = false;
    public bool IsOilFluid { get; set; } = false;
    public bool IsFilter { get; set; } = false;
    public bool IsTire { get; set; } = false;
    public bool IsAccessory { get; set; } = false;
    public int TotalParts { get; set; } = 0;
    public decimal TotalValue { get; set; } = 0;
}

/// <summary>
/// DTO for product with inventory summary
/// Used for comprehensive product display with stock information
/// </summary>
public class HangHoaWithInventoryDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public double SoLuongTon { get; set; } = 0;
    public double TriGiaTon { get; set; } = 0;
    public double SoLuongTonToiThieu { get; set; } = 0;
    public double GiaBan { get; set; } = 0;
    public double GiaVon { get; set; } = 0;
    public DateTime? NgayNhapGanNhat { get; set; }
    public DateTime? NgayXuatGanNhat { get; set; }
    public bool IsLowStock { get; set; } = false;
    public bool IsOutOfStock { get; set; } = false;
    public bool HasWarranty { get; set; } = false;
    public int ThoiGianBaoHanh { get; set; } = 0;
    public string QuayKe { get; set; } = string.Empty;
}

/// <summary>
/// DTO for product pricing information
/// Used for price management and quotations
/// </summary>
public class HangHoaPricingDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public double GiaBan { get; set; } = 0;
    public double GiaVon { get; set; } = 0;
    public double GiaCongBo { get; set; } = 0;
    public double GiaBanXe { get; set; } = 0;
    public double GiaLanBanh { get; set; } = 0;
    public double KhuyenMaiTienMat { get; set; } = 0;
    public double TongLePhi { get; set; } = 0;
    public decimal GiaBanTruocThue { get; set; } = 0;
    public decimal GiaBanSauThue { get; set; } = 0;
    public int TyLeThue { get; set; } = 0;
    public DateTime NgayCapNhat { get; set; }
    public string LoaiTien { get; set; } = string.Empty;
    public bool IsPromotionActive { get; set; } = false;
}

/// <summary>
/// DTO for vehicle products
/// Specialized for vehicle inventory management
/// </summary>
public class VehicleProductDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string SoChoNgoi { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string XuatXu { get; set; } = string.Empty;
    public string LoaiHopSo { get; set; } = string.Empty;
    public string LoaiDongCo { get; set; } = string.Empty;
    public string LoaiThanXe { get; set; } = string.Empty;
    public string PhongCachXe { get; set; } = string.Empty;
    public double GiaCongBo { get; set; } = 0;
    public double GiaBanXe { get; set; } = 0;
    public double GiaLanBanh { get; set; } = 0;
    public double KhuyenMaiTienMat { get; set; } = 0;
    public double TongLePhi { get; set; } = 0;
    public string GhiChuQuaTang { get; set; } = string.Empty;
    public string ChatLuongXe { get; set; } = string.Empty;
    public bool IsMayLanh { get; set; } = false;
    public string NguonGoc { get; set; } = string.Empty;
    public bool HasImage { get; set; } = false;
}

/// <summary>
/// DTO for parts compatibility
/// Used for vehicle parts compatibility management
/// </summary>
public class PartsCompatibilityDto
{
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string MaHangHoa { get; set; } = string.Empty;
    public string TenHangHoa { get; set; } = string.Empty;
    public string KhoaLoaiXe { get; set; } = string.Empty;
    public string LoaiXe { get; set; } = string.Empty;
    public string DoiXe { get; set; } = string.Empty;
    public string ApDungDongXe { get; set; } = string.Empty;
    public string HangSanXuat { get; set; } = string.Empty;
    public bool IsCompatible { get; set; } = true;
    public string CompatibilityNotes { get; set; } = string.Empty;
    public DateTime NgayCapNhat { get; set; }
}
