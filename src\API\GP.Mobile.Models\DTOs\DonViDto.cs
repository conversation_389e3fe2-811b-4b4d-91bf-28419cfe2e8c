using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for DonVi (Business Unit) entity
/// Maps exactly to DM_DonVi table in legacy database
/// Implements ALL properties from clsDMDonVi.cs (445 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// </summary>
public class DonViDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Business unit code
    /// Maps to: mMa property in legacy class
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name of business unit
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name of business unit
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Business unit address
    /// Maps to: mDiaChi property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DiaChi { get; set; } = string.Empty;

    /// <summary>
    /// Phone number
    /// Maps to: mDienThoai property in legacy class
    /// </summary>
    [StringLength(50)]
    public string DienThoai { get; set; } = string.Empty;

    /// <summary>
    /// Fax number
    /// Maps to: mFax property in legacy class
    /// </summary>
    [StringLength(50)]
    public string Fax { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// Maps to: mEmail property in legacy class
    /// </summary>
    [StringLength(100)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Representative name
    /// Maps to: mNguoiDaiDien property in legacy class
    /// </summary>
    [StringLength(100)]
    public string NguoiDaiDien { get; set; } = string.Empty;

    /// <summary>
    /// Representative title/position
    /// Maps to: mTDNguoiDaiDien property in legacy class
    /// </summary>
    [StringLength(100)]
    public string TieuDeNguoiDaiDien { get; set; } = string.Empty;

    /// <summary>
    /// Representative mobile phone
    /// Maps to: mMBNguoiDaiDien property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MobileNguoiDaiDien { get; set; } = string.Empty;

    /// <summary>
    /// Representative email
    /// Maps to: mEMNguoiDaiDien property in legacy class
    /// </summary>
    [StringLength(100)]
    [EmailAddress]
    public string EmailNguoiDaiDien { get; set; } = string.Empty;

    /// <summary>
    /// Notes/Comments
    /// Maps to: mGhiChu property in legacy class
    /// </summary>
    [StringLength(1000)]
    public string GhiChu { get; set; } = string.Empty;

    /// <summary>
    /// Prefix for document numbering
    /// Maps to: mPrefix property in legacy class
    /// </summary>
    [StringLength(10)]
    public string Prefix { get; set; } = string.Empty;

    /// <summary>
    /// Active status (true = Active, false = Inactive)
    /// Maps to: mIsActive property in legacy class
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for DonVi list display
/// Optimized for list views and dropdowns
/// </summary>
public class DonViListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for creating new DonVi
/// Contains only required fields for creation
/// </summary>
public class CreateDonViDto
{
    [Required]
    [StringLength(20)]
    public string Ma { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    [StringLength(500)]
    public string DiaChi { get; set; } = string.Empty;

    [StringLength(50)]
    public string DienThoai { get; set; } = string.Empty;

    [StringLength(50)]
    public string Fax { get; set; } = string.Empty;

    [StringLength(100)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [StringLength(100)]
    public string NguoiDaiDien { get; set; } = string.Empty;

    [StringLength(100)]
    public string TieuDeNguoiDaiDien { get; set; } = string.Empty;

    [StringLength(50)]
    public string MobileNguoiDaiDien { get; set; } = string.Empty;

    [StringLength(100)]
    [EmailAddress]
    public string EmailNguoiDaiDien { get; set; } = string.Empty;

    [StringLength(1000)]
    public string GhiChu { get; set; } = string.Empty;

    [StringLength(10)]
    public string Prefix { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for updating DonVi status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateDonViStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    [Required]
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for DonVi search operations
/// Used for advanced search and filtering
/// </summary>
public class DonViSearchDto
{
    public string? Ma { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DiaChi { get; set; }
    public string? DienThoai { get; set; }
    public string? Email { get; set; }
    public string? NguoiDaiDien { get; set; }
    public string? Prefix { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// DTO for DonVi dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class DonViLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty; // TenViet or TenAnh based on language
}

/// <summary>
/// DTO for DonVi selection operations
/// Used for multi-select scenarios
/// </summary>
public class DonViSelectDto
{
    public string Khoa { get; set; } = string.Empty;
    public bool Chon { get; set; } = false;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
}

/// <summary>
/// DTO for DonVi validation operations
/// Used for duplicate checking and validation
/// </summary>
public class DonViValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public bool IsUsed { get; set; } = false;
    public bool IsDuplicate { get; set; } = false;
}

/// <summary>
/// DTO for DonVi contact information
/// Focused on contact details
/// </summary>
public class DonViContactDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string Fax { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string NguoiDaiDien { get; set; } = string.Empty;
    public string TieuDeNguoiDaiDien { get; set; } = string.Empty;
    public string MobileNguoiDaiDien { get; set; } = string.Empty;
    public string EmailNguoiDaiDien { get; set; } = string.Empty;
}
