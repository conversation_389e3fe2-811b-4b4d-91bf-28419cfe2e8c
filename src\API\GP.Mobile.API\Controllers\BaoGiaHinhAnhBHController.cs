using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Controller for Insurance Image Management (Báo Giá H<PERSON>nh Ảnh Bảo Hiểm)
    /// Provides REST API endpoints for managing insurance approval images
    /// Implements exact functionality from clsBaoGiaHinhAnhBH.cs legacy class
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BaoGiaHinhAnhBHController : ControllerBase
    {
        private readonly IBaoGiaHinhAnhBHService _service;
        private readonly ILogger<BaoGiaHinhAnhBHController> _logger;

        public BaoGiaHinhAnhBHController(IBaoGiaHinhAnhBHService service, ILogger<BaoGiaHinhAnhBHController> logger)
        {
            _service = service;
            _logger = logger;
        }

        /// <summary>
        /// Get insurance image by quotation ID
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Insurance image data</returns>
        [HttpGet("{khoaBaoGia}")]
        public async Task<ActionResult<BaoGiaHinhAnhBHDto>> GetInsuranceImage(string khoaBaoGia)
        {
            try
            {
                var result = await _service.GetInsuranceImageAsync(khoaBaoGia);
                if (result == null)
                {
                    return NotFound($"Không tìm thấy hình ảnh bảo hiểm cho báo giá {khoaBaoGia}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy hình ảnh bảo hiểm");
            }
        }

        /// <summary>
        /// Upload insurance image
        /// </summary>
        /// <param name="uploadDto">Upload data</param>
        /// <returns>Upload result</returns>
        [HttpPost("upload")]
        public async Task<ActionResult> UploadInsuranceImage([FromBody] UploadInsuranceImageDto uploadDto)
        {
            try
            {
                var result = await _service.UploadInsuranceImageAsync(uploadDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading insurance image for quotation {KhoaBaoGia}", uploadDto.KhoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi tải lên hình ảnh");
            }
        }

        /// <summary>
        /// Upload insurance image from form data (for mobile app)
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="file">Image file</param>
        /// <param name="moTa">Description</param>
        /// <param name="viTriChup">Capture location</param>
        /// <param name="thietBiChup">Capture device</param>
        /// <returns>Upload result</returns>
        [HttpPost("upload-form/{khoaBaoGia}")]
        public async Task<ActionResult> UploadInsuranceImageFromForm(
            string khoaBaoGia,
            IFormFile file,
            [FromForm] string? moTa = null,
            [FromForm] string? viTriChup = null,
            [FromForm] string? thietBiChup = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("File không được để trống");
                }

                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                var imageData = memoryStream.ToArray();

                var uploadDto = new UploadInsuranceImageDto
                {
                    KhoaBaoGia = khoaBaoGia,
                    HinhAnh = imageData,
                    MoTa = moTa ?? string.Empty,
                    TenFile = file.FileName,
                    LoaiFile = file.ContentType,
                    ViTriChup = viTriChup ?? string.Empty,
                    ThietBiChup = thietBiChup ?? "Mobile App",
                    NguoiTao = User.Identity?.Name ?? "Unknown"
                };

                var result = await _service.UploadInsuranceImageAsync(uploadDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading insurance image from form for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi tải lên hình ảnh");
            }
        }

        /// <summary>
        /// Update insurance approval status
        /// </summary>
        /// <param name="approvalDto">Approval data</param>
        /// <returns>Update result</returns>
        [HttpPut("approval")]
        public async Task<ActionResult> UpdateApprovalStatus([FromBody] InsuranceApprovalDto approvalDto)
        {
            try
            {
                var result = await _service.UpdateApprovalStatusAsync(approvalDto);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating approval status for quotation {KhoaBaoGia}", approvalDto.KhoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi cập nhật trạng thái duyệt");
            }
        }

        /// <summary>
        /// Delete insurance image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Delete result</returns>
        [HttpDelete("{khoaBaoGia}")]
        public async Task<ActionResult> DeleteInsuranceImage(string khoaBaoGia)
        {
            try
            {
                var result = await _service.DeleteInsuranceImageAsync(khoaBaoGia);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting insurance image for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi xóa hình ảnh");
            }
        }

        /// <summary>
        /// Get insurance images requiring approval
        /// </summary>
        /// <param name="donViId">Branch/Unit ID</param>
        /// <returns>List of images pending approval</returns>
        [HttpGet("pending-approvals/{donViId}")]
        public async Task<ActionResult<List<InsuranceImageListDto>>> GetPendingApprovals(string donViId)
        {
            try
            {
                var result = await _service.GetPendingApprovalsAsync(donViId);
                if (result.Success)
                {
                    return Ok(result.Data);
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending approvals for unit {DonViId}", donViId);
                return StatusCode(500, "Lỗi hệ thống khi lấy danh sách chờ duyệt");
            }
        }

        /// <summary>
        /// Check if quotation has insurance image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>True if image exists</returns>
        [HttpGet("has-image/{khoaBaoGia}")]
        public async Task<ActionResult<bool>> HasInsuranceImage(string khoaBaoGia)
        {
            try
            {
                var hasImage = await _service.HasInsuranceImageAsync(khoaBaoGia);
                return Ok(new { hasImage = hasImage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if quotation has insurance image {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra hình ảnh");
            }
        }

        /// <summary>
        /// Get image thumbnail
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Base64 encoded thumbnail</returns>
        [HttpGet("thumbnail/{khoaBaoGia}")]
        public async Task<ActionResult<string>> GetImageThumbnail(string khoaBaoGia)
        {
            try
            {
                var thumbnail = await _service.GetImageThumbnailBase64Async(khoaBaoGia);
                if (thumbnail == null)
                {
                    return NotFound("Không tìm thấy thumbnail cho báo giá này");
                }

                return Ok(new { thumbnail = thumbnail });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting thumbnail for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy thumbnail");
            }
        }

        /// <summary>
        /// Get full image
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <returns>Base64 encoded full image</returns>
        [HttpGet("full-image/{khoaBaoGia}")]
        public async Task<ActionResult<string>> GetFullImage(string khoaBaoGia)
        {
            try
            {
                var fullImage = await _service.GetFullImageBase64Async(khoaBaoGia);
                if (fullImage == null)
                {
                    return NotFound("Không tìm thấy hình ảnh cho báo giá này");
                }

                return Ok(new { image = fullImage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting full image for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi lấy hình ảnh");
            }
        }

        /// <summary>
        /// Update image metadata
        /// </summary>
        /// <param name="khoaBaoGia">Quotation ID</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Update result</returns>
        [HttpPut("metadata/{khoaBaoGia}")]
        public async Task<ActionResult> UpdateImageMetadata(string khoaBaoGia, [FromBody] UpdateImageMetadataDto updateDto)
        {
            try
            {
                var result = await _service.UpdateImageMetadataAsync(khoaBaoGia, updateDto.MoTa, updateDto.TenFile);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating image metadata for quotation {KhoaBaoGia}", khoaBaoGia);
                return StatusCode(500, "Lỗi hệ thống khi cập nhật thông tin hình ảnh");
            }
        }

        /// <summary>
        /// Validate image file
        /// </summary>
        /// <param name="file">Image file to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate")]
        public async Task<ActionResult> ValidateImageFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("File không được để trống");
                }

                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                var imageData = memoryStream.ToArray();

                var result = await _service.ValidateImageFileAsync(imageData, file.FileName);
                if (result.Success)
                {
                    return Ok(new { success = true, message = result.Message });
                }
                else
                {
                    return BadRequest(new { success = false, message = result.Message, errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating image file");
                return StatusCode(500, "Lỗi hệ thống khi kiểm tra file");
            }
        }
    }

    /// <summary>
    /// DTO for updating image metadata
    /// </summary>
    public class UpdateImageMetadataDto
    {
        public string MoTa { get; set; } = string.Empty;
        public string TenFile { get; set; } = string.Empty;
    }
}
