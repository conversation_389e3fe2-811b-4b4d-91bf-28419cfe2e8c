@echo off
:: test-database-connection.bat - Test CARSOFT_GIAPHAT database connection
title Test CARSOFT_GIAPHAT Database Connection

echo ========================================
echo   TESTING CARSOFT_GIAPHAT DATABASE
echo   WITH WINDOWS AUTHENTICATION
echo ========================================
echo.

echo [1/3] Building API project...
cd /d "%~dp0src\API\GP.Mobile.API"
dotnet build --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ API build failed!
    pause
    exit /b 1
)
echo ✅ API build successful

echo.
echo [2/3] Starting API temporarily for database test...
start "Temp API" cmd /c "dotnet run --urls=http://localhost:5001 --environment=Development"

:: Wait for API to start
echo Waiting for API to start...
timeout /t 10 /nobreak > nul

echo.
echo [3/3] Testing database connection...

:: Test basic connection
echo Testing basic database connection...
curl -s http://localhost:5001/api/databasetest/connection-test
echo.

:: Test required tables
echo.
echo Testing required tables...
curl -s http://localhost:5001/api/databasetest/tables-test
echo.

:: Test authentication data
echo.
echo Testing authentication data...
curl -s http://localhost:5001/api/databasetest/auth-data-test
echo.

:: Stop the temporary API
echo.
echo Stopping temporary API...
taskkill /f /im dotnet.exe > nul 2>&1

echo.
echo ========================================
echo   DATABASE CONNECTION TEST COMPLETE
echo ========================================
echo.
echo If you see connection errors above:
echo 1. Make sure SQL Server is running
echo 2. Verify CARSOFT_GIAPHAT database exists
echo 3. Check Windows Authentication is enabled
echo 4. Run database-test-data.sql to create test data
echo.
echo If connection is successful, you can run:
echo ./dev-start.bat
echo.
pause
