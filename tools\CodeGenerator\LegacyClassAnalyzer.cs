using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace GP.Mobile.CodeGenerator
{
    /// <summary>
    /// Analyzes legacy C# classes to extract properties, methods, and SQL queries
    /// for automatic modern API generation
    /// </summary>
    public class LegacyClassAnalyzer
    {
        public class PropertyInfo
        {
            public string Name { get; set; } = "";
            public string Type { get; set; } = "";
            public string PrivateField { get; set; } = "";
            public bool IsRequired { get; set; } = false;
            public int MaxLength { get; set; } = 0;
        }

        public class MethodInfo
        {
            public string Name { get; set; } = "";
            public string ReturnType { get; set; } = "";
            public List<ParameterInfo> Parameters { get; set; } = new();
            public string SqlQuery { get; set; } = "";
            public string StoredProcedure { get; set; } = "";
            public bool IsAsync { get; set; } = false;
        }

        public class ParameterInfo
        {
            public string Name { get; set; } = "";
            public string Type { get; set; } = "";
            public bool IsOptional { get; set; } = false;
            public string DefaultValue { get; set; } = "";
        }

        public class ClassAnalysisResult
        {
            public string ClassName { get; set; } = "";
            public string TableName { get; set; } = "";
            public string StoredProcedureName { get; set; } = "";
            public List<PropertyInfo> Properties { get; set; } = new();
            public List<MethodInfo> Methods { get; set; } = new();
            public List<string> SqlQueries { get; set; } = new();
            public string Namespace { get; set; } = "";
        }

        /// <summary>
        /// Analyzes a legacy C# class file and extracts all information needed for modern API generation
        /// </summary>
        public ClassAnalysisResult AnalyzeClass(string filePath)
        {
            var result = new ClassAnalysisResult();
            var content = File.ReadAllText(filePath);

            // Extract class name
            result.ClassName = ExtractClassName(content);
            result.TableName = InferTableName(result.ClassName);
            result.StoredProcedureName = InferStoredProcedureName(result.ClassName);
            result.Namespace = ExtractNamespace(content);

            // Extract properties
            result.Properties = ExtractProperties(content);

            // Extract methods
            result.Methods = ExtractMethods(content);

            // Extract SQL queries
            result.SqlQueries = ExtractSqlQueries(content);

            return result;
        }

        private string ExtractClassName(string content)
        {
            var match = Regex.Match(content, @"public class (\w+)");
            return match.Success ? match.Groups[1].Value : "";
        }

        private string ExtractNamespace(string content)
        {
            var match = Regex.Match(content, @"namespace ([\w\.]+)");
            return match.Success ? match.Groups[1].Value : "";
        }

        private string InferTableName(string className)
        {
            // Convert clsDMDoiTuong -> DM_DoiTuong
            if (className.StartsWith("cls"))
            {
                var tableName = className.Substring(3);
                
                // Handle DM prefix
                if (tableName.StartsWith("DM"))
                    return "DM_" + tableName.Substring(2);
                
                // Handle BH prefix  
                if (tableName.StartsWith("BH"))
                    return "BH_" + tableName.Substring(2);
                
                // Handle SC prefix
                if (tableName.StartsWith("SC"))
                    return "SC_" + tableName.Substring(2);
                
                // Handle ST prefix
                if (tableName.StartsWith("ST"))
                    return "ST_" + tableName.Substring(2);
                
                // Handle GL prefix
                if (tableName.StartsWith("GL"))
                    return "GL_" + tableName.Substring(2);
                
                return tableName;
            }
            
            return className;
        }

        private string InferStoredProcedureName(string className)
        {
            var tableName = InferTableName(className);
            
            // Most legacy classes use sp_[TableName] pattern
            if (tableName.StartsWith("DM_"))
                return "sp_" + tableName;
            else if (tableName.StartsWith("BH_"))
                return "BH_sp_" + tableName.Substring(3);
            else if (tableName.StartsWith("SC_"))
                return "SC_sp_" + tableName.Substring(3);
            else if (tableName.StartsWith("ST_"))
                return "ST_sp_" + tableName.Substring(3);
            else if (tableName.StartsWith("GL_"))
                return "GL_sp_" + tableName.Substring(3);
            
            return "sp_" + tableName;
        }

        private List<PropertyInfo> ExtractProperties(string content)
        {
            var properties = new List<PropertyInfo>();
            
            // Extract private fields first (they map to properties)
            var fieldMatches = Regex.Matches(content, @"private\s+(\w+)\s+(\w+)\s*;");
            var fieldMap = new Dictionary<string, string>();
            
            foreach (Match match in fieldMatches)
            {
                var type = match.Groups[1].Value;
                var name = match.Groups[2].Value;
                fieldMap[name] = type;
            }

            // Extract public properties
            var propertyPattern = @"public\s+(\w+)\s+(\w+)\s*\{[^}]+\}";
            var propertyMatches = Regex.Matches(content, propertyPattern, RegexOptions.Singleline);

            foreach (Match match in propertyMatches)
            {
                var type = match.Groups[1].Value;
                var name = match.Groups[2].Value;
                
                var property = new PropertyInfo
                {
                    Name = name,
                    Type = MapLegacyTypeToCSharp(type),
                    PrivateField = FindCorrespondingField(name, fieldMap),
                    IsRequired = IsRequiredField(name, content),
                    MaxLength = ExtractMaxLength(name, content)
                };
                
                properties.Add(property);
            }

            return properties;
        }

        private List<MethodInfo> ExtractMethods(string content)
        {
            var methods = new List<MethodInfo>();
            
            // Extract public methods
            var methodPattern = @"public\s+(\w+)\s+(\w+)\s*\(([^)]*)\)\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}";
            var methodMatches = Regex.Matches(content, methodPattern, RegexOptions.Singleline);

            foreach (Match match in methodMatches)
            {
                var returnType = match.Groups[1].Value;
                var methodName = match.Groups[2].Value;
                var parametersStr = match.Groups[3].Value;
                var methodBody = match.Groups[4].Value;

                var method = new MethodInfo
                {
                    Name = methodName,
                    ReturnType = MapLegacyTypeToCSharp(returnType),
                    Parameters = ExtractParameters(parametersStr),
                    SqlQuery = ExtractSqlFromMethodBody(methodBody),
                    StoredProcedure = ExtractStoredProcedureFromMethodBody(methodBody),
                    IsAsync = ShouldBeAsync(methodName, methodBody)
                };

                methods.Add(method);
            }

            return methods;
        }

        private List<string> ExtractSqlQueries(string content)
        {
            var queries = new List<string>();
            
            // Extract SQL strings
            var sqlPattern = @"""([^""]*(?:SELECT|INSERT|UPDATE|DELETE|EXEC)[^""]*)""";
            var matches = Regex.Matches(content, sqlPattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                var sql = match.Groups[1].Value;
                if (sql.Trim().Length > 10) // Filter out short strings
                {
                    queries.Add(sql);
                }
            }

            return queries.Distinct().ToList();
        }

        private string MapLegacyTypeToCSharp(string legacyType)
        {
            return legacyType switch
            {
                "string" => "string",
                "int" => "int",
                "double" => "double",
                "bool" => "bool",
                "DateTime" => "DateTime",
                "DataTable" => "DataTable",
                "object" => "object",
                _ => "string" // Default to string for unknown types
            };
        }

        private string FindCorrespondingField(string propertyName, Dictionary<string, string> fieldMap)
        {
            // Try common patterns: mPropertyName, _propertyName, propertyName
            var candidates = new[] { "m" + propertyName, "_" + propertyName, propertyName.ToLower() };
            
            foreach (var candidate in candidates)
            {
                if (fieldMap.ContainsKey(candidate))
                    return candidate;
            }
            
            return "";
        }

        private bool IsRequiredField(string fieldName, string content)
        {
            // Check if field is validated as required in the content
            var requiredPatterns = new[]
            {
                $@"if\s*\(\s*string\.IsNullOrEmpty\s*\(\s*{fieldName}",
                $@"if\s*\(\s*{fieldName}\.Trim\(\)\.Length\s*==\s*0",
                $@"throw.*{fieldName}.*empty"
            };

            return requiredPatterns.Any(pattern => Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase));
        }

        private int ExtractMaxLength(string fieldName, string content)
        {
            // Try to find length validation
            var lengthPattern = $@"{fieldName}\.Length\s*[<>]=?\s*(\d+)";
            var match = Regex.Match(content, lengthPattern);
            
            if (match.Success && int.TryParse(match.Groups[1].Value, out int length))
                return length;
            
            return 0; // No length restriction found
        }

        private List<ParameterInfo> ExtractParameters(string parametersStr)
        {
            var parameters = new List<ParameterInfo>();
            
            if (string.IsNullOrWhiteSpace(parametersStr))
                return parameters;

            var paramParts = parametersStr.Split(',');
            
            foreach (var part in paramParts)
            {
                var trimmed = part.Trim();
                var match = Regex.Match(trimmed, @"(\w+)\s+(\w+)(?:\s*=\s*(.+))?");
                
                if (match.Success)
                {
                    parameters.Add(new ParameterInfo
                    {
                        Type = MapLegacyTypeToCSharp(match.Groups[1].Value),
                        Name = match.Groups[2].Value,
                        IsOptional = match.Groups[3].Success,
                        DefaultValue = match.Groups[3].Value
                    });
                }
            }

            return parameters;
        }

        private string ExtractSqlFromMethodBody(string methodBody)
        {
            // Extract SQL command text
            var sqlPattern = @"commandText\s*=\s*""([^""]+(?:\s*\+\s*""[^""]*"")*)"";
            var match = Regex.Match(methodBody, sqlPattern, RegexOptions.Singleline);
            
            if (match.Success)
            {
                return CleanSqlString(match.Groups[1].Value);
            }

            return "";
        }

        private string ExtractStoredProcedureFromMethodBody(string methodBody)
        {
            // Extract stored procedure name - simplified pattern
            if (methodBody.Contains("StoredProcedure"))
            {
                var lines = methodBody.Split(new char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains("StoredProcedure") && line.Contains("\""))
                    {
                        var start = line.IndexOf('"');
                        var end = line.IndexOf('"', start + 1);
                        if (start >= 0 && end > start)
                        {
                            return line.Substring(start + 1, end - start - 1);
                        }
                    }
                }
            }

            return "";
        }

        private bool ShouldBeAsync(string methodName, string methodBody)
        {
            // Methods that interact with database should be async
            var dbOperations = new[] { "Execute", "Query", "Load", "Save", "Delete", "Show", "Get" };
            return dbOperations.Any(op => methodName.Contains(op)) || 
                   methodBody.Contains("ExecuteReader") || 
                   methodBody.Contains("ExecuteNonQuery") ||
                   methodBody.Contains("ExecuteDataset");
        }

        private string CleanSqlString(string sql)
        {
            // Clean up concatenated SQL strings
            return sql.Replace("\" + \"", "")
                     .Replace("\\\"", "\"")
                     .Trim();
        }
    }
}
