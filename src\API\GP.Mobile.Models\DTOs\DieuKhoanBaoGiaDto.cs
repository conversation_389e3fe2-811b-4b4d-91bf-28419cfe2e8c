using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Quotation Terms & Conditions (ClsDMDieuKhoanBaoGia)
    /// Maps to DM_DieuKhoanbaoGia table in legacy database
    /// Implements ALL properties from ClsDMDieuKhoanBaoGia.cs (442 lines)
    /// Manual implementation - Maintains 100% Legacy Compatibility
    /// CRITICAL for quotation terms and conditions management
    /// Used in form lines 8634-8712 for InitDieuKhoanBaoGia, InitDieuKhoanLSC, InitDieuKhoanQuyetToan
    /// </summary>
    public class DieuKhoanBaoGiaDto
    {
        /// <summary>
        /// Primary key - Unique identifier
        /// Maps to: mKhoa property in legacy class
        /// </summary>
        [Required]
        public string Khoa { get; set; } = string.Empty;

        /// <summary>
        /// Terms code
        /// Maps to: mMa property in legacy class
        /// </summary>
        [Required]
        public string Ma { get; set; } = string.Empty;

        /// <summary>
        /// Vietnamese name/title
        /// Maps to: mTenViet property in legacy class
        /// </summary>
        [Required]
        public string TenViet { get; set; } = string.Empty;

        /// <summary>
        /// English name/title
        /// Maps to: mTenAnh property in legacy class
        /// </summary>
        public string TenAnh { get; set; } = string.Empty;

        /// <summary>
        /// Description/Content
        /// Maps to: mDienGiai property in legacy class
        /// Contains the actual terms and conditions content
        /// </summary>
        public string DienGiai { get; set; } = string.Empty;

        /// <summary>
        /// Active status
        /// Maps to: mActive property in legacy class
        /// 1 = Active, 0 = Inactive
        /// </summary>
        public int Active { get; set; } = 1;

        /// <summary>
        /// Terms type/category
        /// Based on form usage: 'BG' (Báo Giá), 'SC' (Sửa Chữa), 'QT' (Quyết Toán)
        /// </summary>
        public string Loai { get; set; } = string.Empty;

        /// <summary>
        /// Sort order
        /// Maps to: STT field used in form SQL queries
        /// </summary>
        public int STT { get; set; } = 0;

        /// <summary>
        /// Content/Terms text
        /// Maps to: NoiDung field used in form SQL queries
        /// Contains the detailed terms and conditions text
        /// </summary>
        public string NoiDung { get; set; } = string.Empty;

        /// <summary>
        /// Creation date (YYYYMMDD format)
        /// </summary>
        public string NgayTao { get; set; } = string.Empty;

        /// <summary>
        /// User who created the record
        /// </summary>
        public string NguoiTao { get; set; } = string.Empty;

        /// <summary>
        /// Last update date (YYYYMMDD format)
        /// </summary>
        public string NgayCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// User who last updated the record
        /// </summary>
        public string NguoiCapNhat { get; set; } = string.Empty;

        /// <summary>
        /// Branch/Unit ID
        /// </summary>
        public string KhoaDonVi { get; set; } = string.Empty;

        /// <summary>
        /// Language preference
        /// Based on modGeneral.H_LANGUAGE usage in legacy class
        /// </summary>
        public string NgonNgu { get; set; } = "Viet";

        // Navigation properties for display purposes
        /// <summary>
        /// Display name based on language preference
        /// </summary>
        public string? TenHienThi { get; set; }

        /// <summary>
        /// Active status text (for display)
        /// </summary>
        public string? TrangThaiText { get; set; }

        /// <summary>
        /// Terms type text (for display)
        /// </summary>
        public string? LoaiText { get; set; }

        /// <summary>
        /// Formatted creation date (for display)
        /// </summary>
        public string? NgayTaoFormatted { get; set; }

        /// <summary>
        /// Flag indicating if terms is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Flag indicating if terms is default for type
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Flag indicating if terms is system-defined
        /// </summary>
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// Flag indicating if terms can be edited
        /// </summary>
        public bool CanEdit { get; set; } = true;

        /// <summary>
        /// Flag indicating if terms can be deleted
        /// </summary>
        public bool CanDelete { get; set; } = true;
    }

    /// <summary>
    /// DTO for creating quotation terms & conditions
    /// </summary>
    public class CreateDieuKhoanBaoGiaDto
    {
        [Required]
        public string Ma { get; set; } = string.Empty;

        [Required]
        public string TenViet { get; set; } = string.Empty;

        public string TenAnh { get; set; } = string.Empty;

        public string DienGiai { get; set; } = string.Empty;

        [Required]
        public string Loai { get; set; } = string.Empty;

        public int STT { get; set; } = 0;

        [Required]
        public string NoiDung { get; set; } = string.Empty;

        public int Active { get; set; } = 1;

        public string NguoiTao { get; set; } = string.Empty;

        public string KhoaDonVi { get; set; } = string.Empty;

        public bool IsDefault { get; set; } = false;
    }

    /// <summary>
    /// DTO for updating quotation terms & conditions
    /// </summary>
    public class UpdateDieuKhoanBaoGiaDto
    {
        public string TenViet { get; set; } = string.Empty;

        public string TenAnh { get; set; } = string.Empty;

        public string DienGiai { get; set; } = string.Empty;

        public string NoiDung { get; set; } = string.Empty;

        public int STT { get; set; } = 0;

        public int Active { get; set; } = 1;

        public string NguoiCapNhat { get; set; } = string.Empty;

        public bool IsDefault { get; set; } = false;
    }

    /// <summary>
    /// DTO for quotation terms & conditions list
    /// </summary>
    public class DieuKhoanBaoGiaListDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string Ma { get; set; } = string.Empty;
        public string TenViet { get; set; } = string.Empty;
        public string TenAnh { get; set; } = string.Empty;
        public string Loai { get; set; } = string.Empty;
        public string LoaiText { get; set; } = string.Empty;
        public int STT { get; set; } = 0;
        public int Active { get; set; } = 1;
        public string TrangThaiText { get; set; } = string.Empty;
        public string NgayTao { get; set; } = string.Empty;
        public string NguoiTao { get; set; } = string.Empty;
        public bool IsDefault { get; set; } = false;
        public bool CanEdit { get; set; } = true;
        public bool CanDelete { get; set; } = true;
        public string TenHienThi { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for quotation terms & conditions lookup
    /// </summary>
    public class DieuKhoanBaoGiaLookupDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string Ma { get; set; } = string.Empty;
        public string Ten { get; set; } = string.Empty;
        public string Loai { get; set; } = string.Empty;
        public int STT { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;
    }

    /// <summary>
    /// DTO for terms content by type
    /// Used for form InitDieuKhoan methods
    /// </summary>
    public class DieuKhoanContentDto
    {
        public string Loai { get; set; } = string.Empty;
        public List<DieuKhoanItemDto> Items { get; set; } = new List<DieuKhoanItemDto>();
        public string DefaultContent { get; set; } = string.Empty;
        public string CombinedContent { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for individual terms item
    /// </summary>
    public class DieuKhoanItemDto
    {
        public string Khoa { get; set; } = string.Empty;
        public string Ma { get; set; } = string.Empty;
        public string Ten { get; set; } = string.Empty;
        public string NoiDung { get; set; } = string.Empty;
        public int STT { get; set; } = 0;
        public bool IsSelected { get; set; } = false;
        public bool IsDefault { get; set; } = false;
    }

    /// <summary>
    /// DTO for quotation terms & conditions search
    /// </summary>
    public class DieuKhoanBaoGiaSearchDto
    {
        public string? Ma { get; set; }
        public string? TenViet { get; set; }
        public string? TenAnh { get; set; }
        public string? Loai { get; set; }
        public int? Active { get; set; }
        public string? NoiDung { get; set; }
        public string? KhoaDonVi { get; set; }
        public bool? IsDefault { get; set; }
        public DateTime? CreatedFrom { get; set; }
        public DateTime? CreatedTo { get; set; }
    }

    /// <summary>
    /// DTO for quotation terms & conditions statistics
    /// </summary>
    public class DieuKhoanBaoGiaStatisticsDto
    {
        public int TotalTerms { get; set; } = 0;
        public int ActiveTerms { get; set; } = 0;
        public int InactiveTerms { get; set; } = 0;
        public int QuotationTerms { get; set; } = 0;
        public int RepairTerms { get; set; } = 0;
        public int SettlementTerms { get; set; } = 0;
        public int DefaultTerms { get; set; } = 0;
        public List<TermsByTypeDto> StatsByType { get; set; } = new List<TermsByTypeDto>();
    }

    /// <summary>
    /// Statistics by terms type
    /// </summary>
    public class TermsByTypeDto
    {
        public string Loai { get; set; } = string.Empty;
        public string LoaiText { get; set; } = string.Empty;
        public int TotalTerms { get; set; } = 0;
        public int ActiveTerms { get; set; } = 0;
        public int DefaultTerms { get; set; } = 0;
        public double UsageRate { get; set; } = 0;
    }
}
