using Dapper;
using GP.Mobile.Models.DTOs;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;

namespace GP.Mobile.Data.Repositories;

/// <summary>
/// Interface for LoaiDichVu (Service Type) repository
/// Defines ALL methods from clsDMLoaiDichVu.cs (468 lines)
/// Includes both legacy methods and modern API methods
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
public interface ILoaiDichVuRepository
{
    #region Legacy Methods (Exact mapping from clsDMLoaiDichVu.cs)
    
    // Core CRUD operations
    Task<bool> LoadAsync(string khoa);
    Task<bool> SaveAsync(LoaiDichVuDto dto);
    Task<bool> DelDataAsync(string khoa);
    
    // List and search methods
    Task<DataTable> ShowListAsync(string condition = "");
    Task<DataTable> ShowAllListAsync();
    Task<string> SearchByCodeAsync(string code = "", string condition = "");
    Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "");
    
    // Utility methods
    Task<bool> TrungMaAsync(string ma, string khoa);
    Task<bool> WasUsedAsync(string khoa);
    
    #endregion

    #region Modern API Methods
    
    Task<IEnumerable<LoaiDichVuListDto>> GetAllAsync();
    Task<LoaiDichVuDto?> GetByIdAsync(string khoa);
    Task<LoaiDichVuDto?> GetByCodeAsync(string ma);
    Task<string> CreateAsync(CreateLoaiDichVuDto createDto);
    Task<bool> UpdateAsync(LoaiDichVuDto dto);
    Task<bool> UpdateStatusAsync(UpdateLoaiDichVuStatusDto statusDto);
    Task<IEnumerable<LoaiDichVuListDto>> SearchAsync(LoaiDichVuSearchDto searchDto);
    Task<IEnumerable<LoaiDichVuLookupDto>> GetLookupAsync(string language = "vi");
    Task<LoaiDichVuValidationDto> ValidateAsync(string khoa, string ma);
    Task<LoaiDichVuSearchByCodeDto?> SearchServiceByCodeAsync(string code, string condition = "");
    Task<IEnumerable<ServiceCategoryDto>> GetServiceCategoriesAsync();
    Task<LoaiDichVuStatsDto?> GetServiceStatsAsync(string khoa);
    
    #endregion
}

/// <summary>
/// Complete Repository for LoaiDichVu entity
/// Implements ALL methods from clsDMLoaiDichVu.cs (468 lines)
/// Includes exact SQL queries and stored procedures from legacy
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE SERVICE FOCUSED - Essential for service categorization
/// </summary>
public class LoaiDichVuRepository : ILoaiDichVuRepository
{
    private readonly IDbConnection _connection;
    private readonly ILogger<LoaiDichVuRepository> _logger;

    public LoaiDichVuRepository(IDbConnection connection, ILogger<LoaiDichVuRepository> logger)
    {
        _connection = connection;
        _logger = logger;
    }

    #region Legacy Methods Implementation

    public async Task<bool> LoadAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy Load method (line 168)
            string commandText = "SELECT * FROM DM_LoaiDichVu WHERE Khoa = @Khoa";
            var result = await _connection.QueryFirstOrDefaultAsync<LoaiDichVuDto>(commandText, new { Khoa = khoa });
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading LoaiDichVu: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<bool> SaveAsync(LoaiDichVuDto dto)
    {
        try
        {
            // Exact stored procedure from legacy Save method (line 210)
            var parameters = new DynamicParameters();
            parameters.Add("@Khoa", dto.Khoa);
            parameters.Add("@Ma", dto.Ma);
            parameters.Add("@TenViet", dto.TenViet);
            parameters.Add("@TenAnh", dto.TenAnh);
            parameters.Add("@DienGiai", dto.DienGiai);
            parameters.Add("@TuNgay", dto.TuNgay);
            parameters.Add("@KhoaNhanVienCapNhat", dto.KhoaNhanVienCapNhat);
            parameters.Add("@Active", dto.Active);
            parameters.Add("@Send", dto.Send);

            await _connection.ExecuteAsync("sp_DM_LoaiDichVu", parameters, commandType: CommandType.StoredProcedure);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving LoaiDichVu: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> DelDataAsync(string khoa)
    {
        try
        {
            // Check if used first (from legacy DelData method line 370)
            var wasUsed = await WasUsedAsync(khoa);
            if (wasUsed)
            {
                _logger.LogWarning("Cannot delete LoaiDichVu {Khoa} - it is being used", khoa);
                return false;
            }

            // Exact SQL from legacy DelData method (line 382)
            string commandText = "DELETE FROM DM_LoaiDichVu WHERE Khoa = @Khoa";
            var rowsAffected = await _connection.ExecuteAsync(commandText, new { Khoa = khoa.Trim() });
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting LoaiDichVu: {Khoa}", khoa);
            return false;
        }
    }

    public async Task<DataTable> ShowListAsync(string condition = "")
    {
        try
        {
            // Exact SQL from legacy ShowList method (line 234)
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                whereClause = " AND " + condition;
            }

            // Note: Using TenViet as default language (modGeneral.H_LANGUAGE would be "Viet")
            string commandText = $@"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM(TenViet) as Ten  
                FROM DM_LoaiDichVu 
                WHERE Active = 1 {whereClause} 
                ORDER BY Ma";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiDichVu list");
            return new DataTable();
        }
    }

    public async Task<DataTable> ShowAllListAsync()
    {
        try
        {
            // Exact SQL from legacy ShowAllList method (line 259)
            string commandText = @"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM(TenViet) as Ten  
                FROM DM_LoaiDichVu 
                ORDER BY Ma";
            
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing all LoaiDichVu list");
            return new DataTable();
        }
    }

    public async Task<string> SearchByCodeAsync(string code = "", string condition = "")
    {
        try
        {
            // Exact SQL from legacy SearchByCode method (line 274)
            string codeFilter = "";
            string conditionFilter = "";

            if (!string.IsNullOrWhiteSpace(code))
            {
                codeFilter = " AND RTRIM(Ma) = @Code";
            }

            if (!string.IsNullOrWhiteSpace(condition))
            {
                conditionFilter = " AND " + condition;
            }

            string commandText = $@"
                SELECT Khoa, Ma, TenViet as Ten  
                FROM DM_LoaiDichVu 
                WHERE Active = 1 {codeFilter} {conditionFilter}";

            var result = await _connection.QueryFirstOrDefaultAsync(commandText, new { Code = code.Trim() });
            
            if (result != null)
            {
                // Return in legacy format: "Khoa|Ma|Ten"
                return $"{result.Khoa}|{result.Ma}|{result.Ten}";
            }

            return "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiDichVu by code");
            return "";
        }
    }

    public async Task<DataTable> ShowListByFieldAsync(string fieldList, string conditions = "", string order = "")
    {
        try
        {
            // Exact SQL from legacy ShowListByField method (line 400)
            fieldList = fieldList.Replace("|", ",");
            
            string whereClause = "";
            if (!string.IsNullOrWhiteSpace(conditions))
            {
                whereClause = " WHERE " + conditions;
            }

            string orderClause = "";
            if (!string.IsNullOrWhiteSpace(order))
            {
                orderClause = " ORDER BY " + order;
            }

            string commandText = $"SELECT {fieldList} FROM DM_LoaiDichVu {whereClause} {orderClause}";
            var result = await _connection.QueryAsync(commandText);
            return ConvertToDataTable(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing LoaiDichVu list by field");
            return new DataTable();
        }
    }

    public async Task<bool> TrungMaAsync(string ma, string khoa)
    {
        try
        {
            // Exact SQL from legacy TrungMa method (line 317)
            string commandText = @"
                SELECT * FROM DM_LoaiDichVu 
                WHERE RTRIM(Ma) = @Ma AND RTRIM(Khoa) <> @Khoa";
            
            var result = await _connection.QueryAsync(commandText, new { Ma = ma.Trim(), Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate service type code");
            return true; // Return true to be safe
        }
    }

    public async Task<bool> WasUsedAsync(string khoa)
    {
        try
        {
            // Exact SQL from legacy WasUsed method (line 352)
            string commandText = "SELECT * FROM SC_BaoGia WHERE RTRIM(KhoaLoaiDichVu) = @Khoa";
            var result = await _connection.QueryAsync(commandText, new { Khoa = khoa.Trim() });
            return result.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if LoaiDichVu was used");
            return true; // Return true to be safe
        }
    }

    #endregion

    #region Modern API Methods Implementation

    public async Task<IEnumerable<LoaiDichVuListDto>> GetAllAsync()
    {
        try
        {
            string commandText = @"
                SELECT Khoa, Ma, TenViet, TenAnh, DienGiai, TuNgay, Active
                FROM DM_LoaiDichVu
                WHERE Active = 1
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiDichVuListDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all LoaiDichVu records");
            return new List<LoaiDichVuListDto>();
        }
    }

    public async Task<LoaiDichVuDto?> GetByIdAsync(string khoa)
    {
        try
        {
            string commandText = "SELECT * FROM DM_LoaiDichVu WHERE Khoa = @Khoa";
            return await _connection.QueryFirstOrDefaultAsync<LoaiDichVuDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by ID: {Khoa}", khoa);
            return null;
        }
    }

    public async Task<LoaiDichVuDto?> GetByCodeAsync(string ma)
    {
        try
        {
            string commandText = "SELECT * FROM DM_LoaiDichVu WHERE RTRIM(Ma) = @Ma";
            return await _connection.QueryFirstOrDefaultAsync<LoaiDichVuDto>(commandText, new { Ma = ma.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu by code: {Ma}", ma);
            return null;
        }
    }

    public async Task<string> CreateAsync(CreateLoaiDichVuDto createDto)
    {
        try
        {
            var khoa = Guid.NewGuid().ToString();
            var dto = new LoaiDichVuDto
            {
                Khoa = khoa,
                Ma = createDto.Ma,
                TenViet = createDto.TenViet,
                TenAnh = createDto.TenAnh,
                DienGiai = createDto.DienGiai,
                TuNgay = createDto.TuNgay,
                Active = createDto.Active,
                Send = 0
            };

            var success = await SaveAsync(dto);
            return success ? khoa : "";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating LoaiDichVu");
            return "";
        }
    }

    public async Task<bool> UpdateAsync(LoaiDichVuDto dto)
    {
        try
        {
            return await SaveAsync(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiDichVu: {Khoa}", dto.Khoa);
            return false;
        }
    }

    public async Task<bool> UpdateStatusAsync(UpdateLoaiDichVuStatusDto statusDto)
    {
        try
        {
            string commandText = @"
                UPDATE DM_LoaiDichVu
                SET Active = @Active,
                    KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat
                WHERE Khoa = @Khoa";

            var rowsAffected = await _connection.ExecuteAsync(commandText, new
            {
                statusDto.Khoa,
                statusDto.Active,
                statusDto.KhoaNhanVienCapNhat
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating LoaiDichVu status: {Khoa}", statusDto.Khoa);
            return false;
        }
    }

    public async Task<IEnumerable<LoaiDichVuListDto>> SearchAsync(LoaiDichVuSearchDto searchDto)
    {
        try
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchDto.Ma))
            {
                conditions.Add("Ma LIKE @Ma");
                parameters.Add("@Ma", $"%{searchDto.Ma}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenViet))
            {
                conditions.Add("TenViet LIKE @TenViet");
                parameters.Add("@TenViet", $"%{searchDto.TenViet}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TenAnh))
            {
                conditions.Add("TenAnh LIKE @TenAnh");
                parameters.Add("@TenAnh", $"%{searchDto.TenAnh}%");
            }

            if (!string.IsNullOrWhiteSpace(searchDto.DienGiai))
            {
                conditions.Add("DienGiai LIKE @DienGiai");
                parameters.Add("@DienGiai", $"%{searchDto.DienGiai}%");
            }

            if (searchDto.Active.HasValue)
            {
                conditions.Add("Active = @Active");
                parameters.Add("@Active", searchDto.Active.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayFrom))
            {
                conditions.Add("TuNgay >= @TuNgayFrom");
                parameters.Add("@TuNgayFrom", searchDto.TuNgayFrom);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.TuNgayTo))
            {
                conditions.Add("TuNgay <= @TuNgayTo");
                parameters.Add("@TuNgayTo", searchDto.TuNgayTo);
            }

            if (!string.IsNullOrWhiteSpace(searchDto.KhoaNhanVienCapNhat))
            {
                conditions.Add("KhoaNhanVienCapNhat = @KhoaNhanVienCapNhat");
                parameters.Add("@KhoaNhanVienCapNhat", searchDto.KhoaNhanVienCapNhat);
            }

            string whereClause = conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
            string commandText = $@"
                SELECT Khoa, Ma, TenViet, TenAnh, DienGiai, TuNgay, Active
                FROM DM_LoaiDichVu
                {whereClause}
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiDichVuListDto>(commandText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching LoaiDichVu");
            return new List<LoaiDichVuListDto>();
        }
    }

    public async Task<IEnumerable<LoaiDichVuLookupDto>> GetLookupAsync(string language = "vi")
    {
        try
        {
            string nameField = language.ToLower() == "en" ? "TenAnh" : "TenViet";
            string commandText = $@"
                SELECT Khoa, RTRIM(Ma) as Ma, RTRIM({nameField}) as Ten
                FROM DM_LoaiDichVu
                WHERE Active = 1
                ORDER BY Ma";

            return await _connection.QueryAsync<LoaiDichVuLookupDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting LoaiDichVu lookup");
            return new List<LoaiDichVuLookupDto>();
        }
    }

    public async Task<LoaiDichVuValidationDto> ValidateAsync(string khoa, string ma)
    {
        try
        {
            var result = new LoaiDichVuValidationDto
            {
                Khoa = khoa,
                Ma = ma
            };

            // Check if duplicate
            result.IsDuplicate = await TrungMaAsync(ma, khoa);

            // Check if used (only if not creating new)
            if (!string.IsNullOrEmpty(khoa))
            {
                result.IsUsed = await WasUsedAsync(khoa);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating LoaiDichVu");
            return new LoaiDichVuValidationDto { Khoa = khoa, Ma = ma };
        }
    }

    public async Task<LoaiDichVuSearchByCodeDto?> SearchServiceByCodeAsync(string code, string condition = "")
    {
        try
        {
            string conditionFilter = "";
            if (!string.IsNullOrWhiteSpace(condition))
            {
                conditionFilter = " AND " + condition;
            }

            string commandText = $@"
                SELECT Khoa, Ma, TenViet as Ten
                FROM DM_LoaiDichVu
                WHERE Active = 1
                AND RTRIM(Ma) = @Code {conditionFilter}";

            return await _connection.QueryFirstOrDefaultAsync<LoaiDichVuSearchByCodeDto>(commandText,
                new { Code = code.Trim() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching service by code");
            return null;
        }
    }

    public async Task<IEnumerable<ServiceCategoryDto>> GetServiceCategoriesAsync()
    {
        try
        {
            string commandText = @"
                SELECT
                    Khoa, Ma, TenViet, TenAnh, DienGiai,
                    CASE WHEN UPPER(Ma) LIKE '%MAINTENANCE%' OR UPPER(TenViet) LIKE '%BẢO DƯỠNG%' THEN 1 ELSE 0 END as IsMaintenanceService,
                    CASE WHEN UPPER(Ma) LIKE '%REPAIR%' OR UPPER(TenViet) LIKE '%SỬA CHỮA%' THEN 1 ELSE 0 END as IsRepairService,
                    CASE WHEN UPPER(Ma) LIKE '%INSPECTION%' OR UPPER(TenViet) LIKE '%KIỂM TRA%' THEN 1 ELSE 0 END as IsInspectionService,
                    CASE WHEN UPPER(Ma) LIKE '%WARRANTY%' OR UPPER(TenViet) LIKE '%BẢO HÀNH%' THEN 1 ELSE 0 END as IsWarrantyService
                FROM DM_LoaiDichVu
                WHERE Active = 1
                ORDER BY Ma";

            return await _connection.QueryAsync<ServiceCategoryDto>(commandText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service categories");
            return new List<ServiceCategoryDto>();
        }
    }

    public async Task<LoaiDichVuStatsDto?> GetServiceStatsAsync(string khoa)
    {
        try
        {
            string commandText = @"
                SELECT
                    ldv.Khoa, ldv.Ma, ldv.TenViet,
                    COUNT(bg.Khoa) as TotalServices,
                    COUNT(CASE WHEN bg.Active = 1 THEN 1 END) as ActiveServices,
                    ISNULL(SUM(bg.TongTien), 0) as TotalRevenue,
                    ISNULL(MAX(bg.NgayBaoGia), '1900-01-01') as LastUsed
                FROM DM_LoaiDichVu ldv
                LEFT JOIN SC_BaoGia bg ON ldv.Khoa = bg.KhoaLoaiDichVu
                WHERE ldv.Khoa = @Khoa
                GROUP BY ldv.Khoa, ldv.Ma, ldv.TenViet";

            return await _connection.QueryFirstOrDefaultAsync<LoaiDichVuStatsDto>(commandText, new { Khoa = khoa });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service stats");
            return null;
        }
    }

    #endregion

    #region Helper Methods

    private DataTable ConvertToDataTable(IEnumerable<dynamic> data)
    {
        var dataTable = new DataTable();

        if (data.Any())
        {
            var firstRow = data.First() as IDictionary<string, object>;
            if (firstRow != null)
            {
                foreach (var column in firstRow.Keys)
                {
                    dataTable.Columns.Add(column);
                }

                foreach (var row in data)
                {
                    var dataRow = dataTable.NewRow();
                    var rowDict = row as IDictionary<string, object>;
                    if (rowDict != null)
                    {
                        foreach (var column in rowDict.Keys)
                        {
                            dataRow[column] = rowDict[column] ?? DBNull.Value;
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }
            }
        }

        return dataTable;
    }

    #endregion
}
