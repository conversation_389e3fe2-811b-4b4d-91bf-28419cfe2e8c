<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 GP Mobile App - Web Simulator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .mobile-container {
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 40px;
            padding: 10px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
            position: relative;
        }
        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: #667eea;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .app-content {
            padding: 20px;
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8f9fa;
        }
        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .app-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
        .app-subtitle {
            font-size: 14px;
            color: #666;
            margin: 5px 0 0 0;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 15px 0;
        }
        .config-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .config-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .config-label {
            font-weight: 500;
            color: #555;
        }
        .config-value {
            color: #333;
            font-family: monospace;
            font-size: 12px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .status-label {
            font-weight: 500;
            color: #333;
        }
        .status-value {
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-testing {
            background: #fff3cd;
            color: #856404;
        }
        .button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .button:active {
            transform: translateY(0);
        }
        .results-section {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="mobile-screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>GP Mobile</span>
                <span>100%</span>
            </div>
            
            <div class="app-content">
                <div class="app-header">
                    <h1 class="app-title">🔧 GP Mobile</h1>
                    <p class="app-subtitle">API Connection Test</p>
                </div>
                
                <div class="card">
                    <h2 class="card-title">📊 Configuration</h2>
                    <div class="config-row">
                        <span class="config-label">API URL:</span>
                        <span class="config-value">http://localhost:5001</span>
                    </div>
                    <div class="config-row">
                        <span class="config-label">Environment:</span>
                        <span class="config-value">Development</span>
                    </div>
                    <div class="config-row">
                        <span class="config-label">Platform:</span>
                        <span class="config-value">Web Simulator</span>
                    </div>
                    <div class="config-row">
                        <span class="config-label">Database:</span>
                        <span class="config-value">CARSOFT_GIAPHAT</span>
                    </div>
                    <div class="config-row">
                        <span class="config-label">Your IP:</span>
                        <span class="config-value">************</span>
                    </div>
                </div>
                
                <div class="card">
                    <h2 class="card-title">🔍 Connection Status</h2>
                    <div class="status-item">
                        <span class="status-label">API Health Check:</span>
                        <span id="api-status" class="status-value status-testing">Testing...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Database Connection:</span>
                        <span id="db-status" class="status-value status-testing">Testing...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Tables Check:</span>
                        <span id="tables-status" class="status-value status-testing">Testing...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Users Check:</span>
                        <span id="users-status" class="status-value status-testing">Testing...</span>
                    </div>
                </div>
                
                <button class="button" onclick="runTests()">🔄 Retry Connection Tests</button>
                <button class="button" onclick="toggleResults()">📊 Show Detailed Results</button>
                
                <div id="results" class="results-section" style="display: none;">
                    <div class="loading">Click "Show Detailed Results" to see API responses...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status-value status-${status}`;
        }
        
        async function runTests() {
            // Reset all statuses
            updateStatus('api-status', 'testing', 'Testing...');
            updateStatus('db-status', 'testing', 'Testing...');
            updateStatus('tables-status', 'testing', 'Testing...');
            updateStatus('users-status', 'testing', 'Testing...');
            
            // Test API Health
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                updateStatus('api-status', 'success', '✅ Connected');
            } catch (error) {
                updateStatus('api-status', 'error', '❌ Failed');
            }
            
            // Test Database
            try {
                const response = await fetch(`${API_BASE}/api/test-database`);
                const data = await response.json();
                updateStatus('db-status', 'success', '✅ Connected');
            } catch (error) {
                updateStatus('db-status', 'error', '❌ Failed');
            }
            
            // Test Tables
            try {
                const response = await fetch(`${API_BASE}/api/test-tables`);
                const data = await response.json();
                updateStatus('tables-status', 'success', '✅ Found');
            } catch (error) {
                updateStatus('tables-status', 'error', '❌ Failed');
            }
            
            // Test Users
            try {
                const response = await fetch(`${API_BASE}/api/test-users`);
                const data = await response.json();
                if (data.Status === 'Users Table Not Found') {
                    updateStatus('users-status', 'testing', '⚠️ Table Missing');
                } else {
                    updateStatus('users-status', 'success', '✅ Found');
                }
            } catch (error) {
                updateStatus('users-status', 'error', '❌ Failed');
            }
        }
        
        async function toggleResults() {
            const resultsDiv = document.getElementById('results');
            if (resultsDiv.style.display === 'none') {
                resultsDiv.style.display = 'block';
                await showDetailedResults();
            } else {
                resultsDiv.style.display = 'none';
            }
        }
        
        async function showDetailedResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">Loading detailed results...</div>';
            
            let results = '';
            
            try {
                const healthResponse = await fetch(`${API_BASE}/api/health`);
                const healthData = await healthResponse.json();
                results += `<strong>API Health:</strong>\n${JSON.stringify(healthData, null, 2)}\n\n`;
            } catch (error) {
                results += `<strong>API Health Error:</strong>\n${error.message}\n\n`;
            }
            
            try {
                const dbResponse = await fetch(`${API_BASE}/api/test-database`);
                const dbData = await dbResponse.json();
                results += `<strong>Database Test:</strong>\n${JSON.stringify(dbData, null, 2)}\n\n`;
            } catch (error) {
                results += `<strong>Database Error:</strong>\n${error.message}\n\n`;
            }
            
            try {
                const tablesResponse = await fetch(`${API_BASE}/api/test-tables`);
                const tablesData = await tablesResponse.json();
                results += `<strong>Tables Test:</strong>\n${JSON.stringify(tablesData, null, 2)}\n\n`;
            } catch (error) {
                results += `<strong>Tables Error:</strong>\n${error.message}\n\n`;
            }
            
            resultsDiv.innerHTML = `<pre>${results}</pre>`;
        }
        
        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
