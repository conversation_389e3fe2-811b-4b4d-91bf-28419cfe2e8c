using GP.Mobile.Core.Services;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers;

/// <summary>
/// Complete Controller for BaoGiaChiTiet (Service Quotation Details) entity
/// Implements ALL endpoints from clsBaoGiaChiTiet.cs (1,140 lines)
/// Includes REST API and 5+ legacy method endpoints
/// Maps to SC_BaoGiaChiTiet table with 46 properties
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service quotation line items and detailed pricing
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class BaoGiaChiTietController : ControllerBase
{
    private readonly IBaoGiaChiTietService _baoGiaChiTietService;
    private readonly ILogger<BaoGiaChiTietController> _logger;

    public BaoGiaChiTietController(IBaoGiaChiTietService baoGiaChiTietService, ILogger<BaoGiaChiTietController> logger)
    {
        _baoGiaChiTietService = baoGiaChiTietService;
        _logger = logger;
    }

    #region Modern REST API Endpoints

    /// <summary>
    /// Get all quotation details
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> GetAll()
    {
        try
        {
            var result = await _baoGiaChiTietService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all quotation details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get quotation details by quotation ID
    /// </summary>
    [HttpGet("baogia/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> GetByBaoGia(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaChiTietService.GetByBaoGiaAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quotation details by BaoGia");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get quotation detail by ID
    /// </summary>
    [HttpGet("{khoa}")]
    public async Task<ActionResult<BaoGiaChiTietDto>> GetById(string khoa)
    {
        try
        {
            var result = await _baoGiaChiTietService.GetByIdAsync(khoa);
            if (result == null)
                return NotFound();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quotation detail by ID");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Create new quotation detail
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateBaoGiaChiTietDto createDto)
    {
        try
        {
            var result = await _baoGiaChiTietService.CreateAsync(createDto);
            if (string.IsNullOrEmpty(result))
                return BadRequest("Không thể tạo chi tiết báo giá");
            
            return CreatedAtAction(nameof(GetById), new { khoa = result }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update quotation detail
    /// </summary>
    [HttpPut("{khoa}")]
    public async Task<ActionResult<bool>> Update(string khoa, [FromBody] BaoGiaChiTietDto dto)
    {
        try
        {
            if (khoa != dto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaChiTietService.UpdateAsync(dto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Delete quotation detail
    /// </summary>
    [HttpDelete("{khoa}")]
    public async Task<ActionResult<bool>> Delete(string khoa)
    {
        try
        {
            var result = await _baoGiaChiTietService.DeleteAsync(khoa);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting quotation detail");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update quotation detail status
    /// </summary>
    [HttpPut("{khoa}/status")]
    public async Task<ActionResult<bool>> UpdateStatus(string khoa, [FromBody] UpdateBaoGiaChiTietStatusDto statusDto)
    {
        try
        {
            if (khoa != statusDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaChiTietService.UpdateStatusAsync(statusDto);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quotation detail status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Search quotation details
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> Search([FromBody] BaoGiaChiTietSearchDto searchDto)
    {
        try
        {
            var result = await _baoGiaChiTietService.SearchAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching quotation details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Calculate pricing for quotation detail
    /// </summary>
    [HttpGet("{khoa}/pricing")]
    public async Task<ActionResult<BaoGiaChiTietPricingDto>> CalculatePricing(string khoa)
    {
        try
        {
            var result = await _baoGiaChiTietService.CalculatePricingAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating quotation detail pricing");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Update pricing for quotation detail
    /// </summary>
    [HttpPut("{khoa}/pricing")]
    public async Task<ActionResult<bool>> UpdatePricing(string khoa, [FromBody] BaoGiaChiTietPricingDto pricingDto)
    {
        try
        {
            if (khoa != pricingDto.Khoa)
                return BadRequest("ID mismatch");

            var result = await _baoGiaChiTietService.UpdatePricingAsync(pricingDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quotation detail pricing");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get automotive quotation details
    /// </summary>
    [HttpGet("automotive/{khoaBaoGia}")]
    public async Task<ActionResult<IEnumerable<AutomotiveQuotationDetailDto>>> GetAutomotiveQuotationDetails(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaChiTietService.GetAutomotiveQuotationDetailsAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automotive quotation details");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Reorder quotation detail items
    /// </summary>
    [HttpPut("baogia/{khoaBaoGia}/reorder")]
    public async Task<ActionResult<bool>> ReorderItems(string khoaBaoGia, [FromBody] List<string> orderedKhoas)
    {
        try
        {
            var result = await _baoGiaChiTietService.ReorderItemsAsync(khoaBaoGia, orderedKhoas);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering quotation detail items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Bulk update status for multiple quotation details
    /// </summary>
    [HttpPut("bulk-status")]
    public async Task<ActionResult<bool>> BulkUpdateStatus([FromBody] BulkUpdateStatusRequestDto request)
    {
        try
        {
            var result = await _baoGiaChiTietService.BulkUpdateStatusAsync(request.Khoas, request.Status);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating quotation detail status");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get total amount by quotation
    /// </summary>
    [HttpGet("baogia/{khoaBaoGia}/total")]
    public async Task<ActionResult<decimal>> GetTotalAmountByBaoGia(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaChiTietService.GetTotalAmountByBaoGiaAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total amount by BaoGia");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get item count by quotation
    /// </summary>
    [HttpGet("baogia/{khoaBaoGia}/count")]
    public async Task<ActionResult<int>> GetItemCountByBaoGia(string khoaBaoGia)
    {
        try
        {
            var result = await _baoGiaChiTietService.GetItemCountByBaoGiaAsync(khoaBaoGia);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting item count by BaoGia");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get backordered items
    /// </summary>
    [HttpGet("backordered")]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> GetBackorderedItems()
    {
        try
        {
            var result = await _baoGiaChiTietService.GetBackorderedItemsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backordered items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get warranty items
    /// </summary>
    [HttpGet("warranty")]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> GetWarrantyItems()
    {
        try
        {
            var result = await _baoGiaChiTietService.GetWarrantyItemsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warranty items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Get subcontracted items
    /// </summary>
    [HttpGet("subcontracted")]
    public async Task<ActionResult<IEnumerable<BaoGiaChiTietListDto>>> GetSubcontractedItems()
    {
        try
        {
            var result = await _baoGiaChiTietService.GetSubcontractedItemsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subcontracted items");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    #endregion

    #region Legacy Method Endpoints

    /// <summary>
    /// Legacy Load method endpoint
    /// </summary>
    [HttpPost("load")]
    public async Task<ActionResult<bool>> Load([FromBody] string khoa)
    {
        try
        {
            var result = await _baoGiaChiTietService.LoadAsync(khoa);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Load endpoint");
            return StatusCode(500, "Lỗi hệ thống");
        }
    }

    /// <summary>
    /// Legacy Save method endpoint
    /// </summary>
    [HttpPost("save")]
    public async Task<ActionResult<bool>> Save([FromBody] BaoGiaChiTietDto dto)
    {
        try
        {
            var result = await _baoGiaChiTietService.SaveAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Save endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy UpdatePhanCongToSuaChua method endpoint
    /// </summary>
    [HttpPost("updatephancongtosuachua")]
    public async Task<ActionResult<bool>> UpdatePhanCongToSuaChua([FromBody] UpdatePhanCongToSuaChuaRequestDto request)
    {
        try
        {
            var result = await _baoGiaChiTietService.UpdatePhanCongToSuaChuaAsync(request.KhoaBoPhan, request.DienGiai, request.KhoaBaoGiaChiTiet);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdatePhanCongToSuaChua endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy SaveTmp method endpoint
    /// </summary>
    [HttpPost("savetmp")]
    public async Task<ActionResult<bool>> SaveTmp([FromBody] SaveTmpRequestDto request)
    {
        try
        {
            var result = await _baoGiaChiTietService.SaveTmpAsync(request.Dto, request.Guid);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveTmp endpoint");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Legacy SaveLogId method endpoint
    /// </summary>
    [HttpPost("savelogid")]
    public async Task<ActionResult<bool>> SaveLogId([FromBody] SaveLogIdRequestDto request)
    {
        try
        {
            var result = await _baoGiaChiTietService.SaveLogIdAsync(request.Id, request.Guid);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveLogId endpoint");
            return BadRequest(ex.Message);
        }
    }

    #endregion
}

#region Request DTOs for BaoGiaChiTiet

/// <summary>
/// Request DTO for UpdatePhanCongToSuaChua method
/// </summary>
public class UpdatePhanCongToSuaChuaRequestDto
{
    public string KhoaBoPhan { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public string KhoaBaoGiaChiTiet { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SaveTmp method
/// </summary>
public class SaveTmpRequestDto
{
    public BaoGiaChiTietDto Dto { get; set; } = new();
    public string Guid { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for SaveLogId method
/// </summary>
public class SaveLogIdRequestDto
{
    public string Id { get; set; } = string.Empty;
    public string Guid { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for bulk status update
/// </summary>
public class BulkUpdateStatusRequestDto
{
    public List<string> Khoas { get; set; } = new();
    public int Status { get; set; } = 0;
}

#endregion
