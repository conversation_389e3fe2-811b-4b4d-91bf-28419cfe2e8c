using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for DoiTuong (Customer/Partner) entity
/// Maps exactly to DM_DoiTuong table in legacy database
/// Implements ALL properties from clsDMDoiTuong.cs
/// </summary>
public class DoiTuongDto
{
    // Core identification
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;

    // Geographic references
    public string KhoaQuocGia { get; set; } = string.Empty;
    public string KhoaTinhThanh { get; set; } = string.Empty;
    public string KhoaNganh { get; set; } = string.Empty;
    public string KhoaKhuVuc { get; set; } = string.Empty;

    // Names
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;

    // Contact information
    public string DiaChi { get; set; } = string.Empty;
    public string DiaChiGiaoHang { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string Fax { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Website { get; set; } = string.Empty;

    // Business information
    public string MaSoThue { get; set; } = string.Empty;
    public string Loai { get; set; } = string.Empty;
    public string GhiChu { get; set; } = string.Empty;

    // Payment terms
    public int HanThanhToan { get; set; } = 0;
    public double GioiHanNo { get; set; } = 0.0;

    // Customer categorization
    public string KhoaLoaiKhachHang { get; set; } = string.Empty;
    public string KhoaNhanVienQuanLy { get; set; } = string.Empty;
    public string KhoaHangBaoHiem { get; set; } = string.Empty;

    // Vehicle information
    public string SoXe { get; set; } = string.Empty;
    public string KhoaHangSanXuat { get; set; } = string.Empty;

    // System fields
    public string TuNgay { get; set; } = string.Empty;
    public string KhoaNhanVienCapNhat { get; set; } = string.Empty;
    public int Active { get; set; } = 1;
    public int Send { get; set; } = 0;

    // Department and organization
    public string KhoaBoPhan { get; set; } = string.Empty;

    // Banking information
    public string TaiKhoanNganHang { get; set; } = string.Empty;
    public string NganHang { get; set; } = string.Empty;

    // Grouping
    public string KhoaNhomDoiTuong { get; set; } = string.Empty;

    // Personal identification
    public string CMND { get; set; } = string.Empty;
    public string NgayCap { get; set; } = string.Empty;
    public string NoiCap { get; set; } = string.Empty;
    public string NgaySinh { get; set; } = string.Empty;
    public string NguyenQuan { get; set; } = string.Empty;
    public string ChucVu { get; set; } = string.Empty;

    // Representative information
    public string NguoiDaiDien { get; set; } = string.Empty;

    // Internal codes
    public string SoNoiBo { get; set; } = string.Empty;
    public string DienThoaiBan { get; set; } = string.Empty;
    public string NoiLamViec { get; set; } = string.Empty;

    // Customer classification
    public string KhoaXepLoaiKhachHang { get; set; } = string.Empty;
    public int IsKhachHangKinhDoanh { get; set; } = 0;
    public int IsKhachHangDichVu { get; set; } = 0;
    public int IsKhachHangChinhThuc { get; set; } = 0;

    // Vehicle preferences
    public string KhoaLoaiXeQuanTam { get; set; } = string.Empty;
    public string KhoaMauSac { get; set; } = string.Empty;
    public int HinhThucThanhToan { get; set; } = 0;
    public string DuKienThoiGianMuaXe { get; set; } = string.Empty;

    // Location details
    public string KhoaQuanHuyen { get; set; } = string.Empty;

    // Audit fields - Creation
    public string GioTao { get; set; } = string.Empty;
    public string NgayTao { get; set; } = string.Empty;
    public string NguoiTao { get; set; } = string.Empty;

    // Audit fields - Update
    public string GioCapNhat { get; set; } = string.Empty;
    public string NgayCapNhat { get; set; } = string.Empty;
    public string NguoiCapNhat { get; set; } = string.Empty;

    // Marketing
    public string KhoaKenhTiepThi { get; set; } = string.Empty;

    // Employee flags
    public bool IsNhanVienKinhDoanh { get; set; } = false;

    // Branch and organization
    public string KhoaChiNhanh { get; set; } = string.Empty;

    // Additional personal info
    public string NgheNghiep { get; set; } = string.Empty;
    public string DienThoai2 { get; set; } = string.Empty;

    // Role flags
    public bool IsToTruong { get; set; } = false;
    public bool IsXuongDichVu { get; set; } = false;

    // Unit and payment
    public string KhoaDonVi { get; set; } = string.Empty;
    public string KhoaDoiTuongThanhToan { get; set; } = string.Empty;
}

/// <summary>
/// Simplified DTO for list views
/// </summary>
public class DoiTuongListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public string DienThoai { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int Active { get; set; }
}

/// <summary>
/// DTO for creating new DoiTuong
/// </summary>
public class CreateDoiTuongDto
{
    [Required]
    [StringLength(255)]
    public string TenViet { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string DiaChi { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string DienThoai { get; set; } = string.Empty;
    
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [StringLength(50)]
    public string MaSoThue { get; set; } = string.Empty;
    
    public string Loai { get; set; } = "K"; // Default to customer
}
