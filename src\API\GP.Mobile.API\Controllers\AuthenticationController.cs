using GP.Mobile.Core.Interfaces;
using GP.Mobile.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GP.Mobile.API.Controllers
{
    /// <summary>
    /// Authentication controller for mobile login
    /// Based on Frm_Login.cs functionality
    /// Provides REST API endpoints for user authentication
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthenticationService _authService;
        private readonly ILogger<AuthenticationController> _logger;

        public AuthenticationController(
            IAuthenticationService authService,
            ILogger<AuthenticationController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Login endpoint
        /// Exact implementation from legacy Frm_Login OK_Click method
        /// </summary>
        /// <param name="loginRequest">Login credentials and client selection</param>
        /// <returns>Login response with token and user info</returns>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto loginRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new LoginResponseDto
                    {
                        Success = false,
                        Message = string.Join(", ", errors)
                    });
                }

                var result = await _authService.LoginAsync(loginRequest);
                
                if (result.Success)
                {
                    _logger.LogInformation("User {Username} logged in successfully to client {ClientId}", 
                        loginRequest.Username, loginRequest.ClientId);
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning("Failed login attempt for user {Username}: {Message}", 
                        loginRequest.Username, result.Message);
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user {Username}", loginRequest.Username);
                return StatusCode(500, new LoginResponseDto
                {
                    Success = false,
                    Message = "Lỗi hệ thống khi đăng nhập"
                });
            }
        }

        /// <summary>
        /// Get available clients for user
        /// Maps to CboClient population from legacy form
        /// </summary>
        /// <param name="username">Username to get clients for</param>
        /// <returns>List of available clients/branches</returns>
        [HttpGet("clients/{username}")]
        public async Task<ActionResult<List<ClientSelectionDto>>> GetAvailableClients(string username)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                {
                    return BadRequest(new { message = "Tên đăng nhập không được để trống" });
                }

                // Use the new direct method that gets clients by username
                var clients = await _authService.GetClientsForUsernameAsync(username);
                return Ok(clients);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available clients for user {Username}", username);
                return StatusCode(500, new { message = "Lỗi hệ thống khi lấy danh sách đơn vị" });
            }
        }

        /// <summary>
        /// Logout endpoint
        /// </summary>
        /// <param name="logoutDto">Logout request data</param>
        /// <returns>Logout result</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<ActionResult> Logout([FromBody] LogoutDto logoutDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "Token không hợp lệ" });
                }

                var result = await _authService.LogoutAsync(userId, logoutDto);
                
                if (result)
                {
                    _logger.LogInformation("User {UserId} logged out successfully", userId);
                    return Ok(new { success = true, message = "Đăng xuất thành công" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "Lỗi khi đăng xuất" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "Lỗi hệ thống khi đăng xuất" });
            }
        }

        /// <summary>
        /// Refresh token endpoint
        /// </summary>
        /// <param name="refreshRequest">Refresh token request</param>
        /// <returns>New access token</returns>
        [HttpPost("refresh")]
        public async Task<ActionResult<LoginResponseDto>> RefreshToken([FromBody] RefreshTokenDto refreshRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { message = "Dữ liệu không hợp lệ" });
                }

                var result = await _authService.RefreshTokenAsync(refreshRequest);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return StatusCode(500, new LoginResponseDto
                {
                    Success = false,
                    Message = "Lỗi hệ thống khi làm mới token"
                });
            }
        }

        /// <summary>
        /// Get current user info
        /// </summary>
        /// <returns>Current user information</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<UserInfoDto>> GetCurrentUser()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "Token không hợp lệ" });
                }

                var userInfo = await _authService.GetUserInfoAsync(userId);
                if (userInfo == null)
                {
                    return NotFound(new { message = "Không tìm thấy thông tin người dùng" });
                }

                return Ok(userInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user info");
                return StatusCode(500, new { message = "Lỗi hệ thống khi lấy thông tin người dùng" });
            }
        }

        /// <summary>
        /// Change password endpoint
        /// </summary>
        /// <param name="changePasswordDto">Password change request</param>
        /// <returns>Change password result</returns>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new { message = string.Join(", ", errors) });
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "Token không hợp lệ" });
                }

                var result = await _authService.ChangePasswordAsync(userId, changePasswordDto);
                
                if (result)
                {
                    _logger.LogInformation("User {UserId} changed password successfully", userId);
                    return Ok(new { success = true, message = "Đổi mật khẩu thành công" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "Mật khẩu hiện tại không đúng" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password");
                return StatusCode(500, new { message = "Lỗi hệ thống khi đổi mật khẩu" });
            }
        }

        /// <summary>
        /// Validate token endpoint
        /// </summary>
        /// <returns>Token validation result</returns>
        [HttpGet("validate")]
        [Authorize]
        public async Task<ActionResult> ValidateToken()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { valid = false, message = "Token không hợp lệ" });
                }

                return Ok(new { valid = true, userId = userId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return StatusCode(500, new { message = "Lỗi hệ thống khi kiểm tra token" });
            }
        }

        /// <summary>
        /// Get user permissions
        /// </summary>
        /// <returns>User permissions and roles</returns>
        [HttpGet("permissions")]
        [Authorize]
        public async Task<ActionResult<UserPermissionsDto>> GetUserPermissions()
        {
            try
            {
                var userId = GetCurrentUserId();
                var clientId = GetCurrentClientId();
                
                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(clientId))
                {
                    return Unauthorized(new { message = "Token không hợp lệ" });
                }

                var permissions = await _authService.GetUserPermissionsAsync(userId, clientId);
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions");
                return StatusCode(500, new { message = "Lỗi hệ thống khi lấy quyền người dùng" });
            }
        }

        #region Helper Methods

        private string GetCurrentUserId()
        {
            return User.FindFirst("userId")?.Value ?? "";
        }

        private string GetCurrentClientId()
        {
            return User.FindFirst("clientId")?.Value ?? "";
        }

        private async Task<UserInfoDto?> GetUserByUsernameAsync(string username)
        {
            // This is a simplified implementation
            // In a real scenario, you might want to create a separate method in the service
            try
            {
                var authResult = await _authService.AuthenticateAsync(username, "dummy");
                return authResult.UserInfo;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}
