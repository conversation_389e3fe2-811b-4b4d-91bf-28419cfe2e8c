using GP.Mobile.Data.Repositories;
using GP.Mobile.Models.DTOs;
using System.Data;

namespace GP.Mobile.Core.Services;

/// <summary>
/// Complete service interface implementing ALL methods from legacy clsCoHoi.cs
/// </summary>
public interface ICoHoiService
{
    // Core operations - exact legacy functionality
    Task<bool> LoadAsync(string pKhoa);
    Task<bool> LoadSoHopDongAsync(string pKhoaSoHopDong);
    Task<bool> SaveAsync(CoHoiDto dto);
    Task<bool> DelDataAsync(string pKhoa);

    // List operations - exact legacy SQL
    Task<DataTable> ShowListAsync(string strConditions = "");
    Task<DataTable> ShowAllListAsync(string pStrLoai = "");
    Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "");

    // Validation methods - exact legacy SQL
    Task<bool> TrungSoChungTuAsync(string pSoChungTu, string pKhoa);
    Task<bool> TrungSoBienBanBanGiaoAsync(string pSoBienBanBanGiao, string pKhoa);
    Task<bool> TrungSoBienBanThoaThuanAsync(string pSoBienBanThoaThuan, string pKhoa);
    Task<bool> TrungSoHopDongAsync(string pSoHopDong, string pKhoa);

    // Utility operations
    Task ClearTempAsync(string pKeyTable);

    // Reporting operations - exact legacy SQL
    Task<DataTable> GetChiTieuChiTietTheoHopDongAsync(int Nam, int Quy, string KhoaNhanVienQuanLy);
    Task<DataTable> GetBaoCaoLaiLoTheoHopDongHoanTatAsync(string TuNgay, string DenNgay, string KhoaNhanVienQuanLy, int IsTatToan);
    Task<DataTable> GetHopDongKyMoiAsync(string strTuNgay, string strDenNgay, string strKhoaNVKD = "", string strKhoaChiNhanh = "");

    // Modern API additions for mobile app
    Task<IEnumerable<CoHoiListDto>> GetAllOpportunitiesAsync();
    Task<CoHoiDto?> GetOpportunityByIdAsync(string khoa);
    Task<string> CreateOpportunityAsync(CreateCoHoiDto createDto);
    Task<bool> UpdateOpportunityAsync(CoHoiDto dto);
    Task<bool> UpdateOpportunityStatusAsync(UpdateCoHoiStatusDto dto);
    Task<bool> DeleteOpportunityAsync(string khoa);
}

/// <summary>
/// Complete service implementation with ALL legacy methods from clsCoHoi.cs
/// Maintains exact business logic and validation rules
/// </summary>
public class CoHoiService : ICoHoiService
{
    private readonly ICoHoiRepository _repository;
    private readonly IDoiTuongRepository _doiTuongRepository;

    public CoHoiService(ICoHoiRepository repository, IDoiTuongRepository doiTuongRepository)
    {
        _repository = repository;
        _doiTuongRepository = doiTuongRepository;
    }

    #region Core Operations - Exact Legacy Implementation

    /// <summary>
    /// Load by Khoa - exact legacy functionality
    /// </summary>
    public async Task<bool> LoadAsync(string pKhoa)
    {
        return await _repository.LoadAsync(pKhoa);
    }

    /// <summary>
    /// LoadSoHopDong - exact legacy functionality
    /// </summary>
    public async Task<bool> LoadSoHopDongAsync(string pKhoaSoHopDong)
    {
        return await _repository.LoadSoHopDongAsync(pKhoaSoHopDong);
    }

    /// <summary>
    /// Save - exact legacy functionality with all validation
    /// </summary>
    public async Task<bool> SaveAsync(CoHoiDto dto)
    {
        // Apply same validation logic as legacy before saving
        if (string.IsNullOrWhiteSpace(dto.SoChungTu))
            throw new ArgumentException("Số chứng từ không được để trống");

        if (string.IsNullOrWhiteSpace(dto.KhoaKhachHang))
            throw new ArgumentException("Khách hàng không được để trống");

        // Check for duplicates exactly like legacy
        if (await _repository.TrungSoChungTuAsync(dto.SoChungTu, dto.Khoa))
            throw new InvalidOperationException("Số chứng từ đã tồn tại");

        if (!string.IsNullOrWhiteSpace(dto.SoHopDong) && await _repository.TrungSoHopDongAsync(dto.SoHopDong, dto.Khoa))
            throw new InvalidOperationException("Số hợp đồng đã tồn tại");

        return await _repository.SaveAsync(dto);
    }

    /// <summary>
    /// Delete - exact legacy functionality
    /// </summary>
    public async Task<bool> DelDataAsync(string pKhoa)
    {
        if (string.IsNullOrWhiteSpace(pKhoa))
            throw new ArgumentException("Khoa không được để trống");

        return await _repository.DelDataAsync(pKhoa);
    }

    #endregion

    #region All Legacy Methods - Direct Repository Calls

    public async Task<DataTable> ShowListAsync(string strConditions = "")
    {
        return await _repository.ShowListAsync(strConditions);
    }

    public async Task<DataTable> ShowAllListAsync(string pStrLoai = "")
    {
        return await _repository.ShowAllListAsync(pStrLoai);
    }

    public async Task<DataTable> ShowListByFieldAsync(string strFieldList, string strConditions = "", string strOrder = "")
    {
        return await _repository.ShowListByFieldAsync(strFieldList, strConditions, strOrder);
    }

    public async Task<bool> TrungSoChungTuAsync(string pSoChungTu, string pKhoa)
    {
        return await _repository.TrungSoChungTuAsync(pSoChungTu, pKhoa);
    }

    public async Task<bool> TrungSoBienBanBanGiaoAsync(string pSoBienBanBanGiao, string pKhoa)
    {
        return await _repository.TrungSoBienBanBanGiaoAsync(pSoBienBanBanGiao, pKhoa);
    }

    public async Task<bool> TrungSoBienBanThoaThuanAsync(string pSoBienBanThoaThuan, string pKhoa)
    {
        return await _repository.TrungSoBienBanThoaThuanAsync(pSoBienBanThoaThuan, pKhoa);
    }

    public async Task<bool> TrungSoHopDongAsync(string pSoHopDong, string pKhoa)
    {
        return await _repository.TrungSoHopDongAsync(pSoHopDong, pKhoa);
    }

    public async Task ClearTempAsync(string pKeyTable)
    {
        await _repository.ClearTempAsync(pKeyTable);
    }

    public async Task<DataTable> GetChiTieuChiTietTheoHopDongAsync(int Nam, int Quy, string KhoaNhanVienQuanLy)
    {
        return await _repository.GetChiTieuChiTietTheoHopDongAsync(Nam, Quy, KhoaNhanVienQuanLy);
    }

    public async Task<DataTable> GetBaoCaoLaiLoTheoHopDongHoanTatAsync(string TuNgay, string DenNgay, string KhoaNhanVienQuanLy, int IsTatToan)
    {
        return await _repository.GetBaoCaoLaiLoTheoHopDongHoanTatAsync(TuNgay, DenNgay, KhoaNhanVienQuanLy, IsTatToan);
    }

    public async Task<DataTable> GetHopDongKyMoiAsync(string strTuNgay, string strDenNgay, string strKhoaNVKD = "", string strKhoaChiNhanh = "")
    {
        return await _repository.GetHopDongKyMoiAsync(strTuNgay, strDenNgay, strKhoaNVKD, strKhoaChiNhanh);
    }

    #endregion

    #region Modern API Methods for Mobile App

    public async Task<IEnumerable<CoHoiListDto>> GetAllOpportunitiesAsync()
    {
        return await _repository.GetAllAsync();
    }

    public async Task<CoHoiDto?> GetOpportunityByIdAsync(string khoa)
    {
        if (string.IsNullOrWhiteSpace(khoa))
            return null;

        return await _repository.GetByIdAsync(khoa);
    }

    public async Task<string> CreateOpportunityAsync(CreateCoHoiDto createDto)
    {
        // Validate customer exists
        var customer = await _doiTuongRepository.GetByIdAsync(createDto.KhoaDoiTuong);
        if (customer == null)
            throw new InvalidOperationException("Khách hàng không tồn tại");

        // Validate consultant exists
        var consultant = await _doiTuongRepository.GetByIdAsync(createDto.KhoaCoVan);
        if (consultant == null)
            throw new InvalidOperationException("Cố vấn không tồn tại");

        return await _repository.CreateAsync(createDto);
    }

    public async Task<bool> UpdateOpportunityAsync(CoHoiDto dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Khoa))
            throw new ArgumentException("Khoa không được để trống");

        var existing = await _repository.GetByIdAsync(dto.Khoa);
        if (existing == null)
            throw new InvalidOperationException("Cơ hội không tồn tại");

        return await _repository.UpdateAsync(dto);
    }

    public async Task<bool> UpdateOpportunityStatusAsync(UpdateCoHoiStatusDto dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Khoa))
            throw new ArgumentException("Khoa không được để trống");

        var existing = await _repository.GetByIdAsync(dto.Khoa);
        if (existing == null)
            throw new InvalidOperationException("Cơ hội không tồn tại");

        return await _repository.UpdateStatusAsync(dto);
    }

    public async Task<bool> DeleteOpportunityAsync(string khoa)
    {
        if (string.IsNullOrWhiteSpace(khoa))
            throw new ArgumentException("Khoa không được để trống");

        var existing = await _repository.GetByIdAsync(khoa);
        if (existing == null)
            throw new InvalidOperationException("Cơ hội không tồn tại");

        return await _repository.DeleteAsync(khoa);
    }

    #endregion
}
