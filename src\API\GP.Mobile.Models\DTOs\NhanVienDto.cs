using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for NhanVien (Employees) entity
/// Maps exactly to DM_NhanVien table in legacy database
/// Implements ALL properties from clsDMNhanVien.cs (468 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service operations and employee management
/// </summary>
public class NhanVienDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Employee code
    /// Maps to: mMaNhanVien property in legacy class
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Ma<PERSON><PERSON><PERSON><PERSON> { get; set; } = string.Empty;

    /// <summary>
    /// Employee group key - Foreign key to DM_NhomNhanVien
    /// Maps to: mKhoaNhomNhanVien property in legacy class
    /// </summary>
    public string <PERSON>hoa<PERSON><PERSON><PERSON><PERSON>Vien { get; set; } = string.Empty;

    /// <summary>
    /// Vietnamese name
    /// Maps to: mTenViet property in legacy class
    /// </summary>
    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    /// <summary>
    /// English name
    /// Maps to: mTenAnh property in legacy class
    /// </summary>
    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    /// <summary>
    /// Birth date (YYYYMMDD format)
    /// Maps to: mNgaySinh property in legacy class
    /// </summary>
    [StringLength(8)]
    public string NgaySinh { get; set; } = string.Empty;

    /// <summary>
    /// Address
    /// Maps to: mDiaChi property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DiaChi { get; set; } = string.Empty;

    /// <summary>
    /// Active status (true = Active, false = Inactive)
    /// Maps to: mActive property in legacy class
    /// </summary>
    public bool Active { get; set; } = true;
}

/// <summary>
/// DTO for NhanVien list display with joined data
/// Optimized for automotive employee lists with group info
/// Used by ShowList and GetListAllNhanVien methods
/// </summary>
public class NhanVienListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string KhoaNhomNhanVien { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty; // From DM_NhomNhanVien.TenViet
    public DateTime? NgaySinh { get; set; } // Converted from char2date
    public int? Age { get; set; } // Calculated age
    public bool Active { get; set; } = true;
    public bool IsAutomotiveTechnician { get; set; } = false;
    public bool IsServiceAdvisor { get; set; } = false;
    public bool IsManager { get; set; } = false;
    public bool IsSalesStaff { get; set; } = false;
    public bool IsPartsSpecialist { get; set; } = false;
    public string Position { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Skills { get; set; } = string.Empty;
    public string Certifications { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new NhanVien
/// Contains only required fields for creation
/// </summary>
public class CreateNhanVienDto
{
    [Required]
    [StringLength(50)]
    public string MaNhanVien { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string TenViet { get; set; } = string.Empty;

    [StringLength(200)]
    public string TenAnh { get; set; } = string.Empty;

    public string KhoaNhomNhanVien { get; set; } = string.Empty;
    public string NgaySinh { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
}

/// <summary>
/// DTO for updating NhanVien status
/// Used for activation/deactivation operations
/// </summary>
public class UpdateNhanVienStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public bool Active { get; set; } = true;
}

/// <summary>
/// DTO for NhanVien search operations
/// Used for advanced search and filtering
/// </summary>
public class NhanVienSearchDto
{
    public string? MaNhanVien { get; set; }
    public string? TenViet { get; set; }
    public string? TenAnh { get; set; }
    public string? DiaChi { get; set; }
    public string? KhoaNhomNhanVien { get; set; }
    public string? NgaySinhFrom { get; set; }
    public string? NgaySinhTo { get; set; }
    public bool? Active { get; set; }
    public bool? IsAutomotiveTechnician { get; set; }
    public bool? IsServiceAdvisor { get; set; }
    public bool? IsManager { get; set; }
    public bool? IsSalesStaff { get; set; }
    public bool? IsPartsSpecialist { get; set; }
    public string? Position { get; set; }
    public string? Department { get; set; }
    public string? Skills { get; set; }
}

/// <summary>
/// DTO for NhanVien dropdown/lookup operations
/// Minimal data for UI components
/// </summary>
public class NhanVienLookupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsAutomotiveTechnician { get; set; } = false;
    public bool IsServiceAdvisor { get; set; } = false;
    public string Position { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
}

/// <summary>
/// DTO for NhanVien validation operations
/// Used for duplicate checking and validation
/// </summary>
public class NhanVienValidationDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public bool IsDuplicateCode { get; set; } = false;
    public bool IsDuplicateName { get; set; } = false;
    public bool IsUsedInServiceOperations { get; set; } = false;
    public bool CanDelete { get; set; } = true;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for employee search by code
/// Used by SearchByCode method
/// </summary>
public class NhanVienSearchByCodeDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public bool Found { get; set; } = false;
}

/// <summary>
/// DTO for automotive technicians
/// Specialized for automotive service technicians
/// </summary>
public class AutomotiveTechnicianDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty;
    public bool IsEngineTechnician { get; set; } = false;
    public bool IsBodyTechnician { get; set; } = false;
    public bool IsElectricalTechnician { get; set; } = false;
    public bool IsBrakeTechnician { get; set; } = false;
    public bool IsTransmissionTechnician { get; set; } = false;
    public bool IsDiagnosticTechnician { get; set; } = false;
    public bool IsMaintenanceTechnician { get; set; } = false;
    public bool IsPaintTechnician { get; set; } = false;
    public bool IsACTechnician { get; set; } = false;
    public bool IsTireTechnician { get; set; } = false;
    public string Certifications { get; set; } = string.Empty;
    public string Specializations { get; set; } = string.Empty;
    public int ExperienceYears { get; set; } = 0;
    public string PreferredBrands { get; set; } = string.Empty;
    public decimal HourlyRate { get; set; } = 0;
    public bool IsAvailable { get; set; } = true;
    public int CurrentWorkload { get; set; } = 0;
    public string WorkingShift { get; set; } = string.Empty;
}

/// <summary>
/// DTO for service advisors
/// Specialized for automotive service advisors
/// </summary>
public class ServiceAdvisorDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty;
    public bool IsCustomerServiceSpecialist { get; set; } = false;
    public bool IsWarrantySpecialist { get; set; } = false;
    public bool IsInsuranceSpecialist { get; set; } = false;
    public bool IsEstimateSpecialist { get; set; } = false;
    public string Languages { get; set; } = string.Empty;
    public string CustomerSegments { get; set; } = string.Empty;
    public decimal SalesTarget { get; set; } = 0;
    public decimal CurrentSales { get; set; } = 0;
    public int CustomerSatisfactionRating { get; set; } = 0;
    public int ActiveCustomers { get; set; } = 0;
    public bool IsAvailable { get; set; } = true;
    public string WorkingShift { get; set; } = string.Empty;
}

/// <summary>
/// DTO for employee with performance statistics
/// Used for comprehensive employee display with performance information
/// </summary>
public class NhanVienWithStatsDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenAnh { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty;
    public DateTime? NgaySinh { get; set; }
    public bool Active { get; set; } = true;
    public int TotalServiceJobs { get; set; } = 0;
    public int CompletedServiceJobs { get; set; } = 0;
    public int PendingServiceJobs { get; set; } = 0;
    public decimal TotalServiceRevenue { get; set; } = 0;
    public decimal AverageJobValue { get; set; } = 0;
    public int CustomerSatisfactionRating { get; set; } = 0;
    public DateTime? LastServiceDate { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public bool HasActiveAssignments { get; set; } = false;
    public bool IsOverloaded { get; set; } = false;
    public string PerformanceLevel { get; set; } = string.Empty; // Excellent, Good, Average, Poor
    public string CurrentStatus { get; set; } = string.Empty; // Available, Busy, On Break, Off Duty
}

/// <summary>
/// DTO for employee field-based queries
/// Used by ShowListByField method
/// </summary>
public class NhanVienFieldQueryDto
{
    public string FieldList { get; set; } = string.Empty; // Pipe-separated field list
    public string Conditions { get; set; } = string.Empty; // WHERE conditions
    public string OrderBy { get; set; } = string.Empty; // ORDER BY clause
}

/// <summary>
/// DTO for employee groups
/// Used for employee group management
/// </summary>
public class EmployeeGroupDto
{
    public string Khoa { get; set; } = string.Empty;
    public string MaNhanVien { get; set; } = string.Empty;
    public string TenViet { get; set; } = string.Empty;
    public string TenNhomNhanVien { get; set; } = string.Empty;
    public bool IsTechnicalGroup { get; set; } = false;
    public bool IsServiceGroup { get; set; } = false;
    public bool IsSalesGroup { get; set; } = false;
    public bool IsManagementGroup { get; set; } = false;
    public bool IsAdministrativeGroup { get; set; } = false;
    public int TotalEmployees { get; set; } = 0;
    public int ActiveEmployees { get; set; } = 0;
    public string GroupLeader { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Responsibilities { get; set; } = string.Empty;
}
