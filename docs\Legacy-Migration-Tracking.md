# Legacy Business Migration Tracking Plan

## Overview
This document tracks the migration of ALL legacy business classes from `Base/Business/` to modern .NET Core API.

**CRITICAL RULE: NO TOUCH OR MODIFY Base folder or anything inside it.**

## Migration Status Dashboard

### Summary Statistics
- **Total Legacy Classes**: 181
- **Completed**: 6 (3.3%)
- **In Progress**: 0 (0%)
- **Not Started**: 175 (96.7%)

### Completion Criteria
For each class to be marked as "COMPLETED", it must have:
1. ✅ **Complete DTO** with ALL properties from legacy class
2. ✅ **Complete Repository** with ALL methods and exact SQL queries
3. ✅ **Complete Service** with ALL business logic and validation
4. ✅ **Complete Controller** with ALL endpoints (REST + Legacy)
5. ✅ **Team Review** - Verified by business experts
6. ✅ **Testing** - All methods tested and working
7. ✅ **Documentation** - All business rules documented

## Priority Classes (Mobile App Requirements)

### ✅ COMPLETED (5/5) - ALL PRIORITY CLASSES DONE!
| Class | DTO | Repository | Service | Controller | Review | Testing | Status |
|-------|-----|------------|---------|------------|--------|---------|--------|
| clsDMDoiTuong | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | **COMPLETE** |
| clsCoHoi | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | **COMPLETE** |
| clsBaoGia | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | **COMPLETE** |
| clsNhapKho | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | **COMPLETE** |
| clsXuatKho | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | **COMPLETE** |

### 🔄 IN PROGRESS (0/5)
| Class | DTO | Repository | Service | Controller | Review | Testing | Status |
|-------|-----|------------|---------|------------|--------|---------|--------|
| - | - | - | - | - | - | - | - |

### ✅ COMPLETED (1/1) - SERVICE-RELATED DM_ CLASSES
| Class | DTO | Repository | Service | Controller | Review | Testing | Status |
|-------|-----|------------|---------|------------|--------|---------|--------|
| clsDMBaoDuong | ✅ | ✅ | ✅ | ✅ | ⏳ | ⏳ | **NEEDS REVIEW** |

## All Legacy Classes Inventory

### Data Management (DM_) Classes - 45 classes
| Class | Properties | Methods | Complexity | Priority | Status |
|-------|------------|---------|------------|----------|--------|
| clsDMDoiTuong | 50+ | 25+ | High | P1 | ✅ COMPLETE |
| clsDMBaoDuong | 6 | 8 | Medium | P2 | ✅ COMPLETE |
| clsDMChiNhanh | ? | ? | Medium | P2 | ❌ NOT STARTED |
| clsDMNhanVien | ? | ? | High | P2 | ❌ NOT STARTED |
| clsDMKhachHang | ? | ? | High | P2 | ❌ NOT STARTED |
| clsDMHangHoa | ? | ? | High | P2 | ❌ NOT STARTED |
| clsDMXe | ? | ? | High | P2 | ❌ NOT STARTED |
| clsDMLoaiXe | ? | ? | Medium | P3 | ❌ NOT STARTED |
| clsDMKho | ? | ? | Medium | P3 | ❌ NOT STARTED |
| clsDMTaiKhoan | ? | ? | Medium | P3 | ❌ NOT STARTED |
| clsDMPhongBan | ? | ? | Medium | P3 | ❌ NOT STARTED |
| ... (34 more DM classes) | | | | | ❌ NOT STARTED |

### Service Center (SC_) Classes - 25 classes
| Class | Properties | Methods | Complexity | Priority | Status |
|-------|------------|---------|------------|----------|--------|
| clsBaoGia | 133 | 75 | **VERY HIGH** | P1 | ✅ COMPLETE |
| clsCoHoi | 45+ | 35+ | High | P1 | ✅ COMPLETE |
| clsNhapKho | 60+ | 30+ | High | P1 | ✅ COMPLETE |
| clsXuatKho | 74+ | 30+ | High | P1 | ✅ COMPLETE |
| clsHoaDon | ? | ? | High | P2 | ❌ NOT STARTED |
| clsThanhToan | ? | ? | High | P2 | ❌ NOT STARTED |
| clsBaoGiaChiTiet | ? | ? | High | P2 | ❌ NOT STARTED |
| clsLenhSuaChua | ? | ? | High | P2 | ❌ NOT STARTED |
| clsPhanCongSuaChua | ? | ? | High | P2 | ❌ NOT STARTED |
| clsGhiSoSuaChua | ? | ? | Medium | P3 | ❌ NOT STARTED |
| ... (15 more SC classes) | | | | | ❌ NOT STARTED |

### General Ledger (GL_) Classes - 15 classes
| Class | Properties | Methods | Complexity | Priority | Status |
|-------|------------|---------|------------|----------|--------|
| clsChungTuQuy | ? | ? | High | P2 | ❌ NOT STARTED |
| clsChungTuTongHop | ? | ? | High | P2 | ❌ NOT STARTED |
| clsCongNo | ? | ? | High | P2 | ❌ NOT STARTED |
| ... (12 more GL classes) | | | | | ❌ NOT STARTED |

### Business Logic Classes - 96 classes
| Class | Properties | Methods | Complexity | Priority | Status |
|-------|------------|---------|------------|----------|--------|
| clsConfig | ? | ? | High | P2 | ❌ NOT STARTED |
| clsNguoiDung | ? | ? | High | P2 | ❌ NOT STARTED |
| clsMenu | ? | ? | Medium | P3 | ❌ NOT STARTED |
| clsLog | ? | ? | Medium | P3 | ❌ NOT STARTED |
| ... (92 more business classes) | | | | | ❌ NOT STARTED |

## Migration Process

### Phase 1: Analysis (Before Implementation)
1. **Class Analysis**
   - Count total properties in legacy class
   - Count total methods in legacy class
   - Identify all SQL queries and stored procedures
   - Document business rules and validation logic
   - Assess complexity level (Low/Medium/High/Very High)

2. **Dependencies Analysis**
   - Identify which other classes this class depends on
   - Identify which classes depend on this class
   - Map database table relationships
   - Document integration points

### Phase 2: Implementation (Manual Only)
1. **DTO Creation**
   - Map ALL properties exactly from legacy class
   - Include ALL validation attributes
   - Create List, Create, Update DTOs as needed
   - Document legacy field mappings

2. **Repository Implementation**
   - Implement ALL methods from legacy class
   - Use exact SQL queries from legacy class
   - Implement exact stored procedure calls
   - Handle all data access patterns

3. **Service Implementation**
   - Implement ALL business logic from legacy class
   - Include ALL validation rules
   - Implement ALL business workflows
   - Handle all exception scenarios

4. **Controller Implementation**
   - Create REST endpoints for modern API
   - Create legacy method endpoints for compatibility
   - Implement proper error handling
   - Add comprehensive logging

### Phase 3: Verification (Critical)
1. **Team Review**
   - Business expert review of all logic
   - Technical review of implementation
   - Security review of endpoints
   - Performance review of queries

2. **Testing**
   - Unit tests for all methods
   - Integration tests for workflows
   - Comparison testing with legacy system
   - Load testing for performance

3. **Documentation**
   - Document all business rules
   - Document all API endpoints
   - Document migration decisions
   - Update this tracking document

## Team Assignments

### Current Team Roles
- **PM**: Overall tracking and coordination
- **Business Experts**: Logic review and validation
- **Senior Developers**: Complex class implementation
- **Junior Developers**: Simple class implementation under supervision
- **QA Team**: Testing and verification

### Assignment Strategy
1. **Very High Complexity** (clsBaoGia): Senior developer + business expert pair
2. **High Complexity**: Senior developer with business expert review
3. **Medium Complexity**: Experienced developer with peer review
4. **Low Complexity**: Junior developer with senior review

## Risk Management

### High-Risk Classes (Require Extra Attention)
1. **clsBaoGia** - 133 properties, 75 methods, core business logic
2. **clsDMDoiTuong** - ✅ Already completed safely
3. **clsHoaDon** - Financial transactions, audit requirements
4. **clsThanhToan** - Payment processing, security critical
5. **clsNhapKho/clsXuatKho** - Inventory management, data integrity

### Mitigation Strategies
1. **Parallel Running** - Keep legacy system running alongside new API
2. **Gradual Migration** - Migrate one class at a time
3. **Rollback Plan** - Ability to revert to legacy system immediately
4. **Extensive Testing** - Test every method against legacy system
5. **Business Validation** - Business team must approve every implementation

## Success Metrics

### Quality Metrics
- **100% Method Coverage** - All legacy methods implemented
- **100% Property Coverage** - All legacy properties mapped
- **100% SQL Coverage** - All legacy queries implemented
- **Zero Business Logic Loss** - No functionality reduction

### Progress Metrics
- **Weekly Progress Reports** - Classes completed per week
- **Milestone Tracking** - Priority classes completion
- **Risk Assessment** - Issues identified and resolved
- **Team Velocity** - Implementation speed per complexity level

## Next Steps

### Immediate Actions (This Week)
1. **Complete clsBaoGia Analysis** - Full property and method inventory
2. **Assign clsBaoGia Implementation** - Senior developer + business expert
3. **Start clsHoaDon Analysis** - Prepare for next implementation
4. **Update Tracking System** - Weekly progress updates

### Short Term (Next Month)
1. **Complete Priority Classes** - All P1 classes implemented
2. **Begin P2 Classes** - Start medium priority implementations
3. **Establish Testing Process** - Automated comparison testing
4. **Team Training** - Ensure all developers understand process

### Long Term (Next Quarter)
1. **Complete All High Priority** - P1 and P2 classes done
2. **Begin Bulk Implementation** - P3 classes in parallel
3. **Performance Optimization** - Optimize implemented classes
4. **Legacy System Retirement Planning** - Prepare for cutover
