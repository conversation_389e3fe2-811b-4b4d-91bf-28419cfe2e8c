using System.ComponentModel.DataAnnotations;

namespace GP.Mobile.Models.DTOs;

/// <summary>
/// Complete DTO for BaoGiaChiTiet (Service Quotation Details) entity
/// Maps exactly to SC_BaoGiaChiTiet table in legacy database
/// Implements ALL properties from clsBaoGiaChiTiet.cs (1,140 lines)
/// Manual implementation - Maintains 100% Legacy Compatibility
/// AUTOMOTIVE FOCUSED - Essential for automotive service quotation line items and detailed pricing
/// </summary>
public class BaoGiaChiTietDto
{
    /// <summary>
    /// Primary key - Unique identifier
    /// Maps to: mKhoa property in legacy class
    /// </summary>
    [Required]
    public string Khoa { get; set; } = string.Empty;

    /// <summary>
    /// Reference to parent quotation
    /// Maps to: mKhoaBaoGia property in legacy class
    /// </summary>
    [Required]
    public string KhoaBaoGia { get; set; } = string.Empty;

    /// <summary>
    /// Service item category
    /// Maps to: mKhoaHangMuc property in legacy class
    /// </summary>
    public string KhoaHangMuc { get; set; } = string.Empty;

    /// <summary>
    /// Service/item description
    /// Maps to: mNoiDung property in legacy class
    /// </summary>
    [Required]
    [StringLength(500)]
    public string NoiDung { get; set; } = string.Empty;

    /// <summary>
    /// Unit of measure reference
    /// Maps to: mKhoaDonViTinh property in legacy class
    /// </summary>
    public string KhoaDonViTinh { get; set; } = string.Empty;

    /// <summary>
    /// Quantity
    /// Maps to: mSoLuong property in legacy class
    /// </summary>
    public decimal SoLuong { get; set; } = 0;

    /// <summary>
    /// Unit price
    /// Maps to: mDonGia property in legacy class
    /// </summary>
    public decimal DonGia { get; set; } = 0;

    /// <summary>
    /// Total amount (SoLuong * DonGia)
    /// Maps to: mThanhTien property in legacy class
    /// </summary>
    public decimal ThanhTien { get; set; } = 0;

    /// <summary>
    /// Discount percentage
    /// Maps to: mTyLeChietKhau property in legacy class
    /// </summary>
    public decimal TyLeChietKhau { get; set; } = 0;

    /// <summary>
    /// Discount amount
    /// Maps to: mTienChietKhau property in legacy class
    /// </summary>
    public decimal TienChietKhau { get; set; } = 0;

    /// <summary>
    /// Tax percentage
    /// Maps to: mTyLeThue property in legacy class
    /// </summary>
    public int TyLeThue { get; set; } = 0;

    /// <summary>
    /// Tax amount
    /// Maps to: mTienThue property in legacy class
    /// </summary>
    public decimal TienThue { get; set; } = 0;

    /// <summary>
    /// Additional notes/description
    /// Maps to: mDienGiai property in legacy class
    /// </summary>
    [StringLength(500)]
    public string DienGiai { get; set; } = string.Empty;

    /// <summary>
    /// Item type (0=Service, 1=Product, etc.)
    /// Maps to: mLoai property in legacy class
    /// </summary>
    public int Loai { get; set; } = 0;

    /// <summary>
    /// Product reference (if applicable)
    /// Maps to: mKhoaHangHoa property in legacy class
    /// </summary>
    public string KhoaHangHoa { get; set; } = string.Empty;

    /// <summary>
    /// Category reference
    /// Maps to: mKhoaLoai property in legacy class
    /// </summary>
    public string KhoaLoai { get; set; } = string.Empty;

    /// <summary>
    /// Cancelled status (0=Active, 1=Cancelled)
    /// Maps to: mHuy property in legacy class
    /// </summary>
    public int Huy { get; set; } = 0;

    /// <summary>
    /// Sort order
    /// Maps to: mSoThuTu property in legacy class
    /// </summary>
    public int SoThuTu { get; set; } = 0;

    /// <summary>
    /// Issued quantity
    /// Maps to: mSoLuongXuat property in legacy class
    /// </summary>
    public decimal SoLuongXuat { get; set; } = 0;

    /// <summary>
    /// Commission percentage
    /// Maps to: mPhanTramHoaHong property in legacy class
    /// </summary>
    public int PhanTramHoaHong { get; set; } = 0;

    /// <summary>
    /// Commission amount
    /// Maps to: mTienHoaHong property in legacy class
    /// </summary>
    public decimal TienHoaHong { get; set; } = 0;

    /// <summary>
    /// Price before commission
    /// Maps to: mDonGiaTruocHoaHong property in legacy class
    /// </summary>
    public decimal DonGiaTruocHoaHong { get; set; } = 0;

    /// <summary>
    /// Price approved status
    /// Maps to: mDaChoGia property in legacy class
    /// </summary>
    public int DaChoGia { get; set; } = 0;

    /// <summary>
    /// Origin/manufacturer country
    /// Maps to: mXuatXu property in legacy class
    /// </summary>
    [StringLength(100)]
    public string XuatXu { get; set; } = string.Empty;

    /// <summary>
    /// Total amount before insurance approval
    /// Maps to: mThanhTienTruocDuyetBH property in legacy class
    /// </summary>
    public decimal ThanhTienTruocDuyetBH { get; set; } = 0;

    /// <summary>
    /// Unit price before insurance approval
    /// Maps to: mDonGiaTruocDuyetBH property in legacy class
    /// </summary>
    public decimal DonGiaTruocDuyetBH { get; set; } = 0;

    /// <summary>
    /// Service type reference
    /// Maps to: mKhoaLoaiDichVu property in legacy class
    /// </summary>
    public string KhoaLoaiDichVu { get; set; } = string.Empty;

    /// <summary>
    /// Additional discount amount
    /// Maps to: mCKTang property in legacy class
    /// </summary>
    public decimal CKTang { get; set; } = 0;

    /// <summary>
    /// Reason for additional discount
    /// Maps to: mLyDoCKTang property in legacy class
    /// </summary>
    [StringLength(200)]
    public string LyDoCKTang { get; set; } = string.Empty;

    /// <summary>
    /// Department reference
    /// Maps to: mKhoaBoPhan property in legacy class
    /// </summary>
    public string KhoaBoPhan { get; set; } = string.Empty;

    /// <summary>
    /// Warranty coverage flag
    /// Maps to: mIsBaoHanh property in legacy class
    /// </summary>
    public bool IsBaoHanh { get; set; } = false;

    /// <summary>
    /// Manufacturer name
    /// Maps to: mNhaSanXuat property in legacy class
    /// </summary>
    [StringLength(200)]
    public string NhaSanXuat { get; set; } = string.Empty;

    /// <summary>
    /// Print on service card flag
    /// Maps to: mKInLSC property in legacy class
    /// </summary>
    public int KInLSC { get; set; } = 0;

    /// <summary>
    /// Invoice unit price
    /// Maps to: mDonGiaXHD property in legacy class
    /// </summary>
    public decimal DonGiaXHD { get; set; } = 0;

    /// <summary>
    /// Invoice total amount
    /// Maps to: mThanhTienXHD property in legacy class
    /// </summary>
    public decimal ThanhTienXHD { get; set; } = 0;

    /// <summary>
    /// Replacement part flag
    /// Maps to: mIsThayThe property in legacy class
    /// </summary>
    public bool IsThayThe { get; set; } = false;

    /// <summary>
    /// Replacement deadline (YYYYMMDD format)
    /// Maps to: mHanThayThe property in legacy class
    /// </summary>
    [StringLength(8)]
    public string HanThayThe { get; set; } = string.Empty;

    /// <summary>
    /// Additional item flag
    /// Maps to: mIsGhiThem property in legacy class
    /// </summary>
    public bool IsGhiThem { get; set; } = false;

    /// <summary>
    /// Back-ordered parts flag
    /// Maps to: mIsNoPhuTung property in legacy class
    /// </summary>
    public bool IsNoPhuTung { get; set; } = false;

    /// <summary>
    /// Back-ordered quantity
    /// Maps to: mSoLuongNoPhuTung property in legacy class
    /// </summary>
    public decimal SoLuongNoPhuTung { get; set; } = 0;

    /// <summary>
    /// Back-order deadline (YYYYMMDD format)
    /// Maps to: mHanNoPhuTung property in legacy class
    /// </summary>
    [StringLength(8)]
    public string HanNoPhuTung { get; set; } = string.Empty;

    /// <summary>
    /// Handling direction/instructions
    /// Maps to: mHuongXuLy property in legacy class
    /// </summary>
    [StringLength(500)]
    public string HuongXuLy { get; set; } = string.Empty;

    /// <summary>
    /// Damage condition description
    /// Maps to: mTinhTrangHuHong property in legacy class
    /// </summary>
    [StringLength(500)]
    public string TinhTrangHuHong { get; set; } = string.Empty;

    /// <summary>
    /// Subcontracted work flag
    /// Maps to: mIsThuocGoiThau property in legacy class
    /// </summary>
    public bool IsThuocGoiThau { get; set; } = false;

    /// <summary>
    /// Core exchange flag
    /// Maps to: mIsThuHoiDoCu property in legacy class
    /// </summary>
    public bool IsThuHoiDoCu { get; set; } = false;

    /// <summary>
    /// Part code for quotation
    /// Maps to: mMaPhuTungBG property in legacy class
    /// </summary>
    [StringLength(50)]
    public string MaPhuTungBG { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGiaChiTiet list display with joined data
/// Optimized for automotive quotation detail lists with related information
/// Used by list and search operations
/// </summary>
public class BaoGiaChiTietListDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string SoBaoGia { get; set; } = string.Empty; // From parent quotation
    public string NoiDung { get; set; } = string.Empty;
    public string DonViTinh { get; set; } = string.Empty; // From unit lookup
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public decimal TienChietKhau { get; set; } = 0;
    public decimal TienThue { get; set; } = 0;
    public decimal ThanhTienSauCK { get; set; } = 0; // After discount
    public decimal ThanhTienCuoiCung { get; set; } = 0; // Final amount
    public int Loai { get; set; } = 0;
    public string LoaiText { get; set; } = string.Empty; // Service/Product text
    public string TenHangHoa { get; set; } = string.Empty; // Product name if applicable
    public string TenLoaiDichVu { get; set; } = string.Empty; // Service type name
    public string TenBoPhan { get; set; } = string.Empty; // Department name
    public int SoThuTu { get; set; } = 0;
    public int Huy { get; set; } = 0;
    public bool IsService { get; set; } = false;
    public bool IsProduct { get; set; } = false;
    public bool IsLabor { get; set; } = false;
    public bool IsParts { get; set; } = false;
    public bool IsWarranty { get; set; } = false;
    public bool IsInsurance { get; set; } = false;
    public bool IsSubcontracted { get; set; } = false;
    public bool IsBackordered { get; set; } = false;
    public bool IsReplacement { get; set; } = false;
    public string Status { get; set; } = string.Empty;
    public string StatusColor { get; set; } = string.Empty;
}

/// <summary>
/// DTO for creating new BaoGiaChiTiet
/// Contains only required fields for creation
/// </summary>
public class CreateBaoGiaChiTietDto
{
    [Required]
    public string KhoaBaoGia { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string NoiDung { get; set; } = string.Empty;

    public string KhoaDonViTinh { get; set; } = string.Empty;
    public decimal SoLuong { get; set; } = 1;
    public decimal DonGia { get; set; } = 0;
    public int Loai { get; set; } = 0;
    public string KhoaHangHoa { get; set; } = string.Empty;
    public string KhoaLoaiDichVu { get; set; } = string.Empty;
    public string KhoaBoPhan { get; set; } = string.Empty;
    public string DienGiai { get; set; } = string.Empty;
    public int SoThuTu { get; set; } = 0;
}

/// <summary>
/// DTO for updating BaoGiaChiTiet status
/// Used for cancellation and status change operations
/// </summary>
public class UpdateBaoGiaChiTietStatusDto
{
    [Required]
    public string Khoa { get; set; } = string.Empty;

    public int Huy { get; set; } = 0;
    public int DaChoGia { get; set; } = 0;
    public string DienGiai { get; set; } = string.Empty;
}

/// <summary>
/// DTO for BaoGiaChiTiet search operations
/// Used for advanced search and filtering
/// </summary>
public class BaoGiaChiTietSearchDto
{
    public string? KhoaBaoGia { get; set; }
    public string? NoiDung { get; set; }
    public string? KhoaHangHoa { get; set; }
    public string? KhoaLoaiDichVu { get; set; }
    public string? KhoaBoPhan { get; set; }
    public int? Loai { get; set; }
    public int? Huy { get; set; }
    public decimal? DonGiaFrom { get; set; }
    public decimal? DonGiaTo { get; set; }
    public decimal? ThanhTienFrom { get; set; }
    public decimal? ThanhTienTo { get; set; }
    public bool? IsBaoHanh { get; set; }
    public bool? IsThayThe { get; set; }
    public bool? IsNoPhuTung { get; set; }
    public bool? IsThuocGoiThau { get; set; }
    public bool? IsThuHoiDoCu { get; set; }
    public string? XuatXu { get; set; }
    public string? NhaSanXuat { get; set; }
}

/// <summary>
/// DTO for BaoGiaChiTiet pricing calculations
/// Used for price calculation and validation
/// </summary>
public class BaoGiaChiTietPricingDto
{
    public string Khoa { get; set; } = string.Empty;
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal DonGiaTruocHoaHong { get; set; } = 0;
    public decimal TyLeChietKhau { get; set; } = 0;
    public decimal TienChietKhau { get; set; } = 0;
    public decimal CKTang { get; set; } = 0;
    public int TyLeThue { get; set; } = 0;
    public decimal TienThue { get; set; } = 0;
    public int PhanTramHoaHong { get; set; } = 0;
    public decimal TienHoaHong { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public decimal ThanhTienTruocDuyetBH { get; set; } = 0;
    public decimal DonGiaTruocDuyetBH { get; set; } = 0;
    public decimal DonGiaXHD { get; set; } = 0;
    public decimal ThanhTienXHD { get; set; } = 0;
    public decimal ThanhTienSauChietKhau { get; set; } = 0;
    public decimal ThanhTienSauThue { get; set; } = 0;
    public decimal ThanhTienCuoiCung { get; set; } = 0;
    public bool IsValidPricing { get; set; } = true;
    public List<string> PricingErrors { get; set; } = new();
}

/// <summary>
/// DTO for automotive service quotation details
/// Specialized for automotive service line items
/// </summary>
public class AutomotiveQuotationDetailDto
{
    public string Khoa { get; set; } = string.Empty;
    public string KhoaBaoGia { get; set; } = string.Empty;
    public string NoiDung { get; set; } = string.Empty;
    public int Loai { get; set; } = 0;
    public string LoaiText { get; set; } = string.Empty;
    public bool IsLaborService { get; set; } = false;
    public bool IsPartsService { get; set; } = false;
    public bool IsDiagnosticService { get; set; } = false;
    public bool IsMaintenanceService { get; set; } = false;
    public bool IsRepairService { get; set; } = false;
    public bool IsBodyworkService { get; set; } = false;
    public bool IsPaintingService { get; set; } = false;
    public bool IsElectricalService { get; set; } = false;
    public bool IsEngineService { get; set; } = false;
    public bool IsTransmissionService { get; set; } = false;
    public bool IsBrakeService { get; set; } = false;
    public bool IsSuspensionService { get; set; } = false;
    public bool IsACService { get; set; } = false;
    public bool IsTireService { get; set; } = false;
    public bool IsOilChangeService { get; set; } = false;
    public bool IsInspectionService { get; set; } = false;
    public decimal SoLuong { get; set; } = 0;
    public decimal DonGia { get; set; } = 0;
    public decimal ThanhTien { get; set; } = 0;
    public string DonViTinh { get; set; } = string.Empty;
    public string TenHangHoa { get; set; } = string.Empty;
    public string TenLoaiDichVu { get; set; } = string.Empty;
    public string TenBoPhan { get; set; } = string.Empty;
    public bool IsBaoHanh { get; set; } = false;
    public bool IsThayThe { get; set; } = false;
    public bool IsNoPhuTung { get; set; } = false;
    public bool IsThuocGoiThau { get; set; } = false;
    public string HuongXuLy { get; set; } = string.Empty;
    public string TinhTrangHuHong { get; set; } = string.Empty;
    public string XuatXu { get; set; } = string.Empty;
    public string NhaSanXuat { get; set; } = string.Empty;
    public int SoThuTu { get; set; } = 0;
    public string Status { get; set; } = string.Empty;
}
