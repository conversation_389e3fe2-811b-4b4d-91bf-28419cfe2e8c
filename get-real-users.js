/**
 * Get Real Users from Database
 * Retrieves actual users with their employee information from DM_DoiTuong
 */

const sql = require('mssql');

// Database configuration
const config = {
    server: 'DESKTOP-J990JBB',
    database: 'carsoft_giaphat',
    user: 'sa',
    password: 'cxM123654',
    options: {
        enableArithAbort: true,
        trustServerCertificate: true,
        encrypt: false
    }
};

async function getRealUsers() {
    try {
        console.log('🔍 Getting Real Users from Database');
        console.log('==================================\n');
        
        const pool = await sql.connect(config);
        
        // Get users with their employee information (exact legacy logic)
        const usersResult = await pool.request().query(`
            SELECT 
                u.TenDangNhap,
                u.KhoaNhanVien,
                u.MatKhau,
                u.DonViDangNhap,
                ISNULL(e.TenViet, u.TenDangNhap) as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                ISNULL(e.Ma, '') as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                CASE WHEN u.KhoaNhanVien = '0000000000' THEN 'ADMIN' ELSE 'USER' END as UserType
            FROM HT_NguoiDung u
            LEFT JOIN DM_DoiTuong e ON e.Khoa = u.KhoaNhanVien
            WHERE u.TenDangNhap IS NOT NULL 
            AND u.TenDangNhap != ''
            ORDER BY u.TenDangNhap
        `);
        
        console.log(`👥 Found ${usersResult.recordset.length} real users:\n`);
        
        usersResult.recordset.forEach((user, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. Username: ${user.TenDangNhap}`);
            console.log(`    Employee: ${user.EmployeeName} (${user.EmployeeCode})`);
            console.log(`    Type: ${user.UserType}`);
            console.log(`    Employee ID: ${user.KhoaNhanVien}`);
            console.log(`    Password Hash: ${user.MatKhau}`);
            
            if (user.DonViDangNhap) {
                console.log(`    Allowed Clients: ${user.DonViDangNhap}`);
            }
            console.log('');
        });
        
        // Get client information
        console.log('🏢 Available Clients:');
        console.log('===================\n');
        
        const clientsResult = await pool.request().query(`
            SELECT Khoa, Ma, TenViet, TenAnh
            FROM DM_DonVi
            ORDER BY Ma
        `);
        
        clientsResult.recordset.forEach((client, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${client.TenViet} (${client.Ma})`);
            console.log(`    Khoa: ${client.Khoa}`);
            console.log('');
        });
        
        await pool.close();
        
        console.log('🎯 TESTING RECOMMENDATIONS:');
        console.log('===========================');
        console.log('1. Use existing usernames from the list above');
        console.log('2. Try common passwords with legacy encryption');
        console.log('3. Use master password if available');
        console.log('4. Test with actual employee relationships');
        
        return usersResult.recordset;
        
    } catch (error) {
        console.error('❌ Error getting real users:', error.message);
        return [];
    }
}

// Run the script
getRealUsers();
