using Microsoft.EntityFrameworkCore;

namespace GP.Mobile.Data;

/// <summary>
/// Entity Framework DbContext for GP Mobile API
/// This is used for any new tables we might need to create
/// Legacy tables are accessed via Dapper/raw SQL to avoid conflicts
/// </summary>
public class GPMobileDbContext : DbContext
{
    public GPMobileDbContext(DbContextOptions<GPMobileDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure any new entities here
        // DO NOT configure legacy tables - they are accessed via Dapper
        
        // Example: If we need to add mobile-specific tables
        // modelBuilder.Entity<MobileSession>().ToTable("Mobile_Sessions");
    }
}

/// <summary>
/// Example of a mobile-specific entity (if needed)
/// </summary>
public class MobileSession
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
    public DateTime? LogoutTime { get; set; }
    public bool IsActive { get; set; }
}
